# DIPS Pro 计费系统详细设计文档

## 1. 系统概述

### 1.1 项目背景

DIPS Pro 是一个基于 Spring Boot 3.3.2 的企业级 AI 对话应用。为了实现商业化运营，需要建立一套完整的计费系统，对用户在 AI 对话过程中的 Token 使用进行精确计量和计费。

### 1.2 设计目标

- 精确计算对话过程中的输入输出 Token 用量
- 支持思维链 Token 统计和合并计算
- 实现预付费模式和余额管理
- 提供灵活的套餐配置机制
- 支持用户申诉和退费机制
- 预留支付接口（微信、支付宝）
- 确保计费的准确性和可靠性

### 1.3 技术选型

- **Token 计算**: JTokkit 1.1.0（OpenAI 官方推荐 Java 库）
- **计算策略**: 异步计算（后台计算，前端显示预估）
- **扣费时机**: 收到完整响应后扣费
- **支付方式**: 预留微信、支付宝接口
- **数据库**: PostgreSQL

## 2. 系统架构设计

### 2.1 系统架构图

以下是 DIPS Pro 计费系统的整体架构图，展示了各个组件之间的关系和数据流向：

![http://pic.mei6.pub/2025/06/11/mermaid-diagram-2025-06-11-202943.png](http://pic.mei6.pub/2025/06/11/mermaid-diagram-2025-06-11-202943.png)

```mermaid
graph TB
    subgraph "用户端"
        U1[用户发送消息]
        U2[余额检查]
        U3[Token预估]
        U4[接收响应]
        U5[查看余额]
        U6[充值]
        U7[申诉]
    end

    subgraph "前端 Vue3"
        F1[ChatSender组件]
        F2[BalanceDisplay组件]
        F3[TokenCounter组件]
        F4[RechargeForm组件]
        F5[AppealForm组件]
        F6[UsageHistory组件]
    end

    subgraph "后端 Spring Boot"
        subgraph "billing模块"
            B1[BillingController]
            B2[BillingService]
            B3[TokenCalculatorService]
            B4[BalanceService]
            B5[PackageService]
            B6[AppealService]
            B7[PaymentService]
        end

        subgraph "chat模块"
            C1[ChatController]
            C2[ChatService]
        end

        subgraph "工具类"
            T1[TokenCalculatorUtil]
            T2[BillingUtil]
        end
    end

    subgraph "数据存储"
        DB1[(billing_packages<br/>套餐配置)]
        DB2[(user_balances<br/>用户余额)]
        DB3[(billing_usage_records<br/>使用记录)]
        DB4[(billing_transactions<br/>交易记录)]
        DB5[(billing_appeals<br/>申诉记录)]
        DB6[(payment_records<br/>支付记录)]
    end

    subgraph "外部服务"
        EX1[n8n AI服务]
        EX2[微信支付]
        EX3[支付宝]
        EX4[JTokkit库]
    end

    U1 --> F1
    U2 --> F2
    U3 --> F3
    U5 --> F2
    U6 --> F4
    U7 --> F5

    F1 --> B1
    F1 --> C1
    F2 --> B1
    F3 --> B1
    F4 --> B1
    F5 --> B1
    F6 --> B1

    B1 --> B2
    B1 --> B4
    B1 --> B5
    B1 --> B6
    B1 --> B7

    C1 --> C2
    C2 --> EX1

    B2 --> B3
    B2 --> B4
    B3 --> T1
    B2 --> T2

    T1 --> EX4
    B7 --> EX2
    B7 --> EX3

    B2 --> DB3
    B4 --> DB2
    B5 --> DB1
    B6 --> DB5
    B7 --> DB4
    B7 --> DB6

    style U1 fill:#e1f5fe
    style B2 fill:#f3e5f5
    style T1 fill:#e8f5e8
    style DB2 fill:#fff3e0
```

### 2.2 计费流程图

以下流程图详细展示了从用户发送消息到完成计费的完整业务流程：

![http://pic.mei6.pub/2025/06/11/mermaid-diagram-2025-06-11-203259.png](http://pic.mei6.pub/2025/06/11/mermaid-diagram-2025-06-11-203259.png)

```mermaid
graph TD
    A[用户发送消息] --> B{余额是否>0?}
    B -->|否| C[显示余额不足<br/>跳转充值页面]
    B -->|是| D[发送请求到AI服务]

    D --> E[AI服务处理]
    E --> F{处理是否成功?}
    F -->|否| G[不扣费<br/>显示错误信息]
    F -->|是| H[接收完整响应]

    H --> I[Token计算服务]
    I --> J[计算输入Token]
    I --> K[计算思维链Token]
    I --> L[计算输出Token]

    J --> M[获取用户套餐配置]
    K --> M
    L --> M

    M --> N[计算总费用]
    N --> O[余额扣费]

    O --> P[免费Token优先]
    P --> Q[赠送余额其次]
    Q --> R[充值余额最后]

    R --> S[记录使用明细]
    S --> T[更新用户余额]
    T --> U[返回响应给用户]

    C --> V[充值流程]
    V --> W[支付成功]
    W --> X[更新余额]
    X --> Y[记录充值记录]

    U --> Z{用户是否申诉?}
    Z -->|是| AA[提交申诉]
    AA --> BB[管理员审核]
    BB --> CC{审核结果}
    CC -->|同意| DD[退还费用]
    CC -->|拒绝| EE[申诉驳回]
    DD --> FF[更新余额]

    style A fill:#e1f5fe
    style B fill:#fff3e0
    style O fill:#f3e5f5
    style S fill:#e8f5e8
    style AA fill:#ffebee
```

### 2.3 模块架构

采用独立计费模块设计，符合 DIPS Pro 的模块化架构原则：

```
src/main/java/com/dipspro/modules/billing/
├── controller/              # 控制器层
│   ├── BillingController.java
│   ├── PackageController.java
│   ├── BalanceController.java
│   ├── PaymentController.java
│   └── AppealController.java
├── service/                 # 业务逻辑层
│   ├── impl/
│   ├── BillingService.java
│   ├── TokenCalculatorService.java
│   ├── PackageService.java
│   ├── BalanceService.java
│   ├── PaymentService.java
│   └── AppealService.java
├── entity/                  # 实体层
│   ├── BillingPackage.java
│   ├── BillingTransaction.java
│   ├── BillingUsageRecord.java
│   ├── BillingAppeal.java
│   ├── UserBalance.java
│   └── PaymentRecord.java
├── repository/              # 数据访问层
├── dto/                     # 数据传输对象
├── enums/                   # 枚举类型
├── config/                  # 配置类
└── exception/               # 异常类
```

### 2.2 核心组件关系

- **TokenCalculatorService**: 负责 Token 计算和缓存
- **BillingService**: 核心计费逻辑，协调各组件
- **BalanceService**: 余额管理和扣费操作
- **PackageService**: 套餐配置和价格管理
- **PaymentService**: 支付处理（预留接口）
- **AppealService**: 申诉处理流程

## 3. 数据库设计

### 3.1 数据库关系图

以下是计费系统数据库的实体关系图，展示了各个表之间的关联关系：

![http://pic.mei6.pub/2025/06/11/mermaid-diagram-2025-06-11-222912.png](http://pic.mei6.pub/2025/06/11/mermaid-diagram-2025-06-11-222912.png)

```mermaid
erDiagram
    users {
        bigint id PK
        string username
        string mobile
        timestamp created_at
        timestamp updated_at
    }

    chat_messages {
        uuid id PK
        uuid conversation_id
        string role "USER/ASSISTANT"
        text content
        jsonb intermediate_steps_json
        timestamp created_at
        timestamp updated_at
    }

    b_billing_packages {
        bigint id PK
        string name "套餐名称"
        text description "套餐描述"
        decimal input_token_price "输入Token单价"
        decimal output_token_price "输出Token单价"
        bigint free_tokens "免费Token额度"
        bigint max_tokens_per_request "单次请求Token限制"
        bigint daily_token_limit "每日Token限制"
        boolean is_active
        integer sort_order "排序权重"
        timestamp created_at
        timestamp updated_at
    }

    b_user_balances {
        bigint id PK
        bigint user_id FK "关联users.id"
        bigint package_id FK "关联b_billing_packages.id"
        decimal recharged_balance "充值余额"
        decimal gift_balance "赠送余额"
        bigint free_tokens "免费Token"
        bigint used_tokens_today "今日已用Token"
        date last_reset_date "上次重置日期"
        decimal total_recharged "累计充值"
        decimal total_consumed "累计消费"
        timestamp created_at
        timestamp updated_at
    }

    b_billing_usage_records {
        bigint id PK
        bigint user_id FK "关联users.id"
        uuid message_id FK "关联chat_messages.id获取内容"
        uuid conversation_id "对话ID"
        string message_role "消息角色USER/ASSISTANT"
        string model_name "使用的模型"
        bigint input_tokens "输入Token数"
        bigint output_tokens "输出Token数"
        bigint thought_chain_tokens "思维链Token数"
        bigint total_tokens "总Token数"
        decimal input_cost "输入费用"
        decimal output_cost "输出费用"
        decimal total_cost "总费用"
        string status "状态SUCCESS/FAILED/PENDING"
        string billing_status "计费状态BILLED/UNBILLED/REFUNDED"
        bigint package_id FK "使用的套餐ID"
        decimal input_token_price "计费时输入单价"
        decimal output_token_price "计费时输出单价"
        boolean is_appealed "是否已申诉"
        string appeal_status "申诉状态"
        timestamp request_time "请求时间"
        timestamp response_time "响应时间"
        bigint duration_ms "处理时长毫秒"
        timestamp created_at
        timestamp updated_at
    }

    b_billing_transactions {
        bigint id PK
        bigint user_id FK "关联users.id"
        string transaction_no "交易流水号"
        string type "交易类型RECHARGE/DEDUCT/REFUND/GIFT"
        decimal amount "交易金额"
        string balance_type "余额类型RECHARGED/GIFT/FREE_TOKENS"
        bigint usage_record_id FK "关联b_billing_usage_records.id"
        bigint payment_record_id FK "关联b_payment_records.id"
        bigint appeal_id FK "关联b_billing_appeals.id"
        decimal balance_before "交易前余额"
        decimal balance_after "交易后余额"
        text description "交易描述"
        bigint operator_id FK "操作员ID"
        timestamp created_at
    }

    b_billing_appeals {
        bigint id PK
        string appeal_no "申诉单号"
        bigint user_id FK "关联users.id"
        bigint usage_record_id FK "关联b_billing_usage_records.id"
        text reason "申诉原因"
        text user_description "用户描述"
        text evidence_urls "证据URLs(JSON)"
        bigint admin_id FK "处理管理员ID"
        string status "PENDING/APPROVED/REJECTED/CANCELLED"
        text admin_comment "管理员意见"
        decimal refund_amount "退费金额"
        bigint refund_transaction_id FK "退费交易ID"
        timestamp submitted_at "提交时间"
        timestamp processed_at "处理时间"
        timestamp created_at
        timestamp updated_at
    }

    b_payment_records {
        bigint id PK
        string payment_no "支付单号"
        bigint user_id FK "关联users.id"
        string payment_method "支付方式WECHAT/ALIPAY/ADMIN"
        string payment_platform "支付平台"
        string platform_order_no "第三方订单号"
        string platform_transaction_no "第三方交易号"
        decimal amount "支付金额"
        string currency "货币代码"
        string status "PENDING/SUCCESS/FAILED/CANCELLED/REFUNDED"
        text callback_data "回调数据"
        timestamp callback_time "回调时间"
        string client_ip "客户端IP"
        text user_agent "用户代理"
        timestamp expired_at "过期时间"
        timestamp paid_at "支付完成时间"
        timestamp created_at
        timestamp updated_at
    }

    %% 用户相关关系
    users ||--o{ b_user_balances : "用户拥有余额"
    users ||--o{ b_billing_usage_records : "用户使用记录"
    users ||--o{ b_billing_transactions : "用户交易记录"
    users ||--o{ b_billing_appeals : "用户申诉记录"
    users ||--o{ b_payment_records : "用户支付记录"
    users ||--o{ b_billing_appeals : "管理员处理申诉"

    %% 套餐相关关系
    b_billing_packages ||--o{ b_user_balances : "套餐配置余额"
    b_billing_packages ||--o{ b_billing_usage_records : "套餐计费"

    %% 消息相关关系
    chat_messages ||--o| b_billing_usage_records : "消息计费记录"

    %% 使用记录相关关系
    b_billing_usage_records ||--o{ b_billing_transactions : "使用产生交易"
    b_billing_usage_records ||--o| b_billing_appeals : "使用记录申诉"

    %% 申诉相关关系
    b_billing_appeals ||--o| b_billing_transactions : "申诉退费交易"

    %% 支付相关关系
    b_payment_records ||--o{ b_billing_transactions : "支付产生交易"
```

### 3.2 套餐配置表 (b_billing_packages)

```sql
CREATE TABLE b_billing_packages (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL, -- 套餐名称
    description TEXT, -- 套餐描述
    input_token_price DECIMAL(10,6) NOT NULL, -- 输入Token单价（每千Token价格）
    output_token_price DECIMAL(10,6) NOT NULL, -- 输出Token单价（每千Token价格）
    free_tokens BIGINT DEFAULT 0, -- 新用户免费Token数量
    max_tokens_per_request BIGINT DEFAULT 0, -- 单次请求最大Token限制，0表示无限制
    daily_token_limit BIGINT DEFAULT 0, -- 每日Token使用限制，0表示无限制
    is_active BOOLEAN DEFAULT true, -- 是否激活
    sort_order INTEGER DEFAULT 0, -- 排序权重
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_b_billing_packages_active_sort ON b_billing_packages (is_active, sort_order);
CREATE INDEX idx_b_billing_packages_created_at ON b_billing_packages (created_at);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_b_billing_packages_updated_at
    BEFORE UPDATE ON b_billing_packages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 添加表注释
COMMENT ON TABLE b_billing_packages IS '计费套餐配置表';
COMMENT ON COLUMN b_billing_packages.id IS '主键ID';
COMMENT ON COLUMN b_billing_packages.name IS '套餐名称';
COMMENT ON COLUMN b_billing_packages.description IS '套餐描述';
COMMENT ON COLUMN b_billing_packages.input_token_price IS '输入Token单价（每千Token价格）';
COMMENT ON COLUMN b_billing_packages.output_token_price IS '输出Token单价（每千Token价格）';
COMMENT ON COLUMN b_billing_packages.free_tokens IS '新用户免费Token数量';
COMMENT ON COLUMN b_billing_packages.max_tokens_per_request IS '单次请求最大Token限制，0表示无限制';
COMMENT ON COLUMN b_billing_packages.daily_token_limit IS '每日Token使用限制，0表示无限制';
COMMENT ON COLUMN b_billing_packages.is_active IS '是否激活';
COMMENT ON COLUMN b_billing_packages.sort_order IS '排序权重';
COMMENT ON COLUMN b_billing_packages.created_at IS '创建时间';
COMMENT ON COLUMN b_billing_packages.updated_at IS '更新时间';
```

### 3.3 用户余额表 (b_user_balances)

```sql
CREATE TABLE b_user_balances (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL, -- 用户ID，关联users.id
    package_id BIGINT, -- 当前套餐ID，关联b_billing_packages.id
    recharged_balance DECIMAL(15,2) DEFAULT 0, -- 充值余额（人民币）
    gift_balance DECIMAL(15,2) DEFAULT 0, -- 赠送余额（人民币）
    free_tokens BIGINT DEFAULT 0, -- 免费Token余额
    used_tokens_today BIGINT DEFAULT 0, -- 今日已使用Token数
    last_reset_date DATE, -- 上次重置日期
    total_recharged DECIMAL(15,2) DEFAULT 0, -- 累计充值金额
    total_consumed DECIMAL(15,2) DEFAULT 0, -- 累计消费金额
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建唯一约束和索引
ALTER TABLE b_user_balances ADD CONSTRAINT uk_b_user_balances_user_id UNIQUE (user_id);
CREATE INDEX idx_b_user_balances_package_id ON b_user_balances (package_id);
CREATE INDEX idx_b_user_balances_updated_at ON b_user_balances (updated_at);

-- 创建更新时间触发器
CREATE TRIGGER update_b_user_balances_updated_at
    BEFORE UPDATE ON b_user_balances
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 添加表注释
COMMENT ON TABLE b_user_balances IS '用户余额表';
COMMENT ON COLUMN b_user_balances.id IS '主键ID';
COMMENT ON COLUMN b_user_balances.user_id IS '用户ID，关联users.id';
COMMENT ON COLUMN b_user_balances.package_id IS '当前套餐ID，关联b_billing_packages.id';
COMMENT ON COLUMN b_user_balances.recharged_balance IS '充值余额（人民币）';
COMMENT ON COLUMN b_user_balances.gift_balance IS '赠送余额（人民币）';
COMMENT ON COLUMN b_user_balances.free_tokens IS '免费Token余额';
COMMENT ON COLUMN b_user_balances.used_tokens_today IS '今日已使用Token数';
COMMENT ON COLUMN b_user_balances.last_reset_date IS '上次重置日期';
COMMENT ON COLUMN b_user_balances.total_recharged IS '累计充值金额';
COMMENT ON COLUMN b_user_balances.total_consumed IS '累计消费金额';
COMMENT ON COLUMN b_user_balances.created_at IS '创建时间';
COMMENT ON COLUMN b_user_balances.updated_at IS '更新时间';
```

### 3.4 使用记录表 (b_billing_usage_records)

```sql
CREATE TABLE b_billing_usage_records (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL, -- 用户ID，关联users.id
    message_id UUID NOT NULL, -- 消息ID，关联chat_messages.id
    conversation_id UUID, -- 对话ID
    message_role VARCHAR(20), -- 消息角色：USER, ASSISTANT
    model_name VARCHAR(50) NOT NULL, -- 使用的模型名称

    -- Token统计
    input_tokens BIGINT NOT NULL DEFAULT 0, -- 输入Token数量
    output_tokens BIGINT NOT NULL DEFAULT 0, -- 输出Token数量
    thought_chain_tokens BIGINT DEFAULT 0, -- 思维链Token数量
    total_tokens BIGINT NOT NULL DEFAULT 0, -- 总Token数量

    -- 费用计算
    input_cost DECIMAL(10,6) DEFAULT 0, -- 输入费用
    output_cost DECIMAL(10,6) DEFAULT 0, -- 输出费用
    total_cost DECIMAL(10,6) NOT NULL DEFAULT 0, -- 总费用

    -- 状态管理
    status VARCHAR(20) DEFAULT 'SUCCESS', -- 状态：SUCCESS, FAILED, PENDING
    billing_status VARCHAR(20) DEFAULT 'BILLED', -- 计费状态：BILLED, UNBILLED, REFUNDED

    -- 套餐信息
    package_id BIGINT, -- 使用的套餐ID，关联b_billing_packages.id
    input_token_price DECIMAL(10,6), -- 计费时的输入Token单价
    output_token_price DECIMAL(10,6), -- 计费时的输出Token单价

    -- 申诉相关
    is_appealed BOOLEAN DEFAULT false, -- 是否已申诉
    appeal_status VARCHAR(20), -- 申诉状态：PENDING, APPROVED, REJECTED

    -- 时间字段
    request_time TIMESTAMP WITH TIME ZONE, -- 请求时间
    response_time TIMESTAMP WITH TIME ZONE, -- 响应时间
    duration_ms BIGINT, -- 处理时长（毫秒）
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_b_billing_usage_records_user_created ON b_billing_usage_records (user_id, created_at);
CREATE INDEX idx_b_billing_usage_records_message_id ON b_billing_usage_records (message_id);
CREATE INDEX idx_b_billing_usage_records_conversation_id ON b_billing_usage_records (conversation_id);
CREATE INDEX idx_b_billing_usage_records_message_role ON b_billing_usage_records (message_role);
CREATE INDEX idx_b_billing_usage_records_status ON b_billing_usage_records (status);
CREATE INDEX idx_b_billing_usage_records_billing_status ON b_billing_usage_records (billing_status);
CREATE INDEX idx_b_billing_usage_records_appeal ON b_billing_usage_records (is_appealed, appeal_status);
CREATE INDEX idx_b_billing_usage_records_package_id ON b_billing_usage_records (package_id);
CREATE INDEX idx_b_billing_usage_records_created_at ON b_billing_usage_records (created_at);

-- 创建更新时间触发器
CREATE TRIGGER update_b_billing_usage_records_updated_at
    BEFORE UPDATE ON b_billing_usage_records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 添加表注释
COMMENT ON TABLE b_billing_usage_records IS '计费使用记录表（以消息为单位）';
COMMENT ON COLUMN b_billing_usage_records.id IS '主键ID';
COMMENT ON COLUMN b_billing_usage_records.user_id IS '用户ID，关联users.id';
COMMENT ON COLUMN b_billing_usage_records.message_id IS '消息ID，关联chat_messages.id（消息内容通过此ID关联获取）';
COMMENT ON COLUMN b_billing_usage_records.conversation_id IS '对话ID';
COMMENT ON COLUMN b_billing_usage_records.message_role IS '消息角色：USER, ASSISTANT';
COMMENT ON COLUMN b_billing_usage_records.model_name IS '使用的模型名称';
COMMENT ON COLUMN b_billing_usage_records.input_tokens IS '输入Token数量';
COMMENT ON COLUMN b_billing_usage_records.output_tokens IS '输出Token数量';
COMMENT ON COLUMN b_billing_usage_records.thought_chain_tokens IS '思维链Token数量';
COMMENT ON COLUMN b_billing_usage_records.total_tokens IS '总Token数量';
COMMENT ON COLUMN b_billing_usage_records.input_cost IS '输入费用';
COMMENT ON COLUMN b_billing_usage_records.output_cost IS '输出费用';
COMMENT ON COLUMN b_billing_usage_records.total_cost IS '总费用';
COMMENT ON COLUMN b_billing_usage_records.status IS '状态：SUCCESS, FAILED, PENDING';
COMMENT ON COLUMN b_billing_usage_records.billing_status IS '计费状态：BILLED, UNBILLED, REFUNDED';
COMMENT ON COLUMN b_billing_usage_records.package_id IS '使用的套餐ID，关联b_billing_packages.id';
COMMENT ON COLUMN b_billing_usage_records.input_token_price IS '计费时的输入Token单价';
COMMENT ON COLUMN b_billing_usage_records.output_token_price IS '计费时的输出Token单价';
COMMENT ON COLUMN b_billing_usage_records.is_appealed IS '是否已申诉';
COMMENT ON COLUMN b_billing_usage_records.appeal_status IS '申诉状态：PENDING, APPROVED, REJECTED';
COMMENT ON COLUMN b_billing_usage_records.request_time IS '请求时间';
COMMENT ON COLUMN b_billing_usage_records.response_time IS '响应时间';
COMMENT ON COLUMN b_billing_usage_records.duration_ms IS '处理时长（毫秒）';
COMMENT ON COLUMN b_billing_usage_records.created_at IS '创建时间';
COMMENT ON COLUMN b_billing_usage_records.updated_at IS '更新时间';
```

### 3.5 交易记录表 (b_billing_transactions)

```sql
CREATE TABLE b_billing_transactions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL, -- 用户ID，关联users.id
    transaction_no VARCHAR(100) NOT NULL, -- 交易流水号
    type VARCHAR(20) NOT NULL, -- 交易类型：RECHARGE, DEDUCT, REFUND, GIFT
    amount DECIMAL(15,2) NOT NULL, -- 交易金额（正数为收入，负数为支出）
    balance_type VARCHAR(20) NOT NULL, -- 余额类型：RECHARGED, GIFT, FREE_TOKENS

    -- 关联记录
    usage_record_id BIGINT, -- 关联的使用记录ID，关联b_billing_usage_records.id
    payment_record_id BIGINT, -- 关联的支付记录ID，关联b_payment_records.id
    appeal_id BIGINT, -- 关联的申诉记录ID，关联b_billing_appeals.id

    -- 余额快照
    balance_before DECIMAL(15,2), -- 交易前余额
    balance_after DECIMAL(15,2), -- 交易后余额

    description TEXT, -- 交易描述
    operator_id BIGINT, -- 操作员ID（管理员操作时），关联users.id

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建唯一约束和索引
ALTER TABLE b_billing_transactions ADD CONSTRAINT uk_b_billing_transactions_transaction_no UNIQUE (transaction_no);
CREATE INDEX idx_b_billing_transactions_user_created ON b_billing_transactions (user_id, created_at);
CREATE INDEX idx_b_billing_transactions_type ON b_billing_transactions (type);
CREATE INDEX idx_b_billing_transactions_usage_record ON b_billing_transactions (usage_record_id);
CREATE INDEX idx_b_billing_transactions_payment_record ON b_billing_transactions (payment_record_id);
CREATE INDEX idx_b_billing_transactions_appeal_id ON b_billing_transactions (appeal_id);
CREATE INDEX idx_b_billing_transactions_operator_id ON b_billing_transactions (operator_id);
CREATE INDEX idx_b_billing_transactions_created_at ON b_billing_transactions (created_at);

-- 添加表注释
COMMENT ON TABLE b_billing_transactions IS '计费交易记录表';
COMMENT ON COLUMN b_billing_transactions.id IS '主键ID';
COMMENT ON COLUMN b_billing_transactions.user_id IS '用户ID，关联users.id';
COMMENT ON COLUMN b_billing_transactions.transaction_no IS '交易流水号';
COMMENT ON COLUMN b_billing_transactions.type IS '交易类型：RECHARGE, DEDUCT, REFUND, GIFT';
COMMENT ON COLUMN b_billing_transactions.amount IS '交易金额（正数为收入，负数为支出）';
COMMENT ON COLUMN b_billing_transactions.balance_type IS '余额类型：RECHARGED, GIFT, FREE_TOKENS';
COMMENT ON COLUMN b_billing_transactions.usage_record_id IS '关联的使用记录ID，关联b_billing_usage_records.id';
COMMENT ON COLUMN b_billing_transactions.payment_record_id IS '关联的支付记录ID，关联b_payment_records.id';
COMMENT ON COLUMN b_billing_transactions.appeal_id IS '关联的申诉记录ID，关联b_billing_appeals.id';
COMMENT ON COLUMN b_billing_transactions.balance_before IS '交易前余额';
COMMENT ON COLUMN b_billing_transactions.balance_after IS '交易后余额';
COMMENT ON COLUMN b_billing_transactions.description IS '交易描述';
COMMENT ON COLUMN b_billing_transactions.operator_id IS '操作员ID（管理员操作时），关联users.id';
COMMENT ON COLUMN b_billing_transactions.created_at IS '创建时间';
```

### 3.6 申诉记录表 (b_billing_appeals)

```sql
CREATE TABLE b_billing_appeals (
    id BIGSERIAL PRIMARY KEY,
    appeal_no VARCHAR(100) NOT NULL, -- 申诉单号
    user_id BIGINT NOT NULL, -- 申诉用户ID，关联users.id
    usage_record_id BIGINT NOT NULL, -- 申诉的使用记录ID，关联b_billing_usage_records.id

    -- 申诉内容
    reason TEXT NOT NULL, -- 申诉原因
    user_description TEXT, -- 用户详细描述
    evidence_urls TEXT, -- 证据文件URLs（JSON数组）

    -- 处理信息
    admin_id BIGINT, -- 处理管理员ID，关联users.id
    status VARCHAR(20) DEFAULT 'PENDING', -- 申诉状态：PENDING, APPROVED, REJECTED, CANCELLED
    admin_comment TEXT, -- 管理员处理意见

    -- 退费信息
    refund_amount DECIMAL(10,6), -- 退费金额
    refund_transaction_id BIGINT, -- 退费交易记录ID，关联b_billing_transactions.id

    -- 时间字段
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 提交时间
    processed_at TIMESTAMP WITH TIME ZONE, -- 处理时间
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建唯一约束和索引
ALTER TABLE b_billing_appeals ADD CONSTRAINT uk_b_billing_appeals_appeal_no UNIQUE (appeal_no);
ALTER TABLE b_billing_appeals ADD CONSTRAINT uk_b_billing_appeals_usage_record UNIQUE (usage_record_id);
CREATE INDEX idx_b_billing_appeals_user_status ON b_billing_appeals (user_id, status);
CREATE INDEX idx_b_billing_appeals_admin_status ON b_billing_appeals (admin_id, status);
CREATE INDEX idx_b_billing_appeals_status_submitted ON b_billing_appeals (status, submitted_at);
CREATE INDEX idx_b_billing_appeals_refund_transaction ON b_billing_appeals (refund_transaction_id);
CREATE INDEX idx_b_billing_appeals_created_at ON b_billing_appeals (created_at);

-- 创建更新时间触发器
CREATE TRIGGER update_b_billing_appeals_updated_at
    BEFORE UPDATE ON b_billing_appeals
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 添加表注释
COMMENT ON TABLE b_billing_appeals IS '计费申诉记录表';
COMMENT ON COLUMN b_billing_appeals.id IS '主键ID';
COMMENT ON COLUMN b_billing_appeals.appeal_no IS '申诉单号';
COMMENT ON COLUMN b_billing_appeals.user_id IS '申诉用户ID，关联users.id';
COMMENT ON COLUMN b_billing_appeals.usage_record_id IS '申诉的使用记录ID，关联b_billing_usage_records.id';
COMMENT ON COLUMN b_billing_appeals.reason IS '申诉原因';
COMMENT ON COLUMN b_billing_appeals.user_description IS '用户详细描述';
COMMENT ON COLUMN b_billing_appeals.evidence_urls IS '证据文件URLs（JSON数组）';
COMMENT ON COLUMN b_billing_appeals.admin_id IS '处理管理员ID，关联users.id';
COMMENT ON COLUMN b_billing_appeals.status IS '申诉状态：PENDING, APPROVED, REJECTED, CANCELLED';
COMMENT ON COLUMN b_billing_appeals.admin_comment IS '管理员处理意见';
COMMENT ON COLUMN b_billing_appeals.refund_amount IS '退费金额';
COMMENT ON COLUMN b_billing_appeals.refund_transaction_id IS '退费交易记录ID，关联b_billing_transactions.id';
COMMENT ON COLUMN b_billing_appeals.submitted_at IS '提交时间';
COMMENT ON COLUMN b_billing_appeals.processed_at IS '处理时间';
COMMENT ON COLUMN b_billing_appeals.created_at IS '创建时间';
COMMENT ON COLUMN b_billing_appeals.updated_at IS '更新时间';
```

### 3.7 支付记录表 (b_payment_records)

```sql
CREATE TABLE b_payment_records (
    id BIGSERIAL PRIMARY KEY,
    payment_no VARCHAR(100) NOT NULL, -- 支付单号
    user_id BIGINT NOT NULL, -- 用户ID，关联users.id

    -- 支付信息
    payment_method VARCHAR(20) NOT NULL, -- 支付方式：WECHAT, ALIPAY, ADMIN
    payment_platform VARCHAR(20), -- 支付平台：WECHAT_PAY, ALIPAY
    platform_order_no VARCHAR(100), -- 第三方平台订单号
    platform_transaction_no VARCHAR(100), -- 第三方平台交易号

    -- 金额信息
    amount DECIMAL(15,2) NOT NULL, -- 支付金额
    currency VARCHAR(3) DEFAULT 'CNY', -- 货币代码

    -- 状态信息
    status VARCHAR(20) DEFAULT 'PENDING', -- 支付状态：PENDING, SUCCESS, FAILED, CANCELLED, REFUNDED

    -- 回调数据
    callback_data TEXT, -- 支付平台回调数据
    callback_time TIMESTAMP WITH TIME ZONE, -- 回调时间

    -- 客户端信息
    client_ip INET, -- 客户端IP（使用PostgreSQL的INET类型）
    user_agent TEXT, -- 用户代理

    -- 时间字段
    expired_at TIMESTAMP WITH TIME ZONE, -- 支付过期时间
    paid_at TIMESTAMP WITH TIME ZONE, -- 支付完成时间
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建唯一约束和索引
ALTER TABLE b_payment_records ADD CONSTRAINT uk_b_payment_records_payment_no UNIQUE (payment_no);
ALTER TABLE b_payment_records ADD CONSTRAINT uk_b_payment_records_platform_order_no UNIQUE (platform_order_no);
CREATE INDEX idx_b_payment_records_user_status ON b_payment_records (user_id, status);
CREATE INDEX idx_b_payment_records_status_created ON b_payment_records (status, created_at);
CREATE INDEX idx_b_payment_records_platform_order ON b_payment_records (platform_order_no);
CREATE INDEX idx_b_payment_records_platform_transaction ON b_payment_records (platform_transaction_no);
CREATE INDEX idx_b_payment_records_created_at ON b_payment_records (created_at);

-- 创建更新时间触发器
CREATE TRIGGER update_b_payment_records_updated_at
    BEFORE UPDATE ON b_payment_records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 添加表注释
COMMENT ON TABLE b_payment_records IS '支付记录表';
COMMENT ON COLUMN b_payment_records.id IS '主键ID';
COMMENT ON COLUMN b_payment_records.payment_no IS '支付单号';
COMMENT ON COLUMN b_payment_records.user_id IS '用户ID，关联users.id';
COMMENT ON COLUMN b_payment_records.payment_method IS '支付方式：WECHAT, ALIPAY, ADMIN';
COMMENT ON COLUMN b_payment_records.payment_platform IS '支付平台：WECHAT_PAY, ALIPAY';
COMMENT ON COLUMN b_payment_records.platform_order_no IS '第三方平台订单号';
COMMENT ON COLUMN b_payment_records.platform_transaction_no IS '第三方平台交易号';
COMMENT ON COLUMN b_payment_records.amount IS '支付金额';
COMMENT ON COLUMN b_payment_records.currency IS '货币代码';
COMMENT ON COLUMN b_payment_records.status IS '支付状态：PENDING, SUCCESS, FAILED, CANCELLED, REFUNDED';
COMMENT ON COLUMN b_payment_records.callback_data IS '支付平台回调数据';
COMMENT ON COLUMN b_payment_records.callback_time IS '回调时间';
COMMENT ON COLUMN b_payment_records.client_ip IS '客户端IP';
COMMENT ON COLUMN b_payment_records.user_agent IS '用户代理';
COMMENT ON COLUMN b_payment_records.expired_at IS '支付过期时间';
COMMENT ON COLUMN b_payment_records.paid_at IS '支付完成时间';
COMMENT ON COLUMN b_payment_records.created_at IS '创建时间';
COMMENT ON COLUMN b_payment_records.updated_at IS '更新时间';
```

## 4. 核心服务设计

### 4.1 Token 计算服务 (TokenCalculatorService)

#### 4.1.1 接口定义

```java
public interface TokenCalculatorService {

    /**
     * 计算输入Token数量
     * @param content 输入内容
     * @param model 模型名称
     * @return Token数量
     */
    long calculateInputTokens(String content, String model);

    /**
     * 计算输出Token数量
     * @param content 输出内容
     * @param model 模型名称
     * @return Token数量
     */
    long calculateOutputTokens(String content, String model);

    /**
     * 计算思维链Token数量
     * @param thoughtChain 思维链内容
     * @param model 模型名称
     * @return Token数量
     */
    long calculateThoughtChainTokens(String thoughtChain, String model);

    /**
     * 批量计算Token
     * @param requests 计算请求列表
     * @return 计算结果
     */
    CompletableFuture<List<TokenCalculationResult>> calculateTokensBatch(List<TokenCalculationRequest> requests);

    /**
     * 异步计算Token
     * @param content 内容
     * @param model 模型
     * @param callback 回调函数
     */
    void calculateTokensAsync(String content, String model, Consumer<Long> callback);
}
```

#### 4.1.2 实现要点

- 集成 JTokkit 库进行精确 Token 计算
- 实现缓存机制减少重复计算
- 支持异步计算处理大文本
- 提供批量计算接口提升性能
- 支持多种模型的编码格式

### 4.2 计费服务 (BillingService)

#### 4.2.1 接口定义

```java
public interface BillingService {

    /**
     * 检查用户余额是否足够
     * @param userId 用户ID
     * @param estimatedTokens 预估Token数量
     * @return 余额检查结果
     */
    BalanceCheckResult checkBalance(Long userId, long estimatedTokens);

    /**
     * 记录Token使用并扣费
     * @param usageRequest 使用记录请求
     * @return 计费结果
     */
    BillingResult recordUsageAndBill(UsageRecordRequest usageRequest);

    /**
     * 处理失败的请求（不扣费）
     * @param usageRequest 使用记录请求
     * @return 处理结果
     */
    BillingResult recordFailedUsage(UsageRecordRequest usageRequest);

    /**
     * 计算费用
     * @param inputTokens 输入Token数
     * @param outputTokens 输出Token数
     * @param packageId 套餐ID
     * @return 费用计算结果
     */
    CostCalculationResult calculateCost(long inputTokens, long outputTokens, Long packageId);
}
```

#### 4.2.2 核心流程设计

**计费流程**：

1. 用户发送请求前调用余额检查
2. 余额充足时允许发送请求
3. 收到完整响应后进行 Token 计算
4. 根据套餐价格计算费用
5. 扣除用户余额并记录使用明细
6. 失败请求不进行扣费

### 4.3 余额服务 (BalanceService)

#### 4.3.1 接口定义

```java
public interface BalanceService {

    /**
     * 获取用户余额信息
     * @param userId 用户ID
     * @return 余额信息
     */
    UserBalanceInfo getUserBalance(Long userId);

    /**
     * 扣除用户余额
     * @param userId 用户ID
     * @param amount 扣除金额
     * @param usageRecordId 使用记录ID
     * @return 扣费结果
     */
    DeductionResult deductBalance(Long userId, BigDecimal amount, Long usageRecordId);

    /**
     * 增加用户余额
     * @param userId 用户ID
     * @param amount 增加金额
     * @param type 余额类型
     * @param description 描述
     * @return 充值结果
     */
    RechargeResult addBalance(Long userId, BigDecimal amount, BalanceType type, String description);

    /**
     * 退费
     * @param userId 用户ID
     * @param amount 退费金额
     * @param appealId 申诉ID
     * @return 退费结果
     */
    RefundResult refundBalance(Long userId, BigDecimal amount, Long appealId);

    /**
     * 初始化新用户余额
     * @param userId 用户ID
     * @param packageId 默认套餐ID
     * @return 初始化结果
     */
    InitializationResult initializeUserBalance(Long userId, Long packageId);
}
```

#### 4.3.2 余额扣除优先级

1. 优先消费免费 Token
2. 其次消费赠送余额
3. 最后消费充值余额

### 4.4 套餐服务 (PackageService)

#### 4.4.1 接口定义

```java
public interface PackageService {

    /**
     * 获取所有激活的套餐
     * @return 套餐列表
     */
    List<BillingPackageVO> getActivePackages();

    /**
     * 根据ID获取套餐
     * @param packageId 套餐ID
     * @return 套餐信息
     */
    BillingPackageVO getPackageById(Long packageId);

    /**
     * 创建套餐
     * @param packageRequest 套餐请求
     * @return 创建结果
     */
    BillingPackageVO createPackage(CreatePackageRequest packageRequest);

    /**
     * 更新套餐
     * @param packageId 套餐ID
     * @param packageRequest 更新请求
     * @return 更新结果
     */
    BillingPackageVO updatePackage(Long packageId, UpdatePackageRequest packageRequest);

    /**
     * 获取默认套餐
     * @return 默认套餐
     */
    BillingPackageVO getDefaultPackage();
}
```

### 4.5 申诉服务 (AppealService)

#### 4.5.1 接口定义

```java
public interface AppealService {

    /**
     * 提交申诉
     * @param appealRequest 申诉请求
     * @return 申诉结果
     */
    AppealResult submitAppeal(SubmitAppealRequest appealRequest);

    /**
     * 获取用户申诉列表
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 申诉列表
     */
    Page<AppealVO> getUserAppeals(Long userId, Pageable pageable);

    /**
     * 管理员处理申诉
     * @param appealId 申诉ID
     * @param processRequest 处理请求
     * @return 处理结果
     */
    AppealProcessResult processAppeal(Long appealId, ProcessAppealRequest processRequest);

    /**
     * 获取申诉详情（包含对话内容）
     * @param appealId 申诉ID
     * @return 申诉详情
     */
    AppealDetailVO getAppealDetail(Long appealId);
}
```

## 5. API 接口设计

### 5.1 用户端 API

#### 5.1.1 余额相关

```
GET /api/billing/balance
- 功能：获取用户余额信息
- 响应：{rechargedBalance, giftBalance, freeTokens, totalBalance}

POST /api/billing/recharge
- 功能：发起充值
- 请求：{amount, paymentMethod}
- 响应：{paymentNo, paymentUrl, expiredAt}

GET /api/billing/transactions
- 功能：获取交易记录
- 参数：page, size, type, startDate, endDate
- 响应：分页的交易记录列表
```

#### 5.1.2 使用记录相关

```
GET /api/billing/usage
- 功能：获取使用记录
- 参数：page, size, startDate, endDate, status
- 响应：分页的使用记录列表

GET /api/billing/usage/{id}
- 功能：获取使用记录详情
- 响应：详细的使用记录信息

GET /api/billing/usage/statistics
- 功能：获取使用统计
- 参数：period（日/周/月）
- 响应：统计数据（Token使用量、费用等）
```

#### 5.1.3 申诉相关

```
POST /api/billing/appeals
- 功能：提交申诉
- 请求：{usageRecordId, reason, description, evidenceUrls}
- 响应：{appealNo, status, submittedAt}

GET /api/billing/appeals
- 功能：获取申诉列表
- 参数：page, size, status
- 响应：分页的申诉列表

GET /api/billing/appeals/{id}
- 功能：获取申诉详情
- 响应：申诉详情和处理状态
```

#### 5.1.4 套餐相关

```
GET /api/billing/packages
- 功能：获取可用套餐列表
- 响应：套餐列表及价格信息

GET /api/billing/packages/{id}
- 功能：获取套餐详情
- 响应：套餐详细信息
```

### 5.2 管理端 API

#### 5.2.1 套餐管理

```
POST /api/admin/billing/packages
- 功能：创建套餐
- 权限：管理员
- 请求：套餐配置信息

PUT /api/admin/billing/packages/{id}
- 功能：更新套餐
- 权限：管理员
- 请求：更新的套餐信息

DELETE /api/admin/billing/packages/{id}
- 功能：删除套餐
- 权限：管理员
```

#### 5.2.2 用户管理

```
GET /api/admin/billing/users/{userId}/balance
- 功能：获取用户余额
- 权限：管理员

POST /api/admin/billing/users/{userId}/recharge
- 功能：管理员充值
- 权限：管理员
- 请求：{amount, type, description}

GET /api/admin/billing/users/{userId}/usage
- 功能：获取用户使用记录
- 权限：管理员
- 参数：分页和筛选条件
```

#### 5.2.3 申诉处理

```
GET /api/admin/billing/appeals
- 功能：获取申诉列表
- 权限：管理员
- 参数：status, priority, assignee

PUT /api/admin/billing/appeals/{id}/process
- 功能：处理申诉
- 权限：管理员
- 请求：{status, comment, refundAmount}

GET /api/admin/billing/appeals/{id}/conversation
- 功能：获取申诉对应的对话内容
- 权限：管理员
```

## 6. 前端页面设计

### 6.1 用户端页面

#### 6.1.1 余额显示组件

**文件位置**: `dp-web-fa/src/components/billing/BalanceDisplay.vue`

**功能特性**：

- 实时显示用户余额（充值余额、赠送余额、免费 Token）
- 余额不足时红色警告提示
- 支持一键跳转充值页面
- 显示今日已使用 Token 数量

#### 6.1.2 Token 计数器组件

**文件位置**: `dp-web-fa/src/components/chat/TokenCounter.vue`

**功能特性**：

- 实时计算输入文本的预估 Token 数
- 显示本次对话预估费用
- 余额不足时禁用发送按钮
- 支持多种模型的 Token 计算

#### 6.1.3 充值页面

**文件位置**: `dp-web-fa/src/views/billing/Recharge.vue`

**功能模块**：

- 充值金额选择（预设金额和自定义金额）
- 支付方式选择（微信、支付宝）
- 充值记录查看
- 支付状态实时查询

#### 6.1.4 消费记录页面

**文件位置**: `dp-web-fa/src/views/billing/UsageHistory.vue`

**功能模块**：

- 使用记录列表（分页显示）
- 记录详情查看（包含对话内容）
- 筛选和搜索功能
- 导出使用记录
- 申诉入口

#### 6.1.5 申诉页面

**文件位置**: `dp-web-fa/src/views/billing/Appeal.vue`

**功能模块**：

- 申诉表单（原因选择、详细描述、证据上传）
- 申诉记录查看
- 申诉状态跟踪
- 申诉结果展示

### 6.2 管理端页面

#### 6.2.1 套餐管理页面

**文件位置**: `dp-web-fa/src/views/sys/billing_package/index.vue`

**功能模块**：

- 套餐列表管理
- 套餐创建和编辑
- 价格历史记录
- 套餐使用统计

#### 6.2.2 申诉处理页面

**文件位置**: `dp-web-fa/src/views/sys/billing_appeal/index.vue`

**功能模块**：

- 申诉队列管理
- 申诉详情查看
- 对话内容查看
- 申诉处理操作
- 批量处理功能

#### 6.2.3 用户余额管理页面

**文件位置**: `dp-web-fa/src/views/sys/user_balance/index.vue`

**功能模块**：

- 用户余额查询
- 手动充值功能
- 余额调整记录
- 用户消费统计

## 7. 工具类设计

### 7.1 Token 计算工具类

**文件位置**: `src/main/java/com/dipspro/util/TokenCalculatorUtil.java`

```java
@Slf4j
@Component
public class TokenCalculatorUtil {

    private static final Map<String, EncodingRegistry> ENCODING_CACHE = new ConcurrentHashMap<>();
    private static final Cache<String, Long> TOKEN_CACHE = Caffeine.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();

    /**
     * 根据模型名称获取Token计算器
     */
    public EncodingRegistry getEncodingForModel(String model) {
        return ENCODING_CACHE.computeIfAbsent(model, this::createEncoding);
    }

    /**
     * 计算文本Token数量
     */
    public long calculateTokens(String text, String model) {
        if (StringUtils.isBlank(text)) {
            return 0;
        }

        String cacheKey = model + ":" + DigestUtils.md5Hex(text);
        return TOKEN_CACHE.get(cacheKey, key -> doCalculateTokens(text, model));
    }

    /**
     * 批量计算Token
     */
    public Map<String, Long> calculateTokensBatch(Map<String, String> textMap, String model) {
        return textMap.entrySet().parallelStream()
                .collect(Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> calculateTokens(entry.getValue(), model)
                ));
    }

    /**
     * 异步计算Token
     */
    @Async("tokenCalculatorExecutor")
    public CompletableFuture<Long> calculateTokensAsync(String text, String model) {
        return CompletableFuture.completedFuture(calculateTokens(text, model));
    }

    private long doCalculateTokens(String text, String model) {
        try {
            EncodingRegistry registry = getEncodingForModel(model);
            return registry.getEncoding(getEncodingType(model))
                    .encode(text)
                    .size();
        } catch (Exception e) {
            log.error("Token计算失败，model: {}, text length: {}", model, text.length(), e);
            // 降级处理：使用字符数除以4作为估算
            return (long) Math.ceil(text.length() / 4.0);
        }
    }

    private EncodingType getEncodingType(String model) {
        if (model.contains("gpt-4o")) {
            return EncodingType.O200K_BASE;
        } else if (model.contains("gpt-4") || model.contains("gpt-3.5")) {
            return EncodingType.CL100K_BASE;
        } else {
            return EncodingType.CL100K_BASE; // 默认编码
        }
    }
}
```

### 7.2 计费工具类

**文件位置**: `src/main/java/com/dipspro/util/BillingUtil.java`

```java
@Slf4j
public class BillingUtil {

    /**
     * 生成交易流水号
     */
    public static String generateTransactionNo() {
        return "TXN" + System.currentTimeMillis() + RandomStringUtils.randomNumeric(6);
    }

    /**
     * 生成申诉单号
     */
    public static String generateAppealNo() {
        return "APL" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))
               + RandomStringUtils.randomNumeric(8);
    }

    /**
     * 生成支付单号
     */
    public static String generatePaymentNo() {
        return "PAY" + System.currentTimeMillis() + RandomStringUtils.randomNumeric(6);
    }

    /**
     * 计算Token费用
     */
    public static BigDecimal calculateTokenCost(long tokens, BigDecimal pricePerThousand) {
        if (tokens <= 0 || pricePerThousand == null || pricePerThousand.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal tokenDecimal = new BigDecimal(tokens);
        BigDecimal thousandDecimal = new BigDecimal(1000);

        return tokenDecimal.multiply(pricePerThousand)
                .divide(thousandDecimal, 6, RoundingMode.HALF_UP);
    }

    /**
     * 验证余额是否充足
     */
    public static boolean isBalanceSufficient(UserBalance balance, BigDecimal requiredAmount) {
        if (balance == null || requiredAmount == null || requiredAmount.compareTo(BigDecimal.ZERO) < 0) {
            return false;
        }

        BigDecimal totalBalance = balance.getRechargedBalance()
                .add(balance.getGiftBalance());

        return totalBalance.compareTo(requiredAmount) >= 0;
    }

    /**
     * 格式化Token数量显示
     */
    public static String formatTokens(long tokens) {
        if (tokens < 1000) {
            return tokens + " tokens";
        } else if (tokens < 1000000) {
            return String.format("%.1fK tokens", tokens / 1000.0);
        } else {
            return String.format("%.1fM tokens", tokens / 1000000.0);
        }
    }

    /**
     * 格式化金额显示
     */
    public static String formatAmount(BigDecimal amount) {
        if (amount == null) {
            return "¥0.00";
        }
        return "¥" + amount.setScale(2, RoundingMode.HALF_UP).toString();
    }
}
```

## 8. 配置文件设计

### 8.1 application.yml 配置

```yaml
# 计费系统配置
billing:
  # 默认配置
  default-package-id: 1
  free-tokens-for-new-user: 10000

  # Token计算配置
  token-calculation:
    cache-enabled: true
    cache-ttl: 3600
    async-threshold: 1000
    batch-size: 100

  # 支付配置
  payment:
    # 微信支付
    wechat:
      app-id: ${WECHAT_APP_ID:}
      secret: ${WECHAT_SECRET:}
      mch-id: ${WECHAT_MCH_ID:}
      api-key: ${WECHAT_API_KEY:}
      notify-url: ${WECHAT_NOTIFY_URL:}

    # 支付宝
    alipay:
      app-id: ${ALIPAY_APP_ID:}
      private-key: ${ALIPAY_PRIVATE_KEY:}
      public-key: ${ALIPAY_PUBLIC_KEY:}
      notify-url: ${ALIPAY_NOTIFY_URL:}
      return-url: ${ALIPAY_RETURN_URL:}

  # 申诉配置
  appeal:
    max-appeals-per-day: 5
    appeal-expiry-days: 7
    auto-approve-threshold: 0.01

  # 余额配置
  balance:
    min-recharge-amount: 1.00
    max-recharge-amount: 10000.00
    low-balance-threshold: 10.00

# 异步线程池配置
async:
  token-calculator:
    core-pool-size: 4
    max-pool-size: 8
    queue-capacity: 200
    thread-name-prefix: "TokenCalc-"
```

### 8.2 Maven 依赖配置

```xml
<!-- JTokkit Token计算库 -->
<dependency>
    <groupId>com.knuddels</groupId>
    <artifactId>jtokkit</artifactId>
    <version>1.1.0</version>
</dependency>

<!-- Caffeine缓存 -->
<dependency>
    <groupId>com.github.ben-manes.caffeine</groupId>
    <artifactId>caffeine</artifactId>
    <version>3.1.8</version>
</dependency>

<!-- 支付相关依赖（预留） -->
<dependency>
    <groupId>com.github.wechatpay-apiv3</groupId>
    <artifactId>wechatpay-java</artifactId>
    <version>0.2.12</version>
</dependency>

<dependency>
    <groupId>com.alipay.sdk</groupId>
    <artifactId>alipay-sdk-java</artifactId>
    <version>4.38.157.ALL</version>
</dependency>
```

## 9. 监控和日志设计

### 9.1 关键指标监控

- **实时指标**：

  - 当前在线用户数
  - 实时 Token 消费速率
  - 余额不足用户数量
  - 支付成功率

- **业务指标**：
  - 日/周/月 Token 消费总量
  - 用户平均消费金额
  - 申诉处理时效
  - 套餐转化率

### 9.2 日志记录规范

```java
// 计费操作日志
log.info("计费成功 - 用户ID: {}, 使用记录ID: {}, 输入Token: {}, 输出Token: {}, 总费用: {}",
         userId, recordId, inputTokens, outputTokens, totalCost);

// 余额变动日志
log.info("余额变动 - 用户ID: {}, 变动类型: {}, 变动金额: {}, 变动前余额: {}, 变动后余额: {}",
         userId, type, amount, balanceBefore, balanceAfter);

// 支付日志
log.info("支付请求 - 用户ID: {}, 支付单号: {}, 支付方式: {}, 金额: {}",
         userId, paymentNo, paymentMethod, amount);

// 申诉日志
log.info("申诉处理 - 申诉ID: {}, 处理结果: {}, 管理员ID: {}, 退费金额: {}",
         appealId, result, adminId, refundAmount);
```

## 10. 安全性设计

### 10.1 数据安全

- 敏感数据加密存储（支付密钥、用户隐私信息）
- 数据库连接加密
- API 接口 HTTPS 传输
- 敏感操作日志记录

### 10.2 业务安全

- 用户请求频率限制
- 异常消费行为检测和告警
- Token 计算结果验证机制
- 支付安全验证

### 10.3 权限控制

- 用户只能查看自己的计费信息
- 管理员权限分级控制
- 敏感操作二次验证
- API 接口权限校验

## 11. 性能优化设计

### 11.1 Token 计算优化

- 常用文本 Token 结果缓存
- 批量计算接口减少计算次数
- 异步计算处理大文本
- 降级策略保证服务可用性

### 11.2 数据库优化

- 合理的索引设计
- 分区表存储历史数据
- 读写分离提升查询性能
- 定期数据归档

### 11.3 缓存策略

- Token 计算结果缓存
- 用户余额信息缓存
- 套餐配置缓存
- 热点数据预加载

## 12. 部署和运维

### 12.1 数据库迁移

- 提供 SQL 脚本创建表结构
- 数据迁移脚本和回滚方案
- 索引优化脚本
- 基础数据初始化脚本

### 12.2 监控告警

- 余额不足用户告警
- 异常消费行为告警
- 支付失败率告警
- 系统性能指标告警

### 12.3 备份策略

- 数据库定期备份
- 重要配置文件备份
- 支付相关数据特殊保护
- 数据恢复验证机制

---

## 总结

本设计文档详细描述了 DIPS Pro 计费系统的完整架构，包括数据库设计、服务设计、API 接口、前端页面、工具类、配置文件等各个方面。

### 核心特性

1. **精确计费**：基于 JTokkit 库实现精确的 Token 计算
2. **模块化设计**：独立的 billing 模块，职责清晰
3. **异步处理**：支持异步 Token 计算，提升用户体验
4. **灵活配置**：支持多种套餐配置和动态价格调整
5. **完整流程**：从余额检查到扣费、申诉的完整业务流程
6. **安全可靠**：多重安全保障和性能优化措施

### 技术亮点

- 使用 JTokkit 库确保 Token 计算准确性
- 缓存机制提升计算性能
- 预留支付接口支持多种支付方式
- 完善的申诉机制保护用户权益
- 详细的监控和日志记录

该设计方案可以满足 DIPS Pro 商业化运营的计费需求，同时具备良好的扩展性和维护性。
