# 服务器部署指南

本文档描述了如何将 `dips-pro` 项目部署到生产服务器。部署过程主要依赖 Docker 和 Docker Compose。

## 1. 环境要求

*   **操作系统**: Linux (推荐 Ubuntu LTS 或 CentOS)
*   **Docker**: 最新稳定版本。请参考 [Docker 官方文档](https://docs.docker.com/engine/install/) 进行安装。
*   **Docker Compose**: 最新稳定版本。请参考 [Docker Compose 官方文档](https://docs.docker.com/compose/install/) 进行安装。
*   **Git**: 用于从代码仓库拉取代码。
*   **网络**: 服务器需要能够访问外部网络以下载依赖项和 Docker 镜像，并能够连接到项目依赖的外部服务（如数据库、n8n 等）。

## 2. 获取代码

将项目代码克隆到服务器的目标部署目录：

```bash
git clone <YOUR_GIT_REPOSITORY_URL> dips-pro
cd dips-pro
```
将 `<YOUR_GIT_REPOSITORY_URL>` 替换为实际的 Git 仓库地址。

## 3. 配置

部署前，需要根据实际生产环境调整配置。

### 3.1 后端配置 (`dips-pro-server`)

*   主要配置文件: `dips-pro-server/src/main/resources/application-prod.yml`
*   **数据库连接**:
    *   `spring.datasource.url`: 生产环境数据库 JDBC URL。
    *   `spring.datasource.username`: 数据库用户名。
    *   `spring.datasource.password`: 数据库密码。**强烈建议**: 不要直接写在配置文件中。优先使用环境变量或 Docker Secrets 等更安全的方式注入。
*   **n8n Webhook 配置**:
    *   `n8n.webhook.url`: 生产环境 n8n Webhook 的 URL。
    *   `n8n.auth.username`: Basic Auth 用户名。
    *   `n8n.auth.password`: Basic Auth 密码。**强烈建议**: 使用环境变量或 Docker Secrets。
*   **其他配置**: 根据需要调整其他配置项，如 Redis 连接、日志级别等。

### 3.2 Docker Compose 配置 (`deploy/docker-compose.prod.yml`)

*   **环境变量**: 检查 `docker-compose.prod.yml` 文件中定义的环境变量。特别是 `dips-pro-server` 服务的 `environment` 部分，这里是注入敏感配置（如数据库密码、n8n 凭据）的推荐位置。例如：
    ```yaml
    services:
      dips-pro-server:
        environment:
          - SPRING_DATASOURCE_PASSWORD=${DB_PASSWORD} # 从 .env 文件或运行环境中读取
          - N8N_AUTH_PASSWORD=${N8N_PASSWORD} # 从 .env 文件或运行环境中读取
          # 其他需要的环境变量
    ```
    需要在 `docker-compose.prod.yml` 文件同级目录下创建一个 `.env` 文件，或者在运行 `docker-compose` 命令的环境中设置这些环境变量。`.env` 文件**不应**提交到 Git 仓库。
    ```.env
    # .env 文件示例 (请勿提交到 Git)
    DB_PASSWORD=your_production_db_password
    N8N_PASSWORD=your_production_n8n_password
    ```
*   **卷 (Volumes)**: 检查并确认数据卷的路径和权限设置是否正确，特别是需要持久化存储的数据（如数据库文件、上传的文件等）。
*   **端口映射**: 确认 Nginx 或其他对外暴露服务的端口没有冲突。

## 4. 构建 Docker 镜像

本地拉取镜像再上传到服务器

```bash
# 本地拉取镜像
docker pull --platform linux/amd64 openjdk:23-oraclelinux7
docker images
# 镜像打包
docker save -o jre23.tar openjdk:23-oraclelinux7
# xterminal 上传到服务器 /mnt/tools 目录
# 服务器加载镜像
cd /mnt/tools/ && docker load -i jre23.tar
docker images
```

如果在部署服务器上手动构建：

```bash
# 确保 Docker 和 Docker Compose 已正确安装并运行
# 进入项目根目录
cd /path/to/dips-pro

# 使用 docker-compose 构建所有服务镜像 (根据 docker-compose.prod.yml 定义)
# --no-cache 可以强制重新构建所有层
docker-compose -f deploy/docker-compose.prod.yml build
```

这个命令会根据 `deploy/docker/backend/Dockerfile`, `deploy/docker/web/Dockerfile`, `deploy/docker/nginx/Dockerfile` 等文件构建镜像。

## 5. 部署服务

**创建 docker 网络**
```bash
docker network create dips-pro-network
```

在项目根目录下，使用 `docker-compose.prod.yml` 文件启动服务：

```bash
# 进入项目根目录
cd /path/to/dips-pro

# 启动服务 (后台运行)
# --build 会在启动前尝试重新构建镜像（如果本地没有或有更新）
# -d 表示在后台 (detached) 模式运行
docker-compose -f deploy/docker-compose.prod.yml up -d --build
```

如果已经构建或拉取了最新的镜像，可以省略 `--build`:

```bash
docker-compose -f deploy/docker-compose.prod.yml up -d
```

## 6. 验证部署

*   **检查容器状态**:
    ```bash
    docker-compose -f deploy/docker-compose.prod.yml ps
    ```
    确认所有服务 (如 `dips-pro-server`, `dips-pro-web-nginx`, `postgres` 等) 的状态是 `Up` 或 `Running`。

*   **查看容器日志**:
    ```bash
    # 查看所有服务的日志
    docker-compose -f deploy/docker-compose.prod.yml logs -f

    # 查看特定服务的日志 (例如后端服务)
    docker-compose -f deploy/docker-compose.prod.yml logs -f dips-pro-server
    ```
    检查日志中是否有错误信息。

*   **访问应用**: 通过浏览器访问 Nginx 代理的前端地址 (通常是服务器 IP 或配置的域名)。测试应用的核心功能是否正常。

## 7. 更新与维护

### 7.1 更新应用

1.  **拉取最新代码**:
    ```bash
    cd /path/to/dips-pro
    git pull origin <branch_name> # 例如 main 或 master
    ```
2.  **重新构建镜像 (如果需要)**:
    *   如果使用 CI/CD，确保新的镜像已被推送到 Registry。
    *   如果手动构建：
        ```bash
        docker-compose -f deploy/docker-compose.prod.yml build
        ```
3.  **重新部署服务**: `docker-compose up -d` 会自动停止、移除旧容器，并使用新镜像创建和启动新容器。
    ```bash
    docker-compose -f deploy/docker-compose.prod.yml up -d --build # 如果手动构建或需要强制拉取新镜像
    # 或者
    docker-compose -f deploy/docker-compose.prod.yml up -d # 如果镜像已在本地或由 docker pull 更新
    ```
4.  **清理旧资源 (可选)**: Docker Compose 通常会保留旧的数据卷。如果需要清理不再使用的镜像或数据卷，请谨慎操作。
    ```bash
    # 清理悬空的 Docker 镜像 (没有被任何容器使用的镜像)
    docker image prune

    # 清理悬空的 Docker 数据卷 (谨慎使用!)
    # docker volume prune
    ```

### 7.2 停止服务

```bash
cd /path/to/dips-pro
docker-compose -f deploy/docker-compose.prod.yml down
```
这个命令会停止并移除由该 Compose 文件创建的容器和网络。数据卷默认不会被删除。

### 7.3 查看服务状态

```bash
cd /path/to/dips-pro
docker-compose -f deploy/docker-compose.prod.yml ps
```

## 8. 注意事项

*   **安全性**: 确保 Docker Daemon 的安全配置，管理好敏感信息（密码、API Key 等），不要将它们硬编码或提交到代码仓库。
*   **资源**: 根据应用负载监控服务器资源（CPU, 内存, 磁盘空间），并按需调整 Docker 容器的资源限制。
*   **备份**: 定期备份数据库和重要的数据卷。
*   **日志管理**: 配置合适的日志轮转和收集策略，方便问题排查。 