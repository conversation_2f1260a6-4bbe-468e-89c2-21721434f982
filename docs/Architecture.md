# DIPS Pro 系统架构设计文档

## 最新架构变更 (2025-06-13)

### nginx CSP 配置优化 - 修复 HTTP 图片协议转换问题

**变更时间**：2025-06-13 21:35:00

**问题背景**：

- 聊天消息中的图片链接为 HTTP 协议，在本地开发环境显示正常
- 生产环境通过 HTTPS 访问时，浏览器自动将 HTTP 图片升级为 HTTPS 导致加载失败
- 根本原因：nginx CSP 配置中 `connect-src 'self' https:` 限制了只能使用 HTTPS 连接

**解决方案**：

修改 Content Security Policy (CSP) 配置，将 `connect-src` 指令从仅支持 HTTPS 改为同时支持 HTTP 和 HTTPS

**配置变更**：

```
修改前：connect-src 'self' https:;
修改后：connect-src 'self' http: https:;
```

**影响文件**：

1. `dp-landingpage/nginx.conf`：
   - HTTP server 块 CSP 策略
   - HTTPS server 块 CSP 策略
2. `dp-web-fa/nginx.conf`：
   - HTTP server 块 CSP 策略
   - HTTPS server 块 CSP 策略

**安全考虑**：

- 仅允许已在 `img-src` 中配置的 `*.fg-china.cn` 域名图片资源
- 保持其他 CSP 指令的安全限制不变
- 图片资源仍然受到域名白名单限制

**部署要求**：

- 需要重启相关 nginx 容器使配置生效
- 建议后续将外部图片源升级为 HTTPS 或实施图片代理方案

### 前端计费系统类型安全改进

**变更背景**：修复前端计费模块的 TypeScript 编译错误，提升代码类型安全性

**核心改进**：

1. **类型定义层完善**：

   - 补充 `PaymentMethod`、`PaginationConfig`、`AttachmentInfo` 等缺失类型
   - 扩展 `AppealRecord` 支持前端组件需要的字段（title、contact、priority）
   - 统一分页响应数据结构类型定义

2. **Store 层方法补全**：

   - 新增支付方式、申诉管理、文件下载等业务方法
   - 统一分页对象结构，确保类型一致性
   - 完善错误处理和状态管理机制

3. **API 层接口对齐**：
   - 补充后端控制器对应的前端 API 方法
   - 修复下载类方法的返回类型包装
   - 统一 mock 数据结构与实际接口响应格式

**技术指标**：

- TypeScript 编译错误从 91 个减少至 70 个（修复进度 23%）
- 计费模块核心功能类型安全性显著提升
- API 接口与后端控制器完全对齐

**影响范围**：

- 前端：`src/types/billing.ts`、`src/stores/billing.ts`、`src/api/billing.ts`
- 后端对应：BillingController、BillingAppealController、UserBalanceController 等

---

## 系统概述

本文档描述了 dips-pro 应用的整体架构设计。

## 最新架构更新 (2025-06-12)

### 前端计费系统架构设计

#### 前端计费模块架构

```
dp-web-fa/src/
├── types/billing.ts           # 计费系统类型定义
├── api/billing.ts            # 计费API服务层
├── stores/billing.ts         # Pinia状态管理
├── utils/format.ts           # 格式化工具函数
└── components/billing/       # 计费组件库
    ├── BalanceDisplay.vue    # 余额显示组件
    ├── TokenCounter.vue      # Token计数器组件
    ├── RechargeForm.vue      # 充值表单组件（规划中）
    ├── UsageHistory.vue      # 消费记录组件（规划中）
    └── AppealForm.vue        # 申诉表单组件（规划中）
```

#### 技术特性

- **完整 TypeScript 支持**：类型安全的 API 调用和状态管理
- **响应式设计**：支持移动端和桌面端的自适应布局
- **暗色主题**：完整的暗色主题支持和主题切换
- **实时监控**：余额状态实时监控和自动刷新机制
- **性能优化**：防抖处理、懒加载、状态缓存
- **用户体验**：动画效果、加载状态、错误提示

#### 状态管理设计

- **账户状态**：余额、货币、账户状态实时同步
- **配置缓存**：计费配置本地缓存和自动更新
- **记录管理**：使用记录、充值记录、申诉记录的分页和缓存
- **错误处理**：统一的错误处理和用户提示机制

#### 组件特性详解

**BalanceDisplay 组件**：

- 实时余额显示和状态指示
- 支持多种尺寸（small/default/large）
- 自动刷新和手动刷新功能
- 余额变动动画和通知提示
- 快速充值和详情查看入口

**TokenCounter 组件**：

- 实时 Token 数量计算和费用预估
- 防抖优化，避免频繁 API 调用
- 余额充足性检查和警告提示
- 详细计费信息展开/收起
- 支持多模型配置和切换

#### 开发规划

- [x] 基础设施搭建（类型定义、API、状态管理）
- [x] 核心组件开发（余额显示、Token 计数器）
- [x] 页面功能开发（充值、消费记录、申诉）
- [x] 计费概览页面开发
- [x] 路由配置和导航集成
- [x] 聊天组件集成（TokenCounter 集成到 BaseSender）
- [ ] 余额不足时的发送限制逻辑
- [ ] 完善测试和性能优化

## 1. 整体模块划分

应用主要包含以下模块：

- **前端 (Frontend - `dips-pro-web-fa`)**: 用户交互界面，使用 Vue 3 + TypeScript + Vite 和 Ant Design X Vue。负责展示信息、接收用户输入、发送请求给后端。包含以下重要组件：
  - **ChatMessageList**: 聊天消息列表组件，负责渲染用户和助手的消息气泡。
  - **ChatThoughtChain**: 思维链组件，负责解析和展示 AI 助手的中间思考步骤和工具调用过程，提升透明度和可解释性。
  - **ChatSender**: 聊天输入组件，负责接收用户输入并发送消息。
  - **ChatFunctionButtons**: 功能按钮组件，提供新建会话、查看历史会话等功能。
  - **ConversationHistory**: 会话历史组件，展示并管理历史会话列表。
- **后端 (Backend - `dips-pro-server`)**: 应用核心，使用 Java 23 + Spring Boot。处理业务逻辑、管理数据、与外部服务（AI, n8n）交互。包含以下内部模块：
  - **智能问答核心 (Q&A Core)**: 处理用户提问，选择模型，整合信息，生成回答。
  - **智能体管理器 (Agent Manager)**: 通过与 n8n 交互，管理 AI 智能体的配置。
  - **n8n 接口 (n8n Interface)**: 封装与 n8n 通信的逻辑 (使用 Spring WebClient)。
- **数据库 (Database)**:
  - **PostgreSQL**: 主要关系型数据库，存储用户信息、聊天记录、智能体基础信息（可选缓存）。
  - **PGVector (PostgreSQL Extension)**: 用于 RAG 的向量存储和检索。
  - **Redis**: 用于缓存，例如缓存 n8n 获取的 Agent 配置、用户会话信息等。
  - **MySQL (查询历史数据)**: 用于查询历史表数据并构建索引。
  - **MongoDB (可选)**: 未来可用于存储结构更灵活的数据。
- **消息队列 (Message Queue - 可选)**: 如 RabbitMQ 或 Kafka。用于处理耗时任务（调用 AI、复杂 n8n 工作流），提升用户体验。按需引入。
- **知识库/向量数据库 (Knowledge Base/Vector DB)**: 即使用 PostgreSQL + PGVector 实现。

## 2. 关键技术选型

- **前端**:
  - **主应用 (dp-web-fa)**: Vue 3 + TypeScript + Vite + pnpm，使用 Ant Design Vue 和 Element Plus
  - **落地页 (dips-pro-landingpage)**: React 应用
- **后端**: Java 23, Spring Boot 3.x
- **数据库**: PostgreSQL, PGVector, Redis, MySQL
- **工作流/智能体配置**: n8n (外部系统)
- **部署**: Docker, Docker Compose
- **通信协议**: RESTful API
- **数据格式**: JSON
- **身份认证**: JWT (JSON Web Tokens)
- **API 文档**: OpenAPI v3 (Swagger)

## 2.2 后端架构重构 (2025-06-10 更新)

### 楼盘画像服务重构

为解决 `ProjectProfileServiceImpl` 类代码过长（1561 行）的问题，实施了分层架构重构：

#### 新增 DAO 层

- **ProjectAnalyticsDao**: 楼盘分析数据访问接口

  - `queryIndexScore()`: 查询指标得分
  - `queryIndexScoreWithOwnerType()`: 查询带业主类型的指标得分
  - `batchQueryIndexScore()`: 批量查询指标得分
  - `queryTotalSample()`: 查询样框数据
  - `batchQueryTotalSample()`: 批量查询样框数据
  - `queryAgeGroup()`: 查询年龄段数据
  - `queryAna()`: 查询分析数据

- **ProjectEnrichmentDao**: 楼盘数据丰富化访问接口
  - `queryLjLouPan()`: 查询链家楼盘数据
  - `queryLjLouPanLayout()`: 查询链家楼盘户型数据

#### 新增业务处理器层

- **ProjectProfileProcessor**: 楼盘画像业务处理器
  - `processProjectQuery()`: 处理楼盘查询逻辑（精确查询 → 模糊查询 → 选择选项）
  - `enrichProjectProfileWithDetail()`: 丰富楼盘画像基础数据
  - `enrichProjectProfileWithData()`: 丰富楼盘画像完整数据
  - `enrichSalesInfo()`: 丰富销售信息相关数据
  - `enrichAnalysisData()`: 丰富分析数据
  - 私有方法：`enrichIndexScore()`, `enrichTotalSample()`, `enrichLjLouPan()` 等

#### 重构后的服务层

- **ProjectProfileServiceImplRefactored**: 重构后的服务实现
  - 使用依赖注入的 `ProjectProfileProcessor` 和 DAO 组件
  - 专注于流程编排和异常处理
  - 每个主要方法的代码行数大幅减少
  - 提高了代码的可读性和可维护性

#### 重构优势

- **单一职责原则**: 每个类的职责更加明确
- **代码复用**: 消除了大量重复代码
- **可测试性**: 各层组件可以独立测试
- **可维护性**: 代码结构更清晰，便于后续维护和扩展
- **性能优化**: 支持批量查询，减少数据库访问次数

## 2.1 Docker 容器化部署架构

### 反向代理部署架构 (2025-05-28 更新)

从 2025-05-28 开始，前端应用采用反向代理部署架构，通过 dp-landingpage 的 nginx 反向代理访问 dp-web-fa：

#### dp-landingpage (官网/落地页 + 反向代理)

- **域名**: `dipsai.cn`
- **端口**: HTTP (80), HTTPS (443)
- **容器名**: `dp-landingpage`
- **技术栈**: React + TypeScript + Vite
- **构建配置**:
  - 基于 `node:20-alpine` 的多阶段构建
  - 使用 npm 包管理器
  - Nginx 静态文件服务 + 反向代理
- **反向代理配置**:
  - `/app/` → 代理到 `dp-web-fa:8080/`
  - `/server/` → 代理到 `dips-pro-server:8080/api/`
- **部署文件**:
  - `dp-landingpage/Dockerfile`: 独立构建配置
  - `dp-landingpage/nginx.conf`: 包含反向代理配置
  - `deploy/docker-compose.landingpage.prod.yml`: 部署编排
  - `deploy/jenkinsfile.landingpage`: CI/CD 流水线

#### dp-web-fa (主应用)

- **访问路径**: `dipsai.cn/app`
- **端口**: 内部端口 8080/8443 (不对外暴露)
- **容器名**: `dp-web-fa`
- **技术栈**: Vue 3 + TypeScript + Vite + pnpm
- **构建配置**:
  - 基于 `node:20-alpine` 的多阶段构建
  - 使用 pnpm 包管理器，确保依赖版本一致性
  - Nginx 静态文件服务
  - API 基础路径: `/server`
- **部署文件**:
  - `dp-web-fa/Dockerfile`: 独立构建配置，API 路径更新为 `/server`
  - `dp-web-fa/nginx.conf`: 移除独立域名配置，适配反向代理
  - `deploy/docker-compose.webapp.prod.yml`: 移除端口映射
  - `deploy/jenkinsfile.webapp`: CI/CD 流水线

#### 共同特性

- **配置挂载**:
  - SSL 证书目录挂载: `/mnt/{项目名}/certs:/etc/nginx/certs:ro`
  - Nginx 配置文件挂载: `/mnt/{项目名}/nginx.conf:/etc/nginx/conf.d/default.conf:ro`
  - **配置文件同步**: Jenkins 构建过程中自动从 Git 仓库复制最新的 nginx.conf 文件到服务器挂载目录
- **网络**: 共享 `dips-pro-network` 外部网络
- **SSL 支持**: 支持 TLS 1.2/1.3，需要为各自域名准备证书
- **健康检查**: 提供 `/health` 端点
- **生产优化**: gzip 压缩、静态资源缓存、安全头配置

#### 部署优势

- **独立部署**: 两个应用可以独立构建、部署和扩展
- **统一域名**: 通过路径区分不同应用，便于管理和访问
- **资源隔离**: 各自独立的资源配置和监控
- **CI/CD 分离**: 独立的构建流水线，减少相互影响
- **反向代理**: 通过 nginx 反向代理实现路径路由，保持架构灵活性

## 3. 前后端数据交互

- **方式**: 基于 HTTP/HTTPS 的 RESTful API。
- **数据格式**: 请求体和响应体主要使用 JSON。
- **身份认证**: 用户登录后，后端生成 JWT 返回给前端。前端在后续请求的 `Authorization` Header 中携带 `Bearer <token>`。后端通过过滤器/拦截器验证 JWT。
- **API 安全**: 所有 API 接口通过 JWT 认证保护，使用 Bearer Token 方式。配置位于 `SecurityConfig` 类和 `application.yml` 的相关部分。生产环境通过环境变量配置 Redis 连接和数据库连接信息。
  - **JWT 认证传递链路**: 实现了完整的用户身份认证传递机制，支持 n8n 工作流调用需要认证的 dp-server 接口：
    - **认证获取**: `ChatServiceImpl.processAndSaveMessage` 方法通过 `RequestContextHolder` 获取当前 HTTP 请求上下文
    - **Token 提取**: 从 `Authorization` 头提取 Bearer token，从请求属性获取用户信息（userId、username）
    - **信息传递**: 将 `userToken`、`userId`、`username` 添加到 n8n 请求体中
    - **n8n 使用**: n8n 工作流可使用传递的 `userToken` 作为 `Authorization: Bearer <userToken>` 调用 dp-server 接口
    - **应用场景**: 支持 n8n 调用如 `http://47.99.92.35:5681/api/data-query/userProfile/{{ $json.body.slots.customer_feature }}` 等需要认证的接口
    - **技术实现**:
      - `getCurrentUserToken()`: 从 HTTP 请求头获取 JWT token
      - `getCurrentUserId()`: 从请求属性获取用户 ID
      - `getCurrentUsername()`: 从 Spring Security 上下文或请求属性获取用户名
      - 完善的异常处理和详细的日志记录机制
- **敏感数据掩码**: 系统实现了敏感数据掩码功能，通过 `MaskUtil` 工具类对日志中的敏感信息进行脱敏处理：
  - 手机号掩码：只显示前 2 位和后 2 位，中间使用星号(\*)替代，如 `13*******45`
  - 应用场景：所有涉及手机号的日志输出，包括但不限于：
    - `DataMigrationServiceImpl` 中的手机号查询、结果统计和数据处理日志
    - `DataMigrationController` 中的 API 调用日志
    - `DataQueryExample` 中的查询示例日志
    - `DataQueryController` 和 `DataQueryServiceImpl` 等类中的手机号相关日志
  - 实现机制：静态工具方法 `MaskUtil.maskMobile(String mobile)` 处理字符串替换
  - 边界处理：对长度不足 4 位的手机号保持原样，防止掩码过程中出现异常
  - 安全保障：确保业务数据处理过程中原始手机号不受影响，仅在日志输出时应用掩码
- **用户画像标签分析系统**: 基于用户历史购买记录自动分析生成用户标签的模块：
  - **分析器组件架构**:
    - `UserProfileAnalyzer`: 抽象基类，定义标签分析的基础框架和公共方法
      - 提供`addTag`方法：统一添加标签功能，避免重复标签
      - 提供`hasTag`方法：检查标签是否已存在
      - 定义`analyzeAllTags`方法：执行所有标签分析的入口方法
      - 定义`doAnalyze`抽象方法：由子类实现具体分析逻辑
      - 定义`createAnalyzers`方法：由子类实现分析器创建逻辑
    - `MainUserProfileAnalyzer`: 具体实现类，继承 UserProfileAnalyzer，负责协调各专用分析器
      - 创建并管理所有子分析器的实例(basicAnalyzer, propertyAnalyzer, brandAnalyzer, preferenceAnalyzer)
      - 通过 doAnalyze 方法调用各子分析器的 doAnalyze 方法，充分利用多态特性
      - 汇总所有分析结果到统一的标签集合
    - 子分析器类(均继承 UserProfileAnalyzer)：
      - `BasicTagAnalyzer`: 分析基础标签，如多次置业、投资客等购买行为
      - `BrandTagAnalyzer`: 分析品牌相关标签，如品牌忠实、单一品牌持有等
      - `PropertyTagAnalyzer`: 分析房产特性标签，如装修类型、户型偏好等
      - `PreferenceTagAnalyzer`: 分析用户偏好标签，如配套设施、设计规划等
        - `analyzeFacilityPreferenceTag`: 分析配套设施偏好，比较项目设施数量与城市均值
        - `analyzeRawDataBasedPreferenceTag`: 通用偏好分析方法，接受评分字段名和关键词数组作为参数
          - 支持多个评分字段和多个关键词的偏好分析
          - 实现两类分析通道：评分分析和开放题文本关键词匹配
          - 汇总所有项目的 rawData 后一次性分析，避免数据分散
          - 动态参数化标签代码、标签名称和标签描述，提高代码复用性
        - `analyzeDesignPreferenceTag`: 分析设计偏好，调用通用方法检测"rk0a5"评分和"设计"关键词
        - `analyzeQualityPreferenceTag`: 分析质量偏好，调用通用方法检测"re0a5"评分和"质量/做工/工艺/材料/用料"等关键词
        - `analyzeDecorationPreferenceTag`: 分析装修偏好，匹配装修相关关键词
        - `analyzePropertyServicePreferenceTag`: 分析物业服务偏好，匹配服务相关关键词
        - `analyzePromiseFulfillmentTag`: 分析兑现承诺偏好，匹配信用相关关键词
        - `analyzeHighStandardPreferenceTag`: 分析高标准偏好，检测 ra1a5 低分评价
      - 每个子分析器在自己的 doAnalyze 方法中实现对内部各具体分析方法的调用
  - **标签分析流程**:
    1. 用户查询时，`UserProfileDto`调用`analyzeUserProfileTags`方法
    2. 创建`MainUserProfileAnalyzer`实例，传入项目列表
    3. 调用`analyzeAllTags`方法开始分析
    4. `MainUserProfileAnalyzer`在 createAnalyzers 方法中初始化各个专用分析器
    5. `MainUserProfileAnalyzer`的 doAnalyze 方法调用各子分析器的 doAnalyze 方法
    6. 各子分析器在各自的 doAnalyze 方法中执行自己的具体分析方法
    7. 子分析器通过继承的`addTag`方法，向统一的标签集合添加标签
    8. 分析完成后，标签集合被返回并保存在`UserProfileDto`中
  - **标签分类体系**:
    - 购买行为类标签: 多次置业、投资客、跨城市置业等
    - 客户价值类标签: 高净值客户等
    - 品牌偏好类标签: 品牌忠实、单一品牌持有等
    - 居住偏好类标签: 配套设施偏好、设计规划偏好、大/小户型偏好、高标准偏好等
    - 购买决策类标签: 质量做工偏好、物业服务偏好等
  - **标签分析算法**:
    - 基于阈值的标签识别: 使用 PREFERRED_CATEGORY_THRESHOLD(0.5)判断特征是否占主导
    - 基于项目比例的标签生成: 根据符合条件的项目占比判断用户倾向
    - 多维度交叉分析: 综合考虑多个维度数据，如评分、好评文本等
    - **设计偏好标签分析算法**：
      - 实现三种并行评估通道，满足任一条件即可触发标签：
        1. **评分数据分析**：
           - 从`project.getRawData()`中提取`rk0a5`字段值
           - 计算有评分的记录数和低分(1-3 分)记录数
           - 当低分占比`≥50%`时，项目符合设计偏好条件
        2. **好评文本分析**：
           - 从`project.getTextGood()`中提取好评文本
           - 匹配设计关键词"设计"
           - 当好评文本中包含关键词"设计"时，项目符合设计偏好条件
        3. **差评文本分析**：
           - 从`project.getTextBad()`中提取差评文本
           - 匹配设计关键词"设计"
           - 当差评文本中提及关键词"设计"时，项目符合设计偏好条件
      - 标签触发条件：
        - 只要有一个符合条件的项目，即可添加标签
  - **标签数据模型**:
    - `UserProfileTag`: 封装标签基本信息，包括代码、名称、描述、类型、置信度等
    - 匹配项目追踪: 记录每个标签关联的具体项目，支持标签验证和解释
- **API 设计**: 遵循 RESTful 风格，使用 OpenAPI 规范定义接口，便于生成文档和客户端代码。
- **核心聊天 API**:
  - `POST /api/chat`: 发送聊天消息。
    - 请求体 (`ChatRequest`): `{ \"message\": \"用户输入\", \"conversationId\": \"可选的会话ID\", \"templateId\": \"可选的模板ID\", \"slots\": \"可选的槽位值对象\" }`
    - 响应体 (`ChatResponse`): 统一使用 list 格式的消息内容：
      - **统一格式**: `{ \"list\": [{\"type\": \"text\", \"content\": \"文本内容\"}, {\"type\": \"suggestion\", \"content\": [\"建议1\", \"建议2\"]}, {\"type\": \"file\", \"content\": {\"name\": \"文件名\", \"url\": \"下载链接\"}}], \"conversationId\": \"会话ID\", \"intermediateSteps\": \"思维链数据\", \"agentType\": \"智能体类型\" }`
    - **统一消息格式架构** (2025-05-29 重构)：
      - **唯一返回格式**：只返回 list 字段，移除 response 字段，简化前端处理逻辑
      - **数据存储策略**：content 字段存储 JSON 序列化的 MessageContent 列表
      - **支持的消息类型**：
        - `text`: 文本内容，支持 Markdown 格式渲染（包含增强的链接处理：所有链接默认在新窗口打开，具备安全属性 rel="noopener noreferrer"）
        - `suggestion`: 建议选项数组，用于快速操作和引导用户
        - `file`: 文件信息对象，支持文件展示、下载和预览
        - `image`: 图片信息对象，支持图片展示和缩放
        - `chart`: 图表数据对象，支持数据可视化
        - `table`: 表格数据对象，支持结构化数据展示
      - **向后兼容性**：
        - 单一文本消息自动包装为 list 格式：`[{\"type\":\"text\",\"content\":\"文本内容\"}]`
        - 现有数据通过解析逻辑正确处理
        - 错误响应也使用统一的 list 格式
      - **处理逻辑简化**：
        - 移除双重处理逻辑（content vs list）
        - 统一的序列化/反序列化机制
        - 简化的错误处理和响应构造
  - `GET /api/chat/history/{conversationId}`: 获取指定会话的聊天记录。
    - 路径参数: `conversationId`
    - 响应体: `List<ChatMessageDto>` (数组，包含 `id`, `role`, `content`, `intermediateSteps`, `createdAt`)
    - 逻辑: 调用 `ChatService` 查询数据库中对应 `conversationId` 的所有消息，按时间升序返回。
- **提示词模板管理 API**:
  - `GET /api/prompt/templates`: 获取所有提示词模板。
    - 查询参数: `agentType` (可选，按类型过滤)
    - 响应体: `List<PromptTemplateDto>` (数组，包含模板信息)
    - 逻辑: 调用 `PromptTemplateService` 查询并返回模板列表。
  - `GET /api/prompt/templates/{id}`: 获取单个提示词模板详情。
    - 路径参数: `id` (模板 ID)
    - 响应体: `PromptTemplateDto` (包含完整的模板信息，包括槽位定义)
    - 逻辑: 调用 `PromptTemplateService` 查询指定 ID 的模板。
  - `POST /api/prompt/templates`: 创建新的提示词模板。
    - 请求体: `PromptTemplateDto` (不含 ID)
    - 响应体: `PromptTemplateDto` (包含生成的 ID)
    - 逻辑: 调用 `PromptTemplateService` 创建并保存新模板。
  - `PUT /api/prompt/templates/{id}`: 更新现有提示词模板。
    - 路径参数: `id` (模板 ID)
    - 请求体: `PromptTemplateDto` (含更新后的内容)
    - 响应体: `PromptTemplateDto` (更新后的模板对象)
    - 逻辑: 调用 `PromptTemplateService` 更新指定 ID 的模板。
  - `DELETE /api/prompt/templates/{id}`: 删除提示词模板。
    - 路径参数: `id` (模板 ID)
    - 响应: HTTP 204 No Content
    - 逻辑: 调用 `PromptTemplateService` 删除指定 ID 的模板。
- **数据查询 API**:
  - `GET /api/data-migration/query-name-list/{mobile}`: 根据手机号查询名单数据。
    - 路径参数: `mobile` (手机号码)
    - 响应体: `List<NameListDataDto>` (数组，包含表名、ID 和完整数据内容)
    - 逻辑: 调用 `DataMigrationService` 查询与手机号关联的所有名单数据（表名以 data*namelist*开头的数据）。
  - `GET /api/data-migration/query-dataset/{mobile}`: 根据手机号查询调研数据。
    - 路径参数: `mobile` (手机号码)
    - 响应体: `List<NameListDataDto>` (数组，包含表名、ID 和完整数据内容)
    - 逻辑: 调用 `DataMigrationService` 查询与手机号关联的所有调研数据（表名以 dataset\_开头的数据）。
  - `GET /api/data-migration/query-name-list-statistics/{mobile}`: 根据手机号查询名单数据并按项目统计。
    - 路径参数: `mobile` (手机号码)
    - 响应体: `DataStatisticsResultDto` (包含原始数据列表和按项目分组的统计结果)
    - 逻辑: 调用 `DataMigrationService` 查询与手机号关联的名单数据，并按 customer_project 字段进行分组统计。
  - `GET /api/data-migration/query-dataset-statistics/{mobile}`: 根据手机号查询调研数据并按项目统计。
    - 路径参数: `mobile` (手机号码)
    - 响应体: `DataStatisticsResultDto` (包含原始数据列表和按项目分组的统计结果)
    - 逻辑: 调用 `DataMigrationService` 查询与手机号关联的调研数据，并按 customer_project 字段进行分组统计。
  - `GET /api/data-query/history-project-stats/{mobile}`: 查询历史购买楼盘信息和用户画像标签。
    - 路径参数: `mobile` (手机号码)
    - 响应体: `HistoryProjectStatsDto` (包含项目列表、用户画像标签和详细指标分析)
    - 逻辑: 调用 `DataQueryService` 查询历史项目统计信息，包含基础信息、链家楼盘匹配数据，并自动生成用户画像标签。
  - `GET /api/data-migration/query-top-mobiles`: 查询记录数满足条件的手机号列表。
    - 查询参数:
    - `type` (查询类型: ALL、NAMELIST、DATASET，默认 ALL)
    - `threshold` (阈值，查询记录数大于该值的手机号，默认 5)
    - `limit` (限制返回条数，默认 20)
    - 响应体: `MobileStatsResultDto` (包含手机号列表、对应记录数、查询条件和总记录数)
    - 逻辑: 调用 `DataMigrationService` 查询 Redis 中记录数超过阈值的手机号，按记录数降序排序，并限制返回条数。

## 4. n8n 工作流集成

- **核心思想**: n8n 不仅执行自动化任务，还作为"智能体配置中心"。
- **调用方式**: 后端使用 Spring Boot 的 `WebClient` 向 n8n 的 Webhook URL 或 REST API 发送 HTTP 请求。
  - **超时配置**: `WebClient` 配置了 5 分钟的超时时间，以适应复杂查询和大型语言模型可能需要的长时间处理。
  - **服务器异步配置**: 在 `application.yml` 中设置 `server.servlet.async.request-timeout=300000` (5 分钟)，确保 Spring MVC 异步请求处理器的超时与 WebClient 一致。
  - **内存配置**: 设置 `maxInMemorySize=16MB`，以处理可能的大型响应数据。
- **触发时机**: 用户操作触发、AI 判断需要调用时触发。
- **数据传递**: 后端将上下文信息（用户输入、`conversationId`/`sessionId`、Agent ID 等）作为参数传递给 n8n。
- **获取结果**:
  - **同步 (不推荐用于长任务)**: n8n 直接返回结果。
  - **异步回调**: n8n 完成后调用后端预设的 Webhook URL 通知结果。
  - **轮询 (次选)**: 后端定时查询 n8n 工作流状态。
- **安全**: 保护 n8n Webhook URL，考虑使用 API Key 或其他认证机制。
- **后端职责**: 封装调用 n8n 的细节，提供简洁的内部服务接口。

## 5. 核心的智能问答流程和智能体管理机制 (n8n 中心化)

1.  **n8n 定义**:
    - 创建 "Agent Registry" 工作流，维护 Agent 列表及其配置 (ID, name, description, systemPrompt, model, associatedWorkflows - n8n 任务流 URL 映射)。
    - 提供 Webhook 端点，供后端查询 Agent 配置 (查询所有或按 ID 查询)。
    - 创建具体的任务执行工作流 (e.g., 查询订单, 创建工单)。
2.  **后端交互流程 (结合聊天记录保存)**:

    - 前端发送消息 (`message`) + 可选的 `conversationId` 给后端 `POST /api/chat`。
    - 后端判断 `conversationId`：若无则生成新的，若有则使用。
    - 后端保存用户消息 (`role=user`, `content=message`) 到 `chat_messages` 表，关联 `conversationId`。
    - 后端调用 n8n "Agent Registry" Webhook 获取 `agentId` (如果前端指定或有默认) 对应的配置。
    - 后端准备上下文 (System Prompt, 历史记录 - 从 `chat_messages` 表获取, 用户消息)。
    - 后端调用配置中指定的 AI 大模型 (`model`)。
    - AI 返回回答 (`assistantReplyContent`)。
    - 后端保存助手回复 (`role=assistant`, `content=assistantReplyContent`) 到 `chat_messages` 表，关联 `conversationId`。
    - 后端解析助手回复，判断是否需要调用 n8n 任务 (基于 AI 指令或特定模式)。
    - **(条件性)** 若需调用 n8n 任务:
      - 从 `associatedWorkflows` 找到对应任务的 URL。
      - 提取所需参数。
      - 调用 n8n 任务工作流 Webhook (可携带 `conversationId`)。
      - n8n 执行任务，通过回调将结果发送给后端。
      - 后端处理 n8n 结果（可能再次调用 AI 格式化，或直接使用，并可能需要更新/追加 `chat_messages` 表）。
    - 后端将最终回答/结果 (`ChatResponse` 包含 `conversationId`) 发送给前端。
    - 前端可通过 `GET /api/chat/history/{conversationId}` 获取完整或更新后的聊天记录。

3.  **提示词模板处理流程**:

    - 前端发送带有模板请求的消息，包含 `templateId` 和用户填写的 `slots` 值对象。
    - 后端 `ChatService` 在 `processAndSaveMessage` 方法中检测到提供了 `templateId`，执行以下步骤：
      - 调用 `PromptTemplateService.getTemplateById` 查询模板对象
      - 从模板对象中提取 `templateContent` 作为基础提示词
      - 遍历用户提供的 `slots` 键值对(Map<String, Object>)，对每个 key-value 对执行替换：
        - 构造占位符格式 `{{key}}`
        - 将 value 转换为字符串(使用 toString()方法)，处理 null 值为空字符串
        - 使用 String.replace()方法，将模板内容中的占位符替换为实际值
        - 在替换过程中使用日志记录每个占位符的替换情况，便于调试
      - 创建完整的用户消息内容，包含模板使用信息和填充后的内容
      - 保存用户消息至数据库，同时将填充好的完整提示词(processedTemplate)发送给 n8n webhook 的`chatInput`字段
      - 继续后续的处理步骤，获取和保存 AI 回复
    - 如果模板未找到，会生成一个错误消息并使用基本的模板信息替代，同时设置 userMessageForAI 确保与 n8n 正确通信
    - 这种实现确保了模板的灵活性和可维护性，管理员只需更新模板内容和槽位定义，无需修改代码

4.  **JSON/JSONB 数据处理配置**:
    - 系统使用 `JacksonConfig` 配置类统一管理 JSON 序列化/反序列化，特别是 JSONB 类型的数据处理：
      - 采用 Spring 推荐的 `Jackson2ObjectMapperBuilder` 构建全局 `ObjectMapper` Bean
      - 通过 builder 以声明式方式配置特性和模块，减少手动配置错误
      - 启用 `JavaTimeModule`，确保日期/时间类型(如 `Instant`)正确处理
      - 开启大小写不敏感的枚举处理，支持前端传入不同大小写格式的枚举值
      - 配置 JSON 解析容错性：允许单引号、无引号字段名，忽略未知属性和空对象
      - 设置忽略 null 值特性，减少 JSON 响应体积
      - 将 `slotDefinitions` 等 JSONB 字段处理委托给专用的序列化/反序列化方法
      - 日志输出配置过程，确保问题可追踪
    - `PromptTemplateServiceImpl` 中使用两个辅助方法处理 JSONB 数据:
      - `serializeSlotDefinitions`: 将 Java 对象列表(`List<SlotDefinition>`)序列化为 JSON 字符串
      - `deserializeSlotDefinitions`: 将 JSON 字符串反序列化为 Java 对象列表

## 6. 数据库设计

### 6.1 主要表设计

- **`conversations` 表**: 存储会话的基本信息。
  - `id` (UUID, PK): 会话唯一标识符。
  - `label` (VARCHAR): 会话标签/名称（可为空）。
  - `created_at` (TIMESTAMPTZ): 创建时间。
  - `updated_at` (TIMESTAMPTZ): 更新时间。
- **`chat_messages` 表**: 存储具体的聊天消息。
  - `id` (UUID, PK): 消息唯一标识符。
  - `conversation_id` (UUID, FK -> conversations.id): 所属会话 ID。
  - `role` (VARCHAR): 消息角色 ('USER', 'ASSISTANT')。
  - `content` (TEXT): 消息内容。
  - `intermediate_steps_json` (JSONB): 存储助手思考过程/工具调用的 JSON 字符串。
  - `created_at` (TIMESTAMPTZ): 创建时间。
  - `updated_at` (TIMESTAMPTZ): 更新时间。
- **`prompt_templates` 表**: 存储可复用的提示词模板。
  - `id` (UUID, PK): 模板唯一标识符。
  - `name` (VARCHAR, NOT NULL): 模板名称。
  - `description` (TEXT): 模板描述。
  - `agent_type` (VARCHAR): 适用的 Agent 类型。
  - `template_content` (TEXT): 模板内容（带槽位标记，例如 `{{meeting_date}}`）。
  - `slot_definitions` (JSONB, NOT NULL): 槽位定义列表 (包含 key, name, type, required, options 等)。
  - `created_at` (TIMESTAMPTZ): 创建时间。
  - `updated_at` (TIMESTAMPTZ): 更新时间。

### 6.2 多数据源配置

系统采用多数据源设计，用于支持不同的数据访问需求：

1. **主数据源 (PostgreSQL)**

   - 用途：存储系统的主要业务数据，包括会话、消息、用户等
   - 框架支持：作为 JPA 的主数据源，用于实体映射和 ORM 操作
   - 配置要点：
     - 使用@Primary 注解标记为默认数据源
     - 配置 HikariCP 连接池参数优化性能
     - 配置 EntityManagerFactoryBean 绑定此数据源
     - 设置 PostgreSQL 方言确保 SQL 生成正确

2. **MySQL 数据源**

   - 用途：主要用于查询历史业务数据，构建手机号索引
   - 访问方式：主要通过 JdbcTemplate 进行原生 SQL 查询
   - 数据迁移：负责将历史数据表中的记录索引到 Redis 中
   - 配置要点：
     - 使用独立的 HikariCP 连接池配置
     - 不参与 JPA 实体映射，仅用于 JDBC 操作

3. **多数据源实现细节**
   - **DataSourceConfig 类**: 集中管理所有数据源配置
     - primaryDataSource: 主 PostgreSQL 数据源
     - mysqlDataSource: MySQL 历史数据源
     - entityManagerFactory: 配置 JPA 使用主数据源
     - transactionManager: 事务管理器配置
   - **@Qualifier 使用**: 在 service 层使用@Qualifier("mysqlJdbcTemplate")注解指定使用 MySQL 数据源
   - **连接池配置**: 两个数据源使用不同的连接池配置，针对不同使用场景优化
   - **错误处理**: 配置详细的数据库连接日志，便于诊断连接问题

### 6.3 Redis 索引设计与优化

系统使用 Redis 作为手机号索引的存储和快速查询工具：

1. **键值结构设计**

   - 键格式: `{手机号}` (直接使用手机号作为键，不带前缀)
   - 值格式: 字符串数组，格式为 `tableName:id`，如: `["data_namelist_1:123", "data_namelist_1_24:456"]`
   - 优势:
     - 简化存储结构，减少 Redis 空间占用和序列化/反序列化开销
     - 降低网络传输量，提高查询性能
     - 更直观的数据格式，易于调试和排查问题

2. **批量导入策略**

   - **批次大小控制**: 动态调整的批次大小，初始批次最大为 5000 条记录
   - **Pipeline 优化**: 使用 Redis pipeline 批量执行命令，减少网络往返
   - **批次分解**: 大量数据被分解为多个小批次，防止超大批次导致超时
   - **动态批次调整**: 超时后自动减半批次大小进行重试，提高成功率
   - **并行控制**: 设置最大并行任务数(10)，避免 Redis 连接过载
   - **指数退避重试**: 当超时发生时，使用指数退避算法进行重试，最多重试 3 次

3. **并行任务处理机制**

   - **任务拆分**: 将表按 ID 范围拆分成多个任务，每个任务处理 30,000 条记录的范围
   - **CompletableFuture 异步处理**: 每个 ID 范围的处理任务封装为 CompletableFuture，实现异步并行执行
   - **自适应线程数**: 根据处理器核心数量动态调整并行任务数，公式为`min(处理器核心数*2, 8, 任务总数)`
   - **任务完成判定**: 使用 waitForPartialTasksWithRetry 方法，通过以下机制确保任务可靠完成：
     - **全量检查前置**: 在进入等待循环前，首先执行一次全量完成状态检查，避免所有任务已完成时仍进入等待
     - **部分完成策略**: 当超过一半任务完成时，允许提前返回处理下一批任务，提高整体吞吐量
     - **增量检查**: 每轮等待后，计算已完成任务数量，并在所有任务完成时立即返回
     - **超时保护**: 设置最大等待时间(5 分钟)和最大重试次数(3 次)，防止任务永久挂起
     - **递增等待**: 采用递增的等待时间(500ms、1000ms、1500ms...)，减轻轮询压力
     - **最终检查**: 在 processTable 方法结束时，使用 CompletableFuture.allOf 进行最终的任务完成等待
   - **全局超时保护**: 在整个导入流程设置最大运行时间(4 小时)，防止无限运行
   - **任务状态监控**: 详细记录任务完成状态、完成任务数量和进度百分比

4. **连接池与超时配置**

   - **连接池参数**:
     - max-active: 16 (最大活跃连接数)
     - max-idle: 8 (最大空闲连接数)
     - min-idle: 1 (最小空闲连接数)
     - max-wait: 3000ms (连接获取最大等待时间)
     - time-between-eviction-runs: 60000ms (定期清理空闲连接的间隔)
   - **超时设置**:
     - timeout: 10000ms (Redis 操作超时时间)
   - **客户端配置**: 使用 Lettuce 客户端，通过 LettuceClientConfiguration 配置
   - **序列化**: 统一使用 StringRedisSerializer 处理键，GenericJackson2JsonRedisSerializer 处理值

5. **错误处理机制**

   - **Pipeline 异常处理**: 捕获 RedisPipelineException，实现批次降级处理
   - **超时异常处理**: 捕获 QueryTimeoutException，触发重试机制
   - **连接异常处理**: 捕获 RedisConnectionFailureException，提供友好错误信息
   - **批处理降级**: 当批量操作失败时，自动降级为单键处理，确保数据完整性
   - **异常恢复**: 实现 waitForPartialTasksWithRetry 方法，健壮地等待未完成任务
   - **进度状态保护**: 确保在各种异常情况下也能正确更新导入进度状态，防止任务卡住

6. **监控与统计**
   - 记录每个批次的处理时间、新增键数、更新键数和错误数
   - 详细记录 Redis 操作耗时，便于性能分析和调优
   - 对批量获取、设置和处理过程分别统计耗时，识别瓶颈
   - 提供全面的任务完成状态日志，包括完成率、耗时和异常情况

### 6.4 POI 数据查询与地理匹配

系统实现了基于地理坐标的 POI 数据查询功能，用于丰富项目周边环境描述：

1. **数据源与表结构**

   - 数据表: `inf_poi` (存储在 MySQL 数据库中)
   - 主要字段:
     - `id`: POI 唯一标识
     - `name`: POI 名称
     - `type`: 兴趣点类型(大类;中类;小类)，如 "餐饮服务;中餐厅;特色/地方风味餐厅"
     - `typecode`: 兴趣点类型编码，如 "050118"
     - `address`: 详细地址
     - `lng`/`lat`: 经纬度坐标
     - 其他丰富的信息字段：电话、标签、城市、区域等

2. **地理距离计算**

   - **Haversine 公式**: 在 SQL 中使用 Haversine 公式计算地球表面两点间的球面距离
   - **距离计算 SQL**:
     ```sql
     ROUND(6371000 * ACOS(COS(RADIANS(?)) * COS(RADIANS(lat)) * COS(RADIANS(lng) - RADIANS(?)) + SIN(RADIANS(?)) * SIN(RADIANS(lat))), 2) as distance
     ```
   - **参数化配置**:
     - 搜索半径：默认 2 公里，可通过常量配置修改
     - 最大返回数：默认每个项目最多返回 20 个 POI

3. **数据处理与展示优化**

   - **类型分组统计**: 对查询结果按 POI 类型进行分组，展示 Top 3 类型及其数量
   - **距离排序**: 按距离升序排序，优先展示最近的 POI
   - **结果格式化**: 对 POI 结果进行结构化展示
     - 输出格式示例: `餐饮服务(5)，购物服务(3)，生活服务(2)`
     - POI 具体列表示例: `肯德基(120米)，星巴克(230米)，沃尔玛(450米)`

4. **实现机制**
   - **查询流程**:
     - 在`historyProjectStats`方法中，通过`enrichProjectsWithNearbyPois`调用 POI 查询逻辑
     - 对每个有地理坐标的项目，使用`enrichProjectWithNearbyPois`进行 POI 数据查询
     - 将查询结果包装为`PointOfInterestDto`对象列表，存储在项目详情中
   - **异常处理**: 完善的错误处理和日志记录，确保查询过程的稳定性
   - **降级策略**: 当项目无经纬度或查询出错时，返回空列表，确保不影响主流程

## 7. 前端组件设计

项目基于 Ant Design X，用于创建现代化的对话式用户界面。

### 核心组件

- **App.jsx**: 应用根组件，管理全局状态和路由。
- **MainLayout.jsx**: 应用的主布局组件，提供整体页面结构和路由渲染区域。
- **IndexPage.jsx**: 网站首页组件，直接渲染 ChatInterface，提供一个不依赖路由参数的聊天入口。
- **ChatPage.jsx**: 历史会话页面，支持切换/管理不同的会话，使用 conversationId 参数。
- **HomePage.jsx**: 简化的主页面组件，显示应用介绍和功能导航。
- **ChatInterface.jsx**: 主聊天界面容器，协调各聊天子组件间的交互。
- **ChatMessageList.jsx**: 渲染聊天消息列表，包括用户和助手的消息气泡。
- **ChatWelcome.jsx**: 显示欢迎界面，包含热门话题和提示词模板快捷方式。
- **ChatSender.jsx**: 聊天输入组件，使用固定布局策略将输入框定位在聊天区域底部。

### 聊天界面布局优化

聊天界面采用了固定输入框的设计模式，确保用户在任何时候都能方便地输入消息：

- **固定布局结构**: 在 `src/views/chat/container.vue` 中，使用 Flex 布局和相对/绝对定位相结合的方式组织界面结构。
- **输入框固定策略**:
  - 使用 `absolute bottom-0 left-0 right-0` 将输入框容器固定在父容器底部
  - 添加 `z-10` 确保输入框始终位于其他内容上层，避免被滚动内容覆盖
  - 设置 `bg-white dark:bg-gray-900` 适配明暗两种主题模式
  - 添加 `border-t border-gray-200 dark:border-gray-700` 创建清晰的视觉分隔
  - 设置适当的内边距 `py-2`，提升视觉舒适度
- **内容区域滚动**:
  - 中央内容区域使用 `flex-grow overflow-auto` 实现可滚动布局
  - 添加 `pb-24` 底部内边距，确保滚动到底部时内容不被固定输入框遮挡
- **自适应响应**: 整体布局支持各种屏幕尺寸，在不同设备上都能保持良好的交互体验。

这种布局设计确保了用户在浏览长聊天记录时，输入框始终保持可见和可用，大幅提升了使用体验。

### 着陆页设计 (Landing Page)

dips-pro-landingpage 采用了现代化的视觉设计，主要配色方案为：

- **主色调**: 紫色系 (#5661f6) - 代表创新与科技
- **辅助色**: 更丰富的紫色 (#9061f9) - 增强视觉层次
- **强调色**: 醒目的粉色 (#ff5f93) - 用于突出重要元素和按钮
- **文本色**: 深蓝黑色 (#1a1b32) 和蓝灰色 (#5b6084) - 提供良好的对比度和可读性
- **背景色**: 纯白色 (#ffffff) 和偏紫蓝色的浅灰 (#f5f7ff) - 创造层次感
- **渐变**: 主色到辅助色、辅助色到强调色的渐变 - 增加视觉吸引力

配色实现采用多层次策略：

1. **Tailwind 配置**: 自定义颜色变量和渐变样式
2. **HeroUI 主题**: 修改 light/dark 主题配色
3. **CSS 变量**: 在全局 CSS 中定义配色变量，确保全局一致性
4. **公共组件样式类**: 如 `text-gradient-primary`, `btn-gradient-primary` 等

设计同时保留了响应式布局和浅色/深色模式的支持，确保在各种设备和环境下都有最佳的用户体验。

### 全屏分页滚动实现

Landing Page 采用了自定义的全屏分页滚动设计，为用户提供类似 PPT 演示的浏览体验：

- **技术实现**:

  - 使用自定义滚动解决方案，摒弃了之前尝试的`@fullpage/react-fullpage`库
  - 自定义 CSS 样式(`styles.css`)实现平滑过渡和导航样式
  - 自定义 React hooks 处理滚动检测和页面切换
  - 将每个主要内容区域封装为独立的 section

- **核心功能**:
  - 丝滑的页面过渡: 使用 scroll-behavior: smooth 实现平滑滚动
  - 阻尼感: 使用自定义的滚动检测和延迟激活逻辑
  - 自动导航点: 右侧固定位置的导航点随滚动位置更新
  - 键盘导航: 支持通过上下箭头键切换页面
  - 内容过渡动画: 切换页面时内容淡入
  - 滚动提示器: 用动画指示用户可以继续滚动
  - 保留背景视差效果: 使用 react-scroll-parallax 实现背景层次感

### 视觉增强组件

- **GlowingCard**: 提供动态发光效果的卡片容器，跟随鼠标移动调整发光位置和强度。
- **FlowingBorderCard**: 实现鼠标悬停时显示流动渐变边框效果和上浮动画的卡片组件，通过 CSS 变量实现动态配置边框样式（颜色、宽度、圆角、模糊度等），仅在鼠标悬停时触发流动动画和上浮效果，支持自定义渐变色数组、上浮像素值和动画速度参数，为用户提供直观的交互反馈。
- **RadiantText**: 带有闪耀动画效果的文字显示组件，增强标题的视觉冲击力。
- **ParallaxBanner**: 使用 react-scroll-parallax 库实现的视差背景组件，创造页面深度感。

### 响应式设计策略

Landing Page 采用全面的响应式设计策略，确保在从大屏幕桌面到小屏幕移动设备的所有环境中都能提供良好的用户体验：

- **Bento Grid 系统**:

  - 大屏幕(>1024px): 采用 12 列网格，充分利用宽屏空间
  - 中等屏幕(768px-1024px): 减少每个元素的跨度，调整为 6 列或 12 列
  - 小屏幕(<640px): 转为单列布局，确保内容垂直流动，避免挤压
  - 使用 CSS Grid 和媒体查询实现，而非传统 Bootstrap 行列系统

- **响应式组件设计**:

  - **Features 卡片**: 在小屏幕上从水平排列转为垂直排列，内容居中
  - **GlowingCard**: 针对触摸设备优化，显示静态发光效果，不依赖鼠标跟踪
  - **图标大小**: 根据屏幕宽度自动调整(如 text-6xl md:text-8xl)
  - **间距和边距**: 在小屏幕上适当减小，避免浪费宝贵的屏幕空间

- **Tailwind 实践**:

  - 充分利用 Tailwind 的响应式前缀(sm:, md:, lg:)
  - 默认为移动视图设计(Mobile First)，再针对更大屏幕调整
  - 容器类采用 px-4 sm:px-6 md:px-8 等模式，响应式调整内边距
  - flex-col sm:flex-row 模式用于布局方向响应式变化

- **视差效果优化**:
  - 在移动设备上减少视差移动幅度，避免内容过度移动
  - 保留基本动效，但减弱滚动相关的位移量
  - 确保装饰元素在小屏幕上不会遮挡核心内容

### 模板相关组件

- **SlotEditor.jsx**: 单个槽位编辑器，根据槽位类型（文本、日期、选择列表等）显示不同的编辑控件，使用 Popover 浮层进行编辑。
- **InlineTemplateEditor.jsx**: 行内模板编辑器，解析模板文本中的槽位标记（如`{{meeting_date}}`），将其转换为可交互的 SlotEditor 组件。
- **TemplateDrawer.jsx**: 模板选择抽屉，显示可用模板列表。
- **TemplateFormModalWrapper.jsx**: 模板表单模态框，用于表单式编辑模板槽位（作为备选方案）。
- **TemplateManagePage.jsx**: 模板管理页面，显示所有可用模板的列表，支持搜索、编辑和删除操作，提供创建新模板的入口。
- **TemplateEditPage.jsx**: 模板编辑/创建页面，提供完整的模板创建和编辑表单，包括基本信息设置、模板内容编辑和槽位定义管理。

### 发送组件架构

发送组件采用分层设计，提高代码可维护性和扩展性：

- **BaseSender.jsx**: 基础消息发送组件，封装发送功能和交互逻辑。
- **TextSender.jsx**: 基于 BaseSender 的纯文本输入组件。
- **TemplateSender.jsx**: 基于 BaseSender 的模板消息发送组件，集成 InlineTemplateEditor。
- **ChatSender.jsx**: 控制器组件，根据是否有模板选择渲染 TextSender 或 TemplateSender。

### Landing Page 组件设计

Landing Page 为 DIPS Pro 平台提供引人入胜的首页展示，突出平台的核心价值和功能。组件设计注重视觉吸引力、响应式布局和用户引导。

- **LandingPage.jsx**: 主容器组件，负责整体布局和组织所有 Landing Page 子组件。

  - 使用 Ant Design 的 Layout 组件提供整体结构（Header, Content, Footer）
  - 实现固定顶部导航栏，包含 Logo、导航链接和行动按钮
  - 组织各内容区块的布局和顺序
  - 实现页脚设计，包含 Logo、链接分组和版权信息
  - 设置全局的响应式断点和样式变量

- **HeroSection.jsx**: 引人注目的英雄区域，是访问者的第一印象。

  - 设计渐变背景和神经网络动画效果
  - 呈现主标题"DIPS Pro: 释放 AI 群体智能，驱动未来工作流"
  - 添加浮动卡片元素，展示平台核心功能（数据分析、智能对话、流程自动化）
  - 使用 CSS 动画实现浮动效果和视差滚动
  - 提供明确的行动按钮引导用户探索产品

- **ProblemSolution.jsx**: 阐述用户痛点和 DIPS Pro 的解决方案。

  - 使用 Ant Design 的 Card 和 Row/Col 组件创建响应式布局
  - 展示三个主要痛点卡片，每个配有图标、标题和描述
  - 提供清晰的解决方案说明，强调多智能体协作模式的价值
  - 使用简洁的列表展示主要优势点
  - 实现卡片悬停动画，增强交互体验

- **Features.jsx**: 展示平台的四大核心优势。

  - 使用 GlowingCard 组件实现特性卡片的发光效果
  - 为每个特性卡片设计独特的图标和渐变色
  - 添加统计数据展示（工作效率提升、人为错误降低、流程时间缩短）
  - 实现卡片的悬停动画和渐变背景
  - 保持一致的视觉语言和布局结构

- **UseCases.jsx**: 通过标签切换展示四个主要应用场景。

  - 使用 Ant Design 的 Tabs 组件实现场景切换
  - 每个场景包含详细描述、效果说明和工作流程示意图
  - 使用简化的工作流程图展示智能体协作的步骤（数据收集 → 分析处理 → 内容生成 → 人工确认）
  - 添加场景相关的图片和视觉效果
  - 实现标签和内容的动画过渡效果

- **Testimonials.jsx**: 展示客户评价和合作品牌。

  - 使用 Ant Design 的 Carousel 组件实现评价轮播
  - 设计评价卡片包含头像、姓名、职位、公司和评分
  - 添加引号装饰和卡片阴影效果
  - 展示"受到众多企业的信赖"的品牌展示区域
  - 实现响应式布局，确保在移动设备上合理显示

- **CallToAction.jsx**: 引导用户采取下一步行动。
  - 设计突出的行动按钮和引人注目的标题
  - 展示产品核心优势（立即启动、数据安全、全程支持、按需付费）
  - 添加常见问题解答部分，解决潜在用户疑虑
  - 使用渐变背景和卡片阴影增强视觉吸引力
  - 实现响应式布局，确保移动设备上的良好显示效果

所有 Landing Page 组件都使用 CSS 模块或独立的样式文件，实现响应式设计，确保在不同设备（桌面、平板、手机）上都有良好的显示效果。设计风格保持与主应用一致，使用相同的色彩方案和设计语言。

## 8. 日志配置

- **框架**: 使用 Logback (通过 `logback-spring.xml`)，这是 Spring Boot 的默认日志框架。
- **配置文件**: `dips-pro-server/src/main/resources/logback-spring.xml`
- **日志路径**:
  - 在 `logback-spring.xml` 中，文件输出路径由 `${LOG_PATH}` 变量控制 (默认值为 `logs`)。
  - 该变量通过 Docker Compose 文件中的环境变量进行设置。
- **环境区分与持久化 (通过 Docker Compose)**:
  - **`dev` (开发环境)**:
    - 日志级别: `INFO` (根级别), `DEBUG` (对于 `com.dipspro` 包)。
    - 输出: 控制台 (`CONSOLE`)。
    - `docker-compose.dev.yml` 设置 `LOG_PATH=/var/log/dips-pro/dev` 并将宿主机 `./logs/dev` 挂载到此路径。
  - **`test` (测试环境)**:
    - 日志级别: `INFO`。
    - 输出: 控制台 (`CONSOLE`)。 (注意: 当前配置下，测试环境不输出到文件，如需文件输出，需调整 `logback-spring.xml` 中 `test` profile 并配置 Docker Compose)
  - **`prod` (生产环境)**:
    - 日志级别: `INFO`。
    - 输出: 滚动文件 (`FILE`)。
    - `docker-compose.prod.yml` 设置 `LOG_PATH=/var/log/dips-pro/prod` 并将宿主机 `./logs/prod` 挂载到此路径。
- **默认配置**: 如果没有激活特定 profile 或未在 Docker 环境中运行 (即 `LOG_PATH` 环境变量未设置)，文件日志将默认写入项目根目录下的 `logs` 文件夹。控制台日志仍按默认配置输出。

## 9. 部署架构

应用的部署基于 Docker 和 Docker Compose，并可通过 Jenkins 实现自动化。

- **容器化**:
  - 后端 Spring Boot 应用通过 `dips-pro-server/Dockerfile`

## 前端组件结构

### 通用 UI 组件

- **HyperText**: 一个交互式文本组件，当用户悬停时会显示字符变换动画效果，增强用户体验和页面视觉吸引力。位于 `dips-pro-landingpage/src/components/ui/hyper-text.tsx`。

## 数据缓存与索引设计

### MySQL 数据表索引至 Redis

在本系统中，我们实现了一个将 MySQL 数据表数据索引至 Redis 的功能，以提高查询性能。

#### 索引数据源

- 多个历史数据表：`data_namelist_1`、`data_namelist_1_24`、`data_namelist_1_23`、`data_namelist_1_22`、`data_namelist_1_21`、`data_namelist_1_20`
- 每张表包含数百万条记录
- 数据库来源为 MySQL，系统采用多数据源配置，包括：
  - 主数据源(PostgreSQL)：存储系统业务数据
  - MySQL 数据源：专门用于访问历史数据表

#### 数据源配置设计

- **多数据源配置**：

  ```java
  @Configuration
  public class DataSourceConfig {
      @Primary
      @Bean(name = "primaryDataSource")
      @ConfigurationProperties(prefix = "spring.datasource.primary")
      public DataSource primaryDataSource() {...}

      @Bean(name = "mysqlDataSource")
      @ConfigurationProperties(prefix = "spring.datasource.mysql")
      public DataSource mysqlDataSource() {...}

      @Primary
      @Bean(name = "primaryJdbcTemplate")
      public JdbcTemplate primaryJdbcTemplate(...) {...}

      @Bean(name = "mysqlJdbcTemplate")
      public JdbcTemplate mysqlJdbcTemplate(...) {...}
  }
  ```

- **YAML 配置**：
  ```yaml
  spring:
    datasource:
      # 主数据源 (PostgreSQL)
      primary:
        driver-class-name: org.postgresql.Driver
        url: *******************************
        username: xxx
        password: xxx
      # MySQL数据源 (用于data_namelist表)
      mysql:
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: *********************************
        username: xxx
        password: xxx
        hikari:
          maximum-pool-size: 10
    # Redis配置
    data:
      redis:
        host: xxx
        port: 6379
        password: xxx
        database: 1
  ```

#### Redis 键值设计

- **键(Key)**: `{手机号}` (直接使用手机号作为键，不带前缀)
- **值(Value)**: 字符串数组，格式为 `tableName:id`，如: `["data_namelist_1:123", "data_namelist_1_24:456"]`
- **优势**:
  - 简化存储结构，减少 Redis 空间占用和序列化/反序列化开销
  - 降低网络传输量，提高查询性能
  - 更直观的数据格式，易于调试和排查问题

#### 索引流程

1. 分批查询 MySQL 数据，每批 10000 条记录
2. 查询条件：
   ```sql
   d_mobile is not null and d_mobile<>'' and d_mobile<>'0' and d_mobile<>'****'
   AND LEFT(d_mobile, 4) <> '0085'
   AND LEFT(d_mobile, 2) NOT IN ('01','02','03','04','05','06','07','08','09')
   ```
3. 使用异步任务执行导入，避免阻塞 Web 请求
4. 对于已存在的 mobile 键，增量追加新数据而非覆盖
5. 详细记录导入进度和日志

#### 数据库查询优化

1. **基于索引的两阶段查询**：
   - 第一阶段：通过手机号在 Redis 中快速查找对应的表名和 ID
   - 第二阶段：根据表名和 ID 直接在 MySQL 中查询完整记录
2. **批量查询支持**：支持一次查询多个手机号对应的数据
3. **增量更新机制**：当数据库中有新数据时，可以增量添加到 Redis 索引中

#### API 接口

1. **启动导入**

   - 路径: `/api/data-migration/import-to-redis`
   - 方法: POST
   - 参数: `batchSize` (默认 10000)
   - 响应: 导入进程启动状态

2. **查询进度**

   - 路径: `/api/data-migration/import-progress`
   - 方法: GET
   - 响应: 导入进度信息，包括总记录数、已处理记录数、进度百分比、预计剩余时间等

3. **按手机号查询索引记录**

   - 路径: `/api/data-migration/query/{mobile}`
   - 方法: GET
   - 参数: `mobile` (路径参数)
   - 响应: 该手机号对应的数据索引记录列表（仅包含表名和 ID）

4. **按手机号查询完整数据**
   - 路径: `/api/data-migration/query-name-list/{mobile}` - 方法: GET
   - 参数: `mobile` (路径参数)
   - 响应: 该手机号对应的完整数据内容，包括表名、ID 以及实际记录的所有字段数据
   - 特点: 该接口先通过 Redis 索引获取记录位置信息，再从 MySQL 数据库中查询完整记录内容

#### 示例用法

```java
// 1. 查询索引记录（仅包含表名和ID）
List<MobileDataRecord> records = dataMigrationService.queryByMobile("13812345678");

// 2. 查询完整数据内容（包含表中全部字段）
List<NameListDataDto> completeData = dataMigrationService.queryNameList("13812345678");

// 使用示例工具类
DataQueryExample queryExample = new DataQueryExample();

// 3. 只查询索引记录
List<MobileDataRecord> indexRecords = queryExample.findDataByMobile("13812345678");

// 4. 查询完整数据
List<Map<String, Object>> completeData = queryExample.findCompleteDataByMobile("13812345678");

// 5. 批量查询
List<String> mobileList = Arrays.asList("13812345678", "13987654321");
Map<String, List<MobileDataRecord>> batchResult = queryExample.batchQueryMobileData(mobileList);
```

## 数据导入组件

### 主要功能

- 将 MySQL 中的名单数据批量导入到 Redis
- 支持多线程并行导入
- 支持断点续传
- 保存导入进度
- 支持手机号查询

### 数据处理流程

1. 扫描数据表，获取表的 ID 范围
2. 将大表按 ID 范围分割成多个小任务
3. 使用线程池并行处理小任务
4. 任务处理时，从 MySQL 读取数据，然后写入 Redis
5. 记录导入进度和统计信息

### Redis 数据批量操作优化

为解决 Redis 批量获取操作可能出现的超时问题，我们采用以下策略：

1. 数据分批处理：将大批量的键集合拆分为较小的批次（默认每批 5000 个键）分别处理
2. 重试机制：对于 Redis 超时操作实现指数退避重试，最多重试 3 次
3. 降级策略：当批量获取多次失败后，降级为单键逐个获取模式
4. 完善的日志记录：记录每个批次的处理情况，包括成功、失败和重试信息

## 数据查询优化

### 大表分批查询策略

系统采用分批次处理策略优化大数据量表的查询性能，特别是针对可能导致超时的全表扫描操作：

1. **基于 ID 范围的分割处理**

   - 首先使用`getTableIdRange`方法获取表的最小和最大 ID 范围
   - 根据 ID 范围和配置的批次大小(通常为 10 万条记录)计算总批次数
   - 将整个 ID 范围分割为多个不重叠的批次，每个批次独立处理
   - 使用`MinMaxIds`内部类封装 ID 范围信息，提供验证和范围计算功能

2. **批次处理实现技术**

   - 在 SQL 查询中使用`id >= startId AND id <= endId`约束条件限定每批次查询范围
   - 使用 JDBC 标准查询方式将结果集映射为 Java 对象集合
   - 批次处理流程完全顺序化，按照批次计数逐个处理，避免并发带来的复杂性
   - 每批次处理后记录详细进度日志，包括已处理记录数、累计获取的数据量和耗时

3. **提前终止机制**

   - 实现两级提前终止策略：批次内终止和全局终止
   - 批次内终止：当单个批次处理后已找到足够多满足条件的记录(如超过 MAX_RESULTS)时，提前终止该批次处理
   - 全局终止：当所有批次累计找到的满足条件记录超过阈值时，提前终止后续表的处理
   - 终止条件参数化配置，可根据业务需求和系统性能调整

4. **优化查询示例 - queryMobilesByProjectCount 方法**

   - 该方法用于查询在多个项目中出现次数超过阈值的手机号
   - 优化前：一次性查询整张表，容易导致查询超时
   - 优化后：
     - 对每个表获取 ID 范围，将查询分解为多个批次(每批 10 万条)
     - 每批次独立计算满足条件的记录，累计到全局统计中
     - 实现动态阈值判断，找到足够多结果后提前终止
     - 详细记录处理进度，便于监控和调优
     - 增强错误处理，即使单批次查询出错也不影响整体流程

5. **性能收益**

   - 避免单次大数据量查询导致的超时问题
   - 提高系统响应速度，改善用户体验
   - 降低数据库负载，减轻服务器压力
   - 提供更精确的进度反馈和更健壮的错误处理
   - 满足条件时提前终止，避免不必要的全表扫描

6. **适用场景**
   - 大型历史数据表查询和统计
   - 需要进行跨表数据关联和聚合的复杂查询
   - 预计查询结果较小但需要扫描大量记录的场景
   - 后台批处理任务和报表生成

## 7. 工具类设计

系统设计遵循"不依赖 Spring 上下文的功能类型代码都提取到静态工具类中"的原则，主要工具类包括：

- **MapUtils**: 提供安全地从 Map 获取值的方法
  - `safeGetInteger(Map<String, Object> map, String key)`: 安全获取 Integer 值，处理 null 值和类型转换
  - `safeGetDouble(Map<String, Object> map, String key)`: 安全获取 Double 值，处理 null 值和类型转换
  - `safeGetString(Map<String, Object> map, String key)`: 安全获取 String 值，处理 null 值情况
- **更多工具类将根据需要添加**

工具类设计原则：

1. 使用 `final` 类和私有构造函数防止实例化
2. 所有方法都是静态的，不维护状态
3. 方法参数强制非空检查，确保健壮性
4. 提供详细的 Javadoc 文档，说明每个方法的用途、参数和返回值
5. 遵循单一职责原则，每个工具类专注于特定领域的功能

## XIII. 指标缓存设计

### 1. 概述

指标缓存模块提供对系统各项指标的统一管理和查询功能，支持按指标代码查询指标定义、获取指标层级关系等操作。指标数据通过配置文件定义，加载到内存中缓存，不需要数据库持久化。

### 2. 核心组件

- **IndexDefinition**：指标定义实体类，包含指标名称、代码、层级关系等信息
- **IndexService**：指标服务接口，提供指标查询的核心功能
- **IndexServiceImpl**：指标服务实现类，负责从配置文件加载指标定义并提供缓存查询
- **IndexUtils**：指标工具类，提供处理指标代码的静态工具方法
- **IndexController**：指标控制器，提供 REST API 用于查询指标数据
- **IndexHierarchyDto**：指标层级结构 DTO，用于展示主指标与子指标的层级关系

### 3. 数据结构设计

指标定义包含以下关键属性：

- **indexGridName**：指标名称
- **legacyIndexCode**：遗留指标代码（原始系统代码，如 `ra1a5`）
- **planarIndexCode**：planar 指标代码（新系统代码，如 `001`）
- **parentIndexCode**：父指标代码（如子指标 `101003001` 的父指标为 `101003`）
- **isMainIndex**：是否是主指标

### 4. 指标层级关系

系统中的指标分为两个层级：主指标和子指标。主指标如"房屋设计"（`101005`），子指标如"户型设计"（`101005001`）。子指标代码通常以主指标代码为前缀。

主要指标类型包括：

- 满意度指标：`001`-`003`
- 房屋类指标：`101***`
- 服务类指标：`201***`
- 其他指标：`301***`, `302***`

### 5. 配置文件

指标定义存储在 `index-definitions.yml` 配置文件中，采用 YAML 格式，可以通过修改此文件增减指标，而无需修改代码。

示例配置：

```yaml
indexes:
  - indexGridName: 房屋设计
    legacyIndexCode: rk0a5
    planarIndexCode: '101005'
    isMainIndex: true

  - indexGridName: 户型设计
    legacyIndexCode: rk03a5
    planarIndexCode: '101005001'
    parentIndexCode: '101005'
    isMainIndex: false
```

### 6. 缓存实现

指标缓存使用 Spring 应用启动时初始化，数据加载后存储在内存中，支持多种查询方式：

- 按 planar 指标代码查询
- 按遗留指标代码查询
- 获取所有主指标
- 获取指定主指标的子指标
- 获取完整的指标层级结构

### 7. API 接口

系统提供以下 REST API 用于查询指标数据：

- `GET /api/indexes`：获取所有指标定义
- `GET /api/indexes/main`：获取所有主指标
- `GET /api/indexes/planar/{code}`：根据 planar 指标代码查询
- `GET /api/indexes/legacy/{code}`：根据遗留指标代码查询
- `GET /api/indexes/main/{mainCode}/sub`：获取指定主指标的子指标
- `GET /api/indexes/mapping`：获取主指标与子指标的映射关系
- `GET /api/indexes/hierarchy`：获取所有指标的层级结构
- `GET /api/indexes/hierarchy/{mainCode}`：获取指定主指标的层级结构

### 8. 扩展性

- 可以通过修改 `index-definitions.yml` 配置文件轻松添加、修改或删除指标
- 支持未来添加更多指标类型和层级
- 系统设计支持后续与数据分析、报表生成等模块集成

### 7.6 用户画像标签分析功能

HistoryProjectStatsDto 实现了基于历史购买项目数据自动分析生成用户画像标签的功能：

1. **标签系统设计**:

   - 采用灵活的设计模式，使用内部类`UserProfileTag`封装标签属性
   - 每个标签包含代码、名称、描述、类型、置信度和匹配的项目列表
   - 标签按类型分组展示，如购买行为、客户价值、品牌偏好等
   - 通过匹配项目关系，可追踪每个标签的证据来源

2. **标签分析逻辑**:

   - 通过`analyzeUserProfileTags()`方法统一调用各类标签分析函数
   - 实现 17 种标签规则，包括多次置业经历、高净值客户、投资客等
   - 每个标签都有明确的规则定义，基于历史数据生成
   - 分析过程自动执行，集成到 toString()方法中确保 API 返回结果包含标签信息

3. **特色标签类型**:

   - **购买行为标签**: 多次置业经历、投资客、跨城市置业经历等
   - **客户价值标签**: 高净值客户、成长型客户等
   - **品牌偏好标签**: 品牌忠实、单一品牌持有、品牌首购、头部品牌偏好等
   - **居住偏好标签**: 配套设施偏好、设计规划偏好、质量做工偏好、装修品质偏好等

4. **技术实现**:
   - 利用 Java 流式处理 API 高效处理集合数据
   - 采用函数式编程思想，单一职责原则设计标签分析函数
   - 通过缓存优化分析性能，避免重复计算
   - 结构化输出格式，便于前端展示和进一步分析

该功能为业务分析提供了额外价值，通过将原始数据转化为有意义的用户标签，帮助业务人员更好地了解客户特征和偏好。

## 项目架构设计

### 历史楼盘信息模块架构

#### 1. 整体架构

历史楼盘信息模块采用了分层架构设计，将数据表示、业务逻辑、格式化输出等功能进行了清晰的职责划分。主要分为以下几层：

- **数据传输层（DTO）**：负责数据的封装和传输
- **业务逻辑层（Analyzer）**：负责数据分析和处理
- **格式化层（Formatter）**：负责数据的展示格式化
- **工具层（Util）**：提供通用工具方法
- **常量层（Constant）**：存放系统常量定义

#### 2. 核心类说明

##### 2.1 数据传输层

- **HistoryProjectStatsDto**：历史楼盘信息 DTO，包含项目列表和用户标签列表
- **ProjectDetail**：楼盘详情数据，包含楼盘基本信息、评分、配套设施等
- **IndexWithScore**：带分值的指标数据，用于存储各项评分指标
- **UserProfileTag**：用户画像标签，用于描述用户特征和偏好
- **PointOfInterestDto**：兴趣点数据，用于描述楼盘周边设施

##### 2.2 业务逻辑层

- **UserProfileAnalyzer**：用户画像分析总控类，协调各种标签分析
- **BasicTagAnalyzer**：基础标签分析器，处理多次置业、投资客等基础标签
- **BrandTagAnalyzer**：品牌标签分析器，处理品牌忠实度等品牌相关标签
- **PreferenceTagAnalyzer**：偏好标签分析器，处理用户居住偏好相关标签

##### 2.3 格式化层

- **ProjectDetailFormatter**：项目详情格式化器，负责将数据转换为结构化文本输出

##### 2.4 工具与常量

- **ProjectStatsUtil**：提供项目统计相关的通用工具方法
  - 格式化价格(`formatPrice`)：将价格数字转换为不同单位(万元、亿元)的字符串
  - 配套设施追加(`appendFacilityIfNotNull`)：处理项目配套设施文本拼接逻辑
  - 距离计算(`calculateDistance`)：使用 Haversine 公式计算两点间地球表面距离
  - 获取头部开发商(`getTop20Developers`)：获取预定义的头部 TOP20 房地产开发商列表
  - 获取旅游养老目的地(`getTourismRetirementDestinations`)：获取预定义的文旅养老热门目的地列表
  - 提取物业费(`extractPropertyFee`)：从物业费描述文本中提取数值，支持范围格式(如"7~18")
  - 楼盘档次权重(`getBuildingGradeWeight`)：根据楼盘档次返回对应的权重值
  - 查找最早/最晚签约项目(`findEarliestProject`/`findLatestProject`)：在项目列表中查找签约时间最早/最晚的项目
  - 日期比较(`isMoreRecentDate`)：比较两个日期字符串，判断前者是否比后者更近
- **ProjectStatsConstants**：定义项目统计相关的常量值

#### 3. 类关系图

```
                           +-----------------+
                           | HistoryProject  |
                           |    StatsDto     |
                           +-----------------+
                                   |
                 +------------------+------------------+
                 |                  |                  |
        +----------------+  +----------------+  +----------------+
        | ProjectDetail  |  | UserProfileTag |  |UserProfileAnalyzer
        +----------------+  +----------------+  +----------------+
                 |                                      |
        +----------------+                     +--------+--------+--------+
        |IndexWithScore  |                     |BasicTagAnalyzer| |Brand..| |Pref...|
        +----------------+            +---------------+ +-------+ +-------+
               |                      |BasicTagAnalyzer| |Brand..| |Pref...|
        +-------------+               +---------------+ +-------+ +-------+
        |   Formatter |
        +-------------+
```

#### 4. 设计原则应用

- **单一职责原则**：每个类只负责一项职责，如标签分析与数据结构分离
- **开闭原则**：扩展新标签分析只需添加新的分析器，无需修改现有代码
- **依赖倒置原则**：高层模块不依赖于底层模块的实现细节
- **接口隔离原则**：各模块通过精确定义的接口通信
- **组合优于继承**：使用组合方式实现功能扩展

#### 5. 未来优化方向

- 考虑引入策略模式优化标签分析器的实现
- 增加缓存机制提高频繁访问数据的性能
- 引入事件机制处理分析过程中的状态通知
- 优化文本格式化的模板机制，增强灵活性

## XIV. 前端部署架构

### 1. HTTPS 证书配置

系统采用了安全的证书管理方式，将 HTTPS 证书直接打包在 Docker 镜像中：

#### 1.1 证书存储结构

- **证书目录**：证书文件可以通过两种方式提供：

  1. 开发环境：通过 Docker 卷挂载从主机提供
  2. 生产环境：通过 Jenkins 构建脚本处理

- **证书文件**：
  - `dipsai.cn.pem`：完整的证书链文件
  - `dipsai.cn.key`：证书私钥文件

#### 1.2 Docker 集成

- Docker 镜像中只创建了证书目录`/etc/nginx/certs/`
- 在部署时通过卷挂载将实际证书文件挂载到容器中
- Nginx 配置直接从容器内路径读取证书文件
- 实现了证书与容器的解耦，增强了安全性和灵活性

#### 1.3 部署优势

- **灵活部署**：支持多种证书提供方式，适应不同环境需求
- **安全性增强**：证书不包含在镜像中，降低了证书泄露风险
- **CI/CD 友好**：支持 Jenkins 等自动化部署流程，通过挂载提供证书
- **证书更新便捷**：无需重新构建镜像即可更新证书，只需替换挂载的证书文件

## DIPS Pro 架构设计

### 前端架构

#### 技术栈

- React + TypeScript
- Ant Design X（UI 组件库）
- UmiJS（企业级 React 应用框架）
- Mako（基于 Rust 的快速打包工具，对 umi 进行了深度优化）

#### 代码结构

```
dp-web/
├── src/                # 源代码
│   ├── pages/          # 页面组件
│   ├── components/     # 公共组件
│   ├── services/       # API服务
│   ├── models/         # 数据模型
│   ├── utils/          # 工具函数
│   └── layouts/        # 页面布局
├── public/             # 静态资源
├── .umirc.ts           # UmiJS配置
└── package.json        # 依赖管理
```

#### 页面组件

##### 1. 智能体页面(Agents)

智能体展示页面允许用户浏览和选择可用的智能体：

- **路径**: `/agents`
- **组件**: `src/pages/Agents/index.tsx`
- **功能**:
  - 以卡片式布局展示所有可用智能体
  - 每个智能体卡片显示名称、描述、标签、类型、状态和能力值
  - 使用 Ant Design 的 Card、Avatar 和 Tag 等组件构建统一 UI 风格
  - 支持响应式布局，根据屏幕尺寸自适应展示列数
- **数据来源**:
  - 开发环境: 从 mock 数据获取智能体列表
  - 生产环境: 通过 API 从后端获取智能体配置
- **交互设计**:
  - 卡片悬停效果增强用户体验
  - 状态徽标(Badge)直观显示智能体活跃状态
  - 标签系统便于用户理解智能体功能域

##### 2. 欢迎页面(Welcome)

系统入口页面，提供导航和快速访问功能：

- **路径**: `/welcome`
- **组件**: `src/pages/Welcome.tsx`

##### 3. 管理页面(Admin)

管理员功能页面，提供系统管理功能：

- **路径**: `/admin/sub-page`
- **组件**: `src/pages/Admin.tsx`

##### 4. 查询表格(TableList)

数据查询和展示页面：

- **路径**: `/list`
- **组件**: `src/pages/TableList/index.tsx`

#### 最佳实践与解决方案

##### React 严格模式兼容性

在 React 严格模式(Strict Mode)下，`findDOMNode`方法已被弃用，并将在未来版本中移除。这会导致使用 Ant Design 组件时在控制台显示警告信息。为解决此问题，我们采用以下方案：

1. **使用 React.forwardRef**：
   - 将组件改写为使用`forwardRef`和`useImperativeHandle`的形式
   - 示例: `dp-web/src/pages/Table/components/UpdateForm.tsx`
2. **实现步骤**：

   ```typescript
   // 改写前
   const MyComponent: React.FC<Props> = (props) => {
     // 组件逻辑...
     return <div>...</div>;
   };

   // 改写后
   const MyComponent: React.FC<Props> = forwardRef((props, ref) => {
     // 可选：暴露组件方法给父组件
     useImperativeHandle(ref, () => ({
       // 可以在这里暴露方法
     }));

     // 组件逻辑...
     return <div>...</div>;
   });
   ```

3. **适用场景**：

   - 任何使用了 Tooltip、Popconfirm 等弹出组件的自定义组件
   - 包含 Modal、Drawer 等需要 DOM 引用的组件
   - 使用 Form.Item 包装的自定义表单组件

4. **验证方法**：
   - 在开启 React 严格模式的情况下，控制台不再显示`findDOMNode is deprecated`警告

### 后端架构

#### 技术栈

- Spring Boot
- JPA / Hibernate
- PostgreSQL（主数据库）
- Redis（缓存和索引）
- MySQL（数据采集和分析）

#### 代码结构

```
dips-pro-server/
├── src/main/java/com/dipspro/
│   ├── config/         # 配置类
│   ├── controller/     # REST API控制器
│   ├── service/        # 业务逻辑层
│   ├── repository/     # 数据访问层
│   ├── entity/         # JPA实体类
│   ├── dto/            # 数据传输对象
│   ├── exception/      # 异常处理
│   └── util/           # 工具类
└── src/main/resources/
    └── application.yml # 应用配置
```

#### 数据流

1. 前端通过 REST API 与后端交互
2. 控制器层接收请求并委托给服务层处理
3. 服务层实现业务逻辑，通过仓库层访问数据
4. 仓库层负责与数据库的交互

#### 缓存策略

- 使用 Redis 缓存频繁访问的数据
- 实现多级缓存机制，减轻数据库负担

### 部署架构

#### 开发环境

- 本地开发: Docker Compose
- 测试环境: Kubernetes 集群

#### 生产环境

- Kubernetes 集群
- NGINX 作为反向代理和静态资源服务器
- PostgreSQL 集群（主从复制）
- Redis 集群（哨兵模式）

#### CI/CD 流程

- 使用 Jenkins 实现持续集成和部署
- 自动化测试包括单元测试和集成测试
- 代码质量检查使用 SonarQube

### 安全架构

#### 认证与授权

- 使用 JWT 实现无状态认证
- 基于角色的访问控制(RBAC)
- 敏感 API 使用 API 密钥保护

#### 数据安全

- 所有 API 通过 HTTPS 访问
- 敏感数据加密存储
- 定期数据备份和恢复演练

### 监控与日志

#### 监控

- 使用 Prometheus 收集指标
- Grafana 用于可视化监控数据
- 设置关键指标的告警阈值

#### 日志

- 集中式日志收集(ELK Stack)
- 结构化日志格式，便于检索和分析
- 日志分级: DEBUG, INFO, WARN, ERROR

### 性能优化

#### 前端性能

- 代码分割和懒加载
- 静态资源 CDN 加速
- 组件级别的缓存策略

#### 后端性能

- 合理使用索引
- 查询优化和 SQL 调优
- 采用异步处理框架处理耗时操作

### 扩展性设计

#### 水平扩展

- 无状态设计，支持多实例部署
- 数据分片策略
- 负载均衡

#### 垂直扩展

- 模块化设计，便于功能扩展
- 插件系统支持第三方功能集成
- 配置驱动的功能开关

## 模板系统

### 模板插槽处理流程

模板槽位数据处理是 DIPS Pro 中的核心功能之一，它允许用户创建带有动态内容的提示词模板。整个处理流程经过了全面的优化，以确保数据在 UI 到 API 的整个过程中保持一致性。

#### 数据结构

模板槽位定义支持两种格式：

1. **数组格式**：后端 API 返回的格式，每个槽位是一个带有 name、type、defaultValue 等属性的对象

   ```typescript
   SlotDefinition[] = [
     { name: 'name1', type: 'text', defaultValue: 'default1', required: true },
     { name: 'name2', type: 'select', options: [...], defaultValue: 'option1' }
   ]
   ```

2. **对象格式**：前端组件内部处理的格式，key 为槽位名称，value 为槽位定义
   ```typescript
   Record<string, SlotDefinition> = {
     'name1': { type: 'text', defaultValue: 'default1', required: true },
     'name2': { type: 'select', options: [...], defaultValue: 'option1' }
   }
   ```

系统会在多个组件中自动转换这两种格式，确保与后端 API 和前端组件都能正确交互。

#### 数据流

模板槽位数据在系统中的流动路径：

1. **模板选择**：`container.vue` 中的 `handleSelectTemplate` 函数负责初始化槽位定义和默认值
2. **槽位编辑**：`SlotEditor.vue` 组件处理用户输入，并通过 `handleSlotChange` 事件向上传递
3. **数据汇总**：`InlineTemplateEditor.vue` 收集所有槽位的值，并在提交时传递给 `TemplateSender.vue`
4. **消息构建**：`TemplateSender.vue` 和 `ChatSender.vue` 使用模板内容和槽位值构建完整的消息
5. **API 调用**：使用 `chatApi.sendChatMessage` 发送数据到后端，同时传递模板 ID、填充后的消息内容和槽位值

### 模板消息填充机制

为确保模板消息能够被正确处理，系统实现了双重填充保障机制：

1. **前端填充**：在 `ChatSender.vue` 中，使用 `fillTemplate` 函数将模板内容和槽位值合并，生成完整的消息内容，在发送 API 请求时作为 `message` 参数传递。

2. **API 层填充**：在 `chatApi.sendChatMessage` 函数中，如果检测到 `message` 为空但使用了模板，会自动获取模板内容并使用槽位值进行填充，确保发送到后端的请求中 `message` 字段始终有值。

3. **模板缓存**：为提高性能，API 层实现了模板内容缓存机制，避免重复请求相同的模板内容。

这种双重机制确保了即使在某一层填充失败的情况下，模板消息也能被正确处理和发送。

## 消息格式化架构

### MessageContent 统一消息内容类

为了支持丰富的聊天消息内容类型，系统设计了统一的 `MessageContent` 类：

```java
// 位置：com.dipspro.modules.chat.dto.MessageContent
public class MessageContent {
    private String type;    // 内容类型
    private Object content; // 内容数据
}
```

#### 支持的内容类型

- **text**: 文本内容，支持 Markdown 格式
- **suggestion**: 建议选项数组，用于快速操作
- **file**: 文件信息对象，支持文件展示和下载
- **collapsible**: 可折叠内容，支持展开/折叠交互
- **image**: 图片信息对象，支持图片展示
- **chart**: 图表数据对象，支持数据可视化
- **table**: 表格数据对象，支持结构化数据展示

#### 静态工厂方法

```java
MessageContent.text("文本内容")
MessageContent.suggestion(new String[]{"选项1", "选项2"})
MessageContent.collapsible(collapsibleData)
MessageContent.file(fileData)
```

### 格式化器增强

`ProjectDetailFormatter` 类新增结构化方法：

- `formatStructured()`: 返回完整的结构化消息内容列表
- `formatSimpleStructured()`: 返回简化的结构化消息内容列表

支持多种内容组合：

- 项目列表文本
- 可折叠的用户画像分析
- 可折叠的项目详情信息
- 交互式建议选项
- 文件附件下载

### 数据流转

```
UserProfileDto → ProjectDetailFormatter → List<MessageContent> → ChatResponse → 前端渲染
```

这种架构设计确保了：

- 统一的消息内容格式
- 灵活的内容类型扩展
- 前后端数据结构一致性
- 丰富的用户交互体验

### 数据查询模块 (normalize)

#### API 接口

- **用户画像查询 API**:

  - `GET /api/data-query/userProfile/{mobile}`: 根据手机号查询用户画像。

    - 路径参数: `mobile` (手机号码)
    - 响应体: `List<MessageContent>` (用户画像内容列表)
    - 逻辑: 调用 `DataQueryService` 查询用户画像信息，包含项目详情、用户标签等。

  - `POST /api/data-query/userProfile/batch`: 批量查询用户画像。
    - 请求体: `BatchUserProfileRequest` (包含手机号列表和显示详情标志)
      ```json
      {
        "mobiles": ["13800138000", "13800138001"],
        "showDetail": false
      }
      ```
    - 响应体: `List<MessageContent>` (与单个查询接口格式一致的消息内容列表)
      - 包含统计信息、手机号标识、用户画像内容和分隔符
      - 格式示例：
        ```
        批量查询统计: 总数 2, 成功 1, 失败 1, 耗时 1500ms
        ==================================================
        手机号: 138****8000
        [用户画像内容...]
        ---
        手机号: 138****8001 - 未找到用户画像数据
        ```
    - 限制: 单次查询最多支持 100 个手机号
    - 逻辑: 使用并行处理提高查询效率，将所有结果合并到统一的消息格式中
