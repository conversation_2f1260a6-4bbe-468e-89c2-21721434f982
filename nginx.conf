server {
    listen 8080;
    listen [::]:8080;
    server_name _;

    # 启用 gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: http: https: *.fg-china.cn; connect-src 'self' http: https:; font-src 'self' data:; media-src 'self' data: blob: http: https: *.fg-china.cn; object-src 'none'; frame-src 'self'; base-uri 'self'; form-action 'self'" always;

    # Web 应用根路径 - 处理 /app/ 路径
    location /app/ {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /app/index.html;
    }

    # 兼容根路径访问（用于健康检查等）
    location = / {
        return 301 /app/;
    }

    # 静态资源缓存 - 处理 /app/ 路径下的静态资源
    location ~* ^/app/.*\.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        root /usr/share/nginx/html;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}

server {
    # 监听 HTTPS 端口
    listen 8443 ssl http2;
    listen [::]:8443 ssl http2;
    server_name _;

    # === SSL/TLS 配置 ===
    # 使用容器内的证书文件
    ssl_certificate /etc/nginx/certs/dipsai.cn.pem;
    ssl_certificate_key /etc/nginx/certs/dipsai.cn.key;

    # 推荐的 SSL/TLS 安全设置 (可根据需要调整)
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 1d;
    ssl_session_tickets off;
    # HSTS (可选, 但推荐开启以增强安全) - 首次配置建议先用短 max-age 测试
    # add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;

    # 其他配置与 HTTP 相同
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: http: https: *.fg-china.cn; connect-src 'self' http: https:; font-src 'self' data:; media-src 'self' data: blob: http: https: *.fg-china.cn; object-src 'none'; frame-src 'self'; base-uri 'self'; form-action 'self'" always;

    # === 基础根目录 ===
    root /usr/share/nginx/html;
    index index.html index.htm;

    # Let's Encrypt ACME challenge
    location /.well-known/acme-challenge/ {
        # Nginx needs read access to the challenge files placed by Certbot
        # This root should point to the *same* directory Certbot places challenges in
        root /usr/share/nginx/html; # This path must align with the shared volume
    }

    # === dips-pro-web (主应用) ===
    # 处理 /app/ 路径
    location /app/ {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /app/index.html; # SPA fallback for the main app
    }

    # 兼容根路径访问（用于健康检查等）
    location = / {
        return 301 /app/;
    }

    # === API 代理（通过反向代理访问时不需要，由上层 nginx 处理） ===
    # location /api {
    #     proxy_pass http://dips-pro-server:8080;
    #     proxy_set_header Host $host;
    #     proxy_set_header X-Real-IP $remote_addr;
    #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #     proxy_set_header X-Forwarded-Proto $scheme;
    #     proxy_set_header X-Forwarded-Host $host;
    #     proxy_set_header X-Forwarded-Port $server_port;
    # }

    # === 静态资源缓存 ===
    location ~* ^/app/.*\.(?:css|js|jpg|jpeg|gif|png|ico|woff|woff2|ttf|eot|svg)$ {
        root /usr/share/nginx/html;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # === 隐藏文件 ===
    location ~ /\. {
        deny all;
    }
}
