version: '3.8'

services:
  landingpage:
    build:
      context: ../
      dockerfile: dp-landingpage/Dockerfile
    container_name: dp-landingpage
    ports:
      # Map standard HTTP port 80 from host to container
      - "80:80"
      # 添加 HTTPS 端口映射
      - "443:443"
    volumes:
      # 挂载证书目录
      - /mnt/dp-landingpage/certs:/etc/nginx/certs:ro
      # 挂载 nginx 配置文件
      - /mnt/dp-landingpage/nginx.conf:/etc/nginx/conf.d/default.conf:ro
    networks:
      - dips-pro-network
    restart: unless-stopped # Restart policy for production

networks:
  dips-pro-network:
    external: true # 标记为外部网络 