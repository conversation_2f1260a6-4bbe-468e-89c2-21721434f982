pipeline {
    agent any



    environment {
        LANDINGPAGE_COMPOSE_FILE = 'deploy/docker-compose.landingpage.prod.yml'
    }

    stages {
        stage('Checkout') {
            steps {
                echo '拉取源码 (Landing Page Pipeline)'
                sh 'git config --global --add safe.directory /var/jen<PERSON>_home/workspace/dips-pro-landingpage'
                checkout scmGit(branches: [[name: '*/master']], extensions: [], userRemoteConfigs: [[credentialsId: '0df03cbb-96bc-4b90-8e42-e4b8598c7ddb', url: 'http://***********:3000/meizhi/dips_pro.git']])
                echo 'Checking out code...'
                sh 'pwd'
                sh 'ls -la'
            }
        }



        stage('Build and Deploy Landing Page') {
            parallel {
                stage('Prepare Environment') {
                    steps {
                        echo '准备 Landing Page 部署环境...'
                        sh "mkdir -p /mnt/dp-landingpage/certs"
                        sh "chmod 700 /mnt/dp-landingpage/certs"
                        
                        echo '复制最新的 nginx.conf 配置文件...'
                        sh "rm -rf /mnt/dp-landingpage/nginx.conf"
                        sh "cp dp-landingpage/nginx.conf /mnt/dp-landingpage/nginx.conf"
                        sh "chmod 644 /mnt/dp-landingpage/nginx.conf"
                        
                        echo '停止现有 Landing Page 容器...'
                        sh "docker-compose -f ${env.LANDINGPAGE_COMPOSE_FILE} down || true"
                    }
                }
                stage('Build Application') {
                    steps {
                        script {
                            def startTime = System.currentTimeMillis()
                            
                            echo '构建 Landing Page 应用...'
                            sh """
                                unset DOCKER_BUILDKIT
                                unset COMPOSE_DOCKER_CLI_BUILD
                                unset BUILDKIT_PROGRESS
                                docker-compose -f ${env.LANDINGPAGE_COMPOSE_FILE} build
                            """
                            
                            def endTime = System.currentTimeMillis()
                            def duration = (endTime - startTime) / 1000
                            echo "Landing Page 构建耗时: ${duration} 秒"
                            
                            writeFile file: 'landingpage-build-metrics.json', text: """
{
    "build_number": "${env.BUILD_NUMBER}",
    "duration_seconds": ${duration},
    "landing_changed": "${env.LANDING_CHANGED}",
    "timestamp": "${new Date().format('yyyy-MM-dd HH:mm:ss')}"
}
"""
                        }
                    }
                }
            }
            
            post {
                success {
                    echo '启动 Landing Page 服务...'
                    sh "docker-compose -f ${env.LANDINGPAGE_COMPOSE_FILE} up -d --force-recreate"
                }
            }
        }
    }

    post {
        always {
            echo 'Landing Page Pipeline finished.'
            archiveArtifacts artifacts: 'landingpage-build-metrics.json', allowEmptyArchive: true
            sh '''
                docker images --format "table {{.Repository}}:{{.Tag}}" | grep "dp-landingpage:cache-" | tail -n +6 | xargs -r docker rmi || true
            '''
        }
        success {
            echo 'Landing Page Deployment successful!'
            sh "docker tag dp-landingpage:latest dp-landingpage:cache-${env.BUILD_NUMBER} || true"
        }
        failure {
            echo 'Landing Page Deployment failed.'
            sh "docker-compose -f ${env.LANDINGPAGE_COMPOSE_FILE} down --remove-orphans || true"
        }
    }
} 