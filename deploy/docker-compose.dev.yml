version: '3.8'

services:
  backend:
    build:
      context: ../dips-pro-server
      dockerfile: Dockerfile
    container_name: dips-pro-backend-dev
    ports:
      - "8080:8080"
    environment:
      # Spring Profile
      - SPRING_PROFILES_ACTIVE=dev
      # Database Configuration (Replace placeholders)
      - SPRING_DATASOURCE_URL=***************************************************
      - SPRING_DATASOURCE_USERNAME=your_dev_db_user
      - SPRING_DATASOURCE_PASSWORD=your_dev_db_password
      # Redis Configuration (Replace placeholders)
      - SPRING_REDIS_HOST=YOUR_DEV_REDIS_HOST
      - SPRING_REDIS_PORT=6379
      # n8n Configuration (Replace placeholders)
      - N8N_BASE_URL=http://YOUR_N8N_HOST:5678
      - N8N_WEBHOOK_PATH_CHAT=/webhook/YOUR_CHAT_WEBHOOK_ID # Example path
      - N8N_AUTH_USERNAME=your_n8n_basic_auth_user
      - N8N_AUTH_PASSWORD=your_n8n_basic_auth_password
      # Log Path Configuration
      - LOG_PATH=/var/log/dips-pro/dev
    networks:
      - dips-pro-network
    volumes:
      # Mount host log directory to container log directory specified by LOG_PATH
      - ../logs/dev:/var/log/dips-pro/dev
    # Optional: Mount volumes for faster development (e.g., target dir)
    # Requires Spring Boot DevTools and careful setup
    # volumes:
    #   - ../dips-pro-server/target:/app/target

  frontend:
    build:
      context: ../dp-web
      dockerfile: Dockerfile
    container_name: dips-pro-frontend-dev
    ports:
      # Map host port 5173 (common Vite dev port) to container's Nginx port 80
      - "5173:80"
    depends_on:
      - backend
    networks:
      - dips-pro-network
    # Optional: Mount source code for hot-reloading (if Nginx serves dev build or Vite runs in container)
    # This example assumes Nginx serves a static build, so volume mounts are less useful here
    # unless you rebuild the image on code changes.

networks:
  dips-pro-network:
    driver: bridge 