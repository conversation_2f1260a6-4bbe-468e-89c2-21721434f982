pipeline {
    agent any // Or specify a label for an agent with Dock<PERSON>, Docker Compose, Node.js installed



    environment {
        WEBAPP_COMPOSE_FILE = 'deploy/docker-compose.webapp.prod.yml'
    }

    stages {
        stage('Checkout') {
            steps {
                echo '拉取源码 (Web App Pipeline)'
                sh 'git config --global --add safe.directory /var/jen<PERSON>_home/workspace/dp-web-fa'
                checkout scmGit(branches: [[name: '*/master']], extensions: [], userRemoteConfigs: [[credentialsId: '0df03cbb-96bc-4b90-8e42-e4b8598c7ddb', url: 'http://***********:3000/meizhi/dips_pro.git']])
                echo 'Checking out code...'
                sh 'pwd'
                sh 'ls -la'
            }
        }


        stage('Build and Deploy Web App') {
            parallel {
                stage('Prepare Environment') {
                    steps {
                        echo '准备 Web App 部署环境...'
                        sh "mkdir -p /mnt/dp-web-fa/certs"
                        sh "chmod 700 /mnt/dp-web-fa/certs"
                        
                        echo '复制最新的 nginx.conf 配置文件...'
                        sh "rm -rf /mnt/dp-web-fa/nginx.conf"
                        sh "cp dp-web-fa/nginx.conf /mnt/dp-web-fa/nginx.conf"
                        sh "chmod 644 /mnt/dp-web-fa/nginx.conf"
                        
                        echo '停止现有 Web App 容器...'
                        sh "docker-compose -f ${env.WEBAPP_COMPOSE_FILE} down || true"
                    }
                }
                stage('Build Application') {
                    steps {
                        script {
                            def startTime = System.currentTimeMillis()
                            
                            echo '构建 Web App 应用...'
                            sh """
                                unset DOCKER_BUILDKIT
                                unset COMPOSE_DOCKER_CLI_BUILD
                                unset BUILDKIT_PROGRESS
                                docker-compose -f ${env.WEBAPP_COMPOSE_FILE} build
                            """
                            
                            def endTime = System.currentTimeMillis()
                            def duration = (endTime - startTime) / 1000
                            echo "Web App 构建耗时: ${duration} 秒"
                            
                            writeFile file: 'webapp-build-metrics.json', text: """
{
    "build_number": "${env.BUILD_NUMBER}",
    "duration_seconds": ${duration},
    "web_changed": "${env.WEB_CHANGED}",
    "timestamp": "${new Date().format('yyyy-MM-dd HH:mm:ss')}"
}
"""
                        }
                    }
                }
            }
            
            post {
                success {
                    echo '启动 Web App 服务...'
                    sh "docker-compose -f ${env.WEBAPP_COMPOSE_FILE} up -d --force-recreate"
                }
            }
        }


    }

    post {
        always {
            echo 'Web App Pipeline finished.'
            archiveArtifacts artifacts: 'webapp-build-metrics.json', allowEmptyArchive: true
            sh '''
                docker images --format "table {{.Repository}}:{{.Tag}}" | grep "dp-web-fa:cache-" | tail -n +6 | xargs -r docker rmi || true
            '''
        }
        success {
            echo 'Web App Deployment successful!'
            sh "docker tag dp-web-fa:latest dp-web-fa:cache-${env.BUILD_NUMBER} || true"
        }
        failure {
            echo 'Web App Deployment failed.'
            sh "docker-compose -f ${env.WEBAPP_COMPOSE_FILE} down --remove-orphans || true"
        }
    }
} 