pipeline {
    agent any // Or specify a label for an agent with Dock<PERSON>, Docker Compose, Java installed

    environment {
        JAVA_HOME = tool 'jdk23'
        M2_HOME = tool 'maven'
        // Define path to the backend docker compose file
        BACKEND_COMPOSE_FILE = 'deploy/docker-compose.backend.prod.yml'
    }

    stages {
        stage('Checkout') {
            steps {
                echo '拉取源码 (Backend Pipeline)'
                sh 'git config --global --add safe.directory /var/jenkins_home/workspace/dp-server' // Adjust workspace name if needed
                checkout scmGit(branches: [[name: '*/master']], extensions: [], userRemoteConfigs: [[credentialsId: '0df03cbb-96bc-4b90-8e42-e4b8598c7ddb', url: 'http://***********:3000/meizhi/dips_pro.git']])
                echo 'Checking out code...'
                sh 'pwd'
                sh 'ls -la'
            }
        }

        stage('Build Backend') {
            steps {
                echo 'Building backend application...'
                dir('dp-server') {
                    sh """${env.M2_HOME}/bin/mvn -version && ${env.M2_HOME}/bin/mvn -B -s /mnt/YT-Config/maven/settings.xml clean package -U -DskipTests && pwd && ls -l"""
                }
            }
        }

        stage('Build Docker Image') {
            steps {
                echo 'Building backend Docker image...'
                // Maven 构建后，JAR 包在 dp-server/target/ 目录下
                // Dockerfile 也在 dp-server/ 目录下
                dir('dp-server') { 
                    sh 'docker build -t dips_pro_backend_backend:latest .'
                    sh 'docker system prune -f'
                }
            }
        }

        stage('Deploy Backend Production') {
            steps {
                echo 'Deploying backend service using direct docker run...'
                // Stop and remove existing container if it exists
                sh "docker stop dp-server || true" // Use || true to ignore errors if container doesn't exist
                sh "docker rm dp-server || true"
                
                // Run new container with all configurations
                sh '''docker run -d --name dp-server \\
                    -p 5681:8080 \\
                    -e SPRING_PROFILES_ACTIVE=prod \\
                    -e SPRING_DATASOURCE_URL=jdbc:postgresql://***********:5433/dips \\
                    -e SPRING_DATASOURCE_USERNAME=dips \\
                    -e SPRING_DATASOURCE_PASSWORD=VcyAHPgY4nz38wRWJER6 \\
                    -e SPRING_DATA_REDIS_HOST=*********** \\
                    -e SPRING_DATA_REDIS_PORT=6379 \\
                    -e SPRING_DATA_REDIS_PASSWORD=KxpV4cCAZY \\
                    -e SPRING_DATA_REDIS_DATABASE=1 \\
                    -e MYSQL_URL="***************************************************************************************************" \\
                    -e MYSQL_USERNAME=fgit \\
                    -e MYSQL_PASSWORD=Wsm3RZ3gEJpManjJMUDfwvrJUmsv3xqzpUvVKp \\
                    -e N8N_BASE_URL=http://***********:5678/ \\
                    -e N8N_WEBHOOK_PATH_CHAT=http://***********:5678/webhook/25afd8bc-1b85-48ab-a965-1043d2813989 \\
                    -e N8N_AUTH_USERNAME=dipspro \\
                    -e N8N_AUTH_PASSWORD=L4NshqmpVKVL6fpZ8GDK \\
                    -e SERVER_PORT=8080 \\
                    -e LOG_PATH=/var/log/dips-pro/prod \\
                    --network dips-pro-network \\
                    -v /mnt/yt-logs/dips-pro:/var/log/dips-pro/prod \\
                    --restart unless-stopped \\
                    dips_pro_backend_backend:latest'''
            }
        }
    }

    post {
        always {
            echo 'Backend Pipeline finished.'
            // Clean up workspace, notify, etc.
        }
        success {
            echo 'Backend Deployment successful!'
            // Send success notification
        }
        failure {
            echo 'Backend Deployment failed.'
            // Send failure notification
        }
    }
} 