# 应用配置面板
VITE_APP_SETTING = true
# 页面标题
VITE_APP_TITLE = Dips Pro AI
# 接口请求地址，会设置到 axios 的 baseURL 参数上
VITE_APP_API_BASEURL = http://localhost:8080
VITE_BASIC_AUTH_USERNAME = dips-pro
VITE_BASIC_AUTH_PASSWORD = vpx4ywne3rkDAMRHc3vMxYPDRYCnVdjkzLewwp62
# 调试工具，可设置 eruda 或 vconsole，如果不需要开启则留空
VITE_APP_DEBUG_TOOL =
# 是否禁用开发者工具，可防止被调试
VITE_APP_DISABLE_DEVTOOL = false

# 是否开启代理
VITE_OPEN_PROXY = true
# 是否开启开发者工具
VITE_OPEN_DEVTOOLS = true
