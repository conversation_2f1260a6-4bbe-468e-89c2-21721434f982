# === Stage 1: Build the Landing Page ===
FROM node:20-alpine as builder
WORKDIR /app

# 复制依赖文件（缓存层）
COPY dp-landingpage/package.json dp-landingpage/package-lock.json ./

# 尝试使用阿里云镜像源安装，失败时回退到官方源
RUN npm config set registry https://registry.npmmirror.com/ && \
    npm ci && npm cache clean --force || \
    (npm config set registry https://registry.npmjs.org/ && \
     npm ci && npm cache clean --force)

# 复制源码并构建（变更频繁层）
COPY dp-landingpage/ ./
RUN npm run build

# === Stage 2: Serve with Nginx ===
FROM nginx:stable-alpine

# 复制自定义 Nginx 配置
COPY dp-landingpage/nginx.conf /etc/nginx/conf.d/default.conf

# 创建证书目录 (用于 SSL 证书挂载)
RUN mkdir -p /etc/nginx/certs

# 复制构建的静态文件
COPY --from=builder /app/dist /usr/share/nginx/html/

# 暴露端口
EXPOSE 80
EXPOSE 443

# 启动 Nginx
CMD ["nginx", "-g", "daemon off;"] 