{"name": "react-vite-ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc --noCheck && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@fullpage/react-fullpage": "^0.1.48", "@heroui/react": "2.7.6", "@iconify/react": "latest", "@inspira-ui/plugins": "^0.0.1", "@types/node": "^22.15.3", "@vueuse/core": "^13.1.0", "@vueuse/motion": "^3.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^11.18.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-scroll-parallax": "^3.4.5", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "10.4.20", "postcss": "8.4.49", "tailwindcss": "3.4.17", "typescript": "5.7.3", "vite": "^6.0.11"}}