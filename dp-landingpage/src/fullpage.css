/* fullpage.js 自定义样式 */

/* 主要颜色变量 */
:root {
  --primary-color: #5661f6;
  --secondary-color: #ff5f93;
}

/* 右侧导航点样式 */
#fp-nav ul li a span, 
.fp-slidesNav ul li a span {
  background: var(--primary-color) !important;
  box-shadow: 0 0 10px rgba(86, 97, 246, 0.5) !important;
  transition: all 0.3s ease !important;
}

/* 活动导航点样式 */
#fp-nav ul li a.active span, 
.fp-slidesNav ul li a.active span {
  background: var(--secondary-color) !important;
  box-shadow: 0 0 15px rgba(255, 95, 147, 0.7) !important;
  transform: scale(1.5) !important;
}

/* 导航点悬停效果 */
#fp-nav ul li:hover a span, 
.fp-slidesNav ul li:hover a span {
  transform: scale(1.3) !important;
}

/* 导航点提示文字样式 */
#fp-nav ul li .fp-tooltip {
  color: var(--primary-color) !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  background-color: rgba(255, 255, 255, 0.9) !important;
  padding: 2px 8px !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;
}

/* 滚动提示箭头 */
.fp-controlArrow {
  border-color: var(--primary-color) !important;
}

/* 页面间过渡动画 */
.fp-section,
.fp-slide {
  transition: all 1s cubic-bezier(0.645, 0.045, 0.355, 1.000) !important;
}

/* 处理过渡期间的内容淡入淡出效果 */
.fp-section.active .section-content {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.3s;
}

.section-content {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

/* 自定义滚动条样式 */
.fp-scrollable {
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) #f0f0f0;
}

.fp-scrollable::-webkit-scrollbar {
  width: 6px;
}

.fp-scrollable::-webkit-scrollbar-track {
  background: #f0f0f0;
}

.fp-scrollable::-webkit-scrollbar-thumb {
  background-color: var(--primary-color);
  border-radius: 3px;
}

/* 使页面滚动时保持适当的页面内边距 */
.section {
  padding: 0 !important;
}

/* 防止固定导航栏遮挡内容 */
.section:first-child {
  padding-top: 70px !important;
}

/* 垂直居中的内容布局 */
.fp-tableCell {
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
}

/* 鼠标滚轮和触摸板滚动增强 */
@media (pointer: fine) {
  html {
    scroll-behavior: smooth;
  }
}

/* 当屏幕高度较小时调整 */
@media screen and (max-height: 700px) {
  .fp-tableCell {
    padding: 40px 0;
  }
}

/* credits 样式 */
.fp-watermark {
  opacity: 0.4 !important;
  transition: opacity 0.3s ease !important;
}

.fp-watermark:hover {
  opacity: 0.7 !important;
} 