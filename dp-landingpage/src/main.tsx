import {<PERSON><PERSON><PERSON>rov<PERSON>, ToastProvider} from "@heroui/react";
import React from "react";
import ReactDOM from "react-dom/client";
import { ParallaxProvider } from 'react-scroll-parallax';

import App from "./App.tsx";

import "./index.css";
import "./styles/parallax.css";

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <HeroUIProvider>
      <ParallaxProvider>
        <ToastProvider />
        <main className="text-foreground bg-background">
          <App />
        </main>
      </ParallaxProvider>
    </HeroUIProvider>
  </React.StrictMode>,
);
