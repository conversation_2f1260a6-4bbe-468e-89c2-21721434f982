@import './assets/fonts/noto-sans-sc.css';
@import './styles/glowingCard.css'; /* 导入卡片发光效果样式 */

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: 'Noto Sans SC', sans-serif;
  /* 添加全局CSS变量，匹配landing的配色方案 */
  --primary-color: #5661f6; /* 更亮更鲜艳的紫色 */
  --primary-hover: #4149db;
  --secondary-color: #9061f9; /* 更加丰富的紫色渐变 */
  --accent-color: #ff5f93; /* 醒目的粉色作为强调色 */
  --text-color: #1a1b32; /* 较深的蓝黑色 */
  --text-light: #5b6084; /* 较亮的蓝灰色 */
  --background-light: #ffffff;
  --background-gray: #f5f7ff; /* 偏紫蓝色的浅灰背景 */
  --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  --gradient-accent: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
  --box-shadow: 0 10px 30px rgba(86, 97, 246, 0.15);
}

html {
  scroll-behavior: smooth;
}

/* RadiantText 动画效果 */
@keyframes radiant {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.radiant-animation {
  animation: radiant var(--radiant-anim-duration, 4s) ease-in-out infinite;
  overflow: hidden;
}

/* 添加科技风格扫描线动画 */
@keyframes scanVertical {
  0% {
    top: -5%;
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    top: 105%;
    opacity: 0;
  }
}

@keyframes scanHorizontal {
  0% {
    left: -5%;
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    left: 105%;
    opacity: 0;
  }
}

/* 添加科技风格的浮动动画 */
@keyframes float-slow {
  0% {
    transform: translateY(0px) translateX(0px);
  }
  50% {
    transform: translateY(15px) translateX(5px);
  }
  100% {
    transform: translateY(0px) translateX(0px);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px) translateX(0px);
  }
  50% {
    transform: translateY(10px) translateX(5px);
  }
  100% {
    transform: translateY(0px) translateX(0px);
  }
}

@keyframes float-reverse {
  0% {
    transform: translateY(0px) translateX(0px);
  }
  50% {
    transform: translateY(-10px) translateX(-5px);
  }
  100% {
    transform: translateY(0px) translateX(0px);
  }
}

@keyframes spin-slow {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse-slow {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

.animate-float-slow {
  animation: float-slow 8s ease-in-out infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-reverse {
  animation: float-reverse 7s ease-in-out infinite;
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 5s ease-in-out infinite;
}

/* 自定义透明度类 */
.opacity-15 {
  opacity: 0.15;
}

/* 添加渐变文本样式 */
.text-gradient-primary {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.text-gradient-accent {
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* 按钮渐变样式 */
.btn-gradient-primary {
  background: var(--gradient-primary);
  border: none;
  box-shadow: var(--box-shadow);
  color: white;
  transition: all 0.3s ease;
}

.btn-gradient-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 30px rgba(86, 97, 246, 0.25);
}

/* 添加按钮发光效果 */
.glow-hover {
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.glow-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(144, 97, 249, 0.8) 0%, rgba(86, 97, 246, 0) 70%);
  opacity: 0;
  z-index: -1;
  transform: scale(0.5);
  transition: opacity 0.4s ease, transform 0.4s ease;
}

.glow-hover:hover::before {
  opacity: 0.8;
  transform: scale(2);
}

.btn-gradient-accent {
  background: var(--gradient-accent);
  border: none;
  box-shadow: var(--box-shadow);
  color: white;
  transition: all 0.3s ease;
}

.btn-gradient-accent:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 30px rgba(144, 97, 249, 0.25);
}

/* 流光边框按钮效果 */
@keyframes flowingBorder {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes pulseGlow {
  0% {
    filter: blur(5px);
    opacity: 0.5;
  }
  50% {
    filter: blur(10px);
    opacity: 0.8;
  }
  100% {
    filter: blur(5px);
    opacity: 0.5;
  }
}

.btn-flowing-border {
  position: relative;
  transition: all 0.3s ease;
  z-index: 1;
  overflow: hidden;
  border: none !important; /* 移除原生边框 */
  color: var(--secondary-color) !important; /* 设置文本颜色 */
}

.btn-flowing-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  z-index: -1;
  background: linear-gradient(270deg, var(--primary-color), var(--secondary-color), var(--accent-color), var(--secondary-color), var(--primary-color));
  background-size: 300% 300%;
  border-radius: inherit;
  animation: flowingBorder 6s ease-in-out infinite;
  transition: opacity 0.3s ease;
  opacity: 0.7;
}

.btn-flowing-border::after {
  content: '';
  position: absolute;
  inset: 2px;
  background: #ffffff;
  border-radius: inherit;
  z-index: -1;
  transition: background-color 0.3s ease;
}

.btn-flowing-border:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(144, 97, 249, 0.25);
  color: var(--accent-color) !important; /* 悬停时改变文本颜色 */
}

.btn-flowing-border:hover::before {
  opacity: 1;
  animation-duration: 3s;
  filter: blur(3px);
  animation: flowingBorder 3s ease-in-out infinite, pulseGlow 2s ease-in-out infinite;
}

.dark .btn-flowing-border::after {
  background: #1a1b32;
}

.dark .btn-flowing-border {
  color: var(--secondary-color) !important;
}

.dark .btn-flowing-border:hover {
  color: var(--accent-color) !important;
}
