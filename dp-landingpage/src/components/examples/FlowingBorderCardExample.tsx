import React from 'react';
import FlowingBorderCard from '../common/FlowingBorderCard';

const FlowingBorderCardExample: React.FC = () => {
  // 使用主题色渐变
  const themeColors = [
    "#5661f6", // 紫色
    "#7b5ff7",
    "#a25cf8",
    "#c85af8", 
    "#ed57f7", 
    "#ff5f93", // 粉色
    "#5661f6" // 重复第一种颜色形成闭环
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-6">
      {/* 默认样式卡片 */}
      <FlowingBorderCard>
        <div className="p-6 bg-white dark:bg-gray-800 h-full">
          <h3 className="text-xl font-bold mb-4">默认样式卡片</h3>
          <p>鼠标悬停时显示流动彩虹光效边框</p>
        </div>
      </FlowingBorderCard>

      {/* 自定义主题色卡片 */}
      <FlowingBorderCard 
        colors={themeColors}
        duration={3000}
        borderWidth={3}
        borderRadius={12}
        blur={6}
      >
        <div className="p-6 bg-white dark:bg-gray-800 h-full">
          <h3 className="text-xl font-bold mb-4">自定义主题色卡片</h3>
          <p>使用紫色到粉色的主题渐变，流动速度更慢，边框更宽</p>
        </div>
      </FlowingBorderCard>

      {/* 快速流动卡片 */}
      <FlowingBorderCard 
        colors={["#00c6ff", "#0072ff", "#00c6ff"]}
        duration={1500}
        borderWidth={2}
        borderRadius={4}
        blur={3}
      >
        <div className="p-6 bg-white dark:bg-gray-800 h-full">
          <h3 className="text-xl font-bold mb-4">快速流动卡片</h3>
          <p>使用蓝色渐变，流动速度更快，边框更窄</p>
        </div>
      </FlowingBorderCard>

      {/* 高对比度卡片 */}
      <FlowingBorderCard 
        colors={["#ff0080", "#7928ca", "#ff0080"]}
        duration={2000}
        borderWidth={4}
        borderRadius={16}
        blur={8}
      >
        <div className="p-6 bg-white dark:bg-gray-800 h-full">
          <h3 className="text-xl font-bold mb-4">高对比度卡片</h3>
          <p>使用高对比度的亮粉色和紫色，边框更宽，模糊效果更强</p>
        </div>
      </FlowingBorderCard>
    </div>
  );
};

export default FlowingBorderCardExample; 