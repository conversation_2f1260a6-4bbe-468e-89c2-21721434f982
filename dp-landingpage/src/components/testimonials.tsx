import React from "react";
import { Card, CardBody, Avatar } from "@heroui/react";
import { Icon } from "@iconify/react";
import { Parallax } from "react-scroll-parallax";
import "../styles/glowingCard.css";
import GlowingCard from "./common/GlowingCard";

interface Testimonial {
  content: string;
  author: string;
  role: string;
  company: string;
  avatar: string;
  parallaxY: [number, number];
  glowColor: string;
}

export function Testimonials() {
  const testimonials: Testimonial[] = [
    {
      content: "DIPS Pro 彻底改变了我们的内容生产流程。我们的社交媒体团队通过多智能体协作，同时管理 7 个渠道的内容，发布速度提升三倍，而且内容质量更高，互动率增长显著。",
      author: "张明",
      role: "市场总监",
      company: "领创科技",
      avatar: "https://i.pravatar.cc/150?u=zhang-ming",
      parallaxY: [-15, 15],
      glowColor: "from-primary-500 to-secondary-500"
    },
    {
      content: "客户支持一直是我们的瓶颈，自从使用 DIPS Pro 构建智能客服流程后，响应时间缩短了 65%，首次解决率提升了 40%，客服团队终于可以专注于复杂问题，而非重复性工作。",
      author: "李婷",
      role: "客户成功负责人",
      company: "云智科技",
      avatar: "https://i.pravatar.cc/150?u=li-ting",
      parallaxY: [-20, 20],
      glowColor: "from-secondary-500 to-primary-500"
    },
    {
      content: "作为数据分析部门，我们每周要处理大量的报表工作。DIPS Pro 的自动化工作流帮我们节省了80%的数据收集和处理时间，让我们可以花更多精力在数据洞察而非数据整理上。",
      author: "王强",
      role: "数据分析主管",
      company: "远景科技",
      avatar: "https://i.pravatar.cc/150?u=wang-qiang",
      parallaxY: [-25, 25],
      glowColor: "from-primary-500 to-secondary-500"
    }
  ];

  return (
    <section className="parallax-section py-24 bg-background">
      {/* 背景装饰 */}
      <div className="absolute inset-0 opacity-5 z-0">
        <Parallax translateX={[-20, 20]} className="absolute top-[30%] right-0 w-1/3 h-1/3 opacity-20">
          <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <path 
              d="M95,50 C95,30 80,5 50,5 C20,5 5,30 5,50 C5,70 20,95 50,95 C80,95 95,70 95,50 Z" 
              fill="none" 
              stroke="#5661f6" 
              strokeWidth="0.5" 
              strokeDasharray="5,5"
            />
          </svg>
        </Parallax>
        
        <Parallax translateX={[20, -20]} className="absolute bottom-[20%] left-0 w-1/4 h-1/4 opacity-20">
          <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <rect 
              x="10" 
              y="10" 
              width="80" 
              height="80" 
              fill="none" 
              stroke="#ff5f93" 
              strokeWidth="0.5"
              strokeDasharray="5,5"
              rx="20"
            />
          </svg>
        </Parallax>
      </div>
      
      <div className="container mx-auto px-6 relative z-10">
        <Parallax translateY={[-15, 15]} className="text-center max-w-3xl mx-auto mb-16">
          <div className="inline-flex items-center gap-2 bg-landing-primary/10 text-landing-primary px-4 py-1 rounded-full text-sm font-medium mb-6">
            <Icon icon="lucide:heart" className="text-lg" />
            <span>客户喜爱</span>
          </div>
          <h2 className="text-3xl font-bold mb-4">他们如何借助 DIPS Pro 提升工作效率</h2>
          <p className="text-default-600 text-lg">
            来自各行各业客户的真实体验，了解他们如何通过智能体协作解决业务挑战。
          </p>
        </Parallax>

        <div className="bento-grid">
          {testimonials.map((testimonial, index) => (
            <Parallax 
              key={index}
              translateY={testimonial.parallaxY}
              className={`bento-span-4`}
            >
              <GlowingCard 
                glowColor={testimonial.glowColor}
                className="h-full"
              >
                <Card className="border-none shadow-none bg-white h-full">
                  <CardBody className="p-6 flex flex-col h-full">
                    <div className="mb-6">
                      <Icon icon="lucide:quote" className="text-primary-300 text-3xl mb-2" />
                      <p className="text-default-700 italic">{testimonial.content}</p>
                    </div>
                    
                    <div className="mt-auto flex items-center gap-4">
                      <Avatar 
                        src={testimonial.avatar}
                        alt={testimonial.author}
                        className="w-12 h-12"
                      />
                      <div>
                        <div className="font-semibold">{testimonial.author}</div>
                        <div className="text-default-500 text-sm">
                          {testimonial.role}, {testimonial.company}
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </GlowingCard>
            </Parallax>
          ))}
        </div>
        
        <Parallax translateY={[-20, 20]} className="mt-20 relative">
          <div className="absolute -inset-4 bg-gradient-to-br from-primary-500/20 to-secondary-500/20 rounded-xl blur-xl"></div>
          <div className="relative p-8 bg-white rounded-xl">
            <div className="flex flex-col items-center text-center">
              <Icon icon="lucide:heart-handshake" className="text-5xl text-primary mb-4" />
              <h3 className="text-2xl font-bold mb-2">准备好提升您的工作流程了吗？</h3>
              <p className="text-default-600 mb-6 max-w-2xl">
                加入全球已有超过 500+ 企业客户和 10,000+ 专业用户，利用 DIPS Pro 多智能体协作平台释放 AI 的真正潜力。
              </p>
              <div className="flex flex-wrap justify-center gap-6">
                <div className="flex flex-col items-center">
                  <div className="text-3xl font-bold text-primary">500+</div>
                  <div className="text-default-500 text-sm">企业客户</div>
                </div>
                <div className="flex flex-col items-center">
                  <div className="text-3xl font-bold text-secondary">10,000+</div>
                  <div className="text-default-500 text-sm">专业用户</div>
                </div>
                <div className="flex flex-col items-center">
                  <div className="text-3xl font-bold text-primary">98%</div>
                  <div className="text-default-500 text-sm">客户满意度</div>
                </div>
                <div className="flex flex-col items-center">
                  <div className="text-3xl font-bold text-secondary">24/7</div>
                  <div className="text-default-500 text-sm">技术支持</div>
                </div>
              </div>
            </div>
          </div>
        </Parallax>
      </div>
    </section>
  );
}