import React from "react";
import { Card, CardBody, Badge } from "@heroui/react";
import { Icon } from "@iconify/react";
import { Parallax } from "react-scroll-parallax";
import "../styles/glowingCard.css";
import GlowingCard from "./common/GlowingCard";

interface UseCase {
  icon: string;
  title: string;
  description: string;
  result: string;
  image: string;
  glowColor: string;
  parallaxY: [number, number];
}

export function UseCases() {
  const useCases: UseCase[] = [
    {
      icon: "lucide:megaphone",
      title: "市场营销自动化",
      description: "想象一下：内容撰写智能体产出博文初稿，图片生成智能体匹配视觉素材，社交媒体智能体在最佳时间自动分发到 LinkedIn、Twitter。DIPS Pro 让您的营销内容生产和发布流程全自动。",
      result: "节省内容创作时间 70%，提升发布效率。",
      image: "https://img.heroui.chat/image/dashboard?w=600&h=400&u=dips-marketing",
      glowColor: "from-primary-500 to-secondary-500",
      parallaxY: [-15, 15]
    },
    {
      icon: "lucide:message-circle",
      title: "智能客户服务",
      description: "邮件分析智能体自动识别客户意图并打标签，知识库检索智能体快速查找相关解决方案，回复草拟智能体生成个性化回复建议。客服人员只需审核优化，即可高效响应。",
      result: "平均响应时间缩短 50%，提升客户满意度。",
      image: "https://img.heroui.chat/image/crm?w=600&h=400&u=dips-customer-service",
      glowColor: "from-secondary-500 to-primary-500",
      parallaxY: [-20, 20]
    },
    {
      icon: "lucide:bar-chart",
      title: "数据处理与报告生成",
      description: "数据抓取智能体从多个网站或API收集销售数据，数据清洗智能体自动整理格式、剔除异常值，报告生成智能体汇总数据并生成周度销售分析图表。告别繁琐的表格操作。",
      result: "数据处理时间减少 80%，报告准确性提升。",
      image: "https://img.heroui.chat/image/dashboard?w=600&h=400&u=dips-data-report",
      glowColor: "from-primary-500 to-secondary-500",
      parallaxY: [-15, 15]
    },
    {
      icon: "lucide:shopping-bag",
      title: "电商运营助手",
      description: "评论监控智能体实时追踪全平台商品评价，情感分析智能体识别负面反馈，初步回应智能体基于模板生成安抚性回复初稿，等待运营确认后发送。主动管理用户口碑。",
      result: "负面评论响应速度提高 60%，改善品牌形象。",
      image: "https://img.heroui.chat/image/dashboard?w=600&h=400&u=dips-ecommerce",
      glowColor: "from-secondary-500 to-primary-500",
      parallaxY: [-20, 20]
    }
  ];

  return (
    <section id="use-cases" className="parallax-section py-24 bg-content2/50 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 opacity-5 z-0">
        <Parallax translateY={[-20, 20]} className="absolute top-[10%] w-full h-[1px] opacity-30">
          <div className="w-full h-full bg-gradient-to-r from-transparent via-primary-500 to-transparent"></div>
        </Parallax>
        <Parallax translateY={[20, -20]} className="absolute top-[50%] w-full h-[1px] opacity-30">
          <div className="w-full h-full bg-gradient-to-r from-transparent via-secondary-500 to-transparent"></div>
        </Parallax>
        <Parallax translateY={[-20, 20]} className="absolute top-[90%] w-full h-[1px] opacity-30">
          <div className="w-full h-full bg-gradient-to-r from-transparent via-primary-500 to-transparent"></div>
        </Parallax>
        
        {/* 装饰性电路元素 */}
        <Parallax speed={-5} className="absolute top-[5%] right-[5%] w-32 h-32 opacity-30">
          <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <circle cx="50" cy="50" r="40" stroke="#5661f6" strokeWidth="1" fill="none" />
            <path d="M50,10 L50,90" stroke="#5661f6" strokeWidth="1" />
            <path d="M10,50 L90,50" stroke="#5661f6" strokeWidth="1" />
            <circle cx="50" cy="50" r="10" stroke="#ff5f93" strokeWidth="1" fill="none" />
          </svg>
        </Parallax>
        
        <Parallax speed={-8} className="absolute bottom-[10%] left-[8%] w-48 h-48 opacity-20">
          <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <rect x="10" y="10" width="80" height="80" stroke="#ff5f93" strokeWidth="1" fill="none" rx="5" />
            <path d="M10,50 L90,50" stroke="#ff5f93" strokeWidth="1" />
            <path d="M50,10 L50,90" stroke="#ff5f93" strokeWidth="1" />
            <circle cx="50" cy="50" r="20" stroke="#5661f6" strokeWidth="1" fill="none" />
          </svg>
        </Parallax>
      </div>
      
      <div className="container mx-auto px-6 relative z-10">
        <Parallax translateY={[-15, 15]} className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl font-bold mb-4">DIPS Pro 如何在实践中为您创造价值？</h2>
          <p className="text-default-600 text-lg">
            通过多智能体协作，DIPS Pro 能够解决各行各业的复杂业务场景，以下是部分应用案例。
          </p>
        </Parallax>

        <div className="bento-grid">
          {useCases.map((useCase, index) => (
            <Parallax 
              key={index}
              translateY={useCase.parallaxY}
              className={`bento-span-12 mb-12`}
            >
              <GlowingCard 
                glowColor={useCase.glowColor}
                className="transition duration-300 h-full"
              >
                <Card className="border-none shadow-none bg-white h-full">
                  <CardBody className="p-0">
                    <div className="grid md:grid-cols-2 gap-0 h-full">
                      <div className="p-8 flex flex-col justify-center relative">
                        <div className="absolute -top-5 -left-5 w-20 h-20 opacity-10 blur-xl rounded-full bg-gradient-primary"></div>
                        <div className="flex items-center gap-2 mb-4 relative">
                          <div className="bg-primary-100 dark:bg-primary-900/30 p-3 rounded-full">
                            <Icon icon={useCase.icon} className="text-primary text-2xl" />
                          </div>
                          <h3 className="text-2xl font-semibold">{useCase.title}</h3>
                        </div>
                        <p className="text-default-600 mb-6">{useCase.description}</p>
                        <div className="bg-success-100 dark:bg-success-900/20 p-4 rounded-lg flex items-start gap-3 border border-success-200">
                          <Icon icon="lucide:check-circle" className="text-success mt-1 text-xl" />
                          <p className="text-success-700 dark:text-success-400 font-medium">{useCase.result}</p>
                        </div>
                      </div>
                      <div className="h-full min-h-[300px] bg-default-100 dark:bg-default-50/5 relative overflow-hidden">
                        <img 
                          src={useCase.image} 
                          alt={`${useCase.title} 示例`} 
                          className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                        />
                        <div className="absolute inset-0 bg-gradient-to-r from-primary-500/10 to-secondary-500/10 opacity-50 pointer-events-none"></div>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </GlowingCard>
            </Parallax>
          ))}
        </div>
        
        <Parallax translateY={[-20, 20]} className="mt-16 text-center">
          <div className="inline-flex items-center gap-2 bg-landing-primary/10 text-landing-primary px-4 py-2 rounded-full text-sm font-medium">
            <Icon icon="lucide:zap" className="text-lg" />
            <span>更多行业应用场景，等待您的探索</span>
          </div>
        </Parallax>
      </div>
    </section>
  );
}