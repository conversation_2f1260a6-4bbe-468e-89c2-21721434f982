import React from "react";
import { cn } from "../lib/utils";
import { ReactNode } from "react";

interface RadiantTextProps {
  children: ReactNode;
  duration?: number;
  radiantWidth?: number;
  className?: string;
}

// 导出全局样式，用于在_app.tsx或全局CSS文件中引入
export const radiantTextStyles = `
  @keyframes radiant {
    0%,
    90%,
    100% {
      background-position: calc(-100% - var(--radiant-width)) 0;
    }
    30%,
    60% {
      background-position: calc(100% + var(--radiant-width)) 0;
    }
  }

  .radiant-animation {
    animation: radiant var(--radiant-anim-duration) infinite;
  }
`;

export function RadiantText({
  children,
  duration = 10,
  radiantWidth = 100,
  className,
}: RadiantTextProps) {
  const styleVar = {
    "--radiant-anim-duration": `${duration}s`,
    "--radiant-width": `${radiantWidth}px`,
  } as React.CSSProperties;

  return (
    <div className="relative inline-block">
      {/* 实际显示的文本 */}
      <h1 className={className}>
        {children}
      </h1>
      
      {/* 闪耀动画层，绝对定位覆盖在文字上方 */}
      <div 
        style={styleVar}
        className="pointer-events-none absolute inset-0 radiant-animation"
      >
        <div className="h-full w-full bg-gradient-to-r from-transparent via-white/60 to-transparent"></div>
      </div>
    </div>
  );
} 