import React from "react";
import { <PERSON>, Divider } from "@heroui/react";
import { Icon } from "@iconify/react";

export function Footer() {
  return (
    <footer className="bg-landing-bg-gray py-12">
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-12">
          <div className="col-span-2 md:col-span-1">
            <div className="flex items-center gap-2 mb-4">
              <div className="bg-gradient-primary p-1 rounded-md shadow-landing">
                <Icon icon="lucide:brain-circuit" className="text-white text-xl" />
              </div>
              <p className="font-bold text-gradient-primary text-xl">DIPS Pro</p>
            </div>
            <p className="text-landing-text-light mb-4">
              释放 AI 群体智能，驱动未来工作流
            </p>
            <div className="flex gap-4">
              <Link href="#" className="text-landing-text-light hover:text-landing-primary">
                <Icon icon="lucide:twitter" className="text-xl" />
              </Link>
              <Link href="#" className="text-landing-text-light hover:text-landing-primary">
                <Icon icon="lucide:linkedin" className="text-xl" />
              </Link>
              <Link href="#" className="text-landing-text-light hover:text-landing-primary">
                <Icon icon="lucide:github" className="text-xl" />
              </Link>
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold mb-4 text-landing-text">产品</h3>
            <ul className="space-y-2">
              <li><Link href="#" className="text-landing-text-light hover:text-landing-primary">功能</Link></li>
              <li><Link href="#" className="text-landing-text-light hover:text-landing-primary">定价</Link></li>
              <li><Link href="#" className="text-landing-text-light hover:text-landing-primary">案例研究</Link></li>
              <li><Link href="#" className="text-landing-text-light hover:text-landing-primary">文档</Link></li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-semibold mb-4 text-landing-text">资源</h3>
            <ul className="space-y-2">
              <li><Link href="#" className="text-landing-text-light hover:text-landing-primary">博客</Link></li>
              <li><Link href="#" className="text-landing-text-light hover:text-landing-primary">社区</Link></li>
              <li><Link href="#" className="text-landing-text-light hover:text-landing-primary">开发者</Link></li>
              <li><Link href="#" className="text-landing-text-light hover:text-landing-primary">支持</Link></li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-semibold mb-4 text-landing-text">公司</h3>
            <ul className="space-y-2">
              <li><Link href="#" className="text-landing-text-light hover:text-landing-primary">关于我们</Link></li>
              <li><Link href="#" className="text-landing-text-light hover:text-landing-primary">联系我们</Link></li>
              <li><Link href="#" className="text-landing-text-light hover:text-landing-primary">招贤纳士</Link></li>
              <li><Link href="#" className="text-landing-text-light hover:text-landing-primary">隐私政策</Link></li>
            </ul>
          </div>
        </div>
        
        <Divider className="my-6 bg-landing-text-light/20" />
        
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="flex flex-col items-center md:items-start">
            <p className="text-landing-text-light text-sm">© {new Date().getFullYear()} 超深咨询. 保留所有权利。</p>
            <a href="https://beian.miit.gov.cn" target="_blank" rel="noopener noreferrer" className="text-landing-text-light text-sm mt-1 hover:text-landing-primary">
              京ICP备13001538号-5
            </a>
          </div>
          <div className="flex gap-4 mt-4 md:mt-0">
            <Link href="#" className="text-landing-text-light hover:text-landing-primary text-sm">隐私政策</Link>
            <Link href="#" className="text-landing-text-light hover:text-landing-primary text-sm">服务条款</Link>
            <Link href="#" className="text-landing-text-light hover:text-landing-primary text-sm">Cookie 政策</Link>
          </div>
        </div>
      </div>
    </footer>
  );
}