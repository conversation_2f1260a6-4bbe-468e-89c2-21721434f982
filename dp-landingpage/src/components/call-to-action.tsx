import React from "react";
import { Button, Input } from "@heroui/react";
import { Icon } from "@iconify/react";
import { Parallax } from "react-scroll-parallax";

export function CallToAction() {
  return (
    <section className="parallax-section py-24 bg-background relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 opacity-10 z-0">
        <Parallax speed={-10}>
          <div className="absolute top-0 left-0 right-0 h-40 bg-gradient-to-b from-primary-500/30 to-transparent"></div>
        </Parallax>
        
        <Parallax speed={-5} className="absolute -bottom-20 -right-20 w-80 h-80 opacity-20">
          <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <circle cx="50" cy="50" r="40" fill="none" stroke="#5661f6" strokeWidth="1" strokeDasharray="10,5" />
            <circle cx="50" cy="50" r="30" fill="none" stroke="#ff5f93" strokeWidth="1" strokeDasharray="5,5" />
            <circle cx="50" cy="50" r="20" fill="none" stroke="#5661f6" strokeWidth="1" />
          </svg>
        </Parallax>
        
        <Parallax translateY={[-10, 10]} className="absolute top-[20%] left-[10%] opacity-20">
          <div className="particle w-2 h-2 bg-primary-500"></div>
        </Parallax>
        <Parallax translateY={[10, -10]} className="absolute top-[30%] right-[15%] opacity-20">
          <div className="particle w-3 h-3 bg-secondary-500"></div>
        </Parallax>
        <Parallax translateY={[-15, 15]} className="absolute bottom-[25%] left-[30%] opacity-20">
          <div className="particle w-2 h-2 bg-primary-500"></div>
        </Parallax>
      </div>
      
      <div className="container mx-auto px-6 relative z-10">
        <div className="max-w-4xl mx-auto">
          <Parallax translateY={[-20, 20]} className="relative tech-border p-8 md:p-12 rounded-2xl bg-white">
            <div className="absolute -inset-1 bg-gradient-to-br from-primary-500/20 to-secondary-500/20 rounded-2xl blur-xl"></div>
            <div className="absolute -top-8 -right-8 w-32 h-32 opacity-10">
              <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                <circle cx="50" cy="50" r="40" fill="none" stroke="currentColor" strokeWidth="1" />
                <circle cx="50" cy="50" r="30" fill="none" stroke="currentColor" strokeWidth="1" />
                <circle cx="50" cy="50" r="20" fill="none" stroke="currentColor" strokeWidth="1" />
              </svg>
            </div>
            
            <div className="text-center mb-8">
              <Parallax translateY={[-10, 10]}>
                <h2 className="text-3xl md:text-4xl font-bold mb-4">
                  准备好释放 AI 的真正潜能了吗？
                </h2>
                <p className="text-default-600 text-lg max-w-2xl mx-auto">
                  注册免费试用，体验多智能体协作带来的效率提升。企业客户可申请个性化演示。
                </p>
              </Parallax>
            </div>
            
            <Parallax translateY={[-5, 5]}>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl font-semibold mb-4 flex items-center">
                      <Icon icon="lucide:zap" className="text-primary mr-2" />
                      免费开始使用
                    </h3>
                    <div className="relative flex">
                      <Input 
                        placeholder="输入您的邮箱地址" 
                        size="lg"
                        radius="full"
                        className="pr-40"
                      />
                      <Button 
                        color="primary" 
                        size="md" 
                        radius="full"
                        className="absolute right-1 top-1"
                      >
                        注册试用
                      </Button>
                    </div>
                    <p className="text-default-500 text-sm mt-2">
                      免费获得基础版 7 天试用，无需信用卡
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-2">试用版包含：</h4>
                    <ul className="space-y-2">
                      <li className="flex items-start gap-2">
                        <Icon icon="lucide:check" className="text-success mt-1" />
                        <span>5 种基础智能体</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <Icon icon="lucide:check" className="text-success mt-1" />
                        <span>最多 3 个并行工作流</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <Icon icon="lucide:check" className="text-success mt-1" />
                        <span>基础技术支持</span>
                      </li>
                    </ul>
                  </div>
                </div>
                
                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl font-semibold mb-4 flex items-center">
                      <Icon icon="lucide:building" className="text-secondary mr-2" />
                      企业级解决方案
                    </h3>
                    <div className="p-6 border border-secondary-200 rounded-xl bg-secondary-50/50 tech-border">
                      <h4 className="font-semibold mb-2">企业版特权：</h4>
                      <ul className="space-y-2">
                        <li className="flex items-start gap-2">
                          <Icon icon="lucide:check" className="text-success mt-1" />
                          <span>完整智能体库访问权限</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <Icon icon="lucide:check" className="text-success mt-1" />
                          <span>无限工作流和自定义智能体</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <Icon icon="lucide:check" className="text-success mt-1" />
                          <span>专属客户成功经理</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <Icon icon="lucide:check" className="text-success mt-1" />
                          <span>数据安全保障和私有部署选项</span>
                        </li>
                      </ul>
                      <Button 
                        color="secondary" 
                        variant="bordered"
                        radius="full"
                        size="lg"
                        className="w-full mt-4"
                        endContent={<Icon icon="lucide:arrow-right" />}
                      >
                        预约演示
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </Parallax>
          </Parallax>
        </div>
      </div>
    </section>
  );
}