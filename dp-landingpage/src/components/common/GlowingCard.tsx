import React, { useState, useRef, useEffect } from "react";
import "../../styles/glowingCard.css";

// GlowingCard组件：为卡片添加动态发光效果
interface GlowingCardProps {
  children: React.ReactNode;
  className?: string;
  glowColor?: string;
}

function GlowingCard({ children, className = "", glowColor = "from-primary-500 to-secondary-500" }: GlowingCardProps) {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const glowTrackRef = useRef<HTMLDivElement>(null);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!cardRef.current) return;
    
    const rect = cardRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    setPosition({ x, y });
    
    // 更新发光跟踪元素位置
    if (glowTrackRef.current) {
      glowTrackRef.current.style.left = `${x - 50}px`;
      glowTrackRef.current.style.top = `${y - 50}px`;
    }
  };

  const handleMouseEnter = () => {
    setIsHovering(true);
  };

  const handleMouseLeave = () => {
    setIsHovering(false);
  };

  // 确保组件卸载时清理
  useEffect(() => {
    return () => {
      setIsHovering(false);
    };
  }, []);

  return (
    <div 
      ref={cardRef}
      className={`glowing-card ${className}`}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* 发光效果层 */}
      <div 
        className={`glow-effect bg-gradient-to-r ${glowColor}`}
        style={{
          transform: isHovering 
            ? `translate(${(position.x / 20) - 10}px, ${(position.y / 20) - 10}px)` 
            : 'translate(0, 0)',
          opacity: isHovering ? 0.7 : 0,
          transition: isHovering ? 'transform 0.1s ease' : 'transform 0.3s ease, opacity 0.3s ease',
        }}
      />
      
      {/* 鼠标跟踪发光点 */}
      <div 
        ref={glowTrackRef}
        className="glow-track"
        style={{
          opacity: isHovering ? 0.5 : 0,
        }}
      />
      
      {/* 卡片内容 */}
      <div className="card-content">
        {children}
      </div>
    </div>
  );
}

export default GlowingCard; 