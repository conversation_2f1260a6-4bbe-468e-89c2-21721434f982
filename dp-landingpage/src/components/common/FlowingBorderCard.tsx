import React, { useState } from "react";
import "../../styles/flowingBorderCard.css";

// FlowingBorderCard 组件：为卡片添加鼠标悬停时的流动渐变边框效果和上浮动画
interface FlowingBorderCardProps {
  children: React.ReactNode;
  className?: string;
  colors?: string[];
  duration?: number;
  borderWidth?: number;
  borderRadius?: number;
  blur?: number;
  bgColor?: string;
  hoverTranslateY?: number; // 新增：悬停时向上移动的像素值
  transitionDuration?: number; // 新增：过渡动画持续时间（毫秒）
}

function FlowingBorderCard({
  children,
  className = "",
  colors = [
    "#FF0000",
    "#FFA500",
    "#FFFF00", 
    "#008000", 
    "#0000FF", 
    "#4B0082", 
    "#EE82EE", 
    "#FF0000"
  ],
  duration = 2500,
  borderWidth = 2,
  borderRadius = 8,
  blur = 4,
  bgColor = "transparent",
  hoverTranslateY = 3, // 默认上浮3像素
  transitionDuration = 300 // 默认过渡时间300毫秒
}: FlowingBorderCardProps) {
  // 控制鼠标悬停状态
  const [isHovered, setIsHovered] = useState(false);

  // 处理鼠标进入事件
  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  // 处理鼠标离开事件
  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  return (
    <div 
      className={`flowing-border-card ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={{
        // 将样式变量传递给 CSS 变量，可以在 CSS 文件中使用
        '--border-width': `${borderWidth}px`,
        '--border-radius': `${borderRadius}px`,
        '--blur-size': `${blur}px`,
        '--animation-duration': `${duration}ms`,
        '--bg-color': bgColor,
        '--gradient-colors': colors.join(', '),
        '--hover-state': isHovered ? '1' : '0',
        '--transition-duration': `${transitionDuration}ms`,
        transform: isHovered ? `translateY(-${hoverTranslateY}px)` : 'translateY(0)',
        transition: `transform ${transitionDuration}ms ease-out`
      } as React.CSSProperties}
    >
      {/* 卡片内容 */}
      <div className="card-content">
        {children}
      </div>
    </div>
  );
}

export default FlowingBorderCard; 