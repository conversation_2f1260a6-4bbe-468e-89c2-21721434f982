import React, { useState, useRef, useEffect } from "react";
import FlowingBorderCard from "./FlowingBorderCard";
import "../../styles/glowingCard.css";

// EnhancedGlowingCard组件：结合流动边框和发光效果
interface EnhancedGlowingCardProps {
  children: React.ReactNode;
  className?: string;
  glowColor?: string;
  borderColors?: string[];
  borderDuration?: number;
  borderWidth?: number;
  borderRadius?: number;
  borderBlur?: number;
}

function EnhancedGlowingCard({ 
  children, 
  className = "", 
  glowColor = "from-primary-500 to-secondary-500",
  borderColors,
  borderDuration = 3000,
  borderWidth = 2,
  borderRadius = 8,
  borderBlur = 3
}: EnhancedGlowingCardProps) {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const glowTrackRef = useRef<HTMLDivElement>(null);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!cardRef.current) return;
    
    const rect = cardRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    setPosition({ x, y });
    
    // 更新发光跟踪元素位置
    if (glowTrackRef.current) {
      glowTrackRef.current.style.left = `${x - 50}px`;
      glowTrackRef.current.style.top = `${y - 50}px`;
    }
  };

  const handleMouseEnter = () => {
    setIsHovering(true);
  };

  const handleMouseLeave = () => {
    setIsHovering(false);
  };

  // 确保组件卸载时清理
  useEffect(() => {
    return () => {
      setIsHovering(false);
    };
  }, []);

  // 默认使用主题色渐变作为边框颜色
  const defaultBorderColors = [
    "#5661f6", // 紫色
    "#6c5ff7",
    "#935cf8",
    "#b85af8", 
    "#dd57f7", 
    "#ff5f93", // 粉色
    "#5661f6" // 重复第一种颜色形成闭环
  ];

  return (
    <FlowingBorderCard
      colors={borderColors || defaultBorderColors}
      duration={borderDuration}
      borderWidth={borderWidth}
      borderRadius={borderRadius}
      blur={borderBlur}
      className={className}
    >
      <div 
        ref={cardRef}
        className="glowing-card-inner"
        onMouseMove={handleMouseMove}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {/* 发光效果层 */}
        <div 
          className={`glow-effect bg-gradient-to-r ${glowColor}`}
          style={{
            transform: isHovering 
              ? `translate(${(position.x / 20) - 10}px, ${(position.y / 20) - 10}px)` 
              : 'translate(0, 0)',
            opacity: isHovering ? 0.7 : 0,
            transition: isHovering ? 'transform 0.1s ease' : 'transform 0.3s ease, opacity 0.3s ease',
          }}
        />
        
        {/* 鼠标跟踪发光点 */}
        <div 
          ref={glowTrackRef}
          className="glow-track"
          style={{
            opacity: isHovering ? 0.5 : 0,
          }}
        />
        
        {/* 卡片内容 */}
        <div className="card-content">
          {children}
        </div>
      </div>
    </FlowingBorderCard>
  );
}

export default EnhancedGlowingCard; 