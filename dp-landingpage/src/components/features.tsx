import React from "react";
import { Card, CardBody } from "@heroui/react";
import { Icon } from "@iconify/react";
import { Parallax } from "react-scroll-parallax";
import "../styles/flowingBorderCard.css";
import FlowingBorderCard from "./common/FlowingBorderCard";

export function Features() {
  const features = [
    {
      icon: "lucide:network",
      title: "智能体协同作战",
      description: "突破单点 AI 限制。DIPS Pro 的核心在于智能体间的动态协作，信息共享、任务分配、结果整合，实现 1+1 > 2 的自动化效能。",
      color: "#5661f6"
    },
    {
      icon: "lucide:library",
      title: "丰富智能体库",
      description: "涵盖文本生成、图像识别、数据分析、信息检索、流程控制等多种能力。按需选用，灵活组合，满足您千变万化的业务需求。",
      color: "#ff5f93"
    },
    {
      icon: "lucide:layout-dashboard",
      title: "可视化流程编排",
      description: "无需编码！通过直观的拖拽界面，轻松设计、部署和管理您的自动化工作流，就像绘制流程图一样简单。",
      color: "#5661f6"
    },
    {
      icon: "lucide:settings",
      title: "高度定制与扩展",
      description: "DIPS Pro 提供灵活的配置选项和 API 接口，您可以根据业务深度定制智能体行为，或将其集成到现有系统中。",
      color: "#ff5f93"
    }
  ];

  // 卡片主题色渐变过渡
  const purpleToRose = [
    "#5661f6", // 紫色
    "#7b5ff7",
    "#a25cf8", 
    "#ed57f7", 
    "#ff5f93", // 粉色
    "#7b5ff7",
    "#5661f6"  // 回到紫色，形成循环
  ];
  
  const roseToPurple = [
    "#ff5f93", // 粉色
    "#ed57f7",
    "#c85af8", 
    "#a25cf8", 
    "#7b5ff7", 
    "#5661f6", // 紫色
    "#ff5f93"  // 回到粉色，形成循环
  ];

  return (
    <section id="features" className="parallax-section py-24 bg-background/50">
      {/* 背景装饰元素 */}
      <Parallax translateY={[-20, 20]} className="absolute left-0 w-full h-32 opacity-20 z-0">
        <div className="w-full h-full bg-gradient-to-r from-primary-500/30 to-secondary-500/30 skew-y-3"></div>
      </Parallax>
      
      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        <Parallax translateY={[-15, 15]} className="text-center max-w-3xl mx-auto mb-12 md:mb-16">
          <h2 className="text-3xl font-bold mb-4">DIPS Pro 核心优势</h2>
          <p className="text-default-600 text-lg">
            多智能体协同作战平台，彻底改变您的工作方式
          </p>
        </Parallax>

        <div className="bento-grid">
          {/* 第一行 */}
          <Parallax translateY={[-10, 10]} className="bento-cell bento-span-8">
            <FlowingBorderCard 
              colors={purpleToRose}
              className="h-full"
              borderRadius={16}
              borderWidth={3}
              blur={10}
              duration={3000}
            >
              <div className="bg-white h-full rounded-[13px] p-6">
                <div className="flex flex-col sm:flex-row items-center sm:items-start gap-4 sm:gap-6">
                  <div className="bg-primary-100 dark:bg-primary-100 p-3 rounded-full w-fit">
                    <Icon icon={features[0].icon} className="text-primary text-3xl" />
                  </div>
                  <div className="text-center sm:text-left">
                    <h3 className="text-2xl font-semibold">{features[0].title}</h3>
                    <p className="text-default-600 mt-2">{features[0].description}</p>
                  </div>
                </div>
              </div>
            </FlowingBorderCard>
          </Parallax>
          
          <Parallax translateY={[-20, 20]} className="bento-cell bento-span-4">
            <FlowingBorderCard 
              colors={roseToPurple}
              className="h-full"
              borderRadius={16}
              borderWidth={3}
              blur={10}
              duration={3000}
            >
              <div className="bg-white h-full rounded-[13px] p-6">
                <div className="flex flex-col items-center">
                  <div className="bg-secondary-100 dark:bg-secondary-100 p-3 rounded-full w-fit mx-auto">
                    <Icon icon={features[1].icon} className="text-secondary text-3xl" />
                  </div>
                  <h3 className="text-xl font-semibold text-center mt-4">{features[1].title}</h3>
                  <p className="text-default-600 text-center mt-2">{features[1].description}</p>
                </div>
              </div>
            </FlowingBorderCard>
          </Parallax>
          
          {/* 第二行 */}
          <Parallax translateY={[-15, 15]} className="bento-cell bento-span-4">
            <FlowingBorderCard 
              colors={purpleToRose}
              className="h-full"
              borderRadius={16}
              borderWidth={3}
              blur={10}
              duration={3000}
            >
              <div className="bg-white h-full rounded-[13px] p-6">
                <div className="flex flex-col items-center">
                  <div className="bg-primary-100 dark:bg-primary-100 p-3 rounded-full w-fit mx-auto">
                    <Icon icon={features[2].icon} className="text-primary text-3xl" />
                  </div>
                  <h3 className="text-xl font-semibold text-center mt-4">{features[2].title}</h3>
                  <p className="text-default-600 text-center mt-2">{features[2].description}</p>
                </div>
              </div>
            </FlowingBorderCard>
          </Parallax>
          
          <Parallax translateY={[-25, 25]} className="bento-cell bento-span-8">
            <FlowingBorderCard 
              colors={roseToPurple}
              className="h-full"
              borderRadius={16}
              borderWidth={3}
              blur={10}
              duration={3000}
            >
              <div className="bg-white h-full rounded-[13px] p-6">
                <div className="flex flex-col sm:flex-row items-center sm:items-start gap-4 sm:gap-6">
                  <div className="bg-secondary-100 dark:bg-secondary-100 p-3 rounded-full w-fit">
                    <Icon icon={features[3].icon} className="text-secondary text-3xl" />
                  </div>
                  <div className="text-center sm:text-left">
                    <h3 className="text-2xl font-semibold">{features[3].title}</h3>
                    <p className="text-default-600 mt-2">{features[3].description}</p>
                  </div>
                </div>
              </div>
            </FlowingBorderCard>
          </Parallax>
        </div>
        
        <Parallax translateY={[-10, 10]} className="mt-16 md:mt-20 relative">
          <div className="absolute -inset-4 bg-gradient-to-br from-primary-500/20 to-secondary-500/20 rounded-xl blur-xl"></div>
          <div className="relative bg-white p-4 sm:p-6 rounded-xl tech-border">
            <div className="flex flex-col md:flex-row items-center justify-between">
              <div className="w-full md:w-3/5 mb-6 md:mb-0 md:pr-10 text-center md:text-left">
                <h3 className="text-xl sm:text-2xl font-bold mb-4">DIPS Pro 与传统 AI 工具不同</h3>
                <p className="text-default-600">
                  传统 AI 工具依赖单一大模型，能力固定且有限。DIPS Pro 通过多智能体协作，既能发挥每个智能体的专长，又能实现团队协同效应，真正解决复杂业务场景。
                </p>
              </div>
              <div className="w-full md:w-2/5 flex justify-center">
                <div className="relative">
                  <div className="absolute -inset-4 rounded-full bg-gradient-primary opacity-20 blur-xl"></div>
                  <div className="relative bg-gradient-primary p-4 sm:p-6 rounded-full">
                    <Icon icon="lucide:brain-circuit" className="text-white text-6xl md:text-8xl" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Parallax>
      </div>
    </section>
  );
}