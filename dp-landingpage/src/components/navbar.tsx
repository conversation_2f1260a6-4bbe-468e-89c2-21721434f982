import React, { useState, useEffect } from "react";
import { Navbar, Navbar<PERSON><PERSON>, NavbarContent, NavbarI<PERSON>, Button, Link } from "@heroui/react";
import { Icon } from "@iconify/react";

export function MainNavbar() {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <Navbar 
      maxWidth="xl" 
      className={`fixed top-0 left-0 right-0 z-50 transition-colors duration-300 backdrop-blur-md border-b border-landing-bg-gray ${
        isScrolled ? "bg-background/30" : "bg-background/80"
      }`}
    >
      <NavbarBrand>
        <div className="flex items-center gap-2">
          <div className="bg-gradient-primary p-1 rounded-md shadow-landing">
            <Icon icon="lucide:brain-circuit" className="text-white text-xl" />
          </div>
          <p className="font-bold text-gradient-primary text-xl">DIPS Pro</p>
        </div>
      </NavbarBrand>
      <NavbarContent className="hidden sm:flex gap-6" justify="center">
        <NavbarItem className="px-5 py-1">
          <Link className="text-landing-text hover:text-landing-primary" href="#features">
            功能
          </Link>
        </NavbarItem>
        <NavbarItem className="px-5 py-1">
          <Link className="text-landing-text hover:text-landing-primary" href="#use-cases">
            应用场景
          </Link>
        </NavbarItem>
        <NavbarItem className="px-5 py-1">
          <Link className="text-landing-text hover:text-landing-primary" href="#testimonials">
            用户评价
          </Link>
        </NavbarItem>
      </NavbarContent>
      <NavbarContent justify="end">
        <NavbarItem className="hidden sm:flex px-3 py-1">
          <Link href="https://dipsai.cn/app/#/login" className="text-landing-primary hover:text-landing-secondary">登录</Link>
        </NavbarItem>
        <NavbarItem className="pl-3">
          <Button className="btn-gradient-primary rounded-full" as={Link} href="#cta">
            申请抢先体验
          </Button>
        </NavbarItem>
      </NavbarContent>
    </Navbar>
  );
}