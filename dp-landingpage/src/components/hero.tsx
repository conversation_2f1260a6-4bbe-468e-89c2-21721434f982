import React, { useEffect, useRef } from "react";
import { But<PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import { Parallax } from "react-scroll-parallax";
import { RadiantText } from "./RadiantText";

// 添加技术感粒子和连接线效果
const TechParticles = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // 设置画布尺寸
    const setCanvasSize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight * 1.2;
    };
    
    setCanvasSize();
    window.addEventListener('resize', setCanvasSize);
    
    // 创建粒子
    const particlesCount = Math.min(Math.floor(window.innerWidth / 10), 100);
    const particles: {x: number, y: number, radius: number, vx: number, vy: number}[] = [];
    
    for (let i = 0; i < particlesCount; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        radius: Math.random() * 2 + 1,
        vx: (Math.random() - 0.5) * 0.5,
        vy: (Math.random() - 0.5) * 0.5
      });
    }
    
    // 动画
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // 绘制粒子
      for (let i = 0; i < particles.length; i++) {
        const p = particles[i];
        ctx.beginPath();
        ctx.arc(p.x, p.y, p.radius, 0, Math.PI * 2);
        ctx.fillStyle = 'rgba(100, 149, 237, 0.4)';
        ctx.fill();
        
        // 更新位置
        p.x += p.vx;
        p.y += p.vy;
        
        // 边界检测
        if (p.x < 0 || p.x > canvas.width) p.vx = -p.vx;
        if (p.y < 0 || p.y > canvas.height) p.vy = -p.vy;
      }
      
      // 绘制连接线
      for (let i = 0; i < particles.length; i++) {
        for (let j = i + 1; j < particles.length; j++) {
          const p1 = particles[i];
          const p2 = particles[j];
          const distance = Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));
          
          if (distance < 120) {
            ctx.beginPath();
            ctx.moveTo(p1.x, p1.y);
            ctx.lineTo(p2.x, p2.y);
            ctx.strokeStyle = `rgba(100, 149, 237, ${1 - distance / 120})`;
            ctx.lineWidth = 0.5;
            ctx.stroke();
          }
        }
      }
      
      requestAnimationFrame(animate);
    };
    
    animate();
    
    return () => {
      window.removeEventListener('resize', setCanvasSize);
    };
  }, []);
  
  return (
    <canvas 
      ref={canvasRef} 
      className="absolute inset-0 z-0 w-full h-full" 
      style={{ pointerEvents: 'none' }}
    />
  );
};

// 浮动几何形状组件
const FloatingShapes = () => {
  return (
    <div className="absolute inset-0 z-0 overflow-hidden">
      {/* 大六边形 */}
      <div className="absolute top-1/4 -right-16 opacity-20 animate-float-slow">
        <svg width="160" height="160" viewBox="0 0 160 160" fill="none" xmlns="http://www.w3.org/2000/svg">
          <polygon points="80,10 150,50 150,110 80,150 10,110 10,50" stroke="rgba(86, 97, 246, 0.8)" strokeWidth="2" fill="transparent" />
        </svg>
      </div>
      
      {/* 小圆圈 */}
      <div className="absolute bottom-1/3 -left-10 opacity-20 animate-float">
        <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="30" cy="30" r="28" stroke="rgba(255, 95, 147, 0.8)" strokeWidth="2" fill="transparent" />
        </svg>
      </div>
      
      {/* 星形 */}
      <div className="absolute top-1/3 left-1/4 opacity-15 animate-spin-slow">
        <svg width="40" height="40" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
          <polygon points="25,0 30,20 50,25 30,30 25,50 20,30 0,25 20,20" stroke="rgba(100, 149, 237, 0.8)" strokeWidth="1" fill="transparent" />
        </svg>
      </div>
      
      {/* 方形 */}
      <div className="absolute top-3/4 right-1/4 opacity-15 animate-pulse-slow">
        <svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="10" y="10" width="30" height="30" stroke="rgba(86, 97, 246, 0.8)" strokeWidth="2" fill="transparent" transform="rotate(15 25 25)" />
        </svg>
      </div>
      
      {/* 三角形 */}
      <div className="absolute top-32 left-1/2 opacity-20 animate-float-reverse">
        <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
          <polygon points="20,5 35,30 5,30" stroke="rgba(255, 95, 147, 0.8)" strokeWidth="2" fill="transparent" />
        </svg>
      </div>
    </div>
  );
};

// 网格背景
const GridBackground = () => {
  return (
    <div className="absolute inset-0 z-0 opacity-10">
      <div className="absolute inset-0" style={{
        backgroundImage: `
          linear-gradient(to right, rgba(86, 97, 246, 0.2) 1px, transparent 1px),
          linear-gradient(to bottom, rgba(86, 97, 246, 0.2) 1px, transparent 1px)
        `,
        backgroundSize: '40px 40px',
        mask: 'radial-gradient(circle at center, black 40%, transparent 80%)'
      }}></div>
    </div>
  );
};

// 电路板背景
const CircuitBackground = () => {
  return (
    <div className="absolute inset-0 z-0 overflow-hidden pointer-events-none">
      <svg width="100%" height="100%" viewBox="0 0 800 800" xmlns="http://www.w3.org/2000/svg" className="opacity-5">
        <g fill="none" stroke="rgba(86, 97, 246, 0.7)" strokeWidth="1.5">
          {/* 水平线条 */}
          <line x1="0" y1="200" x2="800" y2="200" />
          <line x1="0" y1="400" x2="800" y2="400" />
          <line x1="0" y1="600" x2="800" y2="600" />
          
          {/* 垂直线条 */}
          <line x1="200" y1="0" x2="200" y2="800" />
          <line x1="400" y1="0" x2="400" y2="800" />
          <line x1="600" y1="0" x2="600" y2="800" />
          
          {/* 电路板节点 - 顶部横线 */}
          <circle cx="200" cy="200" r="8" fill="rgba(86, 97, 246, 0.2)" />
          <circle cx="400" cy="200" r="5" fill="rgba(86, 97, 246, 0.2)" />
          <circle cx="600" cy="200" r="10" fill="rgba(86, 97, 246, 0.2)" />
          
          {/* 电路板节点 - 中间横线 */}
          <circle cx="200" cy="400" r="6" fill="rgba(255, 95, 147, 0.2)" />
          <circle cx="400" cy="400" r="12" fill="rgba(86, 97, 246, 0.2)" />
          <circle cx="600" cy="400" r="7" fill="rgba(255, 95, 147, 0.2)" />
          
          {/* 电路板节点 - 底部横线 */}
          <circle cx="200" cy="600" r="9" fill="rgba(86, 97, 246, 0.2)" />
          <circle cx="400" cy="600" r="5" fill="rgba(255, 95, 147, 0.2)" />
          <circle cx="600" cy="600" r="8" fill="rgba(86, 97, 246, 0.2)" />
          
          {/* 对角线连接 */}
          <path d="M200,200 L400,400 L600,600" stroke="rgba(86, 97, 246, 0.5)" strokeDasharray="5,10" />
          <path d="M600,200 L400,400 L200,600" stroke="rgba(255, 95, 147, 0.5)" strokeDasharray="5,10" />
          
          {/* 弧形连接 */}
          <path d="M200,200 Q300,250 400,200" stroke="rgba(86, 97, 246, 0.5)" />
          <path d="M400,200 Q500,250 600,200" stroke="rgba(86, 97, 246, 0.5)" />
          <path d="M200,600 Q300,550 400,600" stroke="rgba(255, 95, 147, 0.5)" />
          <path d="M400,600 Q500,550 600,600" stroke="rgba(255, 95, 147, 0.5)" />
          
          {/* 小电路元素 */}
          <rect x="295" y="195" width="10" height="10" fill="rgba(86, 97, 246, 0.3)" />
          <rect x="495" y="195" width="10" height="10" fill="rgba(86, 97, 246, 0.3)" />
          <rect x="295" y="595" width="10" height="10" fill="rgba(255, 95, 147, 0.3)" />
          <rect x="495" y="595" width="10" height="10" fill="rgba(255, 95, 147, 0.3)" />
        </g>
        
        {/* 发光点效果 */}
        <g>
          <circle cx="200" cy="200" r="4" fill="rgba(86, 97, 246, 0.8)">
            <animate attributeName="opacity" from="0.8" to="0.2" dur="3s" repeatCount="indefinite" />
          </circle>
          <circle cx="400" cy="400" r="4" fill="rgba(86, 97, 246, 0.8)">
            <animate attributeName="opacity" from="0.2" to="0.8" dur="5s" repeatCount="indefinite" />
          </circle>
          <circle cx="600" cy="600" r="4" fill="rgba(86, 97, 246, 0.8)">
            <animate attributeName="opacity" from="0.5" to="1" dur="4s" repeatCount="indefinite" />
          </circle>
          <circle cx="600" cy="200" r="4" fill="rgba(255, 95, 147, 0.8)">
            <animate attributeName="opacity" from="1" to="0.3" dur="6s" repeatCount="indefinite" />
          </circle>
          <circle cx="200" cy="600" r="4" fill="rgba(255, 95, 147, 0.8)">
            <animate attributeName="opacity" from="0.3" to="0.9" dur="7s" repeatCount="indefinite" />
          </circle>
        </g>
      </svg>
    </div>
  );
};

export function Hero() {
  return (
    <section className="relative overflow-hidden py-24 md:py-32">
      {/* Abstract neural network background */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute w-full h-full">
          <svg viewBox="0 0 1000 1000" xmlns="http://www.w3.org/2000/svg" className="w-full h-full">
            <defs>
              <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="var(--heroui-primary-500)" />
                <stop offset="100%" stopColor="var(--heroui-secondary-500)" />
              </linearGradient>
            </defs>
            {Array.from({ length: 20 }).map((_, i) => (
              <circle
                key={i}
                cx={Math.random() * 1000}
                cy={Math.random() * 1000}
                r={Math.random() * 5 + 2}
                fill="url(#grad1)"
              />
            ))}
            {Array.from({ length: 30 }).map((_, i) => (
              <line
                key={i + 100}
                x1={Math.random() * 1000}
                y1={Math.random() * 1000}
                x2={Math.random() * 1000}
                y2={Math.random() * 1000}
                stroke="url(#grad1)"
                strokeWidth="1"
              />
            ))}
          </svg>
        </div>
      </div>

      {/* 添加科技感元素 */}
      <GridBackground />
      <CircuitBackground />
      <TechParticles />
      <FloatingShapes />
      
      <div className="container mx-auto px-6 relative z-10">
        <div className="flex flex-col items-center text-center max-w-5xl mx-auto">
          <Parallax translateY={['-20px', '20px']} className="w-full">
            <div className="inline-flex items-center gap-2 bg-landing-primary/10 text-landing-primary px-4 py-1 rounded-full text-sm font-medium mb-6">
              <Icon icon="lucide:sparkles" className="text-lg" />
              <span>AI 自动化的未来已来临</span>
            </div>
          </Parallax>
          
          <Parallax translateY={['-30px', '30px']} className="w-full mb-6" scale={[0.9, 1.1]}>
            <RadiantText 
              className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight bg-gradient-to-r from-blue-400 via-secondary-400 to-accent-400 bg-clip-text text-transparent" // <-- 修改后的类
              duration={3}
              radiantWidth={200}
            >
              释放 AI 群体智能，驱动未来工作流
            </RadiantText>
          </Parallax>
          
          <Parallax translateY={['-40px', '40px']} className="w-full mb-10">
            <p className="text-xl text-landing-text-light">
              告别繁琐任务，让 DIPS Pro 的多智能体为你自动执行、协同处理，从市场营销到客户服务，全面提升效率。
            </p>
          </Parallax>
          
          <Parallax translateY={['-50px', '50px']} className="flex flex-col sm:flex-row gap-4 mb-16">
            <Button 
              size="lg" 
              className="btn-gradient-primary font-medium text-base rounded-full glow-hover"
              endContent={<Icon icon="lucide:arrow-right" />}
            >
              探索 DIPS Pro
            </Button>
            <Button 
              size="lg" 
              variant="bordered" 
              color="secondary" 
              radius="full"
              startContent={<Icon icon="lucide:play" />}
              className="font-medium text-base btn-flowing-border"
            >
              观看演示
            </Button>
          </Parallax>
          
          <div className="bento-grid w-full">
            <Parallax translateY={[20, -20]} className="bento-span-12">
              <div className="relative tech-border">
                <div className="absolute -inset-4 bg-gradient-primary rounded-xl opacity-20 blur-xl"></div>
                <div className="relative bg-content1 rounded-xl shadow-landing overflow-hidden border border-default-200 dark:border-default-100/20">
                  <img 
                    src="https://img.heroui.chat/image/dashboard?w=1200&h=600&u=dips-pro-dashboard" 
                    alt="DIPS Pro 平台界面预览" 
                    className="w-full h-auto object-cover"
                  />
                </div>
              </div>
            </Parallax>
          </div>
        </div>
      </div>
    </section>
  );
}