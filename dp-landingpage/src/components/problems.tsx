import React from "react";
import { Card, CardBody } from "@heroui/react";
import { Icon } from "@iconify/react";
import { Parallax } from "react-scroll-parallax";
import "../styles/flowingBorderCard.css";
import FlowingBorderCard from "./common/FlowingBorderCard";
import bgCircuitUrl from '@assets/bg-circuit.svg';

export function Problems() {
  const problems = [
    {
      icon: "lucide:repeat",
      title: "重复性任务耗费大量宝贵时间",
      description: "员工深陷数据整理、邮件分类、信息查找的泥潭，无法专注于真正有价值的工作。",
      color: "#5661f6"
    },
    {
      icon: "lucide:unlink",
      title: "跨部门协作流程复杂低效",
      description: "信息传递不畅，项目延期风险高，团队成员疲于应对繁琐的沟通和协调。",
      color: "#ff5f93"
    },
    {
      icon: "lucide:help-circle",
      title: "单一 AI 工具能力有限",
      description: "想用 AI 提升效率，但单一 AI 工具无法处理复杂场景，无法满足多样化需求。",
      color: "#5661f6"
    }
  ];

  // 卡片主题色渐变过渡
  const purpleToRose = [
    "#5661f6", // 紫色
    "#7b5ff7",
    "#a25cf8", 
    "#ed57f7", 
    "#ff5f93", // 粉色
    "#7b5ff7",
    "#5661f6"  // 回到紫色，形成循环
  ];
  
  const roseToPurple = [
    "#ff5f93", // 粉色
    "#ed57f7",
    "#c85af8", 
    "#a25cf8", 
    "#7b5ff7", 
    "#5661f6", // 紫色
    "#ff5f93"  // 回到粉色，形成循环
  ];

  return (
    <section className="parallax-section py-24 bg-content2/50">
      {/* 背景电路图案 */}
      <div className="absolute inset-0 opacity-5 overflow-hidden">
        <Parallax speed={-5}>
          <img src={bgCircuitUrl} alt="" className="w-full h-full object-cover" />
        </Parallax>
      </div>
      
      <div className="container mx-auto px-6 relative z-10">
        <Parallax translateY={[-15, 15]} className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl font-bold mb-4">您的团队是否正被这些问题困扰？</h2>
          <p className="text-default-600 text-lg">
            在数字化转型的今天，许多企业仍在为这些常见问题所困扰，影响效率与创新。
          </p>
        </Parallax>

        <div className="bento-grid">
          {problems.map((problem, index) => (
            <Parallax 
              key={index}
              translateY={[index % 2 === 0 ? -15 : -25, index % 2 === 0 ? 15 : 25]}
              className={`bento-span-4`}
            >
              <FlowingBorderCard 
                className="h-full"
                colors={index % 2 === 0 ? purpleToRose : roseToPurple}
                borderRadius={16}
                borderWidth={3}
                blur={10}
                duration={3000}
              >
                <div className="p-6 bg-white h-full rounded-[13px] flex flex-col items-center text-center">
                  <div className={`bg-${index % 2 === 0 ? 'primary' : 'secondary'}-100 p-4 rounded-full w-fit mb-4`}>
                    <Icon icon={problem.icon} className={`text-${index % 2 === 0 ? 'primary' : 'secondary'} text-3xl`} />
                  </div>
                  <h3 className="text-xl font-semibold">{problem.title}</h3>
                  <p className="text-default-600 mt-2">{problem.description}</p>
                </div>
              </FlowingBorderCard>
            </Parallax>
          ))}
        </div>

        <Parallax translateY={[-20, 20]} className="mt-20">
          <div className="bento-grid">
            <div className="bento-span-12">
              <div className="relative tech-border">
                <div className="absolute -inset-4 bg-gradient-to-br from-primary-500/20 to-secondary-500/20 rounded-xl blur-xl"></div>
                <Card className="border-none bg-white">
                  <CardBody className="p-8">
                    <div className="flex flex-col md:flex-row gap-8 items-center">
                      <div className="md:w-1/3 flex justify-center">
                        <div className="relative w-48 h-48">
                          <div className="absolute inset-0 rounded-full bg-gradient-primary opacity-10 blur-xl"></div>
                          <div className="relative flex items-center justify-center w-full h-full">
                            <Icon icon="lucide:brain-circuit" className="text-primary text-8xl" />
                          </div>
                        </div>
                      </div>
                      <div className="md:w-2/3">
                        <h3 className="text-2xl font-bold mb-4 text-center md:text-left">DIPS Pro，为您而来，化繁为简</h3>
                        <p className="text-default-600">
                          DIPS Pro 引入革命性的多智能体协作模式。我们不再依赖单个 AI，而是构建一个智能体"团队"，每个智能体专注于特定任务，它们无缝协作，如同经验丰富的专业团队，高效、精准地完成您设定好的复杂工作流。
                        </p>
                        <div className="mt-6 grid grid-cols-3 gap-4">
                          <div className="flex flex-col items-center">
                            <div className="text-primary text-xl font-bold">90%</div>
                            <p className="text-xs text-center text-default-500">减少重复性工作</p>
                          </div>
                          <div className="flex flex-col items-center">
                            <div className="text-secondary text-xl font-bold">80%</div>
                            <p className="text-xs text-center text-default-500">提升协作效率</p>
                          </div>
                          <div className="flex flex-col items-center">
                            <div className="text-primary text-xl font-bold">5倍</div>
                            <p className="text-xs text-center text-default-500">拓展AI能力边界</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </div>
            </div>
          </div>
        </Parallax>
      </div>
    </section>
  );
}