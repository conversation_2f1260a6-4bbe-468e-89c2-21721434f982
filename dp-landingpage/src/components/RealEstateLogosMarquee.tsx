import React from 'react';

export interface LogoItem {
  id: string; // Unique ID for key
  src: string;
  alt: string;
  name?: string; // Optional name for the logo
}

interface RealEstateLogosMarqueeProps {
  logos: LogoItem[];
  speed?: string; // e.g., "60s"
  pauseOnHover?: boolean;
  className?: string;
  logoClassName?: string;
  itemClassName?: string; // Will be used for individual item wrappers, gap controlled by --gap
  repeat?: number;
  vertical?: boolean; // New: scroll direction
  gap?: string; // New: gap between items, e.g., "1.5rem"
  gradientColor?: string; // New: color for the fade-out gradient, defaults to white
  gradientWidth?: string; // New: width of the fade-out gradient, e.g., "100px" or "10%"
}

const RealEstateLogosMarquee: React.FC<RealEstateLogosMarqueeProps> = ({
  logos,
  speed = "80s",
  pauseOnHover = true,
  className = "",
  logoClassName = "h-14 md:h-16",
  itemClassName = "flex-shrink-0", // Removed mx-6, gap is now controlled by --gap
  repeat = 4, // Default changed to 4
  vertical = false,
  gap = "2rem", // Default gap
  gradientColor = "white", // Default gradient color
  gradientWidth = "100px", // Default gradient width
}) => {
  if (!logos || logos.length === 0) {
    return (
      <div className={`py-8 text-center text-gray-500 ${className}`}>
        <p>暂无Logo可展示。</p>
      </div>
    );
  }

  const logosRow1: LogoItem[] = [];
  const logosRow2: LogoItem[] = [];
  const logosRow3: LogoItem[] = [];

  logos.forEach((logo, index) => {
    if (index % 3 === 0) {
      logosRow1.push(logo);
    } else if (index % 3 === 1) {
      logosRow2.push(logo);
    } else {
      logosRow3.push(logo);
    }
  });

  const rowsToRender = [logosRow1, logosRow2, logosRow3].filter(row => row.length > 0);

  // CSS classes for gradient overlays
  const gradientBaseClasses = "pointer-events-none absolute z-10";
  const horizontalGradientClassesStart = `${gradientBaseClasses} inset-y-0 left-0 w-1/6 h-full`;
  const horizontalGradientClassesEnd = `${gradientBaseClasses} inset-y-0 right-0 w-1/6 h-full`;
  const verticalGradientClassesStart = `${gradientBaseClasses} inset-x-0 top-0 h-1/6 w-full`;
  const verticalGradientClassesEnd = `${gradientBaseClasses} inset-x-0 bottom-0 h-1/6 w-full`;
  
  // Dynamically create gradient style
  const getGradientStyle = (direction: 'to right' | 'to left' | 'to bottom' | 'to top') => ({
    background: `linear-gradient(${direction}, ${gradientColor}, rgba(255,255,255,0))`
  });
   // Updated: Use gradientWidth prop for gradient style
  const getHorizontalGradientStyleStart = () => ({
    background: `linear-gradient(to right, ${gradientColor} 0%, rgba(255,255,255,0) 100%)`,
    width: gradientWidth,
  });
  const getHorizontalGradientStyleEnd = () => ({
    background: `linear-gradient(to left, ${gradientColor} 0%, rgba(255,255,255,0) 100%)`,
    width: gradientWidth,
  });
  const getVerticalGradientStyleStart = () => ({
    background: `linear-gradient(to bottom, ${gradientColor} 0%, rgba(255,255,255,0) 100%)`,
    height: gradientWidth,
  });
  const getVerticalGradientStyleEnd = () => ({
    background: `linear-gradient(to top, ${gradientColor} 0%, rgba(255,255,255,0) 100%)`,
    height: gradientWidth,
  });


  return (
    <div className={`relative w-full overflow-hidden ${className}`}>
      {/* Gradient Overlays */}
      {!vertical && (
        <>
          <div className={horizontalGradientClassesStart} style={getHorizontalGradientStyleStart()} />
          <div className={horizontalGradientClassesEnd} style={getHorizontalGradientStyleEnd()} />
        </>
      )}
      {vertical && (
        <>
          <div className={verticalGradientClassesStart} style={getVerticalGradientStyleStart()} />
          <div className={verticalGradientClassesEnd} style={getVerticalGradientStyleEnd()} />
        </>
      )}

      {rowsToRender.map((rowLogos, rowIndex) => {
        // Second row (index 1) scrolls in reverse by default if horizontal
        const reverseDirection = !vertical && rowIndex === 1; 
        const animationClass = vertical ? 'animate-marquee-vertical' : 'animate-marquee';
        const flexDirectionClass = vertical ? 'flex-col' : 'flex-row';

        return (
          rowLogos.length > 0 && (
            <div
              key={`marquee-row-${rowIndex}`}
              className={`group flex w-full overflow-hidden py-2 ${flexDirectionClass}`}
              style={{ '--duration': speed, '--gap': gap, gap: `var(--gap)` } as React.CSSProperties}
            >
              {Array.from({ length: repeat }).map((_, i) => (
                <div
                  key={`marquee-repetition-${rowIndex}-${i}`}
                  className={`flex flex-shrink-0 items-center justify-around ${animationClass} ${flexDirectionClass} ${pauseOnHover ? 'group-hover:[animation-play-state:paused]' : ''}`}
                  style={{ 
                    gap: `var(--gap)`, // Apply gap to the inner flex container
                    animationDirection: reverseDirection ? 'reverse' : 'normal'
                  }}
                >
                  {rowLogos.map((logo) => (
                    <div key={`${logo.id}-row-${rowIndex}-repetition-${i}`} className={`flex items-center justify-center p-6 bg-white rounded-lg shadow-md ${itemClassName}`}>
                      <img
                        src={logo.src}
                        alt={logo.alt}
                        title={logo.name || logo.alt}
                        className={`max-w-full max-h-full object-contain ${logoClassName}`}
                      />
                    </div>
                  ))}
                </div>
              ))}
            </div>
          )
        );
      })}
    </div>
  );
};

export { RealEstateLogosMarquee }; 