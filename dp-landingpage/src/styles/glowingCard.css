/* GlowingCard组件样式 */
.glowing-card {
  position: relative;
  border-radius: 0.75rem;
  transform-style: preserve-3d;
  perspective: 1000px;
  transition: transform 0.3s ease;
  width: 100%;
  height: 100%;
}

.glowing-card:hover {
  transform: translateY(-8px);
}

.glowing-card:hover .glow-effect {
  opacity: 0.75;
  animation: glow-pulse 2s infinite alternate;
}

.glow-effect {
  position: absolute;
  inset: -2px;
  border-radius: 0.85rem;
  opacity: 0;
  z-index: 0;
  transition: opacity 0.3s ease, filter 0.3s ease;
  pointer-events: none;
}

.card-content {
  position: relative;
  z-index: 1;
  background-color: #ffffff;
  border-radius: 0.75rem;
  overflow: hidden;
  transform: translateZ(0);
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
  box-shadow: 0 4px 10px -3px rgba(0, 0, 0, 0.05);
  width: 100%;
  height: 100%;
}

.glowing-card:hover .card-content {
  box-shadow: 0 8px 20px -5px rgba(0, 0, 0, 0.08), 0 8px 8px -6px rgba(0, 0, 0, 0.03);
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .card-content {
    background-color: #ffffff;
  }

  .glowing-card:hover .card-content {
    box-shadow: 0 8px 20px -5px rgba(0, 0, 0, 0.08), 0 8px 8px -6px rgba(0, 0, 0, 0.03);
  }

  .glow-effect {
    filter: blur(15px);
  }
}

@keyframes glow-pulse {
  0% {
    filter: blur(12px);
    opacity: 0.5;
  }
  50% {
    filter: blur(15px);
    opacity: 0.7;
  }
  100% {
    filter: blur(18px);
    opacity: 0.5;
  }
}

/* 鼠标跟踪发光效果增强 */
.glowing-card .glow-track {
  position: absolute;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 70%);
  border-radius: 50%;
  opacity: 0;
  pointer-events: none;
  mix-blend-mode: overlay;
  z-index: 2;
  transition: opacity 0.2s ease;
}

.glowing-card:hover .glow-track {
  opacity: 0.5;
}

/* 超大屏幕设备 */
@media (min-width: 1281px) {
  .glowing-card:hover {
    transform: translateY(-8px);
  }
  
  .glow-effect {
    filter: blur(15px);
  }
}

/* 大屏幕设备 */
@media (min-width: 1025px) and (max-width: 1280px) {
  .glowing-card:hover {
    transform: translateY(-6px);
  }
  
  .glow-effect {
    filter: blur(14px);
  }
}

/* 中等屏幕设备 */
@media (min-width: 769px) and (max-width: 1024px) {
  .glowing-card:hover {
    transform: translateY(-5px);
  }
  
  .glow-effect {
    filter: blur(12px);
  }
}

/* 平板设备 */
@media (min-width: 641px) and (max-width: 768px) {
  .glowing-card {
    margin-bottom: 0;
  }
  
  .glowing-card:hover {
    transform: translateY(-4px);
  }
  
  .glow-effect {
    filter: blur(10px);
  }
  
  .card-content > div {
    padding: 1rem;
  }
}

/* 手机设备 */
@media (max-width: 640px) {
  .glowing-card {
    margin-bottom: 0;
    width: 100%;
  }
  
  .glowing-card:hover {
    transform: translateY(-2px);
  }
  
  .glow-effect {
    opacity: 0.2;
    filter: blur(8px);
  }
  
  /* 在触摸设备上始终显示一些发光效果 */
  .glowing-card .glow-effect {
    opacity: 0.2;
  }
  
  .card-content > div {
    padding: 0.75rem;
  }
}

/* 触摸设备适配 */
@media (hover: none) {
  .glowing-card .glow-effect {
    opacity: 0.3;
    filter: blur(12px);
  }

  .glowing-card .card-content {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.03), 0 2px 4px -1px rgba(0, 0, 0, 0.02);
  }
  
  .glowing-card:hover {
    transform: none;
  }
}

/* EnhancedGlowingCard 附加样式 */
.glowing-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: inherit;
  overflow: hidden;
}

/* 确保 EnhancedGlowingCard 内部的内容层正确定位 */
.glowing-card-inner .card-content {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  background-color: inherit;
}

/* 确保内部发光效果与外部流动边框不冲突 */
.glowing-card-inner .glow-effect,
.glowing-card-inner .glow-track {
  z-index: 1;
}

@media (max-width: 768px) {
  /* 在移动设备上降级边框动画效果，保持简单 */
  .flowing-border-card::before {
    animation-duration: 4000ms; /* 降低动画速度，减少性能消耗 */
    filter: blur(2px); /* 减少模糊效果，提高性能 */
  }
} 