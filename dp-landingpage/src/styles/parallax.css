/* 视差滚动样式 */
.parallax-section {
  position: relative;
  overflow: hidden;
}

.parallax-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

/* Bento Grid 样式 */
.bento-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 1.5rem;
  position: relative;
}

.bento-cell {
  position: relative;
  margin-bottom: 1rem;
}

.bento-item {
  position: relative;
  overflow: hidden;
  border-radius: 1rem;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 10px 30px -5px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.bento-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px -5px rgba(0, 0, 0, 0.1);
}

/* 根据大小定义的Bento项目跨度 */
.bento-span-1 { grid-column: span 1; }
.bento-span-2 { grid-column: span 2; }
.bento-span-3 { grid-column: span 3; }
.bento-span-4 { grid-column: span 4; }
.bento-span-5 { grid-column: span 5; }
.bento-span-6 { grid-column: span 6; }
.bento-span-7 { grid-column: span 7; }
.bento-span-8 { grid-column: span 8; }
.bento-span-9 { grid-column: span 9; }
.bento-span-10 { grid-column: span 10; }
.bento-span-11 { grid-column: span 11; }
.bento-span-12 { grid-column: span 12; }

/* 高度变体 */
.bento-h-sm { height: 200px; }
.bento-h-md { height: 300px; }
.bento-h-lg { height: 400px; }
.bento-h-xl { height: 500px; }

/* 响应式调整 */
@media (max-width: 1280px) {
  .bento-grid {
    gap: 1.25rem;
  }
}

@media (max-width: 1024px) {
  .bento-grid {
    gap: 1.25rem;
    grid-template-columns: repeat(12, 1fr);
  }
  
  .bento-cell {
    margin-bottom: 1.25rem;
  }
}

@media (max-width: 768px) {
  .bento-grid {
    gap: 1rem;
    grid-template-columns: repeat(12, 1fr);
  }
  
  .bento-span-8 {
    grid-column: span 12;
  }
  
  .bento-span-4 {
    grid-column: span 6;
  }
  
  .bento-cell {
    margin-bottom: 1rem;
  }
  
  .bento-h-sm { height: 150px; }
  .bento-h-md { height: 250px; }
  .bento-h-lg { height: 350px; }
  .bento-h-xl { height: 400px; }
}

@media (max-width: 640px) {
  .bento-grid {
    gap: 1rem;
    grid-template-columns: repeat(1, 1fr);
  }
  
  .bento-span-1, .bento-span-2, .bento-span-3, .bento-span-4, .bento-span-5, .bento-span-6,
  .bento-span-7, .bento-span-8, .bento-span-9, .bento-span-10, .bento-span-11, .bento-span-12 {
    grid-column: span 1;
  }
  
  .bento-cell {
    margin-bottom: 1rem;
  }
  
  /* 调整卡片在手机上的样式 */
  .bento-grid .card {
    height: auto;
    min-height: 180px;
  }
}

/* 浮动装饰元素 */
.floating-element {
  position: absolute;
  opacity: 0.7;
  transition: transform 0.3s ease;
  pointer-events: none;
  z-index: 1;
}

/* 鼠标悬停时激活的发光效果 */
.glow-hover {
  position: relative;
  transition: transform 0.3s ease;
}

.glow-hover::before {
  content: '';
  position: absolute;
  inset: -5px;
  background: linear-gradient(135deg, #5661f6, #ff5f93);
  border-radius: inherit;
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
  filter: blur(15px);
}

.glow-hover:hover::before {
  opacity: 0.7;
}

.glow-hover:hover {
  transform: translateY(-5px) scale(1.02);
}

/* 科技感边框 */
.tech-border {
  position: relative;
  border-radius: 1rem;
}

.tech-border::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 2px;
  background: linear-gradient(135deg, #5661f6, #ff5f93);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
} 