/* 基础卡片样式 */
.flowing-border-card {
  --border-width: 2px;
  --border-radius: 8px;
  --blur-size: 4px;
  --animation-duration: 2500ms;
  --bg-color: transparent;
  --gradient-colors: #FF0000, #FFA500, #FFFF00, #008000, #0000FF, #4B0082, #EE82EE, #FF0000;
  --hover-state: 0;
  --transition-duration: 300ms;

  display: flex;
  position: relative;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: var(--border-width);
  border-radius: var(--border-radius);
  overflow: hidden;
  will-change: transform;
  transition: transform var(--transition-duration) ease-out;
}

/* 流动边框的伪元素 */
.flowing-border-card::before {
  content: '';
  position: absolute;
  inset: -200%;
  background: conic-gradient(var(--gradient-colors));
  opacity: calc(var(--hover-state) * 1);
  transition: opacity 0.3s ease;
  animation: rotate-rainbow var(--animation-duration) linear infinite;
  animation-play-state: paused;
  filter: blur(var(--blur-size));
  z-index: 0;
}

/* 鼠标悬停时启动动画 */
.flowing-border-card:hover::before {
  animation-play-state: running;
  opacity: 1;
}

/* 卡片内容样式 */
.flowing-border-card .card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  border-radius: calc(var(--border-radius) - var(--border-width));
  background-color: var(--bg-color);
  position: relative;
  z-index: 1;
  overflow: hidden;
  border: 1px solid rgba(200, 200, 200, 0.3);
}

/* 定义旋转动画 */
@keyframes rotate-rainbow {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .flowing-border-card .card-content {
    /* background-color: #121212; */
    border-color: rgba(120, 120, 120, 0.2);
  }
} 