/* 通用平滑滚动样式 */
html {
  scroll-behavior: smooth;
}

body {
  overflow-x: hidden;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 基础滚动布局 */
.smooth-scroll {
  scroll-behavior: smooth;
  overflow-y: visible;
  width: 100%;
  height: auto;
  position: relative;
}

.scroll-section {
  scroll-snap-align: start;
  scroll-snap-stop: normal;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  min-height: 100vh;
  position: relative;
  width: 100%;
}

/* 内容过渡动画 */
.section-content {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.165, 0.84, 0.44, 1);
  width: 100%;
}

.section-content.active {
  opacity: 1;
  transform: translateY(0);
}

/* 自定义导航点样式 */
.fp-nav .nav-dot .bg-pink-500 {
  background-color: #ff9cb8;
}

.fp-nav .nav-dot .bg-indigo-500 {
  background-color: #8f97fa;
}

.shadow-glow {
  box-shadow: 0 0 15px rgba(255, 156, 184, 0.7);
}

/* 页脚样式 */
.footer-container {
  width: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  position: relative;
  z-index: 10;
  border-top: 1px solid rgba(86, 97, 246, 0.1);
}

/* 滚动提示动画 */
.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  width: 34px;
  height: 55px;
  border: 2px solid rgba(86, 97, 246, 0.6);
  border-radius: 17px;
  display: flex;
  justify-content: center;
  opacity: 1;
  transition: opacity 0.5s ease;
  z-index: 100;
  cursor: pointer;
}

.scroll-indicator:hover {
  opacity: 0.8;
}

.scroll-indicator::after {
  content: '';
  width: 6px;
  height: 10px;
  border-radius: 3px;
  background-color: #5661f6;
  margin-top: 8px;
  animation: scrollAnimation 2s infinite;
}

@keyframes scrollAnimation {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  50% {
    opacity: 0.5;
    transform: translateY(15px);
  }
  100% {
    opacity: 0;
    transform: translateY(25px);
  }
}

/* Marquee Component Styles */
.animate-marquee {
  animation: marquee var(--duration) linear infinite;
}

@keyframes marquee {
  from {
    transform: translateX(0%);
  }
  to {
    /* Each animated block translates by its own width plus the defined --gap */
    transform: translateX(calc(-100% - var(--gap)));
  }
}

@keyframes marquee-vertical {
  from { transform: translateY(0); }
  to { transform: translateY(calc(-100% - var(--gap))); }
}

.animate-marquee-vertical {
  animation: marquee-vertical var(--duration) linear infinite;
  /* animation-direction will be set via inline style in the component */
} 