# 图片代理缓存zone定义
proxy_cache_path /tmp/nginx-image-cache levels=1:2 keys_zone=image_cache:10m max_size=1g inactive=24h use_temp_path=off;

# WebSocket连接升级映射
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

server {
    listen 80;
    listen [::]:80;
    server_name dipsai.cn;

    # 启用 gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: http: https: *.fg-china.cn; connect-src 'self' http: https:; font-src 'self' data:; media-src 'self' data: blob: http: https: *.fg-china.cn; object-src 'none'; frame-src 'self'; base-uri 'self'; form-action 'self'" always;

    # === 反向代理到 dp-web-fa 应用 ===
    location /app/ {
        proxy_pass http://dp-web-fa:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # HTTP版本和连接设置
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 缓冲区设置
        proxy_buffering on;
        proxy_buffer_size 32k;
        proxy_buffers 16 32k;  # 启用内存缓存512k
        proxy_busy_buffers_size 64k;
        proxy_max_temp_file_size 1024m;  # 启用磁盘缓存
        proxy_temp_file_write_size 64k;  # 防止大文件写入截断
        
        # 请求体大小限制
        client_max_body_size 100m;
    }

    # === API 代理到后端服务 ===
    location /server/ {
        proxy_pass http://dp-server:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # HTTP版本和连接设置
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 缓冲区设置
        proxy_buffering on;
        proxy_buffer_size 32k;
        proxy_buffers 16 32k;  # 启用内存缓存512k
        proxy_busy_buffers_size 64k;
        proxy_max_temp_file_size 1024m;  # 启用磁盘缓存
        proxy_temp_file_write_size 64k;  # 防止大文件写入截断
        
        # 请求体大小限制
        client_max_body_size 100m;
    }

    # Landing Page 根路径
    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存（排除 /app/ 路径，该路径通过反向代理处理）
    location ~* ^(?!/app/).*\.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        root /usr/share/nginx/html;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # === 图片代理服务 ===
    location /api/image-proxy {
        # 域名白名单检查（只允许fg-china.cn域名）
        if ($arg_url !~ "^https?://.*\.fg-china\.cn/") {
            return 403 "Access denied: Only fg-china.cn domains are allowed";
        }
        
        # DNS解析器
        resolver ******* ******* valid=30s;
        
        # 代理设置
        proxy_pass $arg_url;
        proxy_set_header Host $proxy_host;
        proxy_set_header User-Agent "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";
        proxy_set_header Accept "image/*,*/*;q=0.8";
        proxy_set_header Accept-Encoding "gzip, deflate";
        
        # SSL设置（绕过证书验证）
        proxy_ssl_verify off;
        proxy_ssl_server_name on;
        proxy_ssl_session_reuse off;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓存设置
        proxy_cache image_cache;
        proxy_cache_valid 200 302 24h;
        proxy_cache_valid 404 1h;
        proxy_cache_key "$scheme$proxy_host$request_uri";
        proxy_cache_bypass $http_cache_control;
        
        # 添加缓存状态头
        add_header X-Cache-Status $upstream_cache_status;
        add_header X-Proxy-Cache "nginx-image-proxy";
        
        # 隐藏原始服务器头
        proxy_hide_header "Server";
        proxy_hide_header "X-Powered-By";
        
        # 错误处理
        proxy_intercept_errors on;
        error_page 404 = @image_not_found;
        error_page 500 502 503 504 = @image_error;
    }
    
    # 图片未找到处理
    location @image_not_found {
        add_header Content-Type "text/plain";
        return 404 "Image not found";
    }
    
    # 图片错误处理
    location @image_error {
        add_header Content-Type "text/plain";  
        return 500 "Image proxy error";
    }

    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}

server {
    # 监听 HTTPS 端口
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name dipsai.cn;

    # === SSL/TLS 配置 ===
    # 使用容器内的证书文件
    ssl_certificate /etc/nginx/certs/dipsai.cn.pem;
    ssl_certificate_key /etc/nginx/certs/dipsai.cn.key;

    # 推荐的 SSL/TLS 安全设置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 1d;
    ssl_session_tickets off;

    # 其他配置与 HTTP 相同
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: http: https: *.fg-china.cn; connect-src 'self' http: https:; font-src 'self' data:; media-src 'self' data: blob: http: https: *.fg-china.cn; object-src 'none'; frame-src 'self'; base-uri 'self'; form-action 'self'" always;

    # === 基础根目录 ===
    root /usr/share/nginx/html;
    index index.html index.htm;

    # Let's Encrypt ACME challenge
    location /.well-known/acme-challenge/ {
        root /usr/share/nginx/html;
    }

    # === 反向代理到 dp-web-fa 应用 ===
    location /app/ {
        proxy_pass http://dp-web-fa:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # HTTP版本和连接设置
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 缓冲区设置
        proxy_buffering on;
        proxy_buffer_size 32k;
        proxy_buffers 16 32k;  # 启用内存缓存512k
        proxy_busy_buffers_size 64k;
        proxy_max_temp_file_size 1024m;  # 启用磁盘缓存
        proxy_temp_file_write_size 64k;  # 防止大文件写入截断
        
        # 请求体大小限制
        client_max_body_size 100m;
    }

    # === API 代理到后端服务 ===
    location /server/ {
        proxy_pass http://dp-server:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # HTTP版本和连接设置
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 缓冲区设置
        proxy_buffering on;
        proxy_buffer_size 32k;
        proxy_buffers 16 32k;  # 启用内存缓存512k
        proxy_busy_buffers_size 64k;
        proxy_max_temp_file_size 1024m;  # 启用磁盘缓存
        proxy_temp_file_write_size 64k;  # 防止大文件写入截断
        
        # 请求体大小限制
        client_max_body_size 100m;
    }

    # === Landing Page 根路径 ===
    location / {
        try_files $uri $uri/ /index.html; # SPA fallback for the landing page
    }

    # === 静态资源缓存（排除 /app/ 路径，该路径通过反向代理处理） ===
    location ~* ^(?!/app/).*\.(?:css|js|jpg|jpeg|gif|png|ico|woff|woff2|ttf|eot|svg)$ {
        root /usr/share/nginx/html;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # === 图片代理服务 ===
    location /api/image-proxy {
        # 域名白名单检查（只允许fg-china.cn域名）
        if ($arg_url !~ "^https?://.*\.fg-china\.cn/") {
            return 403 "Access denied: Only fg-china.cn domains are allowed";
        }
        
        # DNS解析器
        resolver ******* ******* valid=30s;
        
        # 代理设置
        proxy_pass $arg_url;
        proxy_set_header Host $proxy_host;
        proxy_set_header User-Agent "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";
        proxy_set_header Accept "image/*,*/*;q=0.8";
        proxy_set_header Accept-Encoding "gzip, deflate";
        
        # SSL设置（绕过证书验证）
        proxy_ssl_verify off;
        proxy_ssl_server_name on;
        proxy_ssl_session_reuse off;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓存设置
        proxy_cache image_cache;
        proxy_cache_valid 200 302 24h;
        proxy_cache_valid 404 1h;
        proxy_cache_key "$scheme$proxy_host$request_uri";
        proxy_cache_bypass $http_cache_control;
        
        # 添加缓存状态头
        add_header X-Cache-Status $upstream_cache_status;
        add_header X-Proxy-Cache "nginx-image-proxy";
        
        # 隐藏原始服务器头
        proxy_hide_header "Server";
        proxy_hide_header "X-Powered-By";
        
        # 错误处理
        proxy_intercept_errors on;
        error_page 404 = @image_not_found;
        error_page 500 502 503 504 = @image_error;
    }
    
    # 图片未找到处理
    location @image_not_found {
        add_header Content-Type "text/plain";
        return 404 "Image not found";
    }
    
    # 图片错误处理
    location @image_error {
        add_header Content-Type "text/plain";  
        return 500 "Image proxy error";
    }

    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # === 隐藏文件 ===
    location ~ /\. {
        deny all;
    }
}