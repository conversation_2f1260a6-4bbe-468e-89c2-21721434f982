import { heroui } from "@heroui/react";

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Noto Sans SC', 'sans-serif'],
      },
      colors: {
        'landing-primary': '#5661f6',  // 亮紫色
        'landing-secondary': '#9061f9', // 丰富紫色
        'landing-accent': '#ff5f93',    // 醒目粉色
        'landing-text': '#1a1b32',      // 深蓝黑色
        'landing-text-light': '#5b6084', // 蓝灰色
        'landing-bg-light': '#ffffff',
        'landing-bg-gray': '#f5f7ff',   // 偏紫蓝色的浅灰背景
      },
      backgroundImage: {
        'gradient-primary': 'linear-gradient(135deg, #5661f6, #9061f9)',
        'gradient-accent': 'linear-gradient(135deg, #9061f9, #ff5f93)',
      },
      boxShadow: {
        'landing': '0 10px 30px rgba(86, 97, 246, 0.15)',
      }
    },
  },
  darkMode: "class",
  plugins: [
    heroui({
      themes: {
        light: {
          colors: {
            background: "#FFFFFF",
            foreground: "#1a1b32", // 更新为landing的文本颜色
            primary: {
              50: "#F0F1FF",
              100: "#E1E3FE",
              200: "#C3C8FD",
              300: "#9FA8FC",
              400: "#7A84F9",
              500: "#5661F6", // 更新为landing的主色
              600: "#3C47DE",
              700: "#2E37B2",
              800: "#24298F",
              900: "#1D2176",
              DEFAULT: "#5661F6", // 更新为landing的主色
              foreground: "#FFFFFF"
            },
            secondary: {
              50: "#F6F0FF",
              100: "#EDDFFF",
              200: "#DABBFF",
              300: "#C293FF",
              400: "#A97CFB",
              500: "#9061F9", // 更新为landing的次色
              600: "#7845E9",
              700: "#6234C5",
              800: "#4E2A9E",
              900: "#402382",
              DEFAULT: "#9061F9", // 更新为landing的次色
              foreground: "#FFFFFF"
            },
            accent: {
              50: "#FFECF2",
              100: "#FFD9E6",
              200: "#FFB3CC",
              300: "#FF8CB3",
              400: "#FF7699",
              500: "#FF5F93", // 添加landing的强调色
              600: "#FF3366",
              700: "#E62D5B",
              800: "#CC2751",
              900: "#B32147",
              DEFAULT: "#FF5F93", // 添加landing的强调色
              foreground: "#FFFFFF"
            }
          }
        },
        dark: {
          colors: {
            background: "#0C0F12",
            foreground: "#ECEDEE",
            primary: {
              50: "#1D2176",
              100: "#24298F",
              200: "#2E37B2",
              300: "#3C47DE",
              400: "#5661F6", // 更新为landing的主色
              500: "#7A84F9",
              600: "#9FA8FC",
              700: "#C3C8FD",
              800: "#E1E3FE",
              900: "#F0F1FF",
              DEFAULT: "#7A84F9", // 亮一点的紫色在暗色主题下
              foreground: "#0C0F12"
            },
            secondary: {
              50: "#402382",
              100: "#4E2A9E",
              200: "#6234C5",
              300: "#7845E9",
              400: "#9061F9", // 更新为landing的次色
              500: "#A97CFB",
              600: "#C293FF",
              700: "#DABBFF",
              800: "#EDDFFF",
              900: "#F6F0FF",
              DEFAULT: "#A97CFB", // 亮一点的紫色在暗色主题下
              foreground: "#0C0F12"
            },
            accent: {
              50: "#B32147",
              100: "#CC2751",
              200: "#E62D5B",
              300: "#FF3366",
              400: "#FF5F93", // 添加landing的强调色
              500: "#FF7699",
              600: "#FF8CB3",
              700: "#FFB3CC",
              800: "#FFD9E6",
              900: "#FFECF2",
              DEFAULT: "#FF7699", // 亮一点的粉色在暗色主题下
              foreground: "#0C0F12"
            }
          }
        }
      }
    })
  ],
};
