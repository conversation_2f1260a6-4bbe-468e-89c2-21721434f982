import type { RecursiveRequired, Settings } from '#/global'
import settingsDefault from '@/settings.default'
import { merge } from '@/utils/object'
import { cloneDeep } from 'es-toolkit'

const globalSettings: Settings.all = {
  app: {
    lightTheme: 'violet',
    darkTheme: 'violet',
    enableDynamicTitle: true,
  },
  menu: {
    mode: 'only-side',
  },
  topbar: {
    mode: 'fixed',
    switchTabbarAndToolbar: true,
  },
  tabbar: {
    enable: true,
    style: 'fashion',
    enableMemory: true,
    enableHotkeys: true,
  },
  toolbar: {
    favorites: true,
    notification: true,
    fullscreen: true,
    pageReload: true,
  },
  breadcrumb: {
    style: 'modern',
  },
  copyright: {
    enable: true,
    dates: '2025',
    company: 'Beyond Deep',
    website: 'https://dipsai.cn/',
    beian: '京ICP备13001538号-5',
  },
}

export default merge(globalSettings, cloneDeep(settingsDefault)) as RecursiveRequired<Settings.all>
