import type { Route } from '#/global'
import type { RouteRecordRaw } from 'vue-router'
import { $t } from '@/locales'
import pinia from '@/store'
import useSettingsStore from '@/store/modules/settings'
import generatedRoutes from 'virtual:generated-pages'
import { setupLayouts } from 'virtual:meta-layouts'
import MenuBillingAppeal from './modules/billing/menu.billing.appeal'
import MenuBillingOverview from './modules/billing/menu.billing.overview'
import MenuBillingRecharge from './modules/billing/menu.billing.recharge'
import MenuBillingUsageHistory from './modules/billing/menu.billing.usage_history'
import MenuChat from './modules/chat/menu.chat'
import MenuChatAgent from './modules/chat/menu.chat.agent'
import MenuChatHistory from './modules/chat/menu.chat.history'
import MenuChatTemplate from './modules/chat/menu.chat.prompt.template'
import MenuSysMenuResource from './modules/sys/menu.sys.menu_resource'
import MenuSysPackageDefinition from './modules/sys/menu.sys.package_definition'
import MenuSysRole from './modules/sys/menu.sys.role'
import MenuSysTenant from './modules/sys/menu.sys.tenant'
import MenuSysUser from './modules/sys/menu.sys.user'

// 固定路由（默认路由）
const constantRoutes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login.vue'),
    meta: {
      whiteList: true,
      title: $t('app.route.login'),
    },
  },
  {
    path: '/:all(.*)*',
    name: 'notFound',
    component: () => import('@/views/[...all].vue'),
    meta: {
      title: '找不到页面',
    },
  },
]

// 系统路由
const systemRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('@/layouts/index.vue'),
    meta: {
      breadcrumb: false,
    },
    children: [
      {
        path: '',
        component: () => import('@/views/index.vue'),
        meta: {
          title: $t(useSettingsStore(pinia).settings.home.title),
          icon: 'i-ant-design:home-twotone',
          breadcrumb: false,
        },
      },
      {
        path: 'reload',
        name: 'reload',
        component: () => import('@/views/reload.vue'),
        meta: {
          title: $t('app.route.reload'),
          breadcrumb: false,
        },
      },
    ],
  },
]

// 动态路由（异步路由、导航栏路由）
const asyncRoutes: Route.recordMainRaw[] = [
  {
    meta: {
      title: '助理',
      icon: 'arcticons:ai-chat',
      // icon: 'fluent:chat-sparkle-16-filled',
      // icon: 'fluent-mdl2:chat-bot',
    },
    children: [
      MenuChat,
      MenuChatAgent,
      MenuChatHistory,
      MenuChatTemplate,
    ],
  },
  {
    meta: {
      title: '费用',
      icon: 'material-symbols:account-balance-wallet-outline',
    },
    children: [
      MenuBillingOverview,
      MenuBillingRecharge,
      MenuBillingUsageHistory,
      MenuBillingAppeal,
    ],
  },
  {
    meta: {
      title: '系统',
      icon: 'ph:gear-light',
    },
    children: [
      MenuSysTenant,
      MenuSysPackageDefinition,
      MenuSysUser,
      MenuSysRole,
      MenuSysMenuResource,
    ],
  },
]

const constantRoutesByFilesystem = generatedRoutes.filter((item) => {
  return item.meta?.enabled !== false && item.meta?.constant === true
})

const asyncRoutesByFilesystem = setupLayouts(generatedRoutes.filter((item) => {
  return item.meta?.enabled !== false && item.meta?.constant !== true && item.meta?.layout !== false
}))

export {
  asyncRoutes,
  asyncRoutesByFilesystem,
  constantRoutes,
  constantRoutesByFilesystem,
  systemRoutes,
}
