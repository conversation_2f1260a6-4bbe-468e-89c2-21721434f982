<script setup lang="ts">
defineProps<{
  isNewChat: boolean
  enableLeftSide: boolean
  enableRightSide: boolean
}>()

const emit = defineEmits<{
  (e: 'createNewChat'): void
  (e: 'toggleLeftSide'): void
  (e: 'toggleRightSide'): void
}>()
</script>

<template>
  <div class="mb-2 flex justify-end">
    <FaTooltip text="新建会话">
      <FaButton
        variant="ghost"
        class="group ml-2"
        @click="emit('createNewChat')"
      >
        <FaIcon
          name="tdesign:chat-bubble-add"
          class="size-6 transition-all"
          :class="isNewChat ? 'opacity-10' : 'opacity-100 text-primary'"
        />
      </FaButton>
    </FaTooltip>

    <FaTooltip text="会话历史">
      <FaButton
        variant="ghost"
        class="group ml-2"
        @click="emit('toggleLeftSide')"
      >
        <FaIcon
          name="tdesign:chat-bubble-history"
          class="size-6 transition-all"
          :class="enableLeftSide ? 'opacity-100 text-primary' : 'opacity-10'"
        />
      </FaButton>
    </FaTooltip>

    <FaTooltip text="模板">
      <FaButton
        variant="ghost"
        class="group ml-2"
        @click="emit('toggleRightSide')"
      >
        <FaIcon
          name="octicon:repo-template-24"
          class="size-6 transition-all"
          :class="enableRightSide ? 'opacity-100 text-primary' : 'opacity-10'"
        />
      </FaButton>
    </FaTooltip>
  </div>
</template>
