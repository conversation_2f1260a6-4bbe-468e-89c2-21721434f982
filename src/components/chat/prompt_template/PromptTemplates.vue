<script setup lang="ts">
import * as promptApi from '@/api/modules/prompt_template'
import { ElMessage, ElSlider } from 'element-plus'
import { onMounted, ref, watch } from 'vue'

// 定义组件属性
const props = defineProps({
  sideWidth: {
    type: Number,
    default: 300,
  },
})

// 定义事件
const emit = defineEmits(['selectTemplate', 'widthChange'])

// 提示词模板相关状态
const templatesLoading = ref(false)
const templates = ref<any[]>([])
const rightSideWidth = ref(props.sideWidth)

// 监听宽度变化
function handleWidthChange() {
  emit('widthChange', rightSideWidth.value)
}

// 加载提示词模板
async function fetchTemplates() {
  templatesLoading.value = true
  try {
    const response = await promptApi.getPromptTemplates()
    templates.value = Array.isArray(response) ? response : (response.data || [])
  }
  catch (error) {
    console.error('获取提示词模板失败', error)
    ElMessage.error('获取提示词模板失败，请稍后重试')
    templates.value = []
  }
  finally {
    templatesLoading.value = false
  }
}

// 选择提示词模板
function handleSelectTemplate(template: any) {
  emit('selectTemplate', template)
}

// 在组件挂载时加载模板
onMounted(() => {
  fetchTemplates()
})

// 监听宽度变化
watch(rightSideWidth, (newValue) => {
  emit('widthChange', newValue)
})
</script>

<template>
  <div class="h-full overflow-auto p-4 space-y-2">
    <div class="flex items-center justify-between">
      <h2 class="text-xl font-medium">
        提示词模板
      </h2>
      <div class="flex items-center space-x-2">
        <FaPopover>
          <FaButton variant="ghost" class="group">
            <FaIcon name="ix:width" class="size-6 opacity-10 transition-opacity group-hover:opacity-100" />
          </FaButton>

          <template #panel>
            <div class="h-10 w-60 flex items-center justify-center">
              <ElSlider
                v-model="rightSideWidth"
                :step="10"
                :min="250"
                :max="550"
                @change="handleWidthChange"
              />
            </div>
          </template>
        </FaPopover>
      </div>
    </div>
    <hr>

    <!-- 模板列表 -->
    <div
      v-loading="templatesLoading"
      element-loading-text="正在加载提示词模板..."
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
      class="mt-4 min-h-[200px]"
    >
      <!-- 空状态 -->
      <div v-if="!templatesLoading && templates.length === 0" class="py-8 text-center text-gray-500">
        没有可用的提示词模板。
      </div>

      <!-- 模板列表 -->
      <div v-else-if="!templatesLoading" class="space-y-3">
        <div
          v-for="template in templates"
          :key="template.id"
          class="mt-5 cursor-pointer border-b border-gray-200 pb-5 transition-colors dark:border-gray-700 hover:bg-gray-50"
          @click="handleSelectTemplate(template)"
        >
          <h3 class="text-base font-medium">
            {{ template.name }}
          </h3>
          <p v-if="template.description" class="mt-1 text-sm text-gray-500">
            {{ template.description }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
