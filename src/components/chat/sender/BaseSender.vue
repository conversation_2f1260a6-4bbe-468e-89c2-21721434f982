<script setup lang="tsx">
import type { PropType, VNode } from 'vue'
import TokenCounter from '@/components/billing/TokenCounter.vue'
import { useBillingStore } from '@/stores/billing'
import { formatCurrency } from '@/utils/format'
import { message, Modal, Space } from 'ant-design-vue'
import { Sender } from 'ant-design-x-vue'
import { computed, ref } from 'vue'

// 定义组件 props
const props = defineProps({
  // 样式对象
  styles: {
    type: Object,
    required: true,
  },
  // 输入框的值
  inputValue: {
    type: String,
    required: true,
  },
  // 输入值变化的回调函数
  onInputChange: {
    type: Function as PropType<(value: string) => void>,
    required: true,
  },
  // 是否正在加载
  isLoading: {
    type: Boolean,
    required: true,
  },
  // 是否正在加载历史记录
  isHistoryLoading: {
    type: Boolean,
    required: true,
  },
  // 提交表单的回调函数
  onSubmit: {
    type: Function as PropType<(message: string) => void>,
    required: true,
  },
  // 输入框的占位符
  placeholder: {
    type: String,
    default: '输入消息...',
  },
  // 是否禁用提交按钮
  isSubmitDisabled: {
    type: Boolean,
    default: false,
  },
  // 自定义操作区域
  actions: {
    type: [Function, Boolean] as PropType<((...args: any[]) => VNode) | false>,
    default: undefined,
  },
  // 自定义类名
  className: {
    type: String,
    default: '',
  },
  // 通知父组件强制重新渲染的回调函数
  onForceRerender: {
    type: Function as PropType<() => void>,
    default: undefined,
  },
  // 是否显示Token计数器
  showTokenCounter: {
    type: Boolean,
    default: true,
  },
  // 聊天模型（用于Token计算）
  chatModel: {
    type: String,
    default: 'gpt-4',
  },
})

// 初始化计费Store
const billingStore = useBillingStore()

// 响应式状态
const isBalanceChecking = ref(false)
const lastTokenCalculation = ref<any>(null)

// 计算是否禁用
const isDisabled = computed(() => {
  return props.isHistoryLoading || props.isLoading || isBalanceChecking.value
})

// 检查余额是否充足
async function checkBalance(messageContent: string): Promise<boolean> {
  isBalanceChecking.value = true
  try {
    // 计算消息的Token和费用
    // 简单估算Token数量
    const inputTokens = Math.ceil(messageContent.length / 4)
    const outputTokens = Math.min(inputTokens * 1.5, 2000)

    const calculation = await billingStore.calculateTokenCost({
      inputTokens,
      outputTokens,
      thoughtChainTokens: 0,
    })

    if (!calculation) {
      message.warning('无法计算消息费用，请稍后重试')
      return false
    }

    lastTokenCalculation.value = calculation

    // 获取当前余额
    await billingStore.refreshBalance()
    const balance = billingStore.balance
    const currency = billingStore.currency

    // 检查余额是否充足
    if (calculation.totalCost > balance) {
      // 余额不足，显示充值提示
      Modal.confirm({
        title: '余额不足',
        content: `当前消息预估费用为 ${formatCurrency(calculation.totalCost, currency)}，但您的余额只有 ${formatCurrency(balance, currency)}。是否前往充值？`,
        okText: '立即充值',
        cancelText: '取消发送',
        onOk() {
          // 导航到充值页面
          window.open('/billing/recharge', '_blank')
        },
      })
      return false
    }

    // 如果费用较高，给出警告
    if (calculation.totalCost > balance * 0.1) {
      return new Promise((resolve) => {
        Modal.confirm({
          title: '费用提醒',
          content: `当前消息预估费用为 ${formatCurrency(calculation.totalCost, currency)}，占您余额的 ${Math.round(calculation.totalCost / balance * 100)}%。确认发送吗？`,
          okText: '确认发送',
          cancelText: '取消',
          onOk() {
            resolve(true)
          },
          onCancel() {
            resolve(false)
          },
        })
      })
    }

    return true
  }
  catch (error) {
    console.error('余额检查失败:', error)
    message.warning('余额检查失败，请稍后重试')
    return false
  }
  finally {
    isBalanceChecking.value = false
  }
}

// 包装onSubmit函数以添加清空逻辑和余额检查
async function wrappedOnSubmit(messageContent: string) {
  // 防重复提交：如果正在加载中，直接返回
  if (props.isLoading || isBalanceChecking.value) {
    return
  }

  // 如果开启了Token计数器且有内容，先检查余额
  if (props.showTokenCounter && messageContent.trim()) {
    const canSend = await checkBalance(messageContent)
    if (!canSend) {
      return
    }
  }

  // 调用原始onSubmit函数
  props.onSubmit(messageContent)

  // 按照 ant-design-x-vue 标准模式，在 onSubmit 中立即清空输入框
  props.onInputChange('')

  // 通知父组件强制重新渲染
  if (props.onForceRerender) {
    props.onForceRerender()
  }
}

// 默认操作区域
function defaultActions(_: any, info: any) {
  const { SendButton, LoadingButton, ClearButton } = info.components

  return (
    <Space size="small">
      <ClearButton onClick={() => {
        console.log('BaseSender - 清除按钮点击')
        props.onInputChange('')
      }}
      />
      {props.isLoading
        ? (
            <LoadingButton type="default" disabled />
          )
        : (
            <SendButton
              type="primary"
              disabled={isDisabled.value || props.isSubmitDisabled}
              onClick={() => console.log('BaseSender - 发送按钮点击')}
            />
          )}
    </Space>
  )
}

// 计算使用的操作区域
const actionsToUse = computed(() => {
  return props.actions !== undefined ? props.actions : defaultActions
})

// 组合类名
const senderClass = computed(() => {
  return `${props.styles.sender || ''} ${props.className || ''}`
})
</script>

<template>
  <Sender
    :placeholder="placeholder"
    :value="inputValue"
    :on-change="onInputChange"
    :loading="isLoading"
    :disabled="isDisabled"
    :class="senderClass"
    :on-submit="wrappedOnSubmit"
    :actions="actionsToUse"
  >
    <!-- 传递插槽给 Sender 组件 -->
    <template #header>
      <slot name="header" />
    </template>

    <template #prefix>
      <slot name="prefix" />
    </template>

    <template #footer>
      <slot name="footer">
        <!-- 默认显示Token计数器 -->
        <TokenCounter
          v-if="showTokenCounter && inputValue"
          :content="inputValue"
          :model-name="chatModel"
          size="small"
        />
      </slot>
    </template>

    <!-- 默认插槽 -->
    <slot />
  </Sender>
</template>

<style scoped>
/* 修复 Sender 组件按钮中图标垂直居中问题 */
:deep(.ant-btn) {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 确保按钮内的图标正确对齐 */
:deep(.ant-btn .anticon) {
  display: inline-flex !important;
  align-items: center !important;
  vertical-align: baseline !important;
}

/* 修复 ant-design-x-vue Sender 组件内按钮的图标对齐 */
:deep(.ant-sender .ant-btn) {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.ant-sender .ant-btn .anticon) {
  display: inline-flex !important;
  align-items: center !important;
  vertical-align: baseline !important;
}
</style>
