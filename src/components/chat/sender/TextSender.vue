<script setup lang="ts">
// import { AppstoreAddOutlined } from '@ant-design/icons-vue'
// import { Button, Space } from 'ant-design-vue'
import type { PropType } from 'vue'
import BaseSender from '@/components/chat/sender/BaseSender.vue'

// 定义组件的 props
const _props = defineProps({
  // 样式对象
  styles: {
    type: Object,
    required: true,
  },
  // 输入框的值
  inputValue: {
    type: String,
    required: true,
  },
  // 输入值变化的回调函数
  onInputChange: {
    type: Function as PropType<(value: string) => void>,
    required: true,
  },
  // 是否正在加载
  isLoading: {
    type: Boolean,
    required: true,
  },
  // 是否正在加载历史记录
  isHistoryLoading: {
    type: Boolean,
    required: true,
  },
  // 提交表单的回调函数
  onSubmit: {
    type: Function as PropType<() => void | Promise<void>>,
    required: true,
  },
  // 打开模板抽屉的回调函数
  onOpenTemplateDrawer: {
    type: Function as PropType<() => void>,
    required: true,
  },
  // 是否禁用提交按钮
  isSubmitDisabled: {
    type: Boolean,
    default: false,
  },
  // 强制重新渲染的回调函数
  onForceRerender: {
    type: Function as PropType<() => void>,
    default: undefined,
  },
})
</script>

<template>
  <BaseSender
    :styles="styles"
    :input-value="inputValue"
    :on-input-change="onInputChange"
    :is-loading="isLoading"
    :is-history-loading="isHistoryLoading"
    :on-submit="onSubmit"
    :on-force-rerender="onForceRerender"
    placeholder="输入消息或使用模板..."
    :is-submit-disabled="isSubmitDisabled"
  >
    <!-- <template #prefix>
      <Space>
        <Button
          type="text"
          @click="onOpenTemplateDrawer"
        >
          <template #icon>
            <AppstoreAddOutlined />
          </template>
          使用模板
        </Button>
      </Space>
    </template> -->
  </BaseSender>
</template>

<style scoped>
/* 自定义样式可以在这里添加 */
</style>
