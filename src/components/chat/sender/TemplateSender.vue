<script setup lang="ts">
import type { PropType } from 'vue'
import InlineTemplateEditor from '@/components/chat/sender/InlineTemplateEditor.vue'
import { ArrowUp, Close } from '@element-plus/icons-vue'
import { computed, ref } from 'vue'

// 定义组件的 props
const props = defineProps({
  // 样式对象
  styles: {
    type: Object,
    required: true,
  },
  // 输入框的值
  inputValue: {
    type: String,
    required: true,
  },
  // 输入值变化的回调函数
  onInputChange: {
    type: Function as PropType<(value: string) => void>,
    required: true,
  },
  // 是否正在加载
  isLoading: {
    type: Boolean,
    required: true,
  },
  // 是否正在加载历史记录
  isHistoryLoading: {
    type: Boolean,
    required: true,
  },
  // 提交表单的回调函数
  onSubmit: {
    type: Function as PropType<(slotValues?: Record<string, any>) => void | Promise<void>>,
    required: true,
  },
  // 模板内容
  templateContent: {
    type: String,
    required: true,
  },
  // 模板名称
  templateName: {
    type: String,
    default: '提示词模板',
  },
  // 清除模板的回调函数
  onClearTemplate: {
    type: Function as PropType<() => void>,
    required: true,
  },
  // 槽位定义
  slotDefinitions: {
    type: Object,
    required: true,
  },
  // 槽位值
  slotValues: {
    type: Object,
    required: true,
  },
  // 槽位值变化的回调函数
  onSlotValueChange: {
    type: Function as PropType<(slotName: string, value: any) => void>,
    required: true,
  },
  // 是否禁用提交按钮
  isSubmitDisabled: {
    type: Boolean,
    default: false,
  },
  // 新增：高亮缺失槽位
  highlightMissingSlots: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  // 新增：显示验证消息
  showValidationMessage: {
    type: Boolean,
    default: false,
  },
})

// 检查模板内容是否包含槽位标记
const hasSlotMarkers = computed(() => {
  return props.templateContent ? /\{\{[^{}]+\}\}/.test(props.templateContent) : false
})

// 检查是否有槽位定义
const hasSlotDefinitions = computed(() => {
  return Object.keys(props.slotDefinitions).length > 0
})

// 焦点状态
const isFocused = ref(false)

// 处理容器点击事件
function handleContainerClick() {
  isFocused.value = true
  // 使用setTimeout使焦点效果延迟消失，增强用户体验
  setTimeout(() => {
    isFocused.value = false
  }, 1000)
}

// 处理槽位编辑完成
function handleSlotValueChange(slotName: string, value: any) {
  console.log('=== TemplateSender.handleSlotValueChange ===')
  console.log('TemplateSender - handleSlotValueChange:', slotName, value)
  console.log('TemplateSender 中的 slotValues:', JSON.stringify(props.slotValues))
  // 触发父组件的槽位值变化回调
  props.onSlotValueChange(slotName, value)
}

// 处理提交
async function handleSubmit(slotValues?: Record<string, any>) {
  console.log('=== TemplateSender.handleSubmit 开始 ===')
  console.log('TemplateSender - handleSubmit 收到的插槽数据:', JSON.stringify(slotValues))
  console.log('TemplateSender 中的 props.slotValues:', JSON.stringify(props.slotValues))
  console.log('TemplateSender - 当前加载状态:', props.isLoading)

  // 防重复提交：如果正在加载中，直接返回
  if (props.isLoading) {
    console.warn('TemplateSender.handleSubmit - 模板消息正在发送中，请勿重复提交')
    return
  }

  // 优先使用传入的slotValues，否则使用props中的slotValues
  const finalSlotValues = slotValues || props.slotValues

  // 确保所有槽位有值
  if (finalSlotValues) {
    // 如果有slots定义但没有对应的值，从定义中获取默认值
    const definitions = Array.isArray(props.slotDefinitions)
      ? props.slotDefinitions
      : Object.entries(props.slotDefinitions).map(([name, def]) => ({ name, ...def }))

    definitions.forEach((slot: any) => {
      if (slot && slot.name
        && (finalSlotValues[slot.name] === undefined || finalSlotValues[slot.name] === null || finalSlotValues[slot.name] === '')) {
        if (slot.defaultValue !== undefined && slot.defaultValue !== null && slot.defaultValue !== '') {
          finalSlotValues[slot.name] = slot.defaultValue
        }
        else {
          finalSlotValues[slot.name] = ''
        }
      }
    })
  }

  console.log('TemplateSender - handleSubmit 最终提交的插槽数据:', JSON.stringify(finalSlotValues))
  await props.onSubmit(finalSlotValues)
}
</script>

<template>
  <div
    class="template-sender-container"
    :class="{ focused: isFocused }"
    @click="handleContainerClick"
  >
    <!-- 标题和关闭按钮 -->
    <div class="template-header">
      <h5 class="template-title">
        {{ templateName }}
      </h5>
      <el-button
        link
        title="清除模板"
        @click.stop="onClearTemplate"
      >
        <el-icon><Close /></el-icon>
      </el-button>
    </div>

    <!-- 模板内容 -->
    <div class="template-content-layout">
      <div class="template-content-main">
        <!-- 带槽位的模板内容 -->
        <div v-if="hasSlotMarkers && hasSlotDefinitions">
          <InlineTemplateEditor
            :template-content="templateContent"
            :slot-definitions="slotDefinitions"
            :slot-values="slotValues"
            :on-slot-value-change="handleSlotValueChange"
            :on-submit="handleSubmit"
            :template-name="templateName"
            :highlight-missing-slots="highlightMissingSlots"
            :show-validation-message="showValidationMessage"
          />
        </div>
        <!-- 无槽位的模板内容 -->
        <div v-else class="template-content-raw">
          {{ templateContent }}
        </div>
      </div>

      <!-- 发送按钮，只在没有槽位时显示 -->
      <div v-if="!(hasSlotMarkers && hasSlotDefinitions)" class="template-send-button">
        <el-button
          type="primary"
          circle
          :loading="isLoading"
          :disabled="isLoading || isSubmitDisabled"
          @click="() => { console.log('TemplateSender - 无槽位的onSubmit按钮点击'); onSubmit(); }"
        >
          <el-icon><ArrowUp /></el-icon>
        </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.template-sender-container {
  position: relative;
  width: 100%;
  padding: 6px 16px 5px;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

/* 焦点样式 */
.template-sender-container.focused {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgb(64 158 255 / 20%);
}

.template-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.template-title {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.template-content-layout {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.template-content-main {
  flex: 1;
}

.template-content-raw {
  min-height: 60px;
  padding: 12px;
  line-height: 1.6;
  word-break: break-word;
  white-space: pre-wrap;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.template-send-button {
  margin-bottom: 4px;
}

/* 深色模式适配 */
:global(.dark) .template-sender-container {
  background-color: #141414;
  border-color: #434343;
}

:global(.dark) .template-title {
  color: #f0f0f0;
}

:global(.dark) .template-content-raw {
  background-color: #1f1f1f;
}
</style>
