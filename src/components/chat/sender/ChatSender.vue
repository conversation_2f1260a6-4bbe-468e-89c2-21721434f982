<script setup lang="ts">
import type { PropType } from 'vue'
import * as chatApi from '@/api/modules/chat'
import { getRandomLoadingMessage } from '@/utils/conversationUtils'
import { fillTemplate } from '@/utils/templateUtils'
import { ElMessage } from 'element-plus'
import { computed, ref } from 'vue'
import TemplateSender from './TemplateSender.vue'
import TextSender from './TextSender.vue'

// 定义组件的 props
const props = defineProps({
  // 当前活跃会话的 ID
  activeConversation: {
    type: String,
    default: '',
  },
  // 是否正在加载历史记录
  isHistoryLoading: {
    type: Boolean,
    default: false,
  },
  // 模板相关属性
  templateContent: {
    type: String,
    default: '',
  },
  templateName: {
    type: String,
    default: '',
  },
  templateId: {
    type: String,
    default: null,
  },
  slotDefinitions: {
    type: Object,
    default: () => ({}),
  },
  slotValues: {
    type: Object,
    default: () => ({}),
  },
  // 新增：高亮缺失槽位
  highlightMissingSlots: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  // 新增：显示验证消息
  showValidationMessage: {
    type: Boolean,
    default: false,
  },
})

// 定义组件的 emits
const emits = defineEmits([
  'messageSent', // 消息发送后触发
  'update:isLoading', // 更新加载状态
  'openTemplateDrawer', // 打开模板抽屉
  'clearTemplate', // 清除模板
  'slotValueChange', // 槽位值变化
  'clearSlotValues', // 清空槽位值
])

// 聊天相关状态
const chatInput = ref('')
const isLoading = ref(false)
const senderKey = ref(0) // 用于强制重新渲染 Sender 组件

// 计算是否使用模板模式
const isTemplateMode = computed(() => {
  return !!props.templateContent
})

// 处理消息发送
async function handleSubmit(messageContentOrSlotValues?: string | Record<string, any>) {
  // 防重复提交：如果正在加载中，直接返回
  if (isLoading.value) {
    return
  }

  // 如果是模板模式且有模板ID，使用模板发送
  if (isTemplateMode.value && props.templateId) {
    // 在模板模式下，参数应该是 slotValues
    const slotValues = typeof messageContentOrSlotValues === 'object' ? messageContentOrSlotValues : undefined
    await sendTemplateMessage(slotValues)
  }
  else {
    // 普通文本发送
    const content = typeof messageContentOrSlotValues === 'string' ? messageContentOrSlotValues : chatInput.value
    if (!content.trim()) {
      return
    }
    await sendTextMessage(content)
  }
}

// 发送文本消息
async function sendTextMessage(content: string) {
  // 防重复提交：如果正在加载中，直接返回
  if (isLoading.value) {
    return
  }

  // 创建用户消息
  const userMessage = {
    id: `user-${Date.now()}`,
    role: 'user',
    content,
    createdAt: new Date().toISOString(),
    conversationId: props.activeConversation || undefined,
  }

  // 创建加载中的助手消息
  const loadingAssistantMessage = {
    id: `assistant-loading-${Date.now()}`,
    role: 'assistant',
    content: getRandomLoadingMessage(),
    createdAt: new Date().toISOString(),
    loading: true,
    conversationId: props.activeConversation || undefined,
  }

  // 立即发送用户消息和加载中的助手消息
  emits('messageSent', {
    userMessage,
    assistantMessage: loadingAssistantMessage,
    isLoading: true,
  })

  // 设置加载状态
  isLoading.value = true
  emits('update:isLoading', true)

  try {
    // 调用chat模块的sendChatMessage函数
    const response: any = await chatApi.sendChatMessage(content, null, null, props.activeConversation)

    // 尝试多种方式从响应中提取内容
    let messageContent = ''
    let messageId = ''

    // 提取消息内容，尝试多种可能的路径
    if (typeof response === 'string') {
      // 如果响应直接是字符串
      messageContent = response
    }
    else if (response && typeof response === 'object') {
      // 首先检查是否是ApiResponse格式 {status: 1, data: ChatResponse}
      if (response.status === 1 && response.data) {
        // 从ApiResponse.data中提取ChatResponse
        const chatResponse = response.data

        // 优先处理新的list格式
        if (chatResponse.list && Array.isArray(chatResponse.list) && chatResponse.list.length > 0) {
          // 直接使用后端返回的完整list数据，不再只提取text内容
          // 后端已经正确解析了嵌套的JSON字符串并展开了内容
          messageContent = chatResponse.list
        }
        // 回退到旧格式
        else {
          messageContent = chatResponse.response || chatResponse.content || chatResponse.message || ''
        }

        messageId = chatResponse.conversationId || `assistant-${Date.now()}`
      }
      // 如果响应是对象，尝试读取多种可能的属性
      else if (response.content !== undefined) {
        // 直接有content属性
        messageContent = response.content
      }
      else if (response.data && response.data.content !== undefined) {
        // 有data子对象的content属性
        messageContent = response.data.content
      }
      else if (response.message !== undefined) {
        // 有message属性
        messageContent = response.message
      }
      else if (response.text !== undefined) {
        // 有text属性
        messageContent = response.text
      }
      else if (response.answer !== undefined) {
        // 有answer属性
        messageContent = response.answer
      }
      else if (response.response !== undefined) {
        // 有response属性（可能是嵌套对象或字符串）
        messageContent = typeof response.response === 'object'
          ? (response.response.content || JSON.stringify(response.response))
          : response.response
      }

      // 提取消息ID
      messageId = response.id || (response.data && response.data.id) || `assistant-${Date.now()}`
    }

    // 如果所有尝试都失败，使用默认错误消息
    if (!messageContent) {
      console.error('无法从API响应中提取消息内容')
      messageContent = '抱歉，我无法处理您的请求。请检查浏览器控制台获取更多信息。'
    }

    // 创建助手回复消息
    const assistantMessage: any = {
      id: messageId,
      role: 'assistant',
      createdAt: new Date().toISOString(),
    }

    // 根据messageContent的类型设置不同的字段
    if (Array.isArray(messageContent)) {
      // 如果是数组，设置为list字段（新的多类型消息格式）
      assistantMessage.list = messageContent
    }
    else {
      // 如果是字符串，设置为content字段（传统格式）
      assistantMessage.content = messageContent
    }

    // 触发消息发送事件，更新助手消息（替换加载中的消息）
    emits('messageSent', {
      assistantMessage,
      isLoading: false,
      isUpdate: true,
      loadingMessageId: loadingAssistantMessage.id,
    })

    // 注意：senderKey 的更新现在在 BaseSender 的 wrappedOnSubmit 中立即执行
  }
  catch (error) {
    console.error('发送消息失败', error)
    ElMessage.error('发送消息失败，请稍后重试')

    // 创建错误提示消息
    const errorMessage = {
      id: `error-${Date.now()}`,
      role: 'assistant',
      content: '消息处理失败，请重试或联系客服。',
      createdAt: new Date().toISOString(),
      error: true,
    }

    // 触发消息发送事件，更新助手消息（替换加载中的消息）
    emits('messageSent', {
      assistantMessage: errorMessage,
      isLoading: false,
      isUpdate: true,
      loadingMessageId: loadingAssistantMessage.id,
    })
  }
  finally {
    // 取消加载状态
    isLoading.value = false
    emits('update:isLoading', false)
  }
}

// 规范化槽位定义格式：将数组格式转换为对象格式
function normalizeSlotDefinitions(slotDefinitions: any): Record<string, any> {
  if (!slotDefinitions) {
    return {}
  }

  // 如果是数组格式，转换为Record格式
  if (Array.isArray(slotDefinitions)) {
    const result: Record<string, any> = {}
    slotDefinitions.forEach((slot) => {
      if (slot && slot.name) {
        result[slot.name] = {
          type: slot.type?.toLowerCase() || 'text',
          label: slot.label,
          description: slot.description,
          options: slot.options,
          required: !!slot.required,
          defaultValue: slot.defaultValue,
          disabled: slot.disabled,
        }
      }
    })
    return result
  }

  // 已经是Record格式，直接返回
  return slotDefinitions
}

// 发送模板消息
async function sendTemplateMessage(
  slotValues: Record<string, any> = props.slotValues,
  templateId: string = props.templateId,
  templateContent: string = props.templateContent,
) {
  // 防重复提交：如果正在加载中，直接返回
  if (isLoading.value) {
    return
  }

  // 确保使用有效的参数
  const finalSlotValues = slotValues || {}
  const finalTemplateId = templateId || props.templateId
  const finalTemplateContent = templateContent || props.templateContent

  // 规范化槽位定义格式
  const normalizedSlotDefinitions = normalizeSlotDefinitions(props.slotDefinitions)

  // 创建用户消息（显示填充后的模板内容）
  const filledContent = fillTemplate(finalTemplateContent, finalSlotValues, normalizedSlotDefinitions)
  const userMessage = {
    id: `user-${Date.now()}`,
    role: 'user',
    content: filledContent,
    createdAt: new Date().toISOString(),
    conversationId: props.activeConversation || undefined,
  }

  // 创建加载中的助手消息
  const loadingAssistantMessage = {
    id: `assistant-loading-${Date.now()}`,
    role: 'assistant',
    content: getRandomLoadingMessage(),
    createdAt: new Date().toISOString(),
    loading: true,
    conversationId: props.activeConversation || undefined,
  }

  // 立即发送用户消息和加载中的助手消息
  emits('messageSent', {
    userMessage,
    assistantMessage: loadingAssistantMessage,
    isLoading: true,
  })

  // 设置加载状态
  isLoading.value = true
  emits('update:isLoading', true)

  try {
    // 调用chat模块的sendChatMessage函数，发送模板ID和槽位值
    // 现在传递填充后的消息内容作为第一个参数，以确保在API端消息字段有值
    const response: any = await chatApi.sendChatMessage(filledContent, finalTemplateId, finalSlotValues, props.activeConversation)

    // 尝试多种方式从响应中提取内容
    let messageContent = ''
    let messageId = ''
    let agentType = ''

    // 提取消息内容，尝试多种可能的路径
    if (typeof response === 'string') {
      // 如果响应直接是字符串
      messageContent = response
    }
    else if (response && typeof response === 'object') {
      // 首先检查是否是ApiResponse格式 {status: 1, data: ChatResponse}
      if (response.status === 1 && response.data) {
        // 从ApiResponse.data中提取ChatResponse
        const chatResponse = response.data
        agentType = chatResponse.agentType

        // 优先处理新的list格式
        if (chatResponse.list && Array.isArray(chatResponse.list) && chatResponse.list.length > 0) {
          // 直接使用后端返回的完整list数据，不再只提取text内容
          // 后端已经正确解析了嵌套的JSON字符串并展开了内容
          messageContent = chatResponse.list
        }
        // 回退到旧格式
        else {
          messageContent = chatResponse.response || chatResponse.content || chatResponse.message || ''
        }

        messageId = chatResponse.conversationId || `assistant-${Date.now()}`
      }
      // 如果响应是对象，尝试读取多种可能的属性
      else if (response.content !== undefined) {
        // 直接有content属性
        messageContent = response.content
      }
      else if (response.data && response.data.content !== undefined) {
        // 有data子对象的content属性
        messageContent = response.data.content
      }
      else if (response.message !== undefined) {
        // 有message属性
        messageContent = response.message
      }
      else if (response.text !== undefined) {
        // 有text属性
        messageContent = response.text
      }
      else if (response.answer !== undefined) {
        // 有answer属性
        messageContent = response.answer
      }
      else if (response.response !== undefined) {
        // 有response属性（可能是嵌套对象或字符串）
        messageContent = typeof response.response === 'object'
          ? (response.response.content || JSON.stringify(response.response))
          : response.response
      }

      // 提取消息ID
      messageId = response.id || (response.data && response.data.id) || `assistant-${Date.now()}`
    }

    // 如果所有尝试都失败，使用默认错误消息
    if (!messageContent) {
      console.error('无法从API响应中提取消息内容')
      messageContent = '抱歉，我无法处理您的请求。请检查浏览器控制台获取更多信息。'
    }

    // 创建助手回复消息
    const assistantMessage: any = {
      id: messageId,
      role: 'assistant',
      agentType,
      createdAt: new Date().toISOString(),
    }

    // 根据messageContent的类型设置不同的字段
    if (Array.isArray(messageContent)) {
      // 如果是数组，设置为list字段（新的多类型消息格式）
      assistantMessage.list = messageContent
    }
    else {
      // 如果是字符串，设置为content字段（传统格式）
      assistantMessage.content = messageContent
    }

    // 触发消息发送事件，更新助手消息（替换加载中的消息）
    emits('messageSent', {
      assistantMessage,
      isLoading: false,
      isUpdate: true,
      loadingMessageId: loadingAssistantMessage.id,
    })

    // 清空槽位值
    clearSlotValues()

    // 清除模板
    emits('clearTemplate')

    // 注意：senderKey 的更新现在在 BaseSender 的 wrappedOnSubmit 中立即执行
  }
  catch (error) {
    console.error('发送消息失败', error)
    ElMessage.error('发送消息失败，请稍后重试')

    // 创建错误提示消息
    const errorMessage = {
      id: `error-${Date.now()}`,
      role: 'assistant',
      content: '消息处理失败，请重试或联系客服。',
      createdAt: new Date().toISOString(),
      error: true,
    }

    // 触发消息发送事件，更新助手消息（替换加载中的消息）
    emits('messageSent', {
      assistantMessage: errorMessage,
      isLoading: false,
      isUpdate: true,
      loadingMessageId: loadingAssistantMessage.id,
    })
  }
  finally {
    // 取消加载状态
    isLoading.value = false
    emits('update:isLoading', false)
  }
}

// 清空槽位值
function clearSlotValues() {
  emits('clearSlotValues')
}

// 强制重新渲染 Sender 组件
function forceRerender() {
  senderKey.value += 1
}

// 处理输入变化
function handleInputChange(value: string) {
  chatInput.value = value
}

// 设置输入内容（用于示例问题选择）
function setInputContent(content: string) {
  chatInput.value = content
}

// 发送当前输入内容
function sendCurrentInput() {
  handleSubmit()
}

// 打开模板抽屉
function handleOpenTemplateDrawer() {
  emits('openTemplateDrawer')
}

// 清除模板
function handleClearTemplate() {
  emits('clearTemplate')
}

// 处理槽位值变化
function handleSlotValueChange(slotName: string, value: any) {
  emits('slotValueChange', slotName, value)
}

// 暴露方法给父组件
defineExpose({
  setInputContent,
  sendCurrentInput,
  sendTemplateMessage,
})
</script>

<template>
  <!-- 模板模式 -->
  <TemplateSender
    v-if="isTemplateMode"
    :styles="{ input: { padding: '16px', minHeight: '60px' } }"
    :input-value="chatInput"
    :on-input-change="handleInputChange"
    :is-loading="isLoading"
    :is-history-loading="props.isHistoryLoading"
    :on-submit="handleSubmit"
    :template-content="props.templateContent"
    :template-name="props.templateName"
    :on-clear-template="handleClearTemplate"
    :slot-definitions="props.slotDefinitions"
    :slot-values="props.slotValues"
    :on-slot-value-change="handleSlotValueChange"
    :highlight-missing-slots="props.highlightMissingSlots"
    :show-validation-message="props.showValidationMessage"
  />

  <!-- 文本模式 -->
  <TextSender
    v-else
    :key="senderKey"
    :styles="{}"
    :input-value="chatInput"
    :on-input-change="handleInputChange"
    :is-loading="isLoading"
    :is-history-loading="props.isHistoryLoading"
    :on-submit="handleSubmit"
    :on-open-template-drawer="handleOpenTemplateDrawer"
    :on-force-rerender="forceRerender"
    class="w-full"
  />
</template>
