<script setup lang="ts">
import type { PropType } from 'vue'
import { useSlotSystem } from '@/utils/composables/useSlotSystem'
import { Top } from '@element-plus/icons-vue'
import { Popover, Space, Spin, Tooltip, Typography } from 'ant-design-vue'
import { computed, ref, toRef } from 'vue'
import SlotEditor from './SlotEditor.vue'

// 定义组件的 props
const props = defineProps({
  // 模板内容
  templateContent: {
    type: String,
    required: true,
  },
  // 槽位定义
  slotDefinitions: {
    type: [Object, Array] as PropType<Record<string, SlotDefinition> | Array<any>>,
    required: true,
  },
  // 槽位值
  slotValues: {
    type: Object as PropType<Record<string, any>>,
    required: true,
  },
  // 槽位值变化的回调函数
  onSlotValueChange: {
    type: Function as PropType<(slotName: string, value: any) => void>,
    required: true,
  },
  // 提交表单的回调函数
  onSubmit: {
    type: Function as PropType<(slotValues?: Record<string, any>) => void | Promise<void>>,
    required: true,
  },
  // 模板名称
  templateName: {
    type: String,
    default: '',
  },
  // 新增：高亮缺失槽位
  highlightMissingSlots: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  // 新增：显示验证消息
  showValidationMessage: {
    type: Boolean,
    default: false,
  },
})

const { Paragraph } = Typography

// 定义槽位定义类型
interface SlotDefinition {
  type: 'text' | 'textarea' | 'select' | 'date' | 'number'
  label?: string
  description?: string
  options?: Array<{ label: string, value: string }>
  required?: boolean
  defaultValue?: any
  disabled?: boolean
}

// 状态管理
const activeSlot = ref<string | null>(null)

// 转换slotDefinitions为Record格式
const normalizedSlotDefinitions = computed(() => {
  if (!props.slotDefinitions) {
    return {}
  }

  // 如果是数组格式，转换为Record格式
  if (Array.isArray(props.slotDefinitions)) {
    const result: Record<string, SlotDefinition> = {}
    props.slotDefinitions.forEach((slot) => {
      if (slot && slot.name) {
        result[slot.name] = {
          type: slot.type?.toLowerCase() || 'text',
          label: slot.label,
          description: slot.description,
          options: slot.options,
          required: !!slot.required,
          defaultValue: slot.defaultValue,
          disabled: slot.disabled,
        }
      }
    })
    // console.log('InlineTemplateEditor - 从数组转换为对象的slotDefinitions:', JSON.stringify(result))
    return result
  }

  // 已经是Record格式，直接返回
  // console.log('InlineTemplateEditor - 使用现有的对象格式slotDefinitions')
  return props.slotDefinitions
})

// 使用SlotSystem钩子
const {
  slotDefinitions,
  slotValues,
  loading,
  updateSlotValue,
} = useSlotSystem({
  templateName: toRef(props, 'templateName'),
  templateContent: toRef(props, 'templateContent'),
  initialSlotDefinitions: computed(() => normalizedSlotDefinitions.value),
  initialSlotValues: computed(() => props.slotValues || {}),
  autoInitialize: true,
  standardParam: ref({}), // todo: 待实现
  onSlotValueChange: props.onSlotValueChange,
})

// 检查所有必填槽位是否都已填写
const allRequiredSlotsComplete = computed(() => {
  return Object.entries(slotDefinitions.value).every(([slotName, definition]) => {
    if (!definition.required) {
      return true
    }
    const value = slotValues.value[slotName]
    return value !== undefined && value !== null && value !== ''
  })
})

// 解析模板内容为可渲染的部分
const templateParts = computed(() => {
  if (!props.templateContent) {
    return []
  }

  // 正则表达式匹配 {{slot_name}}
  const slotRegex = /\{\{([^{}]+)\}\}/g
  const parts: Array<{ type: 'text' | 'slot', content: string, slotName?: string, key: string }> = []
  let lastIndex = 0

  // 查找所有匹配的槽位
  const matches = Array.from(props.templateContent.matchAll(slotRegex))
  for (const match of matches) {
    // 添加槽位前的普通文本
    if (match.index! > lastIndex) {
      parts.push({
        type: 'text',
        content: props.templateContent.substring(lastIndex, match.index),
        key: `text-${lastIndex}`,
      })
    }

    // 提取槽位名称（去除双花括号）
    const slotName = match[1].trim()

    // 验证槽位是否存在于定义中
    if (slotDefinitions.value[slotName]) {
      parts.push({
        type: 'slot',
        content: match[0],
        slotName,
        key: `slot-${slotName}-${match.index}`,
      })
    }
    else {
      // 槽位名不存在，作为普通文本处理
      parts.push({
        type: 'text',
        content: match[0],
        key: `text-unknown-slot-${match.index}`,
      })
    }

    lastIndex = (match.index! + match[0].length)
  }

  // 添加最后一段普通文本
  if (lastIndex < props.templateContent.length) {
    parts.push({
      type: 'text',
      content: props.templateContent.substring(lastIndex),
      key: `text-${lastIndex}`,
    })
  }

  return parts
})

// 获取槽位显示文本
function getSlotDisplayText(slotName: string) {
  const definition = slotDefinitions.value[slotName]
  const value = slotValues.value[slotName]
  // console.log('InlineTemplateEditor - getSlotDisplayText:', slotSystem)
  // console.log('InlineTemplateEditor - getSlotDisplayText:', slotName, definition, value)

  if (value === undefined || value === null || value === '') {
    // 如果没有值，且存在默认值，则使用默认值
    if (definition?.defaultValue !== undefined && definition?.defaultValue !== null && definition?.defaultValue !== '') {
      return definition.defaultValue
    }
    // 否则使用label或slotName
    return definition?.label || slotName
  }

  switch (definition?.type) {
    case 'date':
      return value instanceof Date
        ? value.toLocaleDateString()
        : (typeof value === 'string' ? value : JSON.stringify(value))
    case 'select': {
      const option = (definition?.options || [])
        .find(opt => opt.value === value)
      return option?.label || value
    }
    default:
      return String(value)
  }
}

// 检查槽位是否已完成
function isSlotCompleted(slotName: string) {
  const value = slotValues.value[slotName]
  return value !== undefined && value !== null && value !== ''
}

// 处理槽位点击
function handleSlotClick(slotName: string) {
  const definition = slotDefinitions.value[slotName]
  if (!definition?.disabled) {
    activeSlot.value = slotName
  }
}

// 处理槽位编辑完成
async function handleSlotChange(slotName: string, newValue: any) {
  // console.log('InlineTemplateEditor - handleSlotChange:', slotName, newValue)
  //  钩子函数
  await updateSlotValue(slotName, newValue)
  activeSlot.value = null
  console.log('当前所有槽位值:', JSON.stringify(props.slotValues))
}

// 处理提交
async function handleSubmit() {
  if (props.onSubmit && allRequiredSlotsComplete.value) {
    try {
      loading.value = true
      // 处理slotValues，确保所有必填项都有值
      const finalSlotValues = { ...slotValues.value }

      // 检查是否有空值但有默认值的槽位，使用默认值填充
      Object.entries(slotDefinitions.value).forEach(([name, def]) => {
        if ((finalSlotValues[name] === undefined || finalSlotValues[name] === null || finalSlotValues[name] === '')
          && def.defaultValue !== undefined && def.defaultValue !== null && def.defaultValue !== '') {
          finalSlotValues[name] = def.defaultValue
        }
      })

      // console.log('InlineTemplateEditor - handleSubmit 提交的槽位数据:', JSON.stringify(finalSlotValues))
      await props.onSubmit(finalSlotValues)
    }
    finally {
      loading.value = false
    }
  }
  else if (!allRequiredSlotsComplete.value) {
    console.warn('请完成所有必填槽位')
  }
}

// 处理键盘事件
function handleKeyDown(e: KeyboardEvent) {
  if (e.key === 'Enter' && !e.shiftKey && !activeSlot.value) {
    if (allRequiredSlotsComplete.value) {
      e.preventDefault()
      handleSubmit()
    }
    else {
      console.warn('请完成所有必填槽位')
    }
  }
}

// 检查槽位是否需要高亮显示
function shouldHighlightSlot(slotName: string): boolean {
  return props.highlightMissingSlots.includes(slotName)
}

// 获取槽位的样式类
function getSlotTagStyle(slotName: string) {
  const definition = slotDefinitions.value[slotName]
  const isHighlighted = shouldHighlightSlot(slotName)
  const isCompleted = isSlotCompleted(slotName)
  const isRequired = definition?.required

  return {
    cursor: definition?.disabled ? 'not-allowed' : 'pointer',
    margin: '0 4px',
    padding: '4px 8px',
    fontSize: '14px',
    opacity: definition?.disabled ? '0.5' : '1',
    // 高亮未填充的必选项
    borderColor: isHighlighted ? '#ff4d4f' : (isRequired && !isCompleted ? '#ff7875' : '#1677ff'),
    backgroundColor: isHighlighted ? '#fff2f0' : (isRequired && !isCompleted ? '#fff1f0' : ''),
    color: isHighlighted ? '#ff4d4f' : undefined,
  }
}
</script>

<template>
  <div class="inline-template-editor" tabindex="0" @keydown="handleKeyDown">
    <!-- 新增：验证提示消息 -->
    <div v-if="showValidationMessage && highlightMissingSlots.length > 0" class="validation-message">
      <div class="validation-text">
        <span class="warning-icon">⚠️</span>
        请完成以下必填参数后再发送消息
      </div>
      <div class="missing-slots">
        <span
          v-for="slotName in highlightMissingSlots"
          :key="slotName"
          class="missing-slot-tag"
        >
          {{ slotDefinitions[slotName]?.label || slotName }}
        </span>
      </div>
    </div>

    <div v-if="loading" style="padding: 20px 0; text-align: center;">
      <Spin />
    </div>
    <div v-else>
      <Paragraph :style="{ marginBottom: '16px' }">
        <template v-for="part in templateParts" :key="part.key">
          <!-- 普通文本 -->
          <span v-if="part.type === 'text'" class="template-text">{{ part.content }}</span>

          <!-- 槽位标签 -->
          <Popover
            v-else-if="part.type === 'slot' && part.slotName"
            trigger="click"
            :disabled="slotDefinitions[part.slotName]?.disabled"
            :open="!slotDefinitions[part.slotName]?.disabled && activeSlot === part.slotName"
            placement="bottom"
            @open-change="(visible) => { activeSlot = visible ? part.slotName || null : null }"
          >
            <template #content>
              <SlotEditor
                :slot-name="part.slotName"
                :definition="slotDefinitions[part.slotName]"
                :value="slotValues[part.slotName]"
                :on-change="handleSlotChange"
              />
            </template>

            <el-tag
              type="primary"
              :style="getSlotTagStyle(part.slotName)"
              @click.stop="handleSlotClick(part.slotName)"
            >
              {{ getSlotDisplayText(part.slotName) }}
              <span
                v-if="slotDefinitions[part.slotName]?.required && !isSlotCompleted(part.slotName)"
                style="margin-left: 4px; font-weight: bold; color: #ff4d4f;"
              >*</span>
            </el-tag>
          </Popover>
        </template>
      </Paragraph>

      <div style="margin-top: 16px; text-align: right;">
        <Space>
          <Tooltip :title="allRequiredSlotsComplete ? '发送模板消息' : '请完成所有必填槽位'">
            <el-button
              type="primary"
              circle
              :loading="loading"
              :disabled="!allRequiredSlotsComplete"
              @click="handleSubmit"
            >
              <el-icon v-if="!loading">
                <Top />
              </el-icon>
            </el-button>
          </Tooltip>
        </Space>
      </div>
    </div>
  </div>
</template>

<style scoped>
.inline-template-editor {
  padding: 10px;
  border-radius: 12px;
}

.template-text {
  line-height: 1.6;
  color: #333;
}

/* 新增：验证提示消息样式 */
.validation-message {
  padding: 12px;
  margin-bottom: 16px;
  background-color: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
}

.validation-text {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 500;
  color: #d46b08;
}

.warning-icon {
  margin-right: 8px;
  font-size: 16px;
}

.missing-slots {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.missing-slot-tag {
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  color: #ff4d4f;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
}

/* 确保 Tag 组件的样式正确显示 */
:deep(.ant-tag) {
  display: inline-flex;
  align-items: center;
  transition: all 0.3s;
}

:deep(.ant-tag:hover) {
  opacity: 0.85;
}

/* 成功状态的标签样式 */
:deep(.ant-tag-success) {
  color: #52c41a;
  background-color: #f6ffed;
  border-color: #b7eb8f;
}

/* 处理中状态的标签样式 */
:deep(.ant-tag-processing) {
  color: #1890ff;
  background-color: #e6f7ff;
  border-color: #91d5ff;
}

/* 高亮必填项样式 */
:deep(.ant-tag.highlight-required) {
  color: #ff4d4f;
  background-color: #fff2f0;
  border-color: #ff4d4f;
  animation: highlight-pulse 1.5s ease-in-out infinite;
}

@keyframes highlight-pulse {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgb(255 77 79 / 40%);
  }

  50% {
    box-shadow: 0 0 0 4px rgb(255 77 79 / 10%);
  }
}
</style>
