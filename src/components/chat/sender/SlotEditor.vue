<script setup lang="ts">
import type { SlotDefinition, SlotValue } from '@/utils/slotSystem/types'
import type { Dayjs } from 'dayjs'
import type { PropType } from 'vue'
import { Button, Cascader, DatePicker, Input, InputNumber, Select, Space } from 'ant-design-vue'
import dayjs from 'dayjs'
import { computed, ref, watch } from 'vue'

// 定义组件 props
const props = defineProps({
  // 槽位名称
  slotName: {
    type: String,
    required: true,
  },
  // 槽位定义
  definition: {
    type: Object as PropType<SlotDefinition>,
    required: true,
  },
  // 槽位值
  value: {
    type: [String] as PropType<SlotValue>,
    default: '',
  },
  // 修改回调函数
  onChange: {
    type: Function as PropType<(slotName: string, value: any) => void>,
    required: true,
  },
})

// 当前编辑的值
const currentValue = ref<any>(props.value || props.definition?.defaultValue || '')

// 监听 props.value 的变化
watch(() => props.value, (newValue) => {
  if (newValue !== undefined && newValue !== null && newValue !== '') {
    currentValue.value = newValue
  }
  else if (props.definition?.defaultValue !== undefined && props.definition?.defaultValue !== null) {
    // 如果value为空但有默认值，使用默认值
    currentValue.value = props.definition.defaultValue
  }
  else {
    currentValue.value = ''
  }
  console.log('SlotEditor - watch props.value:', newValue, '当前值:', currentValue.value)
}, { immediate: true })

// 监听 props.definition 的变化，确保required属性变化时UI能够更新
watch(() => props.definition, (newDefinition) => {
  console.log('SlotEditor - watch props.definition changed:', newDefinition)
}, { deep: true })

// 处理确认按钮
function handleConfirm() {
  console.log('SlotEditor - handleConfirm:', props.slotName, currentValue.value)
  // 确保不会传递null或undefined值
  const valueToEmit = currentValue.value === null || currentValue.value === undefined ? '' : currentValue.value
  props.onChange(props.slotName, valueToEmit)
}

// 处理取消按钮
function handleCancel() {
  currentValue.value = props.value
}

// 处理输入变化
function handleInputChange(e: Event) {
  const target = e.target as HTMLInputElement
  currentValue.value = target.value
}

// 处理数字输入变化
function handleNumberChange(val: any) {
  currentValue.value = val
}

// 处理日期变化
function handleDateChange(value: string | Dayjs, dateString: string) {
  const date = typeof value === 'string' ? dayjs(value) : value
  currentValue.value = date ? date.format('YYYY-MM-DD') : null
}

// 处理选择变化
function handleSelectChange(val: any) {
  currentValue.value = val
}

// 获取日期值
const dateValue = computed(() => {
  return currentValue.value ? dayjs(currentValue.value as string) : undefined
})

// 获取选项列表
const optionsList = computed(() => {
  return props.definition.options || []
})
</script>

<template>
  <div class="slot-editor">
    <div style="margin-bottom: 8px;">
      <strong>{{ definition.label || slotName }}</strong>
      <span v-if="definition.required" style="margin-left: 4px; color: #ff4d4f;">*</span>
    </div>

    <!-- 文本输入 -->
    <Input
      v-if="!definition.type || definition.type === 'text'"
      :value="currentValue"
      :placeholder="definition.placeholder"
      @input="handleInputChange"
    />

    <!-- 多行文本输入 -->
    <Input.TextArea
      v-else-if="definition.type === 'textarea'"
      :value="currentValue"
      :placeholder="definition.placeholder"
      :rows="3"
      @input="handleInputChange"
    />

    <!-- 数字输入 -->
    <InputNumber
      v-else-if="definition.type === 'number'"
      :value="currentValue"
      :placeholder="definition.placeholder"
      style="width: 100%;"
      @change="handleNumberChange"
    />

    <!-- 日期选择 -->
    <DatePicker
      v-else-if="definition.type === 'date'"
      :value="dateValue"
      :placeholder="definition.placeholder"
      style="width: 100%;"
      @change="handleDateChange"
    />

    <!-- 下拉选择 -->
    <Select
      v-else-if="definition.type === 'select'"
      :value="currentValue"
      :placeholder="definition.placeholder"
      style="width: 100%;"
      @change="handleSelectChange"
    >
      <Select.Option
        v-for="option in optionsList"
        :key="typeof option === 'object' ? option.value : option"
        :value="typeof option === 'object' ? option.value : option"
      >
        {{ typeof option === 'object' ? option.label : option }}
      </Select.Option>
    </Select>

    <!-- 级联选择器配置 -->
    <Cascader
      v-else-if="definition.type === 'cascader'"
      :options="definition.options"
      :value="currentValue"
      :placeholder="definition.placeholder"
      expand-trigger="hover"
      style="width: 100%;"
      @change="val => handleSelectChange(Array.isArray(val) ? val[val.length - 1] : val)"
    />

    <div style="margin-top: 8px; text-align: right;">
      <Space>
        <Button size="small" @click="handleCancel">
          取消
        </Button>
        <Button size="small" type="primary" @click="handleConfirm">
          确认
        </Button>
      </Space>
    </div>
  </div>
</template>

<style scoped>
.slot-editor {
  min-width: 200px;
  padding: 8px;
}
</style>
