<script setup lang="ts">
import type { ThoughtChainItem } from 'ant-design-x-vue'
import type { VNode } from 'vue'
import { renderMarkdown } from '@/utils/markdownRenderer'
import {
  BulbOutlined,
  DatabaseOutlined,
  FileSearchOutlined,
  MoreOutlined,
  ToolOutlined,
} from '@ant-design/icons-vue'
import { Card, Tag, Typography } from 'ant-design-vue'
import { ThoughtChain } from 'ant-design-x-vue'
import { h, ref } from 'vue'

// --- Interface Definitions ---
export interface IntermediateStepAction {
  log: string
  tool: string
  toolInput?: { input?: any, [key: string]: any }
  messageLog?: Array<{
    kwargs?: {
      content?: string
      additional_kwargs?: {
        tool_calls?: Array<{
          function?: {
            arguments?: string
          }
        }>
      }
      response_metadata?: {
        usage?: {
          total_tokens?: number
          cost?: number
          [key: string]: any
        }
      }
      [key: string]: any
    }
    [key: string]: any
  }>
  toolCallId?: string
  [key: string]: any
}

export interface IntermediateStep {
  action: IntermediateStepAction
  observation: any
  [key: string]: any
}

// 定义组件 props
const props = defineProps({
  steps: {
    type: Array as () => IntermediateStep[],
    required: true,
  },
  messageKey: {
    type: String,
    required: true,
  },
})

const { Text: AText, Paragraph: AParagraph } = Typography

const expandedThoughtChainKeys = ref<string[]>([])

function onExpandThoughtChainKeys(keys: string[] | string) {
  expandedThoughtChainKeys.value = Array.isArray(keys) ? keys : typeof keys === 'string' ? [keys] : []
}

function getToolIconVNode(toolName: string): VNode {
  switch (toolName?.toLowerCase()) {
    case 'thinking_tool':
      return h(BulbOutlined)
    case 'documents':
      return h(FileSearchOutlined)
    case 'query_document_rows':
      return h(DatabaseOutlined)
    case 'list_documnets':
      return h(FileSearchOutlined)
    default:
      return h(ToolOutlined)
  }
}

function formatToolName(toolName: string): string {
  if (!toolName) {
    return '未知工具'
  }
  return toolName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

function renderObservationVNode(observation: any): VNode {
  if (observation === null || observation === undefined) {
    return h(AParagraph, { class: 'observation-unknown' }, () => '(无数据)')
  }

  if (typeof observation === 'string') {
    try {
      const parsed = JSON.parse(observation)
      if (typeof parsed === 'object') {
        return h('pre', { class: 'observation-json' }, JSON.stringify(parsed, null, 2))
      }
    }

    catch { /* Not JSON */ }

    if (observation.startsWith('Error during node execution:')) {
      return h(AParagraph, { class: 'observation-text error-text', style: { whiteSpace: 'pre-wrap', wordBreak: 'break-word' } }, () => observation)
    }
    if (observation.match(/(\*\*|__|[*_#]|```|\[.*\]\(.*\))/)) {
      return h('div', { class: 'observation-markdown', innerHTML: renderMarkdown(observation) })
    }
    return h(AParagraph, { class: 'observation-text', style: { whiteSpace: 'pre-wrap', wordBreak: 'break-word' } }, () => observation)
  }
  else if (Array.isArray(observation)) {
    if (observation.length === 0) {
      return h(AParagraph, { class: 'observation-array empty' }, () => '未找到结果。')
    }
    if (observation.every(item => item && typeof item === 'object' && 'text' in item)) {
      return h('div', { class: 'observation-array' }, observation.map((item, idx) => {
        let content = item.text
        try {
          const parsed = JSON.parse(content)
          if (parsed && typeof parsed.pageContent !== 'undefined') {
            content = parsed.pageContent
          }
        }
        catch { /* ignore */ }
        return h(AParagraph, { key: idx, class: 'observation-array-item', style: { marginBottom: '4px', fontSize: '0.9em', whiteSpace: 'pre-wrap', wordBreak: 'break-word' } }, () => typeof content === 'string' ? content : JSON.stringify(content))
      }))
    }
    return h('div', { class: 'observation-array' }, observation.map((item, idx) =>
      h(AParagraph, { key: idx, class: 'observation-array-item', style: { whiteSpace: 'pre-wrap', wordBreak: 'break-word' } }, () => typeof item === 'string' ? item : JSON.stringify(item, null, 2)),
    ))
  }
  else if (observation && typeof observation === 'object') {
    return h('pre', { class: 'observation-object' }, JSON.stringify(observation, null, 2))
  }
  return h(AParagraph, { class: 'observation-unknown' }, () => '(无法预览结果)')
}

function formatIntermediateStepToThoughtChainItem(
  step: IntermediateStep,
  stepIndex: number,
  messageKey: string,
): ThoughtChainItem {
  if (!step || !step.action) {
    return {
      key: `${messageKey}-step-${stepIndex}-unknown`,
      title: '未知步骤',
      icon: h(MoreOutlined),
      description: h('div', {}, []),
      content: h('div', { class: 'thought-step-content' }, [
        h(Card, { size: 'small', title: '推理过程', class: 'reasoning-card', bodyStyle: { padding: '8px' } }, [
          h('div', { class: 'markdown-content' }, '(无数据)'),
        ]),
        h(Card, { size: 'small', title: '执行结果', class: 'observation-card', bodyStyle: { padding: '8px' }, style: { marginTop: '8px' } }, [
          h(AParagraph, { class: 'observation-unknown' }, () => '(无数据)'),
        ]),
      ]),
      status: 'warning' as any,
    } as ThoughtChainItem
  }

  const toolName = step.action?.tool || '未知工具'
  const toolCallId = step.action?.toolCallId || Date.now().toString()

  let thinkingProcess = step.action?.log || ''
  if (!thinkingProcess && step.action?.messageLog?.[0]?.kwargs?.content) {
    thinkingProcess = step.action.messageLog[0].kwargs.content
  }

  let toolInputText = ''
  if (step.action?.toolInput?.input) {
    toolInputText = typeof step.action.toolInput.input === 'string' ? step.action.toolInput.input : JSON.stringify(step.action.toolInput.input)
  }
  else if (step.action?.messageLog?.[0]?.kwargs?.additional_kwargs?.tool_calls?.[0]?.function?.arguments) {
    try {
      const args = JSON.parse(step.action.messageLog[0].kwargs.additional_kwargs.tool_calls[0].function.arguments)
      if (args && typeof args.input !== 'undefined') {
        toolInputText = typeof args.input === 'string' ? args.input : JSON.stringify(args.input)
      }
      else {
        toolInputText = step.action.messageLog[0].kwargs.additional_kwargs.tool_calls[0].function.arguments
      }
    }
    catch {
      toolInputText = step.action.messageLog[0].kwargs.additional_kwargs.tool_calls[0].function.arguments
    }
  }

  if (toolInputText && thinkingProcess && !thinkingProcess.includes(toolInputText.substring(0, Math.min(toolInputText.length, 50)))) {
    thinkingProcess += `\n\n**Tool Input:**\n\`\`\`\n${toolInputText}\n\`\`\``
  }
  else if (toolInputText && !thinkingProcess) {
    thinkingProcess = `**Tool Input:**\n\`\`\`\n${toolInputText}\n\`\`\``
  }

  if (!thinkingProcess.trim()) {
    thinkingProcess = '(无明确的思考过程文本)'
  }

  let usageTextVNode: VNode | null = null
  const usage = step.action?.messageLog?.[0]?.kwargs?.response_metadata?.usage
  if (usage && typeof usage.total_tokens === 'number' && typeof usage.cost === 'number') {
    const costYuan = usage.cost * 7
    usageTextVNode = h(AText, { type: 'secondary', class: 'usage-info' }, () => `消耗: ${usage.total_tokens} tokens | 费用: $${usage.cost!.toFixed(6)} (约 ¥${costYuan.toFixed(4)})`)
  }

  let status: ThoughtChainItem['status'] = 'success'
  if (typeof step.observation === 'string' && step.observation.startsWith('Error during node execution:')) {
    status = 'error'
  }

  return {
    key: `${messageKey}-step-${stepIndex}-${toolCallId}`,
    title: formatToolName(toolName),
    icon: getToolIconVNode(toolName),
    description: h('div', {}, [
      h(Tag, { bordered: false, color: 'blue', style: { fontSize: '0.9em', marginRight: '8px' } }, () => toolName),
      usageTextVNode,
    ]),
    content: h('div', { class: 'thought-step-content' }, [
      h(Card, { size: 'small', title: '推理过程', class: 'reasoning-card', bodyStyle: { padding: '8px' } }, () => [
        h('div', { class: 'markdown-content', innerHTML: renderMarkdown(thinkingProcess) }),
      ]),
      h(Card, { size: 'small', title: '执行结果', class: 'observation-card', bodyStyle: { padding: '8px' }, style: { marginTop: '8px' } }, () => [
        renderObservationVNode(step.observation),
      ]),
    ]),
    status,
  } as ThoughtChainItem
}

function getThoughtChainItems(): ThoughtChainItem[] {
  if (!props.steps || !Array.isArray(props.steps)) {
    return []
  }

  return props.steps.map((step, idx) =>
    formatIntermediateStepToThoughtChainItem(step, idx, props.messageKey),
  ).filter(Boolean) as ThoughtChainItem[]
}
</script>

<template>
  <ThoughtChain
    v-if="steps && steps.length > 0"
    :items="getThoughtChainItems()"
    size="small"
    class="thought-chain-container"
    :collapsible="true"
    :expanded-keys="expandedThoughtChainKeys"
    :on-expand="onExpandThoughtChainKeys"
    :styles="{
      item: { fontSize: '1em' },
      itemContent: { color: '#333', lineHeight: '1.5' },
    }"
  />
</template>

<style>
/* --- Styles for ThoughtChain and Observations --- */
.thought-chain-container {
  max-width: 100%;
  margin-bottom: 8px;
  overflow-x: hidden; /* 防止横向溢出 */
}

.thought-step-content {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

.thought-step-content .reasoning-card,
.thought-step-content .observation-card {
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  border-radius: 4px;
}

.thought-step-content .reasoning-card .ant-card-body,
.thought-step-content .observation-card .ant-card-body {
  width: 100%;
  max-width: 100%;
  overflow-x: auto; /* 允许内容滚动 */
}

.thought-step-content .reasoning-card .ant-card-head,
.thought-step-content .observation-card .ant-card-head {
  min-height: 36px;
  padding: 0 12px;
}

.thought-step-content .reasoning-card .ant-card-head-title,
.thought-step-content .observation-card .ant-card-head-title {
  padding: 8px 0;
  font-size: 0.95em;
}

.thought-step-content .reasoning-card .markdown-content {
  max-width: 100%;
  overflow-x: auto; /* 添加水平滚动条 */
  font-size: 0.9em;
  line-height: 1.5;
  word-wrap: break-word; /* 确保长单词可以换行 */
}

.thought-step-content .reasoning-card .markdown-content p,
.thought-step-content .reasoning-card .markdown-content pre,
.thought-step-content .reasoning-card .markdown-content ul,
.thought-step-content .reasoning-card .markdown-content ol {
  max-width: 100%;
  margin-top: 4px;
  margin-bottom: 4px;
}

.thought-step-content .reasoning-card .markdown-content pre {
  max-width: 100%;
  padding: 8px;
  overflow-x: auto; /* 如果内容太宽，允许滚动 */
  word-break: break-word; /* 防止长单词溢出 */
  white-space: pre-wrap; /* 确保代码块可以换行 */
  background-color: #f5f5f5; /* 更明显的背景色，提高可读性 */
  border-radius: 4px;
}

.usage-info {
  font-size: 0.8em;
  color: #888;
}

.observation-text,
.observation-json,
.observation-array,
.observation-object,
.observation-unknown,
.observation-markdown {
  width: 100%;
  max-width: 100%;
  padding: 4px;
  overflow-x: auto; /* 添加水平滚动 */
  font-size: 0.9em;
  line-height: 1.5;
  word-break: break-word; /* 修改为 break-word，更好地处理长单词 */
}

.observation-text.error-text {
  padding: 8px;
  color: #ff4d4f;
  background-color: #fff1f0;
  border-left: 3px solid #ff4d4f;
  border-radius: 4px;
}

.observation-json,
.observation-object {
  width: 100%;
  max-width: 100%;
  padding: 8px;
  overflow-x: auto; /* 允许长代码水平滚动 */
  word-break: break-word; /* 确保长内容能够换行 */
  white-space: pre-wrap;
  background-color: #f7f7f7;
  border-radius: 3px;
}

.observation-array.empty {
  font-style: italic;
  color: #aaa;
}

.observation-array-item {
  max-width: 100%;
  padding: 2px 0;
  overflow-x: auto; /* 确保长内容可以滚动 */
  word-break: break-word; /* 支持单词断行 */
  border-bottom: 1px dashed #eee;
}

.observation-array-item:last-child {
  border-bottom: none;
}

/* Ensure markdown within observation also has smaller font if needed */
.observation-markdown {
  width: 100%;
  max-width: 100%;
}

.observation-markdown .markdown-content {
  max-width: 100%;
  overflow-x: auto;
  font-size: 1em; /* Relative to parent, which is 0.9em */
  word-wrap: break-word; /* 允许长单词换行 */
}

.observation-markdown .markdown-content p,
.observation-markdown .markdown-content pre {
  max-width: 100%;
  margin-top: 4px;
  margin-bottom: 4px;
}

.observation-markdown .markdown-content pre code {
  display: block;
  max-width: 100%;
  overflow-x: auto;
  word-break: break-word;
  white-space: pre-wrap;
}

/* 处理表格溢出 */
.markdown-content table {
  display: block;
  width: 100%;
  max-width: 100%;
  margin-bottom: 8px;
  overflow-x: auto;
  border-collapse: collapse;
}

.markdown-content table th,
.markdown-content table td {
  min-width: 80px; /* 给单元格一个最小宽度 */
  padding: 6px 8px;
  word-break: break-word; /* 允许单词在单元格内断行 */
  border: 1px solid #ddd;
}

/* 处理长SQL查询等 */
.markdown-content code {
  padding: 2px 4px;
  word-break: break-word;
  white-space: pre-wrap;
  background-color: rgb(0 0 0 / 4%);
  border-radius: 3px;
}

/* 增强特定类型内容的显示 */
.markdown-content pre code.language-sql,
.markdown-content pre code.language-json {
  max-width: 100%;
  overflow-x: auto;
  word-break: break-word;
  white-space: pre-wrap;
}
</style>
