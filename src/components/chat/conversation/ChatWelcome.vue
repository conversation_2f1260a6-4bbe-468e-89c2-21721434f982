<script setup lang="tsx">
import type { PromptsProps } from 'ant-design-x-vue'
// 导入图片资源
// import robotImage from '@/assets/images/robot-1.webp'
// import robotImage from '@/assets/images/robot-2.png'
// import robotImage from '@/assets/images/robot-3.avif'
// import robotImage from '@/assets/images/robot-5.webp'
// import robotImage from '@/assets/images/robot-6.gif'
// import robotImage from '@/assets/images/robot-7.gif'
import robotImage from '@/assets/images/robot-4.webp'
import {
  DatabaseOutlined,
  FireOutlined,
  HistoryOutlined,
  QuestionCircleOutlined,
  SettingOutlined,
  ToolOutlined,
} from '@ant-design/icons-vue'
import { Card, ConfigProvider, Space, theme, Typography } from 'ant-design-vue'
import { Prompts } from 'ant-design-x-vue'

// 定义组件 props
defineProps({
  // 示例问题列表
  exampleQuestions: {
    type: Array as () => string[],
    default: () => [
      '什么是深度保养？',
      '如何设置工业参数？',
      '如何查看设备历史维修记录？',
      '数据采集频率应该设置多少？',
    ],
  },
  // 欢迎标题
  title: {
    type: String,
    default: '您好，我是 DIPS AI 👋',
  },
  // 欢迎描述
  description: {
    type: String,
    default: '我是您的智能助手，可以帮助您解答各种问题，提供专业建议。',
  },
})

// 定义组件事件
const emit = defineEmits(['selectQuestion'])

// 处理问题点击
function handleQuestionClick(info: any) {
  emit('selectQuestion', info.data.description)
}

// 渲染标题 - 带图标的Space组件
function renderTitle(icon: any, title: string) {
  return (
    <Space align="start">
      {icon}
      <span>{title}</span>
    </Space>
  )
}

// 构建Prompts组件的items数据
const items: PromptsProps['items'] = [
  {
    key: '1',
    label: renderTitle(<FireOutlined style={{ color: '#FF4D4F' }} />, '热门问题'),
    description: '经常被问到的问题',
    children: [
      {
        key: '1-1',
        icon: <QuestionCircleOutlined />,
        description: '什么是深度保养？',
      },
      {
        key: '1-2',
        icon: <QuestionCircleOutlined />,
        description: '如何查看设备健康状态？',
      },
    ],
  },
  {
    key: '2',
    label: renderTitle(<SettingOutlined style={{ color: '#1890FF' }} />, '参数设置'),
    description: '系统参数和配置相关',
    children: [
      {
        key: '2-1',
        icon: <ToolOutlined />,
        description: '如何设置工业参数？',
      },
      {
        key: '2-2',
        icon: <ToolOutlined />,
        description: '数据采集频率应该设置多少？',
      },
    ],
  },
  {
    key: '3',
    label: renderTitle(<HistoryOutlined style={{ color: '#722ED1' }} />, '历史记录'),
    description: '记录查询和数据分析',
    children: [
      {
        key: '3-1',
        icon: <DatabaseOutlined />,
        description: '如何查看设备历史维修记录？',
      },
      {
        key: '3-2',
        icon: <DatabaseOutlined />,
        description: '如何导出历史数据报表？',
      },
    ],
  },
]
</script>

<template>
  <div class="welcome-container">
    <Card class="welcome-card">
      <div class="welcome-header">
        <div class="avatar-container">
          <img :src="robotImage" alt="DIPS AI" class="avatar" onerror="this.onerror=null; this.src='/favicon.png';">
        </div>
        <div class="welcome-text">
          <Typography.Title :level="4" style="margin-top: 0;">
            {{ title }}
          </Typography.Title>
          <p class="mb-4 mt-0 text-gray-500 font-normal">
            {{ description }}
          </p>
        </div>
      </div>
    </Card>

    <Card class="questions-container">
      <h5 class="mb-4 mt-0 text-gray-500 font-normal">
        您可以尝试问我：
      </h5>

      <ConfigProvider :theme="{ algorithm: theme.defaultAlgorithm }">
        <Prompts
          :items="items"
          wrap
          :styles="{
            item: {
              flex: 'none',
              width: 'calc(33.33% - 8px)',
              backgroundImage: 'linear-gradient(137deg, #e5f4ff 0%, #efe7ff 100%)',
              border: 0,
              marginBottom: '12px',
            },
            subItem: {
              background: 'rgba(255, 255, 255, 0.45)',
              border: '1px solid #FFF',
            },
          }"
          @item-click="handleQuestionClick"
        />
      </ConfigProvider>

      <Typography.Paragraph style="margin-top: 20px;">
        <Typography.Text type="secondary">
          您也可以使用右侧的模板功能来快速创建结构化的请求。
        </Typography.Text>
      </Typography.Paragraph>
    </Card>
  </div>
</template>

<style scoped>
.welcome-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.welcome-card {
  width: 100%;
  max-width: 700px;
  margin-bottom: 20px;
  border: none;
}

.welcome-header {
  display: flex;
  align-items: center;
}

.avatar-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  margin-right: 16px;
  overflow: hidden;
  border-radius: 50%;
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;

  /* robot-4 */

  transform: scale(1.8);

  /* robot-5 */

  /* transform: scale(1.9); */

  /* robot-7 */

  /* transform: scale(2.4); */
}

.welcome-text {
  flex: 1;
}

.questions-container {
  width: 100%;
  border: none;
}

/* 美化Prompts组件的样式 */
:deep(.ant-prompts-item) {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

:deep(.ant-prompts-item:hover) {
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  transform: translateY(-2px);
}

:deep(.ant-prompts-sub-item:hover) {
  background: rgb(255 255 255 / 75%);
}
</style>
