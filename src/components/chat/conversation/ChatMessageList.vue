<script setup lang="ts">
// import type { BubbleListProps } from 'ant-design-x-vue'
import type { VNode } from 'vue'
import type { IntermediateStep } from './ChatThoughtChain.vue'
import assistantAvatar from '@/assets/images/robot-4.gif'
import testChatData from '@/assets/test-data/chat-messages.json'
import SubjectVisual from '@/components/visual/SubjectVisual.vue'
import { checkTokenUsageStatus, getRoundTokenUsage } from '@/api/tokenUsage'
import { renderMarkdown } from '@/utils/markdownRenderer'
import {
  LOADING_MESSAGES,
  createCyclicLoadingMessage,
  createTokenStateManager,
  createTokenUsagePoller,
  identifyRounds,
  isLastAssistantMessageInRound,
  isValidUUID,
  validateTokenQueryParams,
} from '@/utils/conversationUtils'
import {
  ExclamationCircleOutlined,
  FileOutlined,
  LineChartOutlined,
  LoadingOutlined,
  SmileOutlined,
} from '@ant-design/icons-vue'
import { But<PERSON>, Flex, Space, Spin } from 'ant-design-vue'
import { Attachments, BubbleList, Prompts } from 'ant-design-x-vue'
import { computed, h, nextTick, onMounted, onUnmounted, readonly, ref, watch } from 'vue'
import ChatThoughtChain from './ChatThoughtChain.vue'

// 定义组件 props
const props = defineProps({
  chatHistory: {
    type: Array as () => ChatMessage[],
    required: true,
  },
  isHistoryLoading: {
    type: Boolean,
    default: false,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
  styles: {
    type: Object,
    default: () => ({}),
  },
  // 新增：测试模式开关
  testMode: {
    type: Boolean,
    default: false,
  },
})

// 定义组件的 emits
const emits = defineEmits([
  'suggestionClick', // suggestion 点击事件
])

// 修复类型定义，使其与测试数据兼容
interface ChatMessage {
  id?: string
  role: 'user' | 'assistant' | string
  content?: string
  createdAt?: string
  loading?: boolean
  error?: boolean
  intermediateSteps?: IntermediateStep[]

  // 新增轮次相关字段
  roundSequence?: number
  messageOrder?: number
  conversationId?: string

  // 修复：使用更宽松的类型定义以兼容测试数据
  list?: Array<{
    type: string
    content: any
  }>

  [key: string]: any
}

// 新增：Token 用量状态接口
interface TokenUsageState {
  status: 'idle' | 'loading' | 'success' | 'error'
  data: any | null
  error: string | null
  retryCount: number
  pollTimer: NodeJS.Timeout | null // 修复 timer 类型
}

// 新增：轮次信息接口
interface RoundInfo {
  roundSequence: number
  conversationId: string
  messages: ChatMessage[]
  isLastRoundMessage: boolean
}

// Define a type for the items that BubbleList will consume.
interface CustomBubbleItem {
  key: string
  role: string
  content: any
  loading?: boolean
  loadingRender?: () => VNode
  header?: VNode
  footer?: (content: any) => VNode | string // 修复footer类型不返回null
  messageRender?: (content: any) => VNode | string
  placement?: 'start' | 'end'
  avatar?: any
  styles?: any
  typing?: any
  classNames?: any
}

// 可折叠内容的状态管理
const collapsedStates = ref<Record<string, Record<string, boolean>>>({})

// Token 管理器集合，用于组件卸载时清理
const tokenManagers = new Set<ReturnType<typeof createTokenUsageManager>>()

// 切换折叠状态的函数
function toggleCollapsed(messageKey: string, itemIndex: number, detailIndex: number) {
  // 创建组合键以避免不同 collapsible 分组间的状态冲突
  const stateKey = `${itemIndex}-${detailIndex}`

  // 确保响应式追踪：使用 Vue 3 推荐的方式更新嵌套对象
  if (!collapsedStates.value[messageKey]) {
    // 使用 Vue.set 等价的方式确保响应式追踪
    collapsedStates.value = {
      ...collapsedStates.value,
      [messageKey]: {},
    }
  }

  // 确保嵌套对象的响应式更新
  const currentState = collapsedStates.value[messageKey][stateKey] ?? true
  collapsedStates.value = {
    ...collapsedStates.value,
    [messageKey]: {
      ...collapsedStates.value[messageKey],
      [stateKey]: !currentState,
    },
  }
}

// 获取折叠状态的函数
function isCollapsed(messageKey: string, itemIndex: number, detailIndex: number) {
  // 使用相同的组合键获取状态
  const stateKey = `${itemIndex}-${detailIndex}`
  const result = collapsedStates.value[messageKey]?.[stateKey] ?? true // 默认折叠
  return result
}

// 处理 suggestion 点击事件
function handleSuggestionClick(suggestionItem: any) {
  // 发射事件给父组件处理
  emits('suggestionClick', suggestionItem)
}

// 使用新的循环加载消息功能
const loadingMessage = ref(LOADING_MESSAGES[0]) // 初始化为第一条消息
const messagesRef = ref<HTMLElement | null>(null)

// 创建循环加载消息控制器
const cyclicLoader = createCyclicLoadingMessage((message: string) => {
  loadingMessage.value = message
})

// 监听加载状态变化，控制循环显示
watch(() => props.isLoading || props.isHistoryLoading, (newLoading: boolean, oldLoading: boolean) => {
  if (newLoading && !oldLoading) {
    // 开始加载时启动循环显示
    cyclicLoader.start()
  }
  else if (!newLoading && oldLoading) {
    // 停止加载时停止循环显示
    cyclicLoader.stop()
  }
})

// 监听聊天历史变化，当有新的加载消息时控制循环显示
watch(() => props.chatHistory, (newHistory: ChatMessage[]) => {
  // 检查是否有新的加载消息
  const hasLoadingMessage = newHistory.some((msg: ChatMessage) => msg.loading)
  if (hasLoadingMessage && !cyclicLoader.isActive()) {
    cyclicLoader.start()
  }
  else if (!hasLoadingMessage && cyclicLoader.isActive()) {
    cyclicLoader.stop()
  }
  nextTick(() => scrollToBottom())
}, { deep: true })

// Define roles for BubbleList. These provide default styling/avatar for 'user' and 'assistant' roles.
const rolesConfig = {
  user: {
    placement: 'end',
    avatar: { icon: '👤', style: { background: '#ffffff' } },
  },
  assistant: {
    placement: 'start',
    avatar: {
      src: assistantAvatar,
      style: {
        background: '#ffffff',
        overflow: 'hidden',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      },
      className: 'assistant-avatar',
    },
    typing: { step: 20, interval: 3 }, // Default typing for assistant messages
  },
  // 新增：文本消息类型
  text: {
    placement: 'start',
    avatar: {
      src: assistantAvatar,
      style: {
        background: '#ffffff',
        overflow: 'hidden',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      },
      className: 'assistant-avatar',
    },
    typing: { step: 20, interval: 3 },
  },
  // 新增：建议消息类型
  suggestion: {
    placement: 'start',
    avatar: { style: { visibility: 'hidden' } },
    variant: 'borderless',
    messageRender: (content: any) => {
      const suggestions = Array.isArray(content) ? content : []
      return h(Prompts, {
        vertical: true,
        items: suggestions.map((item: any) => ({
          key: item.value || item.label || item,
          icon: h(SmileOutlined, { style: { color: '#FAAD14' } }),
          description: item.label || item.value || item,
        })),
        onItemClick: (info: any) => {
          console.log('Suggestion clicked:', info)
          // 找到对应的原始 suggestion 项
          const clickedKey = info.data?.key || info.key
          const originalItem = suggestions.find((s: any) =>
            (s.value || s.label || s) === clickedKey,
          )
          console.log('Original item found:', originalItem)
          if (originalItem && originalItem.linkParams) {
            console.log('Emitting suggestion click with linkParams:', originalItem.linkParams)
            // 直接发射事件，因为在 messageRender 中无法访问组件的 handleSuggestionClick 函数
            // 我们需要通过其他方式处理这个事件
            // 临时解决方案：在全局存储事件处理器
            if ((window as any).__suggestionClickHandler) {
              (window as any).__suggestionClickHandler(originalItem.linkParams)
            }
          }
        },
      })
    },
  },
  // 新增：文件消息类型
  file: {
    placement: 'start',
    avatar: { style: { visibility: 'hidden' } },
    variant: 'borderless',
    messageRender: (content: any) => {
      const files = Array.isArray(content) ? content : []
      return h(Flex, { vertical: true, gap: 'middle' }, {
        default: () => files.map((file: any) =>
          h(Attachments.FileCard, {
            key: file.uid || file.name,
            item: {
              uid: file.uid || file.name,
              name: file.name,
              size: file.size,
              status: file.status || 'done',
              percent: file.percent,
              description: file.description,
            },
          }),
        ),
      })
    },
  },
  // 新增：可折叠消息类型
  collapsible: {
    placement: 'start',
    avatar: { style: { visibility: 'hidden' } },
    variant: 'borderless',
  },
} as const

// 修复轮次识别逻辑中的类型转换
const identifiedRounds = computed(() => {
  let chatHistory = props.testMode ? testChatData : props.chatHistory

  // 处理后端返回的包装数据结构
  if (chatHistory && typeof chatHistory === 'object' && 'data' in chatHistory) {
    chatHistory = (chatHistory as any).data
  }

  if (!chatHistory || chatHistory.length === 0) {
    return new Map<number, RoundInfo>()
  }

  // 类型转换以确保兼容性，使用类型断言
  const messages = chatHistory.map((msg: any) => ({
    ...msg,
    list: msg.list?.map((item: any) => ({
      type: item.type as any, // 使用类型断言绕过严格检查
      content: item.content,
    })),
  })) as any[]

  return identifyRounds(messages)
})

// 修复类似的类型转换问题
function isLastAssistantInRound(message: ChatMessage): boolean {
  let chatHistory = props.testMode ? testChatData : props.chatHistory

  // 处理后端返回的包装数据结构
  if (chatHistory && typeof chatHistory === 'object' && 'data' in chatHistory) {
    chatHistory = (chatHistory as any).data
  }

  if (!chatHistory || chatHistory.length === 0) {
    return false
  }

  // 类型转换以确保兼容性
  const messages = chatHistory.map((msg: any) => ({
    ...msg,
    list: msg.list?.map((item: any) => ({
      type: item.type as any,
      content: item.content,
    })),
  })) as any[]

  return isLastAssistantMessageInRound(message as any, messages)
}

// 获取消息的轮次信息
function _getMessageRoundInfo(message: ChatMessage): RoundInfo | null {
  const rounds = identifiedRounds.value
  return rounds.get(message.roundSequence || 1) || null
}

// 本地 Token 用量格式化函数（避免命名冲突）
function formatTokenUsageDisplay(data: any): string {
  if (!data) {
    return ''
  }

  const inputTokens = data.inputTokens || 0
  const outputTokens = data.outputTokens || 0
  const totalCost = data.totalCost || '0.00'

  return `输入: ${inputTokens.toLocaleString()} · 输出: ${outputTokens.toLocaleString()} · 费用: ¥${totalCost}`
}

// 创建 Token 用量 Footer
function createTokenUsageFooter(message: ChatMessage, tokenState: TokenUsageState) {
  return (content: any) => {
    const { status, data, error } = tokenState

    // 根据状态显示不同内容
    if (status === 'loading') {
      return h(Space, { size: 'small', style: { fontSize: '12px', color: '#666' } }, {
        default: () => [
          h(LoadingOutlined, { spin: true }),
          h('span', '正在计算Token用量...')
        ]
      })
    }

    if (status === 'error') {
      return h(Space, { size: 'small', style: { fontSize: '12px', color: '#ff4d4f' } }, {
        default: () => [
          h(ExclamationCircleOutlined),
          h('span', error || 'Token用量获取失败')
        ]
      })
    }

    if (status === 'success' && data) {
      return h(Space, { size: 'small', style: { fontSize: '12px', color: '#666' } }, {
        default: () => [
          h(LineChartOutlined, { style: { color: '#52c41a' } }),
          h('span', formatTokenUsageDisplay(data))
        ]
      })
    }

    // 默认返回空字符串而不是null
    return ''
  }
}

// Token 状态管理器（不使用 Vue 生命周期钩子）
function createTokenUsageManager(message: ChatMessage) {
  const state = ref<TokenUsageState>({
    status: 'idle',
    data: null,
    error: null,
    retryCount: 0,
    pollTimer: null
  })

  // 开始查询Token用量
  const startQuery = async () => {
    if (!message.conversationId || !message.roundSequence) {
      console.warn('缺少必要的参数进行Token用量查询:', message)
      return
    }

    state.value.status = 'loading'
    state.value.error = null

    try {
      const tokenUsage = await getRoundTokenUsage(
        message.conversationId,
        message.roundSequence
      )

      if (tokenUsage.status === 'PENDING') {
        startPolling()
      } else {
        state.value.status = 'success'
        state.value.data = tokenUsage
      }
    } catch (error: any) {
      console.error('Token用量查询失败:', error)
      state.value.status = 'error'
      state.value.error = error.message
    }
  }

  // 开始轮询
  const startPolling = () => {
    if (state.value.pollTimer) {
      clearInterval(state.value.pollTimer)
    }

    state.value.pollTimer = setInterval(async () => {
      try {
        const result = await checkTokenUsageStatus(
          message.conversationId!,
          message.roundSequence!
        )

        if (result.isCompleted) {
          stopPolling()

          if (result.status === 'SUCCESS' && result.data) {
            state.value.status = 'success'
            state.value.data = result.data
          } else {
            state.value.status = 'error'
            state.value.error = '计算失败'
          }
        }
      } catch (error: any) {
        console.error('轮询Token状态失败:', error)
        state.value.retryCount++

        // 重试次数过多时停止轮询
        if (state.value.retryCount >= 10) {
          stopPolling()
          state.value.status = 'error'
          state.value.error = '查询超时'
        }
      }
    }, 5000) as NodeJS.Timeout
  }

  // 停止轮询
  const stopPolling = () => {
    if (state.value.pollTimer) {
      clearInterval(state.value.pollTimer)
      state.value.pollTimer = null
    }
  }

  // 手动清理资源
  const cleanup = () => {
    stopPolling()
  }

  return {
    state: readonly(state),
    startQuery,
    stopPolling,
    cleanup
  }
}

// 使用计算属性确保 loadingMessage 变化时重新计算
const formattedHistory = computed(() => {
  // 根据测试模式决定使用哪个数据源
  let chatHistory = props.testMode ? testChatData : props.chatHistory

  // 处理后端返回的包装数据结构
  if (chatHistory && typeof chatHistory === 'object' && 'data' in chatHistory) {
    chatHistory = (chatHistory as any).data
  }

  if (!chatHistory || chatHistory.length === 0) {
    return []
  }

  // 使用 flatMap 处理消息，包括加载状态和多类型消息
  const bubbleListItems = chatHistory.flatMap((message: any, index: number, arr: any[]) => {
    const messageKey = message.id || `msg-${Date.now()}-${Math.random()}`
    const roleStr = typeof message.role === 'string' ? message.role.toLowerCase() : 'assistant'
    const isUser = roleStr === 'user' || roleStr.includes('user')
    const currentRole: 'user' | 'assistant' = isUser ? 'user' : 'assistant'

    // 检查是否为轮次中的最后一条助手消息，如果是则需要显示Token用量
    const shouldShowTokenUsage = !isUser && isLastAssistantInRound(message)

    // 为助手消息创建Token状态管理
    let tokenUsageManager: ReturnType<typeof createTokenUsageManager> | null = null
    let tokenValidation: { isValid: boolean; error?: string } | null = null

    if (shouldShowTokenUsage) {
      // 验证Token查询所需的参数
      tokenValidation = validateTokenQueryParams(message.conversationId, message.roundSequence)

      if (tokenValidation.isValid) {
        // 参数有效，创建Token管理器
        const messageWithRoundInfo = {
          ...message,
          conversationId: message.conversationId!,
          roundSequence: message.roundSequence!
        }

        tokenUsageManager = createTokenUsageManager(messageWithRoundInfo)
        tokenManagers.add(tokenUsageManager) // 添加到管理器集合

        // 消息加载完成后触发Token查询
        nextTick(() => {
          if (!message.loading && tokenUsageManager) {
            tokenUsageManager.startQuery()
          }
        })
      } else {
        // 参数无效，记录警告但不阻止消息显示
        console.warn(`Token查询参数无效，跳过Token用量查询: ${tokenValidation.error}`, {
          conversationId: message.conversationId,
          roundSequence: message.roundSequence,
          messageId: message.id
        })
      }
    }

    // 处理加载状态的气泡
    if (message.loading) {
      return {
        role: 'assistant',
        key: `${messageKey}-loading`,
        placement: 'start',
        avatar: rolesConfig.assistant.avatar,
        loading: true,
        loadingRender: () => h(Space, {}, {
          default: () => [
            h(Spin, { size: 'small' }),
            loadingMessage.value, // 这里会响应 loadingMessage 的变化
          ],
        }),
        classNames: { content: 'loading-message' },
      } as CustomBubbleItem
    }

    // 检查是否有多类型内容列表
    if (message.list && message.list.length > 0) {
      // 分离不同类型的内容
      const textAndCollapsibleItems = message.list.filter((item: any) =>
        item.type === 'text' || item.type === 'collapsible',
      )
      const independentItems = message.list.filter((item: any) =>
        item.type === 'suggestion' || item.type === 'file',
      )

      const bubbles: CustomBubbleItem[] = []
      const isSameRoleAsPrevious = index > 0 && arr[index - 1].role === message.role
      const placement = isUser ? 'end' : 'start'

      // 1. 处理主气泡（text + collapsible 内容）
      if (textAndCollapsibleItems.length > 0) {
        const mainAvatarConfig = !isSameRoleAsPrevious
          ? (isUser ? rolesConfig.user.avatar : rolesConfig.assistant.avatar)
          : { style: { visibility: 'hidden' } }

        const mainBubble: CustomBubbleItem = {
          key: `${messageKey}-main`,
          role: isUser ? 'user' : 'assistant',
          content: textAndCollapsibleItems,
          placement,
          avatar: mainAvatarConfig,
          styles: message.agentType === 'visual' || message.list[0].content.indexOf('layout') > -1
            ? { content: { width: '90%', height: '720px' } }
            : { content: { maxWidth: isUser ? '70%' : '80%' } },
        }

        // 为主气泡添加自定义渲染逻辑
        if (isUser) {
          // 用户消息的自定义渲染逻辑
          mainBubble.messageRender = (contentToRender) => {
            const items = Array.isArray(contentToRender) ? contentToRender : []

            return h('div', { class: 'user-message-container' }, [
              // 渲染所有 text 内容
              ...items.map((item: any, itemIndex: number) => {
                if (item.type === 'text') {
                  return h('div', {
                    key: `text-${itemIndex}`,
                    innerHTML: renderMarkdown(String(item.content)),
                    class: 'markdown-content',
                    style: { marginBottom: itemIndex < items.length - 1 ? '12px' : '0' },
                  })
                }
                return null
              }).filter(Boolean),
            ])
          }
        }
        else if (!isUser) {
          mainBubble.messageRender = (contentToRender) => {
            const items = Array.isArray(contentToRender) ? contentToRender : []

            return h('div', { class: 'assistant-message-container' }, [
              // 如果有思维链，先渲染思维链
              message.intermediateSteps && message.intermediateSteps.length > 0
                ? h(ChatThoughtChain, {
                    steps: message.intermediateSteps || [],
                    messageKey,
                  })
                : null,

              // 渲染所有 text 和 collapsible 内容
              ...items.map((item: any, itemIndex: number) => {
                if (item.type === 'text') {
                  return message.agentType === 'visual' || item.content.indexOf('layout') > -1
                    ? h(SubjectVisual, {
                        visualData: item.content,
                      })
                    : h('div', {
                        key: `text-${itemIndex}`,
                        innerHTML: renderMarkdown(String(item.content)),
                        class: 'markdown-content',
                        style: { marginBottom: itemIndex < items.length - 1 ? '12px' : '0' },
                      })
                }
                else if (item.type === 'collapsible') {
                  const { summary, details } = item.content

                  return h('div', {
                    key: `collapsible-${itemIndex}`,
                    class: 'collapsible-content',
                    style: { marginTop: '6px' },
                  }, [
                    // 主要内容（summary）- 始终显示
                    summary
                      ? h('div', {
                          class: 'collapsible-summary',
                          style: {
                            fontWeight: '500',
                            marginBottom: '6px',
                            fontSize: '15px',
                          },
                        }, summary)
                      : null,

                    // 可折叠的详细内容
                    ...(details || []).map((detail: any, detailIndex: number) => {
                      const isExpandable = detail.expandable !== false
                      const collapsed = isCollapsed(messageKey, itemIndex, detailIndex)

                      return h('div', {
                        key: `detail-${detailIndex}`,
                        class: 'collapsible-detail',
                        style: { marginBottom: '8px' },
                      }, [
                        // 根据expandable配置决定渲染方式
                        isExpandable
                          ? h(Button, {
                              type: 'text',
                              size: 'small',
                              onClick: () => toggleCollapsed(messageKey, itemIndex, detailIndex),
                              style: {
                                padding: '4px 8px',
                                height: 'auto',
                                marginBottom: '4px',
                                color: '#6B46C1',
                                fontWeight: '0',
                              },
                            }, {
                              default: () => [
                                h('span', { style: { marginRight: '4px' } }, collapsed ? '▶' : '▼'),
                                h('span', {
                                  innerHTML: renderMarkdown(detail.title),
                                }),
                              ],
                            })
                          : h('div', {
                              style: {
                                padding: '4px 8px',
                                marginBottom: '4px',
                                fontWeight: '0',
                                color: '#000000',
                                fontSize: '14px',
                              },
                              innerHTML: renderMarkdown(detail.title),
                            }),

                        // 可折叠的内容
                        isExpandable && !collapsed
                          ? h('div', {
                              class: 'collapsible-detail-content',
                              style: {
                                paddingLeft: '16px',
                                borderLeft: '3px solid #f0f0f0',
                                marginLeft: '8px',
                              },
                              innerHTML: renderMarkdown(detail.content),
                            })
                          : null,
                      ])
                    }),
                  ])
                }
                return null
              }).filter(Boolean),
            ])
          }

          // 为助手主气泡添加打字效果
          mainBubble.typing = rolesConfig.assistant?.typing
        }

        // 为主气泡添加footer（仅限助手消息的轮次最后一条）
        if (shouldShowTokenUsage) {
          if (tokenUsageManager) {
            mainBubble.footer = createTokenUsageFooter(message, tokenUsageManager.state.value)
          } else {
            // 当Token查询参数无效时，显示提示信息
            const validation = tokenValidation || validateTokenQueryParams(message.conversationId, message.roundSequence)
            mainBubble.footer = () => h(Space, {
              size: 'small',
              style: { fontSize: '12px', color: '#999' }
            }, {
              default: () => [
                h('span', `Token用量暂不可用: ${validation.error || '数据不完整'}`)
              ]
            })
          }
        }

        bubbles.push(mainBubble)
      }

      // 2. 处理独立气泡（suggestion 和 file）
      independentItems.forEach((listItem: any, listIndex: number) => {
        const isFirstIndependent = listIndex === 0 && textAndCollapsibleItems.length === 0

        const avatarConfig = isFirstIndependent && !isSameRoleAsPrevious
          ? rolesConfig[listItem.type as keyof typeof rolesConfig]?.avatar || rolesConfig.assistant.avatar
          : { style: { visibility: 'hidden' } }

        const bubbleItem: CustomBubbleItem = {
          key: `${messageKey}-${listItem.type}-${listIndex}`,
          role: isUser ? 'user' : listItem.type,
          content: listItem.content,
          placement,
          avatar: avatarConfig,
          styles: { content: { maxWidth: isUser ? '70%' : '80%' } },
        }

        bubbles.push(bubbleItem)
      })

      return bubbles
    }

    // 处理常规消息（向后兼容）
    const isSameRoleAsPrevious = index > 0 && arr[index - 1].role === message.role
    const avatarConfig = isSameRoleAsPrevious
      ? { styles: { avatar: { visibility: 'hidden' } } }
      : rolesConfig[currentRole]?.avatar

    const formattedMessage: CustomBubbleItem = {
      key: messageKey,
      role: currentRole,
      content: message.content || '',
      placement: rolesConfig[currentRole]?.placement,
      avatar: avatarConfig,
      styles: { content: { maxWidth: currentRole === 'assistant' ? '80%' : '70%' } },
    }

    if (currentRole === 'assistant') {
      formattedMessage.typing = rolesConfig.assistant?.typing

      // 修改渲染方式，将思维链和消息内容作为整体渲染
      formattedMessage.messageRender = (contentToRender) => {
        const hasThoughtChain = message.intermediateSteps && message.intermediateSteps.length > 0

        return h('div', { class: 'assistant-message-container' }, [
          // 如果有思维链数据，先渲染思维链组件
          hasThoughtChain
            ? h(ChatThoughtChain, {
                steps: message.intermediateSteps || [],
                messageKey,
              })
            : null,

          // 渲染实际消息内容
          h('div', {
            innerHTML: renderMarkdown(String(contentToRender)),
            class: 'markdown-content',
          }),
        ])
      }

      // 为助手消息添加footer（仅限轮次最后一条）
      if (shouldShowTokenUsage) {
        if (tokenUsageManager) {
          formattedMessage.footer = createTokenUsageFooter(message, tokenUsageManager.state.value)
        } else {
          // 当Token查询参数无效时，显示提示信息
          const validation = validateTokenQueryParams(message.conversationId, message.roundSequence)
          formattedMessage.footer = () => h(Space, { 
            size: 'small', 
            style: { fontSize: '12px', color: '#999' } 
          }, {
            default: () => [
              h('span', `Token用量暂不可用: ${validation.error || '数据不完整'}`)
            ]
          })
        }
      }
    }

    return formattedMessage
  })

  // 如果当前正在加载且没有加载消息在历史中，添加一个加载气泡
  if ((props.isLoading || props.isHistoryLoading) && !chatHistory.some((msg: any) => msg.loading)) {
    bubbleListItems.push({
      role: 'assistant',
      key: 'assistant-loading',
      placement: 'start',
      avatar: rolesConfig.assistant.avatar,
      loading: true,
      loadingRender: () => h(Space, {}, {
        default: () => [
          h(Spin, { size: 'small' }),
          loadingMessage.value,
        ],
      }),
      classNames: { content: 'loading-message' },
    } as CustomBubbleItem)
  }

  return bubbleListItems
})

onMounted(() => {
  // 设置全局事件处理器，用于在 messageRender 中调用
  ;(window as any).__suggestionClickHandler = handleSuggestionClick
  scrollToBottom()
})

// 组件卸载时清理定时器和全局事件处理器
onUnmounted(() => {
  cyclicLoader.cleanup()
  
  // 清理所有 Token 管理器
  tokenManagers.forEach(manager => {
    manager.cleanup()
  })
  tokenManagers.clear()
  
  // 清理全局事件处理器
  delete (window as any).__suggestionClickHandler
})

function scrollToBottom() {
  if (messagesRef.value) {
    const container = messagesRef.value
    container.scrollTop = container.scrollHeight
  }
}
</script>

<template>
  <div ref="messagesRef" class="chat-messages" :class="styles.messagesContainer">
    <BubbleList
      :items="formattedHistory"
      :roles="rolesConfig"
    />

    <!-- 滚动到底部按钮可以在这里添加 -->
  </div>
</template>

<style scoped>
.chat-messages {
  height: 100%;
  padding: 12px 16px;
  overflow-y: auto;
}

/* 可以添加更多自定义样式 */
</style>

<style>
/* 新增样式，确保思维链和消息内容的布局正确 */
.assistant-message-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-message-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ant-bubble .ant-bubble-content-filled {
  background-color: #fff;
  border: 1px solid rgb(220 220 220);
  box-shadow: 4px 4px 8px rgb(0 0 0 / 10%);
}

/* 增加气泡之间的间距 */
.ant-bubble-list .ant-bubble {
  margin-bottom: 16px;
}

/* 确保最后一个气泡也有足够的底部间距 */
.ant-bubble-list .ant-bubble:last-child {
  margin-bottom: 24px;
}

/* 助手头像样式 */
.assistant-avatar img,
.ant-avatar.assistant-avatar img,
[class*="assistant-avatar"] img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  transform: scale(1.8) !important;
}

/* 更通用的助手头像样式选择器 */
.ant-bubble-list .ant-bubble[data-role="assistant"] .ant-avatar img,
.ant-bubble-list .ant-bubble .ant-avatar img[src*="robot"] {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  transform: scale(1.8) !important;
}

/* Markdown内容样式 */
.markdown-content {
  font-size: 14px;
  line-height: 1.6;
  word-break: break-word;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-content h1 {
  font-size: 1.5em;
}

.markdown-content h2 {
  font-size: 1.35em;
}

.markdown-content h3 {
  font-size: 1.25em;
}

.markdown-content p {
  min-height: 1.2em; /* 确保空段落也有最小高度 */
  margin-top: 0;
  margin-bottom: 8px;
}

/* 空段落样式 - 确保包含 &nbsp; 的段落显示为空行 */
.markdown-content p:empty {
  min-height: 1.2em;
  margin: 8px 0;
}

.markdown-content blockquote {
  padding: 0 8px;
  margin: 8px 0;
  color: #6a737d;
  border-left: 4px solid #e8e8e8;
}

.markdown-content pre {
  padding: 12px;
  margin: 8px 0;
  overflow-x: auto;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.markdown-content code {
  padding: 2px 4px;
  font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, Courier, monospace;
  font-size: 90%;
  background-color: rgb(0 0 0 / 5%);
  border-radius: 3px;
}

.markdown-content pre code {
  padding: 0;
  font-size: 100%;
  background-color: transparent;
  border-radius: 0;
}

.markdown-content a {
  color: #1677ff;
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

.markdown-content table {
  width: 100%;
  margin: 8px 0;
  border-collapse: collapse;
}

.markdown-content table th,
.markdown-content table td {
  padding: 8px;
  text-align: left;
  border: 1px solid #e8e8e8;
}

.markdown-content table th {
  font-weight: 500;
  background-color: #fafafa;
}

.markdown-content img {
  max-width: 100%;
  height: auto;
  margin: 8px 0;
}

.markdown-content ul,
.markdown-content ol {
  padding-left: 20px;
  margin: 8px 0;
}

.markdown-content li {
  margin-bottom: 4px;
}

.markdown-content hr {
  height: 1px;
  padding: 0;
  margin: 16px 0;
  background-color: #e8e8e8;
  border: 0;
}

/* 加载消息样式 */
.loading-message {
  font-style: italic;
  color: #666;
}

/* 可折叠内容样式 */
.collapsible-content {
  width: 100%;
}

.collapsible-summary {
  line-height: 1.5;
  color: #6b46c1;
}

.collapsible-detail {
  border-radius: 6px;
  transition: all 0.2s ease;
}

.collapsible-detail:hover {
  /* background-color: #fafafa; */
}

.collapsible-detail-content {
  padding: 8px;
  margin-top: 4px;
  font-size: 14px;
  line-height: 1.6;

  /* background-color: #fafafa; */
  border-radius: 4px;
}

.collapsible-detail-content h1,
.collapsible-detail-content h2,
.collapsible-detail-content h3,
.collapsible-detail-content h4,
.collapsible-detail-content h5,
.collapsible-detail-content h6 {
  margin-top: 8px;
  margin-bottom: 4px;
  font-weight: 600;
}

.collapsible-detail-content p {
  margin-bottom: 4px;
}

.collapsible-detail-content pre {
  padding: 8px;
  margin: 4px 0;
  overflow-x: auto;
  background-color: #f0f0f0;
  border-radius: 4px;
}

.collapsible-detail-content code {
  padding: 2px 4px;
  font-family: "Courier New", monospace;
  background-color: #f0f0f0;
  border-radius: 3px;
}

.collapsible-detail-content pre code {
  padding: 0;
  background-color: transparent;
}
</style>
