<script setup lang="tsx">
import type { ConversationsProps } from 'ant-design-x-vue'
import * as conversationApi from '@/api/modules/conversation'
import { CommentOutlined } from '@ant-design/icons-vue'
import { Space } from 'ant-design-vue'
import { Conversations } from 'ant-design-x-vue'
import dayjs from 'dayjs'
import { ElMessage, ElSkeleton, ElSlider } from 'element-plus'
import { computed, onMounted, ref } from 'vue'

const props = defineProps<{
  sideWidth?: number
}>()

const emit = defineEmits<{
  (e: 'conversationChange', key: string): void
  (e: 'widthChange', width: number): void
}>()

// 历史会话列表
const conversationList = ref<ConversationsProps['items']>([])
const isConversationsLoading = ref(false)
const activeConversation = ref('')
const sideWidth = ref(props.sideWidth || 300)

// 获取历史会话
async function fetchConversations() {
  try {
    isConversationsLoading.value = true
    // 调用会话模块的 getConversations 函数获取会话列表
    const response = await conversationApi.getConversations()

    // 处理响应数据：支持ApiResponse格式和直接数组格式
    let conversationsData = []

    if (Array.isArray(response)) {
      // 直接返回数组格式
      conversationsData = response
    }
    else if (response && response.status === 1 && response.data) {
      // ApiResponse格式：{status: 1, data: [...]}
      conversationsData = Array.isArray(response.data) ? response.data : []
    }
    else if (response && response.data && Array.isArray(response.data)) {
      // 旧格式兼容：{data: [...]}
      conversationsData = response.data
    }
    else {
      // 其他情况，使用空数组
      conversationsData = []
    }

    // 转换接口数据为Conversations组件需要的格式，并添加日期分组
    conversationList.value = conversationsData.map((item: any) => {
      // 获取时间戳，如果不存在则使用当前时间
      const timestamp = item.timestamp || Date.now()

      // 根据时间戳确定分组：今天、昨天或具体日期
      const date = dayjs(timestamp)
      const today = dayjs()
      const yesterday = today.subtract(1, 'day')

      let group = date.format('YYYY-MM-DD') // 默认使用日期格式

      // 如果是今天或昨天，使用特定标签
      if (date.isSame(today, 'day')) {
        group = '今天'
      }
      else if (date.isSame(yesterday, 'day')) {
        group = '昨天'
      }

      return {
        key: item.id,
        label: item.label,
        timestamp,
        group,
      }
    })
  }
  catch (error) {
    console.error('获取历史会话失败', error)
    // 显示错误通知
    ElMessage.error('获取历史会话失败，请稍后重试')
    // 设置一个空数组作为降级处理
    conversationList.value = []
  }
  finally {
    isConversationsLoading.value = false
  }
}

// 会话选择变更处理
function handleConversationChange(key: string) {
  activeConversation.value = key
  emit('conversationChange', key)
}

// 初始化时获取会话列表
onMounted(() => {
  fetchConversations()
})

// 会话容器样式
const conversationsStyle = computed(() => ({
  height: 'calc(100% - 60px)',
  width: '100%',
  background: 'var(--el-bg-color)',
  borderRadius: 'var(--el-border-radius-base)',
}))

// 配置分组属性
const groupable: ConversationsProps['groupable'] = {
  // 排序函数，确保"今天"在最前，"昨天"次之，其他按日期倒序排序
  sort(a, b) {
    if (a === b) {
      return 0
    }
    if (a === '今天') {
      return -1
    }
    if (b === '今天') {
      return 1
    }
    if (a === '昨天') {
      return -1
    }
    if (b === '昨天') {
      return 1
    }
    // 其他日期按时间倒序排列（最新的日期在前）
    return b.localeCompare(a)
  },
  // 自定义分组标题渲染
  title: (group, { components: { GroupTitle } }) =>
    group
      ? (
          <GroupTitle>
            <Space align="center" style="gap: 8px;">
              <CommentOutlined style="display: inline-flex; vertical-align: middle;" />
              <span style="vertical-align: middle;">{group}</span>
            </Space>
          </GroupTitle>
        )
      : (
          <GroupTitle />
        ),
}

// 侧边栏宽度变化处理
function handleWidthChange(value: number | number[]) {
  // 如果是数组，取第一个值；否则直接使用该值
  const newWidth = Array.isArray(value) ? value[0] : value
  sideWidth.value = newWidth
  emit('widthChange', newWidth)
}

// 暴露方法给父组件
defineExpose({
  fetchConversations,
  activeConversation,
})
</script>

<template>
  <div class="h-full flex flex-col space-y-2">
    <div class="flex items-center justify-between">
      <h2>历史会话</h2>
      <FaPopover>
        <FaButton variant="ghost" class="group">
          <FaIcon name="ix:width" class="size-6 opacity-10 transition-opacity group-hover:opacity-100" />
        </FaButton>

        <template #panel>
          <div class="h-10 w-60 flex items-center justify-center">
            <ElSlider v-model="sideWidth" :step="10" :min="250" :max="550" @input="handleWidthChange" />
          </div>
        </template>
      </FaPopover>
    </div>
    <hr>

    <!-- 历史会话列表 -->
    <div class="flex-1 overflow-hidden">
      <div v-if="isConversationsLoading" class="h-full flex items-center justify-center">
        <ElSkeleton :rows="5" animated />
      </div>
      <div v-else-if="conversationList?.length === 0" class="h-full flex items-center justify-center text-gray-400">
        暂无历史会话
      </div>
      <Conversations
        v-else
        :items="conversationList"
        :active-key="activeConversation"
        :style="conversationsStyle"
        :groupable="groupable"
        @active-change="handleConversationChange"
      />
    </div>
  </div>
</template>
