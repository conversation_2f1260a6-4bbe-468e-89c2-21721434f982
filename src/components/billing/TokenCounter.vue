<script setup lang="ts">
import { useBillingStore } from '@/stores/billing'
import { formatCurrency, formatTokens } from '@/utils/format'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { computed } from 'vue'

interface Props {
  inputTokens?: number
  outputTokens?: number
  thoughtChainTokens?: number
}

const props = withDefaults(defineProps<Props>(), {
  inputTokens: 0,
  outputTokens: 0,
  thoughtChainTokens: 0,
})

const billingStore = useBillingStore()

// 计算属性
const currentBalance = computed(() => billingStore.balance)
const currentConfig = computed(() => billingStore.currentConfig)

const totalTokens = computed(() => {
  return props.inputTokens + props.outputTokens + props.thoughtChainTokens
})

const estimatedCost = computed(() => {
  if (!currentConfig.value) {
    return 0
  }

  const inputCost = props.inputTokens * currentConfig.value.inputTokenPrice
  const outputCost = props.outputTokens * currentConfig.value.outputTokenPrice
  const thoughtChainCost = props.thoughtChainTokens * (currentConfig.value.thoughtChainTokenPrice || 0)

  return inputCost + outputCost + thoughtChainCost
})

const formattedCost = computed(() => {
  return formatCurrency(estimatedCost.value, 'CNY')
})

const isBalanceSufficient = computed(() => {
  return currentBalance.value >= estimatedCost.value
})

const costClass = computed(() => ({
  'cost-normal': isBalanceSufficient.value,
  'cost-warning': !isBalanceSufficient.value,
}))

const balanceClass = computed(() => ({
  'balance-normal': currentBalance.value > 10,
  'balance-warning': currentBalance.value <= 10 && currentBalance.value > 1,
  'balance-critical': currentBalance.value <= 1,
}))

const showWarning = computed(() => {
  return !isBalanceSufficient.value || currentBalance.value <= 10
})

const warningMessage = computed(() => {
  if (!isBalanceSufficient.value) {
    return '余额不足，无法完成此次请求'
  }
  if (currentBalance.value <= 1) {
    return '余额严重不足，请立即充值'
  }
  if (currentBalance.value <= 10) {
    return '余额偏低，建议及时充值'
  }
  return ''
})
</script>

<template>
  <div class="token-counter">
    <!-- Token 统计 -->
    <div class="token-stats">
      <div class="stat-item">
        <span class="label">输入Token:</span>
        <span class="value">{{ formatTokens(inputTokens) }}</span>
      </div>
      <div class="stat-item">
        <span class="label">输出Token:</span>
        <span class="value">{{ formatTokens(outputTokens) }}</span>
      </div>
      <div class="stat-item">
        <span class="label">总Token:</span>
        <span class="value">{{ formatTokens(totalTokens) }}</span>
      </div>
    </div>

    <!-- 费用预估 -->
    <div class="cost-section">
      <div class="cost-item">
        <span class="label">预估费用:</span>
        <span class="value" :class="costClass">
          {{ formattedCost }}
        </span>
      </div>
    </div>

    <!-- 余额状态 -->
    <div class="balance-section">
      <div class="balance-item">
        <span class="label">当前余额:</span>
        <span class="value" :class="balanceClass">
          {{ formatCurrency(currentBalance, 'CNY') }}
        </span>
      </div>
    </div>

    <!-- 警告信息 -->
    <div v-if="showWarning" class="warning-section">
      <div class="warning-item">
        <ExclamationCircleOutlined />
        {{ warningMessage }}
      </div>
    </div>
  </div>
</template>

<style scoped>
.token-counter {
  padding: 12px;
  font-size: 12px;
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
}

.token-stats,
.cost-section,
.balance-section {
  margin-bottom: 8px;
}

.token-stats:last-child,
.cost-section:last-child,
.balance-section:last-child {
  margin-bottom: 0;
}

.stat-item,
.cost-item,
.balance-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.stat-item:last-child,
.cost-item:last-child,
.balance-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
}

.value {
  font-weight: 500;
}

.cost-normal,
.balance-normal {
  color: #52c41a;
}

.cost-warning,
.balance-warning {
  color: #faad14;
}

.balance-critical {
  color: #ff4d4f;
}

.warning-section {
  padding: 6px;
  margin-top: 8px;
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 4px;
}

.warning-item {
  display: flex;
  gap: 4px;
  align-items: center;
  font-size: 11px;
  color: #faad14;
}
</style>
