<script setup lang="ts">
import { useBillingStore } from '@/stores/billing'
import { formatCurrency } from '@/utils/format'
import {
  ExclamationCircleOutlined,
  PlusOutlined,
  ReloadOutlined,
  WarningOutlined,
} from '@ant-design/icons-vue'
import { computed, onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'

// Props
interface Props {
  size?: 'small' | 'default' | 'large'
  showActions?: boolean
  showStatus?: boolean
  autoRefresh?: boolean
  refreshInterval?: number // 秒
}

const props = withDefaults(defineProps<Props>(), {
  size: 'default',
  showActions: true,
  showStatus: true,
  autoRefresh: false,
  refreshInterval: 30,
})

// Emits
const emit = defineEmits<{
  'recharge-click': []
  'balance-change': [oldBalance: number, newBalance: number]
}>()

// 路由
const router = useRouter()

// Store
const billingStore = useBillingStore()

// 响应式状态
const isRefreshing = ref(false)
const isAnimating = ref(false)
const showBalanceChangeAlert = ref(false)
const balanceChangeMessage = ref('')
const previousBalance = ref(0)
const refreshTimer = ref<NodeJS.Timeout | null>(null)
const error = ref<string | null>(null)
const retryCount = ref(0)
const maxRetries = 3

// 计算属性
const balance = computed(() => billingStore.balance)
const currency = computed(() => billingStore.currency)
const balanceStatus = computed(() => billingStore.balanceStatus)
const accountStatus = computed(() => billingStore.accountStatus)

const formattedBalance = computed(() => {
  return formatCurrency(balance.value, currency.value)
})

const balanceStatusClass = computed(() => {
  return {
    'balance-normal': balanceStatus.value === 'normal',
    'balance-warning': balanceStatus.value === 'warning',
    'balance-critical': balanceStatus.value === 'critical',
    [`balance-${props.size}`]: true,
  }
})

const badgeStatus = computed(() => {
  const statusMap = {
    normal: 'success' as const,
    warning: 'warning' as const,
    critical: 'error' as const,
  }
  return statusMap[balanceStatus.value]
})

const statusText = computed(() => {
  const textMap = {
    normal: '余额充足',
    warning: '余额不足',
    critical: '余额严重不足',
  }
  return textMap[balanceStatus.value]
})

const warningText = computed(() => {
  if (balanceStatus.value === 'warning') {
    return '建议及时充值以确保服务正常使用'
  }
  if (balanceStatus.value === 'critical') {
    return '余额过低，请立即充值避免服务中断'
  }
  return ''
})

// 错误处理方法
function clearError() {
  error.value = null
  retryCount.value = 0
}

async function handleWithRetry<T>(
  operation: () => Promise<T>,
  operationName: string,
): Promise<T | null> {
  try {
    clearError()
    const result = await operation()
    return result
  }
  catch (err: any) {
    console.error(`${operationName}失败:`, err)

    if (retryCount.value < maxRetries) {
      retryCount.value++
      error.value = `${operationName}失败，正在重试... (${retryCount.value}/${maxRetries})`

      // 延迟重试
      await new Promise(resolve => setTimeout(resolve, 1000 * retryCount.value))
      return handleWithRetry(operation, operationName)
    }
    else {
      error.value = `${operationName}失败: ${err.message || '网络错误，请稍后重试'}`
      return null
    }
  }
}

// 方法
async function handleRefresh() {
  if (isRefreshing.value) { return }

  isRefreshing.value = true
  try {
    await handleWithRetry(
      () => billingStore.refreshBalance(),
      '刷新余额',
    )
  }
  finally {
    isRefreshing.value = false
  }
}

function handleQuickRecharge() {
  emit('recharge-click')
  router.push('/billing/recharge')
}

function handleViewHistory() {
  router.push('/billing/history')
}

function triggerBalanceAnimation() {
  isAnimating.value = true
  setTimeout(() => {
    isAnimating.value = false
  }, 600)
}

function showBalanceChange(oldBalance: number, newBalance: number) {
  const change = newBalance - oldBalance
  if (Math.abs(change) > 0.01) {
    const changeText = change > 0 ? `+${formatCurrency(change, currency.value)}` : formatCurrency(change, currency.value)
    balanceChangeMessage.value = `余额变更：${changeText}`
    showBalanceChangeAlert.value = true

    setTimeout(() => {
      hideBalanceChangeAlert()
    }, 5000)
  }
}

function hideBalanceChangeAlert() {
  showBalanceChangeAlert.value = false
  balanceChangeMessage.value = ''
}

function startAutoRefresh() {
  if (props.autoRefresh && props.refreshInterval > 0) {
    refreshTimer.value = setInterval(() => {
      handleRefresh()
    }, props.refreshInterval * 1000)
  }
}

function stopAutoRefresh() {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

// 监听余额变化
watch(balance, (newBalance, oldBalance) => {
  if (oldBalance !== undefined && oldBalance !== newBalance) {
    triggerBalanceAnimation()
    showBalanceChange(oldBalance, newBalance)
    emit('balance-change', oldBalance, newBalance)
  }
  previousBalance.value = newBalance
})

// 生命周期
onMounted(() => {
  previousBalance.value = balance.value
  startAutoRefresh()
})

// 组件卸载时清理定时器
watch(() => props.autoRefresh, (newVal) => {
  if (newVal) {
    startAutoRefresh()
  }
  else {
    stopAutoRefresh()
  }
})
</script>

<template>
  <div class="balance-display" :class="balanceStatusClass">
    <div class="balance-container">
      <!-- 余额标题 -->
      <div class="balance-header">
        <span class="balance-label">账户余额</span>
        <a-button
          type="text"
          size="small"
          :loading="isRefreshing"
          class="refresh-btn"
          @click="handleRefresh"
        >
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新
        </a-button>
      </div>

      <!-- 余额显示 -->
      <div class="balance-amount">
        <span class="currency">{{ currency }}</span>
        <span class="amount" :class="{ 'amount-animated': isAnimating }">
          {{ formattedBalance }}
        </span>
      </div>

      <!-- 状态指示器 -->
      <div class="balance-status">
        <a-badge
          :status="badgeStatus"
          :text="statusText"
          class="status-badge"
        />
        <div v-if="balanceStatus !== 'normal'" class="status-warning">
          <ExclamationCircleOutlined v-if="balanceStatus === 'warning'" />
          <WarningOutlined v-if="balanceStatus === 'critical'" />
          <span class="warning-text">{{ warningText }}</span>
        </div>
      </div>

      <!-- 快速操作按钮 -->
      <div class="balance-actions">
        <a-button
          type="primary"
          size="small"
          :disabled="accountStatus !== 'ACTIVE'"
          @click="handleQuickRecharge"
        >
          <template #icon>
            <PlusOutlined />
          </template>
          充值
        </a-button>

        <a-button
          type="link"
          size="small"
          @click="handleViewHistory"
        >
          查看详情
        </a-button>
      </div>
    </div>

    <!-- 错误提示 -->
    <a-alert
      v-if="error"
      :message="error"
      type="error"
      show-icon
      closable
      class="error-alert"
      @close="clearError"
    />

    <!-- 余额变动提示 -->
    <a-alert
      v-if="showBalanceChangeAlert"
      :message="balanceChangeMessage"
      type="success"
      show-icon
      closable
      class="balance-alert"
      @close="hideBalanceChangeAlert"
    />
  </div>
</template>

<style scoped lang="scss">
.balance-display {
  padding: 16px;
  background: var(--bg-color, #fff);
  border: 1px solid var(--border-color, #d9d9d9);
  border-radius: 8px;
  transition: all 0.3s ease;

  &.balance-small {
    padding: 12px;

    .balance-amount .amount {
      font-size: 20px;
    }
  }

  &.balance-large {
    padding: 24px;

    .balance-amount .amount {
      font-size: 32px;
    }
  }

  &.balance-warning {
    background: #fffbe6;
    border-color: #faad14;
  }

  &.balance-critical {
    background: #fff2f0;
    border-color: #ff4d4f;
  }
}

.balance-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.balance-header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .balance-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color-secondary, #666);
  }

  .refresh-btn {
    height: 24px;
    padding: 0 8px;
    font-size: 12px;
  }
}

.balance-amount {
  display: flex;
  gap: 4px;
  align-items: baseline;

  .currency {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color-secondary, #666);
  }

  .amount {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color, #1890ff);
    transition: all 0.3s ease;

    &.amount-animated {
      color: #52c41a;
      transform: scale(1.05);
    }
  }
}

.balance-status {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .status-badge {
    :deep(.ant-badge-status-text) {
      font-size: 12px;
    }
  }

  .status-warning {
    display: flex;
    gap: 4px;
    align-items: center;
    padding: 6px 8px;
    background: var(--warning-bg, #fffbe6);
    border: 1px solid var(--warning-border, #ffe58f);
    border-radius: 4px;

    .anticon {
      font-size: 12px;
      color: var(--warning-color, #faad14);
    }

    .warning-text {
      font-size: 12px;
      color: var(--warning-color, #faad14);
    }
  }
}

.balance-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-start;

  .ant-btn {
    height: 28px;
    font-size: 12px;
    border-radius: 4px;
  }
}

.balance-alert {
  margin-top: 12px;
  border-radius: 4px;

  :deep(.ant-alert-message) {
    font-size: 12px;
  }
}

// 响应式设计
@media (width <= 768px) {
  .balance-display {
    padding: 12px;

    &.balance-large {
      padding: 16px;
    }
  }

  .balance-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .balance-actions {
    justify-content: stretch;

    .ant-btn {
      flex: 1;
    }
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .balance-display {
    --bg-color: #141414;
    --border-color: #434343;
    --text-color-secondary: #a6a6a6;
    --primary-color: #1890ff;
    --warning-bg: #2d2000;
    --warning-border: #594214;
    --warning-color: #faad14;

    &.balance-warning {
      background: var(--warning-bg);
    }

    &.balance-critical {
      background: #2a1215;
      border-color: #a8071a;
    }
  }
}
</style>
