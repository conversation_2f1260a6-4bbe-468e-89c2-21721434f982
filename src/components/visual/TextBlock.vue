<script setup lang="ts">
import {onMounted, ref} from 'vue'

const props = defineProps<{
  textArray: any[]
  index: number
  height: number
}>()

const contentParts = ref<any[]>([])
const textContent = ref('')
const style = ref('')

const loadText = () => {
  const text = props.textArray[props.index]
  contentParts.value = text.contentParts || []
  textContent.value = text.content ? text.content.replaceAll('|||', '\n') : ''
  style.value = `height:${props.height}px; width:100%;${text.style}`
}

onMounted(() => {
  loadText()
})
</script>

<template>
  <div style="width: 100%;line-height: 1.4">
    <div :style="style">
      <span v-if="contentParts.length" class="pre-wrap">
        <span v-for="(item, index) in contentParts" :style="item.style">{{ item.strValue }}</span>
      </span>
      <span v-else class="pre-wrap">{{ textContent }}</span>
    </div>
  </div>
</template>

<style scoped>
.pre-wrap {
  white-space: pre-wrap;
}
</style>
