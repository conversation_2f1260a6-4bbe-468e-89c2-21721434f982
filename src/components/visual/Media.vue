<script setup lang="ts">
import {computed, onMounted, ref} from 'vue'
import MediaItem from './MediaItem.vue'
import MediaPreview from './MediaPreview.vue'

const props = defineProps<{
  mediaArray: any[]
  index: number
  height: number
}>()

interface CarouselExpose {
  goTo: (index: number) => void
}

const mediaType = ref('')
const displayForm = ref('')
const bottomHeight = ref(30)
const thumbnailItemHeight = ref(50)
const mediaItems = ref<any[]>([])
const mediaItemInnerHeight = computed(() => props.height - thumbnailItemHeight.value - bottomHeight.value * 2)
const mediaItemOuterHeight = computed(() => props.height - thumbnailItemHeight.value - bottomHeight.value)
const currentIndex = ref(0)
const mainCarousel = ref<CarouselExpose | null>(null)
const previewDialog = ref<InstanceType<typeof MediaPreview> | null>(null)

onMounted(() => {
  const media = props.mediaArray[props.index]
  mediaItems.value = media.mediaItems
  mediaType.value = media.mediaType
  displayForm.value = media.displayForm
  initVideoOption()

  if (mediaItems.value.length <= 1) {
    thumbnailItemHeight.value = 0
  }
})

const initVideoOption = () => {
  if (mediaType.value === 'video') {
    mediaItems.value.forEach(item => {
      item.videoOptions = {
        sources: [{
          type: "video/mp4",
          src: item.url,
          label: "原画",
          res: 1
        }],
        language: "zh-CN",
        playbackRates: [1, 1.5, 2.0, 3.0, 4.0, 8.0],
        defaultSrcReId: 2,
        height: mediaItemInnerHeight.value,
        controls: true,
        autoplay: false,
        preload: "auto"
      }
    })
  }
}

const handleSlideChange = (from: number, to: number) => {
  currentIndex.value = to
}

const goToSlide = (index: number) => {
  currentIndex.value = index
  mainCarousel.value?.goTo(index)
}

const preview = (item: any) => {
  previewDialog.value?.onItemDbClick(item, mediaType.value)
}

</script>

<template>
  <div :style="{height: height + 'px', width: '100%'}">
    <img v-if="displayForm === 'Single'"
         :src="mediaItems[0].url"
         :style="{height: height + 'px', maxWidth: '100%', objectFit: 'contain'}"
    />

    <a-carousel v-if="displayForm === 'Carousel'" :style="{height: mediaItemOuterHeight + 'px', width: '100%'}">
      <div v-for="mediaItem in mediaItems">
        <MediaItem :mediaType="mediaType"
                   :mediaItem="mediaItem"
                   :mediaItemOuterHeight="mediaItemOuterHeight"
                   :mediaItemInnerHeight="mediaItemInnerHeight"
                   :isThumbnail="false"
                   @preview="preview"
        />
      </div>
    </a-carousel>

    <div v-if="displayForm === 'Carousel-card'" class="custom-carousel">
      <a-carousel
        ref="mainCarousel"
        :dots="false"
        :style="{height: mediaItemOuterHeight + 'px', width: '100%'}"
        @beforeChange="handleSlideChange"
      >
        <div v-for="(mediaItem, index) in mediaItems" :key="index">
          <MediaItem
            :mediaType="mediaType"
            :mediaItem="mediaItem"
            :mediaItemOuterHeight="mediaItemOuterHeight"
            :mediaItemInnerHeight="mediaItemInnerHeight"
            :isThumbnail="false"
            @preview="preview"
          />
        </div>
      </a-carousel>
      <div v-if="mediaItems.length > 1" class="thumbnail-wrapper">
        <div
          v-for="(item, index) in mediaItems"
          :key="'thumb-'+index"
          class="thumbnail-item"
          :class="{ active: currentIndex === index }"
          @click="goToSlide(index)"
        >
          <MediaItem
            :mediaType="mediaType"
            :mediaItem="item"
            :mediaItemOuterHeight="thumbnailItemHeight"
            :mediaItemInnerHeight="thumbnailItemHeight"
            :isThumbnail="true"
          />
        </div>
      </div>
    </div>

    <MediaPreview ref="previewDialog"/>
  </div>
</template>

<style scoped>
:deep(.slick-slide) {
  height: 100%;
  overflow: hidden;
}

.custom-carousel {
  position: relative;
}

.thumbnail-wrapper {
  display: flex;
  justify-content: center;
  gap: 5px;
  margin-top: 10px;
}

.thumbnail-item {
  width: 80px;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s;
}

.thumbnail-item.active {
  border-color: #1890ff;
}
</style>
