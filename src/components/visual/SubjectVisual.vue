<script setup lang="ts">
import {defineComponent, h, markRaw, onBeforeUnmount, onMounted, ref, shallowRef, watch} from 'vue'
import * as layoutUtil from "@/utils/layoutUtil.ts"
import Empty from "./Empty.vue"
import TextBlock from './TextBlock.vue'
import StoryChart from './StoryChart.vue'
import StoryTable from './StoryTable.vue'
import Media from './Media.vue'
import TagBlock from './TagBlock.vue'

const props = defineProps<{
  visualData: any
}>()

const activePage = shallowRef(defineComponent({render: () => h(Empty)}))

const textArray = ref<any[]>([])
const optionArray = ref<any[]>([])
const tableArray = ref<any[]>([])
const mediaArray = ref<any[]>([])
const tagArray = ref<any[]>([])

const clearOldData = () => {
  textArray.value = []
  optionArray.value = []
  tableArray.value = []
  mediaArray.value = []
  tagArray.value = []
}

const createDynamicComponent = (content: string) => {
  return markRaw(defineComponent({
    components: {Empty, TextBlock, StoryChart, StoryTable, Media, TagBlock},
    setup() {
      const compiled = computed(() => {
        return {
          template: content,
          components: {Empty, TextBlock, StoryChart, StoryTable, Media, TagBlock},
          props: ['textArray', 'optionArray', 'tableArray', 'mediaArray', 'tagArray', 'activeKey']
        }
      })
      return () => h(compiled.value)
    }
  }))
}

const refreshMainZone = () => {
  clearOldData()

  try {
    const visualJson = JSON.parse(props.visualData)

    const content = layoutUtil.assembleVisual({
      textArray,
      optionArray,
      tableArray,
      mediaArray,
      tagArray,
    }, visualJson)

    activePage.value = createDynamicComponent(content)

  } catch (e) {
    console.error('可视化数据解析失败:', e)
    activePage.value = markRaw(defineComponent({render: () => h(Empty)}))
  }
}

onMounted(refreshMainZone)

watch(() => props.visualData, refreshMainZone)

onBeforeUnmount(() => {
  activePage.value = markRaw(defineComponent({render: () => h(Empty)}))
})
</script>

<template>
  <a-row>
    <a-col :span="24">
      <div class="content-container">
        <component
          :is="activePage"
          :textArray="textArray"
          :optionArray="optionArray"
          :tableArray="tableArray"
          :mediaArray="mediaArray"
          :tagArray="tagArray"
        />
      </div>
    </a-col>
  </a-row>
</template>

<style scoped>
.content-container {
  width: 100%;
  height: 600px;
}
</style>
