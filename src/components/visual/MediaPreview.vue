<script setup lang="ts">
import {ref} from 'vue';

const showPreviewDialog = ref(false);
const mediaItem = ref({
  name: '',
  url: ''
});
const mediaType = ref('');

const onItemDbClick = (item: { name: string; url: string }, type: string) => {
  mediaItem.value = item;
  mediaType.value = type;
  showPreviewDialog.value = true;
};

const closePreviewDialog = () => {
  showPreviewDialog.value = false;
};

defineExpose({onItemDbClick});
</script>

<template>
  <div>
    <a-modal
      wrapClassName="previewDialog"
      :open="showPreviewDialog"
      :footer="null"
      :destroyOnClose="true"
      @cancel="closePreviewDialog"
    >
      <p class="title">{{ mediaItem.name }}</p>
      <img
        v-if="mediaType === 'image'"
        :src="mediaItem.url"
        width="100%"
        :alt="mediaItem.name"
      />
    </a-modal>
  </div>
</template>

<style scoped>
.title {
  text-align: left;
  margin-top: 20px;
}
</style>
