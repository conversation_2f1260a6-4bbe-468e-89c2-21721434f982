<script setup lang="ts">
import {onMounted, ref} from 'vue';

const props = defineProps<{
  tableArray: any[],
  index: number,
  height: number
}>();

const tableDiv = ref();
const dataSource = ref<any[]>([]);
const columns = ref<any[]>([]);
const showRowName = ref(true);
const showColumnName = ref(true);
const dataPointArray = ref<any[]>([]);
const tableWidth = ref(0);
const scrollY = ref(0);

onMounted(() => {
  loadTable();
});

const loadTable = () => {
  const tableData = props.tableArray[props.index];
  const columnNames = tableData.columnNames;
  const rowNames = tableData.rowNames;
  dataPointArray.value = tableData.dataPointArray;
  const firstColumnWidth = tableData.firstColumnWidth || 70;
  const eachColumnWidth = tableData.eachColumnWidth;

  if (!columnNames || !rowNames || !dataPointArray.value) return;

  let tableFirstLineHeight = tableData.showColumnName ? (tableData.tableFirstLineHeight ? tableData.tableFirstLineHeight : 46) : 0;
  let firstLineHeight = ';height:' + tableFirstLineHeight + 'px;';
  scrollY.value = props.height - tableFirstLineHeight;

  let elseLineHeight = '';
  if (tableData.tableElseLineTotalHeight) {
    elseLineHeight = ';height:' + (tableData.tableElseLineTotalHeight / rowNames.length) + 'px;';
  } else if (tableData.tableElseLineHeight) {
    elseLineHeight = ';height:' + tableData.tableElseLineHeight + 'px;';
  }

  showRowName.value = tableData.showRowName;
  showColumnName.value = tableData.showColumnName;

  if (showRowName.value) {
    columns.value.push({
      key: 0,
      dataIndex: 'column0',
      title: '',
      width: firstColumnWidth,
      customHeaderCell: (record: any) => ({
        style: rowNames[0].style + columnNames[0].style + firstLineHeight
      }),
      customCell: (record: any) => ({
        style: record['column_style0']
      })
    });
  }

  for (let j = 0; j < columnNames.length; j++) {
    columns.value.push({
      key: j + 1,
      dataIndex: 'column' + (j + 1),
      title: columnNames[j].name,
      width: eachColumnWidth && eachColumnWidth.length > j
        ? eachColumnWidth[j]
        : (tableDiv.value?.clientWidth - firstColumnWidth) / columnNames.length,
      customHeaderCell: (record: any) => ({
        style: columnNames[j].style + firstLineHeight
      }),
      customCell: (record: any) => ({
        style: record[`column_style${j + 1}`],
        rowSpan: record[`rowspan${j + 1}`],
        colSpan: record[`colspan${j + 1}`]
      })
    });
  }

  tableWidth.value = columns.value.reduce((sum, col) => sum + col.width, 0);

  for (let i = 0; i < rowNames.length; i++) {
    const dataItem: any = {
      key: i.toString(),
      column0: rowNames[i].name,
      column_style0: rowNames[i].style + elseLineHeight
    };

    for (let j = 0; j < columnNames.length; j++) {
      const dataPoint = dataPointArray.value[i][j];
      dataItem['column' + (j + 1)] = dataPoint.strValue === '%' ? '' : dataPoint.strValue;
      dataItem['column_style' + (j + 1)] = (dataPoint.style || dataPoint.defaultStyle) + elseLineHeight;
      dataItem['point' + (j + 1)] = dataPoint;

      if (dataPoint.rowspan != null) {
        dataItem[`rowspan${j + 1}`] = dataPoint.rowspan;
        dataItem[`colspan${j + 1}`] = dataPoint.colspan;
      } else {
        dataItem[`rowspan${j + 1}`] = 1;
        dataItem[`colspan${j + 1}`] = 1;
      }
    }

    dataSource.value.push(dataItem);
  }
};
</script>

<template>
  <div ref="tableDiv" class="container">
    <a-table
      class="storyTableClass"
      :columns="columns"
      :dataSource="dataSource"
      :scroll="{ y: scrollY }"
      :pagination="false"
      :style="{ width: tableWidth + 'px', height: height + 'px' }"
      :showHeader="showColumnName"
    >
      <template #bodyCell="{ column, record, text }">
        <span v-if="record['point' + column.key]?.attachmentList">
          <a
            v-for="a in record['point' + column.key].attachmentList"
            :key="a.url"
            :href="a.url"
            target="_blank"
            style="color: #1D8CE0;"
          >
            {{ a.name }}<br>
          </a>
        </span>
        <span v-else-if="record['point' + column.key]?.strValueParts">
          <span v-for="(part, idx) in record['point' + column.key].strValueParts" :key="idx" :style="part.style">
            {{ part.strValue }}
          </span>
        </span>
        <span v-else-if="text === undefined || text === null">-</span>
        <span v-else>
          <span v-for="(str, idx) in text.split('<br>')" :key="idx">{{ str }}<br></span>
        </span>
      </template>
    </a-table>
  </div>
</template>

<style scoped>
.container {
  width: 100%;
  position: relative;
  overflow-x: auto;
}

.storyTableClass {
  margin: 0 auto;
}

.storyTableClass :deep(.ant-table-cell) {
  line-height: normal;
  white-space: pre-line;
  text-align: center;
  padding: 12px 0;
}

.storyTableClass :deep(.ant-table)::before {
  height: 0;
}

</style>
