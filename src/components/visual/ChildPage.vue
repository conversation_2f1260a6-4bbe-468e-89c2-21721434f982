<script setup lang="ts">
import {defineComponent, h, markRaw, onBeforeUnmount, ref, shallowRef} from 'vue'
import * as layoutUtil from "@/utils/layoutUtil.ts"
import Empty from "./Empty.vue"
import TextBlock from './TextBlock.vue'
import StoryChart from './StoryChart.vue'
import StoryTable from './StoryTable.vue'
import Media from './Media.vue'
import TagBlock from './TagBlock.vue'

const showModal = ref(false);
const activePage = shallowRef(defineComponent({render: () => h(Empty)}))

const textArray = ref<any[]>([])
const optionArray = ref<any[]>([])
const tableArray = ref<any[]>([])
const mediaArray = ref<any[]>([])
const tagArray = ref<any[]>([])

const clearOldData = () => {
  textArray.value = []
  optionArray.value = []
  tableArray.value = []
  mediaArray.value = []
  tagArray.value = []
}

const createDynamicComponent = (content: string) => {
  return markRaw(defineComponent({
    components: {Empty, TextBlock, StoryChart, StoryTable, Media, TagBlock},
    setup() {
      const compiled = computed(() => {
        return {
          template: content,
          components: {Empty, TextBlock, StoryChart, StoryTable, Media, TagBlock},
          props: ['textArray', 'optionArray', 'tableArray', 'mediaArray', 'tagArray', 'activeKey']
        }
      })
      return () => h(compiled.value)
    }
  }))
}

const refreshMainZone = (visualJson: string, visualParam: any) => {
  clearOldData()

  try {
    const content = layoutUtil.assembleVisual({
      textArray,
      optionArray,
      tableArray,
      mediaArray,
      tagArray,
      visualParam
    }, visualJson)

    activePage.value = createDynamicComponent(content)

  } catch (e) {
    console.error('可视化数据解析失败:', e)
    activePage.value = markRaw(defineComponent({render: () => h(Empty)}))
  }
}

const closeModal = () => {
  showModal.value = false
}

const openModal = (visualJson: string, visualParam: any) => {
  refreshMainZone(visualJson, visualParam)
  showModal.value = true
}

onBeforeUnmount(() => {
  activePage.value = markRaw(defineComponent({render: () => h(Empty)}))
})

defineExpose({openModal});
</script>

<template>
  <a-modal
    wrapClassName="previewDialog"
    :open="showModal"
    :footer="null"
    :destroyOnClose="true"
    @cancel="closeModal"
    :style="{
      width: '80%'
    }"
  >
    <div class="content-container">
      <component
        :is="activePage"
        :textArray="textArray"
        :optionArray="optionArray"
        :tableArray="tableArray"
        :mediaArray="mediaArray"
        :tagArray="tagArray"
      />
    </div>
  </a-modal>
</template>

<style scoped>
.content-container {
  width: 100%;
  height: 600px;
  margin: 30px 0 20px 0;
}
</style>
