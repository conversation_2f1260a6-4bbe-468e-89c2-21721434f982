<script setup lang="ts">
import {onMounted, ref} from 'vue';
import {ElMessage} from "element-plus";
import * as visualApi from "@/api/modules/visual.ts"
import ChildPage from './ChildPage.vue'

const props = defineProps<{
  tagArray: any[];
  index: number;
  height: number;
}>();

const currentTag = ref<any>({});
const style = ref('');
const childPage = ref<InstanceType<typeof ChildPage> | null>(null)

const loadTag = () => {
  currentTag.value = props.tagArray[props.index];
  style.value = `height:${props.height}px;width:100%;text-align:left;padding:10px 0;`;
};

const openChildPage = async (tagPoint: any) => {
  try {
    let visualJson: string;
    if (tagPoint.type === 'GptDataPoint') {
      visualJson = await visualApi.getGptVisual(
        tagPoint.stencilId,
        tagPoint.shotId,
        tagPoint.splitUnitId,
        tagPoint.id,
        tagPoint.visualId,
        tagPoint.dataPeriod
      );
    } else {
      visualJson = await visualApi.getRhVisual(
        currentTag.value.mapId,
        currentTag.value.shotId,
        tagPoint.splitUnitId,
        tagPoint.id,
        tagPoint.visualId
      );
    }

    if (!visualJson) {
      ElMessage.error('没有查询到可视化');
      return;
    }

    const visualParam = {
      mapId: currentTag.value.mapId,
      shotId: currentTag.value.shotId
    };

    childPage.value?.openModal(visualJson, visualParam)

  } catch (error) {
    ElMessage.error('获取可视化失败')
  }
};

onMounted(() => {
  loadTag();
});
</script>

<template>
  <div :style="style">
    <a-button
      v-for="tagPoint in currentTag.tagPointList"
      @click="openChildPage(tagPoint)"
      type="primary"
      size="small"
      class="tag-button"
    >
      {{ tagPoint.strValue }}
    </a-button>

    <ChildPage ref="childPage"/>
  </div>
</template>

<style scoped>
.tag-button {
  margin: 0 5px 5px 0;
  color: #409eff;
  background: #ecf5ff;
  border-color: #b3d8ff;
  border-radius: 20px;
  font-size: 12px;
}
</style>
