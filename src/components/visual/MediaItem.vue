<script setup lang="ts">
import {onBeforeUnmount, ref} from 'vue'
import {VideoPlayer} from '@videojs-player/vue'
import 'video.js/dist/video-js.css'
import type videojs from 'video.js'

interface PlayerInstance {
  videoPlayer: videojs.Player
}

const props = defineProps<{
  mediaType: string
  mediaItem: any
  mediaItemOuterHeight: number
  mediaItemInnerHeight: number
  isThumbnail: boolean
}>()

const bottomHeight = ref(20)
const player = ref<PlayerInstance | null>(null)

const emit = defineEmits(['preview'])

const preview = (item: any) => {
  emit('preview', item)
}

onBeforeUnmount(() => {
  if (player.value?.videoPlayer) {
    player.value.videoPlayer.pause()
    player.value.videoPlayer.dispose()
  }
})
</script>

<template>
  <div>
    <div
      v-if="mediaType === 'image'"
      class="mediaItem"
      :style="{ height: mediaItemOuterHeight + 'px' }"
      @dblclick="preview(mediaItem)"
    >
      <img
        :src="mediaItem.url"
        :alt="mediaItem.name"
        :style="{ height: mediaItemInnerHeight + 'px' }"
      />
      <a-tooltip v-if="!isThumbnail" class="item" placement="bottom" :title="mediaItem.name">
        <p :style="{ height: bottomHeight + 'px' }">{{ mediaItem.name }}</p>
      </a-tooltip>
    </div>

    <div
      v-if="mediaType === 'video'"
      class="mediaItem"
      :style="{ height: mediaItemOuterHeight + 'px', margin: '5px 0' }"
    >
      <VideoPlayer
        ref="player"
        :options="mediaItem.videoOptions"
        :style="{
          height: mediaItemInnerHeight + 'px',
          minWidth: '400px',
          maxWidth: '100%',
          margin: '0 auto'
        }"
        class="video-js vjs-big-play-centered"
      />
      <a-tooltip v-if="!isThumbnail" class="item" placement="bottom" :title="mediaItem.name">
        <p :style="{ height: bottomHeight + 'px' }">{{ mediaItem.name }}</p>
      </a-tooltip>
    </div>

    <div
      v-if="mediaType === 'audio'"
      class="mediaItem"
      :style="{ height: mediaItemOuterHeight + 'px' }"
    >
      <audio
        id="media_item_audio_player"
        :src="mediaItem.url"
        :autoplay="false"
        :controls="true"
        preload="auto"
        :style="{
          height: mediaItemInnerHeight + 'px',
          margin: '0 auto'
        }"
        class="audio-box"
      />
      <a-tooltip v-if="!isThumbnail" class="item" placement="bottom" :title="mediaItem.name">
        <p :style="{ height: bottomHeight + 'px' }">{{ mediaItem.name }}</p>
      </a-tooltip>
    </div>
  </div>
</template>

<style scoped>
.mediaItem > img {
  width: auto;
  max-width: 100%;
  height: auto;
  max-height: 100%;
  margin: 0 auto;
}

.mediaItem > p {
  color: #1d8ce0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 10px 0;
  font-size: 12px;
}
</style>
