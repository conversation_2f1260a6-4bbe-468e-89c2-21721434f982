/**
 * Markdown 渲染工具模块
 * 提供安全的 Markdown 到 HTML 转换功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */

/**
 * 渲染 Markdown 文本为 HTML
 *
 * 这是一个简化的 Markdown 渲染器，支持常见的 Markdown 语法：
 * - 标题 (# ## ### 等)
 * - 段落和换行
 * - 粗体和斜体
 * - 代码块和行内代码
 * - 链接
 * - 列表
 * - 引用
 * - 水平分割线
 *
 * @param markdown Markdown 文本
 * @returns 渲染后的 HTML 字符串
 */
export function renderMarkdown(markdown: string): string {
  if (!markdown || typeof markdown !== 'string') {
    return ''
  }

  let html = markdown

  // 转义 HTML 特殊字符（防止 XSS）
  html = escapeHtml(html)

  // 处理代码块（必须在其他处理之前）
  html = processCodeBlocks(html)

  // 处理行内代码
  html = processInlineCode(html)

  // 处理标题
  html = processHeadings(html)

  // 处理粗体和斜体
  html = processBoldAndItalic(html)

  // 处理链接
  html = processLinks(html)

  // 处理列表
  html = processLists(html)

  // 处理引用
  html = processBlockquotes(html)

  // 处理水平分割线
  html = processHorizontalRules(html)

  // 处理段落和换行
  html = processParagraphs(html)

  return html
}

/**
 * 转义 HTML 特殊字符
 */
function escapeHtml(text: string): string {
  const htmlEscapes: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#x27;',
  }

  return text.replace(/[&<>"']/g, (match) => htmlEscapes[match])
}

/**
 * 处理代码块
 */
function processCodeBlocks(text: string): string {
  // 处理三个反引号的代码块
  return text.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, language, code) => {
    const lang = language || 'text'
    const escapedCode = code.trim()
    return `<pre class="code-block" data-language="${lang}"><code>${escapedCode}</code></pre>`
  })
}

/**
 * 处理行内代码
 */
function processInlineCode(text: string): string {
  // 处理单个反引号的行内代码（避免与代码块冲突）
  return text.replace(/(?<!`)`([^`\n]+)`(?!`)/g, '<code class="inline-code">$1</code>')
}

/**
 * 处理标题
 */
function processHeadings(text: string): string {
  return text.replace(/^(#{1,6})\s+(.+)$/gm, (match, hashes, content) => {
    const level = hashes.length
    return `<h${level} class="heading-${level}">${content.trim()}</h${level}>`
  })
}

/**
 * 处理粗体和斜体
 */
function processBoldAndItalic(text: string): string {
  // 粗体 **text** 或 __text__
  text = text.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
  text = text.replace(/__([^_]+)__/g, '<strong>$1</strong>')

  // 斜体 *text* 或 _text_
  text = text.replace(/\*([^*]+)\*/g, '<em>$1</em>')
  text = text.replace(/_([^_]+)_/g, '<em>$1</em>')

  return text
}

/**
 * 处理链接
 */
function processLinks(text: string): string {
  // Markdown 链接格式 [text](url)
  return text.replace(/\[([^\]]+)\]\(([^)]+)\)/g, (match, linkText, url) => {
    // 简单的 URL 验证
    const safeUrl = sanitizeUrl(url)
    return `<a href="${safeUrl}" target="_blank" rel="noopener noreferrer">${linkText}</a>`
  })
}

/**
 * 处理列表
 */
function processLists(text: string): string {
  // 处理无序列表
  text = text.replace(/^(\s*[-*+]\s+.+(?:\n\s*[-*+]\s+.+)*)/gm, (match) => {
    const items = match
      .split('\n')
      .map((line) => line.replace(/^\s*[-*+]\s+/, '').trim())
      .filter((item) => item.length > 0)
      .map((item) => `<li>${item}</li>`)
      .join('')
    return `<ul class="unordered-list">${items}</ul>`
  })

  // 处理有序列表
  text = text.replace(/^(\s*\d+\.\s+.+(?:\n\s*\d+\.\s+.+)*)/gm, (match) => {
    const items = match
      .split('\n')
      .map((line) => line.replace(/^\s*\d+\.\s+/, '').trim())
      .filter((item) => item.length > 0)
      .map((item) => `<li>${item}</li>`)
      .join('')
    return `<ol class="ordered-list">${items}</ol>`
  })

  return text
}

/**
 * 处理引用
 */
function processBlockquotes(text: string): string {
  return text.replace(/^>\s+(.+)$/gm, '<blockquote class="blockquote">$1</blockquote>')
}

/**
 * 处理水平分割线
 */
function processHorizontalRules(text: string): string {
  return text.replace(/^(-{3,}|\*{3,}|_{3,})$/gm, '<hr class="horizontal-rule" />')
}

/**
 * 处理段落和换行
 */
function processParagraphs(text: string): string {
  // 分割成段落
  const paragraphs = text.split(/\n\s*\n/).filter((p) => p.trim().length > 0)

  return paragraphs
    .map((paragraph) => {
      const trimmed = paragraph.trim()

      // 如果已经是 HTML 标签，不要包装在 p 标签中
      if (trimmed.match(/^<(h[1-6]|ul|ol|blockquote|pre|hr)/)) {
        return trimmed
      }

      // 处理段落内的换行
      const withBreaks = trimmed.replace(/\n/g, '<br />')
      return `<p class="paragraph">${withBreaks}</p>`
    })
    .join('')
}

/**
 * 清理和验证 URL
 */
function sanitizeUrl(url: string): string {
  // 移除潜在的危险协议
  const dangerousProtocols = ['javascript:', 'data:', 'vbscript:', 'file:']
  const lowerUrl = url.toLowerCase()

  for (const protocol of dangerousProtocols) {
    if (lowerUrl.startsWith(protocol)) {
      return '#'
    }
  }

  // 如果没有协议，添加 https://
  if (!url.match(/^https?:\/\//)) {
    return `https://${url}`
  }

  return url
}

/**
 * 处理数学公式（简化版本）
 * 在实际项目中，可以集成 KaTeX 或 MathJax
 */
export function renderMathFormula(formula: string): string {
  // 这里是一个简化的数学公式处理
  // 在实际项目中，应该使用专门的数学公式渲染库
  return `<span class="math-formula" title="数学公式">${escapeHtml(formula)}</span>`
}

/**
 * 代码高亮（简化版本）
 * 在实际项目中，可以集成 Prism.js 或 highlight.js
 */
export function highlightCode(code: string, language: string): string {
  // 这里是一个简化的代码高亮处理
  // 在实际项目中，应该使用专门的代码高亮库
  return `<pre class="highlighted-code" data-language="${language}"><code>${escapeHtml(code)}</code></pre>`
}

/**
 * 检查文本是否包含 Markdown 语法
 */
export function hasMarkdownSyntax(text: string): boolean {
  if (!text || typeof text !== 'string') {
    return false
  }

  const markdownPatterns = [
    /^#{1,6}\s+/, // 标题
    /\*\*.*\*\*/, // 粗体
    /__.*__/, // 粗体
    /\*.*\*/, // 斜体
    /_.*_/, // 斜体
    /`.*`/, // 行内代码
    /```[\s\S]*```/, // 代码块
    /\[.*\]\(.*\)/, // 链接
    /^[-*+]\s+/m, // 无序列表
    /^\d+\.\s+/m, // 有序列表
    /^>\s+/m, // 引用
    /^(-{3,}|\*{3,}|_{3,})$/m, // 水平分割线
  ]

  return markdownPatterns.some((pattern) => pattern.test(text))
}

/**
 * 获取纯文本内容（移除 Markdown 语法）
 */
export function getPlainText(markdown: string): string {
  if (!markdown || typeof markdown !== 'string') {
    return ''
  }

  let text = markdown

  // 移除代码块
  text = text.replace(/```[\s\S]*?```/g, '')

  // 移除行内代码
  text = text.replace(/`[^`]+`/g, '')

  // 移除标题标记
  text = text.replace(/^#{1,6}\s+/gm, '')

  // 移除粗体和斜体标记
  text = text.replace(/\*\*([^*]+)\*\*/g, '$1')
  text = text.replace(/__([^_]+)__/g, '$1')
  text = text.replace(/\*([^*]+)\*/g, '$1')
  text = text.replace(/_([^_]+)_/g, '$1')

  // 移除链接，保留文本
  text = text.replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')

  // 移除列表标记
  text = text.replace(/^[-*+]\s+/gm, '')
  text = text.replace(/^\d+\.\s+/gm, '')

  // 移除引用标记
  text = text.replace(/^>\s+/gm, '')

  // 移除水平分割线
  text = text.replace(/^(-{3,}|\*{3,}|_{3,})$/gm, '')

  return text.trim()
}

export default {
  renderMarkdown,
  renderMathFormula,
  highlightCode,
  hasMarkdownSyntax,
  getPlainText,
}
