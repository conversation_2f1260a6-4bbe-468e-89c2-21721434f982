/**
 * 模板工具函数
 */

// 定义槽位定义类型
interface SlotDefinition {
  type?: 'text' | 'textarea' | 'select' | 'date' | 'number' | 'TEXT' | 'TEXTAREA' | 'SELECT' | 'DATE' | 'NUMBER'
  label?: string
  description?: string
  options?: Array<{ label: string, value: string | number }>
  required?: boolean
  defaultValue?: any
  disabled?: boolean
}

/**
 * 获取槽位的显示值
 * @param value 槽位的原始值
 * @param definition 槽位定义
 * @returns 用于显示的值
 */
function getSlotDisplayValue(value: any, definition?: SlotDefinition): string {
  if (value === undefined || value === null || value === '') {
    // 如果没有值，且存在默认值，则使用默认值
    if (definition?.defaultValue !== undefined && definition?.defaultValue !== null && definition?.defaultValue !== '') {
      return String(definition.defaultValue)
    }
    // 否则返回空字符串
    return ''
  }

  // 将类型转换为小写进行比较，以支持大小写不敏感的匹配
  const normalizedType = definition?.type?.toLowerCase()

  switch (normalizedType) {
    case 'date':
      return value instanceof Date
        ? value.toLocaleDateString()
        : (typeof value === 'string' ? value : JSON.stringify(value))
    case 'select': {
      const option = (definition?.options || [])
        .find(opt => opt.value === value)
      return option?.label || String(value)
    }
    default:
      return String(value)
  }
}

/**
 * 填充模板内容，将{{key}}格式的占位符替换为对应的值
 *
 * @param template 模板内容字符串
 * @param values 键值对对象，用于替换模板中的占位符
 * @param slotDefinitions 可选的槽位定义，用于处理特殊类型的槽位（如下拉选择）
 * @returns 填充后的字符串
 */
export function fillTemplate(
  template: string,
  values: Record<string, any>,
  slotDefinitions?: Record<string, SlotDefinition>,
): string {
  if (!template) { return '' }
  if (!values) { return template }

  return template.replace(/\{\{([^{}]+)\}\}/g, (match, slotName) => {
    const key = slotName.trim()
    const value = values[key]
    const definition = slotDefinitions?.[key]

    if (value !== undefined && value !== null) {
      return getSlotDisplayValue(value, definition)
    }

    return match
  })
}
