/**
 * Vue 3 插槽系统组合式API
 * 提供响应式的插槽状态管理
 */

import type { SlotChangeParams, SlotDefinition, SlotDefinitions, SlotValues, TemplateInitParams } from '@/utils/slotSystem'
import type { Ref } from 'vue'
import SlotSystemAPI from '@/utils/slotSystem'
import { ref, watch, unref } from 'vue'

export interface UseSlotSystemOptions {
  /** 模板名称 */
  templateName: Ref<string>
  /** 模板内容 */
  templateContent: string | Ref<string>
  /** 初始插槽定义 */
  initialSlotDefinitions?: SlotDefinitions | Ref<SlotDefinitions>
  /** 初始插槽值 */
  initialSlotValues?: SlotValues | Ref<SlotValues>
  /** 是否自动初始化 */
  autoInitialize?: boolean
  /** 标准参数 */
  standardParam?: any | Ref<any>
  /** 槽位值变更后的回调 */
  onSlotValueChange: ((slotName: string, value: any) => void)
}

export interface UseSlotSystemReturn {
  /** 插槽定义 */
  slotDefinitions: Ref<SlotDefinitions>
  /** 插槽值 */
  slotValues: Ref<SlotValues>
  /** 是否正在加载 */
  loading: Ref<boolean>
  /** 是否已初始化 */
  initialized: Ref<boolean>
  /** 初始化插槽定义 */
  initializeSlots: () => Promise<void>
  /** 更新插槽值并触发联动 */
  updateSlotValue: (slotName: string, value: any) => Promise<void>
}

/**
 * 插槽系统组合式API
 */
export function useSlotSystem(options: UseSlotSystemOptions): UseSlotSystemReturn {
  // 解构选项
  const {
    templateName,
    templateContent,
    initialSlotDefinitions = {},
    initialSlotValues = {},
    autoInitialize = true,
    standardParam = {},
    onSlotValueChange,
  } = options

  // 响应式状态
  const slotDefinitions = ref<SlotDefinitions>({ ...unref(initialSlotDefinitions) })
  const slotValues = ref<SlotValues>({ ...unref(initialSlotValues) })
  const loading = ref(false)
  const initialized = ref(false)

  // 保留初始化后的完整插槽定义，用于联动时的过滤还原
  const originalSlotDefinitions = ref<SlotDefinitions>({})

  /**
   * 初始化插槽定义
   * 只负责获取和保留完整的插槽定义，不处理插槽值
   */
  const initializeSlots = async (): Promise<void> => {
    if (loading.value) { return }

    loading.value = true

    try {
      const params: TemplateInitParams = {
        templateName: templateName.value,
        templateContent: unref(templateContent),
        initialSlotDefinitions: unref(initialSlotDefinitions),
        initialSlotValues: unref(initialSlotValues),
        standardParam: unref(standardParam),
      }

      const result = await SlotSystemAPI.initializeTemplate(params)
      console.log('initializeTemplate result', result)

      // 保存完整的插槽定义，用于后续联动时的过滤还原
      originalSlotDefinitions.value = { ...result.definitions }
      slotDefinitions.value = { ...result.definitions }

      initialized.value = true
    }
    catch (err) {
      console.error('Failed to initialize slots:', err)
    }
    finally {
      loading.value = false
    }
  }

  /**
   * 更新插槽值并触发联动
   * 当插槽值变化时，触发联动逻辑更新其他插槽的值和定义
   */
  const updateSlotValue = async (slotName: string, value: any): Promise<void> => {
    const callback = onSlotValueChange;
    if (loading.value) { return }

    const oldValue = slotValues.value[slotName]

    // 更新本地值
    slotValues.value = {
      ...slotValues.value,
      [slotName]: value,
    }

    // 触发回调
    if (callback) {
      callback(slotName, value)
    }

    // 处理联动逻辑
    if (initialized.value && value !== oldValue) {
      loading.value = true

      try {
        const params: SlotChangeParams = {
          changedSlotName: slotName,
          newValue: value,
          oldValue,
          currentSlotValues: slotValues.value,
          currentSlotDefinitions: originalSlotDefinitions.value, // 使用原始完整定义
          standardParam: unref(standardParam),
          onSlotValueChange: callback,
        }

        const result = await SlotSystemAPI.handleSlotChange(templateName.value, params)

        if (result) {
          // 更新插槽值
          if (result.values) {
            slotValues.value = {
              ...slotValues.value,
              ...result.values,
            }
          }

          // 更新插槽定义（可能是过滤后的定义）
          if (result.definitions) {
            slotDefinitions.value = {
              ...originalSlotDefinitions.value, // 从原始定义开始
              ...result.definitions, // 应用联动后的定义变化
            }
          }
        }
      }
      catch (err) {
        console.error('Failed to handle slot change:', err)
      }
      finally {
        loading.value = false
      }
    }
  }

  // 监听模板名称变化，自动重新初始化
  watch(
    () => templateName.value,
    () => {
      initializeSlots()
    },
  )

  // 自动初始化
  if (autoInitialize) {
    initializeSlots()
  }

  return {
    slotDefinitions,
    slotValues,
    loading,
    initialized,
    initializeSlots,
    updateSlotValue,
  }
}
