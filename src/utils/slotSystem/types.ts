/**
 * 插槽系统类型定义
 */
import type { Ref } from 'vue'

// 插槽定义接口
export interface SlotDefinition {
  type: 'text' | 'textarea' | 'select' | 'date' | 'number'
  label?: string
  description?: string
  placeholder?: string
  options?: Array<{ label: string, value: string | number }>
  required?: boolean
  defaultValue?: any
  disabled?: boolean
  visible?: boolean
  validation?: {
    pattern?: RegExp
    message?: string
    min?: number
    max?: number
  }
}

// 插槽值类型
export type SlotValue = string | number | boolean | Date | Array<any> | null | undefined

// 插槽值集合
export type SlotValues = Record<string, SlotValue>

// 插槽定义集合
export type SlotDefinitions = Record<string, SlotDefinition>

// 插槽状态更新结果
export interface SlotStateUpdateResult {
  values: SlotValues
  definitions: SlotDefinitions
  errors?: Record<string, string>
}

// 模板初始化参数
export interface TemplateInitParams {
  templateName: string
  templateContent: string
  initialSlotDefinitions?: SlotDefinitions
  initialSlotValues?: SlotValues
  standardParam?: any
}

// 插槽变化事件参数
export interface SlotChangeParams {
  changedSlotName: string
  newValue: SlotValue
  oldValue: SlotValue
  currentSlotValues: SlotValues
  currentSlotDefinitions: SlotDefinitions
  standardParam?: any | Ref<any>
  onSlotValueChange: ((slotName: string, value: any) => void)
}

// API响应接口
export interface ApiResponse<T = any> {
  data: T | null
  success: boolean
  message?: string
  code?: number
}

// HTTP请求配置
export interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  params?: Record<string, any>
  data?: any
  headers?: Record<string, string>
}
