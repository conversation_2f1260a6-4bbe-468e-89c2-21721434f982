import type {
  ApiResponse,
  RequestConfig,
  SlotChangeParams,
  SlotDefinitions,
  SlotStateUpdateResult,
  SlotValues,
  TemplateInitParams,
} from './types'
import useUserStore from '@/store/modules/user'
/**
 * 模板处理器抽象基类
 * 采用多态设计，每个具体的模板类型都需要继承此基类
 */

export abstract class BaseTemplateProcessor {
  protected templateName: string
  protected templateContent: string
  protected standardParam?: any

  constructor(templateName: string) {
    this.templateName = templateName
    this.templateContent = ''
  }

  /**
   * 获取模板名称
   */
  getTemplateName(): string {
    return this.templateName
  }

  /**
   * 设置模板内容
   */
  setTemplateContent(content: string): void {
    this.templateContent = content
  }

  /**
   * 设置标准参数
   */
  setStandardParam(param: any): void {
    this.standardParam = param
  }

  /**
   * 初始化插槽定义和值（抽象方法，子类必须实现）
   * @param params 初始化参数
   * @returns Promise<SlotStateUpdateResult>
   */
  abstract initializeSlots(params: TemplateInitParams): Promise<SlotStateUpdateResult>

  /**
   * 处理插槽值变化时的联动逻辑（抽象方法，子类必须实现）
   * @param params 插槽变化参数
   * @returns Promise<SlotStateUpdateResult | null>
   */
  abstract handleSlotChange(params: SlotChangeParams): Promise<SlotStateUpdateResult | null>

  /**
   * 验证插槽值（可选重写）
   * @param slotName 插槽名称
   * @param value 插槽值
   * @param definition 插槽定义
   * @returns 验证结果，返回错误信息或null
   */
  protected validateSlotValue(
    slotName: string,
    value: any,
    definition: any,
  ): string | null {
    if (definition.required && (value === undefined || value === null || value === '')) {
      return `${definition.label || slotName}是必填项`
    }

    if (definition.validation) {
      const { pattern, message, min, max } = definition.validation

      if (pattern && typeof value === 'string' && !pattern.test(value)) {
        return message || `${definition.label || slotName}格式不正确`
      }

      if (typeof value === 'string') {
        if (min !== undefined && value.length < min) {
          return `${definition.label || slotName}长度不能少于${min}个字符`
        }
        if (max !== undefined && value.length > max) {
          return `${definition.label || slotName}长度不能超过${max}个字符`
        }
      }

      if (typeof value === 'number') {
        if (min !== undefined && value < min) {
          return `${definition.label || slotName}不能小于${min}`
        }
        if (max !== undefined && value > max) {
          return `${definition.label || slotName}不能大于${max}`
        }
      }
    }

    return null
  }

  /**
   * 通用HTTP请求方法
   * @param config 请求配置
   * @returns Promise<ApiResponse>
   */
  protected async makeRequest<T = any>(config: RequestConfig): Promise<ApiResponse<T>> {
    try {
      const { url, method = 'GET', params, data, headers = {} } = config

      // 添加认证头
      const userStore = useUserStore()
      if (userStore.isLogin) {
        headers.Authorization = `Bearer ${userStore.token}`
      }

      let requestUrl = url
      if (params && method === 'GET') {
        const searchParams = new URLSearchParams()
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            searchParams.append(key, String(value))
          }
        })
        requestUrl += `?${searchParams.toString()}`
      }

      const requestInit: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...headers, // 现在包含了Authorization头
        },
      }

      if (data && method !== 'GET') {
        requestInit.body = JSON.stringify(data)
      }

      const response = await fetch(requestUrl, requestInit)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      // 直接处理JSON响应，让vite处理流式响应
      const result = await response.json()

      return {
        data: result,
        success: true,
      }
    }
    catch (error) {
      console.error(`Request failed for ${config.url}:`, error)
      return {
        data: null,
        success: false,
        message: error instanceof Error ? error.message : '请求失败',
      }
    }
  }

  /**
   * 深拷贝对象
   * @param obj 要拷贝的对象
   * @returns 拷贝后的对象
   */
  protected deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') {
      return obj
    }

    if (obj instanceof Date) {
      return new Date(obj.getTime()) as T
    }

    if (obj instanceof Array) {
      return obj.map(item => this.deepClone(item)) as T
    }

    if (typeof obj === 'object') {
      const cloned = {} as T
      Object.keys(obj).forEach((key) => {
        (cloned as any)[key] = this.deepClone((obj as any)[key])
      })
      return cloned
    }

    return obj
  }

  /**
   * 从模板内容中解析插槽名称
   * @param templateContent 模板内容
   * @returns 插槽名称数组
   */
  protected parseSlotNames(templateContent: string): string[] {
    if (!templateContent) {
      return []
    }

    const slotMatches = templateContent.match(/\{\{([^{}]+)\}\}/g) || []
    return slotMatches.map(match => match.replace(/[{}]/g, '').trim())
  }

  /**
   * 合并插槽定义
   * @param base 基础定义
   * @param override 覆盖定义
   * @returns 合并后的定义
   */
  protected mergeSlotDefinitions(
    base: SlotDefinitions,
    override: SlotDefinitions,
  ): SlotDefinitions {
    const result = this.deepClone(base)

    Object.entries(override).forEach(([key, definition]) => {
      if (result[key]) {
        result[key] = { ...result[key], ...definition }
      }
      else {
        result[key] = definition
      }
    })

    return result
  }
}
