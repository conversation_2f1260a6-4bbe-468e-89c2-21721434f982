export function assembleVisual(dataArray: any, visualJson: any): string {
  if (Array.isArray(visualJson)) {
    return assembleTabs(dataArray, visualJson);
  } else {
    return assembleLayout(dataArray, visualJson);
  }
}

export function assembleTabs(dataArray: any, visualJson: any[]): string {
  let tabs = '';
  if (visualJson.length > 0) {
    visualJson.forEach(tab => tabs += assembleOneTab(dataArray, tab));
  }
  return `<div><a-tabs v-model:activeKey="activeKey" tabPosition="top" style="margin: 5px 10px;">
    ${tabs}
  </a-tabs></div>`;
}

export function assembleOneTab(dataArray: any, tab: any): string {
  if (!tab) return '';
  let tabString = '';

  if (tab.mapId) {
    dataArray.visualParam = {
      mapId: tab.mapId,
      shotId: tab.shotId
    };
  } else {
    dataArray.visualParam = {};
  }

  if (tab.layout) {
    let layoutJson = JSON.parse(tab.layout);
    tabString = assembleLayout(dataArray, layoutJson);
  }
  return `<a-tab-pane key="${tab.name}" tab="${tab.name}">
    ${tabString}
  </a-tab-pane>`;
}

export function assembleLayout(dataArray: any, layoutJson: any): string {
  let rows = '';
  for (let rowNo = 0; rowNo < layoutJson.rows.length; rowNo++) {
    rows += assembleOneRow(dataArray, layoutJson.rows[rowNo]);
  }
  return `<div style="width:100%; text-align: center; margin-bottom: 20px; font-family: Microsoft YaHei;">
    ${rows}
  </div>`;
}

export function assembleOneRow(dataArray: any, row: any): string {
  if (!row) return '';
  let rowString = '';
  let rowHeight = 0;
  const hasColumns = row.columns !== undefined && row.columns.length > 0;

  if (hasColumns) {
    row.columns.forEach((column: any) => {
      rowString += assembleOneColumn(dataArray, column);
      if (column.height > rowHeight) rowHeight = column.height;
    });
  }

  return rowHeight > 0
    ? `<a-row style="height: ${rowHeight}px">${rowString}</a-row>`
    : `<a-row>${rowString}</a-row>`;
}

export function assembleOneColumn(dataArray: any, column: any): string {
  const columnStart = assembleColumnStart(dataArray, column);
  const content = assembleColumnContent(dataArray, column);
  return `${columnStart}${content}</a-col>`;
}

export function assembleColumnStart(dataArray: any, column: any): string {
  const overflow = column.scrollSwitch === 'Y' ? 'overflow: scroll;' : '';
  return `<a-col :span="${column.width}" style="border:none;margin-bottom:1px;height:${column.height}px;${overflow}">`;
}

export function assembleColumnContent(dataArray: any, column: any): string {
  let content = '';
  if (column.rows) {
    column.rows.forEach((subRow: any) => content += assembleOneRow(dataArray, subRow));
  }
  const component = assembleComponentByType(dataArray, column);
  return content + (component || '');
}

export function assembleComponentByType(dataArray: any, column: any): string | undefined {
  const type = blockType(column);
  if (!type) return;

  let component = '';
  let index = 0;

  switch (type) {
    case 'chart':
      const columnOption = column.chart.option;
      if (dataArray.disableAnimation) {
        columnOption.animation = false;
      }
      dataArray.optionArray.value.push(columnOption);
      index = dataArray.optionArray.value.length - 1;
      component = `<StoryChart :optionArray="optionArray" :index="${index}" :height="${column.height}" />`;
      break;

    case 'table':
      if (column.height) {
        dataArray.tableArray.value.push(column.table);
        index = dataArray.tableArray.value.length - 1;
        component = `<StoryTable :tableArray="tableArray" :index="${index}" :height="${column.height}" />`;
      }
      break;

    case 'media':
      if (column.height) {
        dataArray.mediaArray.value.push(column.media);
        index = dataArray.mediaArray.value.length - 1;
        component = `<Media :mediaArray="mediaArray" :index="${index}" :height="${column.height}" />`;
      }
      break;

    case 'wordCloud':
      if (column.height) {
        dataArray.mediaArray.value.push(column.wordCloud);
        index = dataArray.mediaArray.value.length - 1;
        component = `<Media :mediaArray="mediaArray" :index="${index}" :height="${column.height}" />`;
      }
      break;

    case 'text':
      dataArray.textArray.value.push(column.text);
      index = dataArray.textArray.value.length - 1;
      component = `<TextBlock :textArray="textArray" :index="${index}" :height="${column.height}" />`;
      break;

    case 'tag':
      const tag = column.tag;
      if (dataArray.visualParam) {
        tag.mapId = dataArray.visualParam.mapId;
        tag.shotId = dataArray.visualParam.shotId;
      }
      dataArray.tagArray.value.push(tag);
      index = dataArray.tagArray.value.length - 1;
      component = `<TagBlock :tagArray="tagArray" :index="${index}" :height="${column.height}" />`;
      break;
  }

  return component;
}

export function blockType(column: any): string | null {
  if (column.chart) return 'chart';
  if (column.table) return 'table';
  if (column.media) return 'media';
  if (column.wordCloud) return 'wordCloud';
  if (column.text) return 'text';
  if (column.tag) return 'tag';
  return null;
}
