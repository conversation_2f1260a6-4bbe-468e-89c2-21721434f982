<script setup lang="ts">
import type { RechargeRecord, RechargeRequest } from '@/types/billing'
import type { TableColumnsType } from 'ant-design-vue'
import BalanceDisplay from '@/components/billing/BalanceDisplay.vue'
import { useBillingStore } from '@/stores/billing'
import { formatCurrency, getCurrencySymbol } from '@/utils/format'
import {
  AlipayOutlined,
  CreditCardOutlined,
  ReloadOutlined,
  WechatOutlined,
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, nextTick, onMounted, ref } from 'vue'

// Store
const billingStore = useBillingStore()

// 响应式引用
const rechargeFormRef = ref<HTMLElement>()
const selectedAmount = ref<number>(0)
const customAmount = ref<number | null>(null)
const selectedPaymentMethod = ref<string>('')
const isProcessing = ref(false)
const historyLoading = ref(false)
const detailDrawerVisible = ref(false)
const selectedRecord = ref<RechargeRecord | null>(null)

// 充值记录相关
const rechargeHistory = ref<RechargeRecord[]>([])
const historyFilters = ref({
  status: '',
  dateRange: null as any,
})

const historyPagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
})

// 计算属性
const currency = computed(() => billingStore.currency)
const paymentMethods = computed(() => billingStore.paymentMethods || [])

const presetAmounts = computed(() => [
  { value: 10, bonus: 0 },
  { value: 50, bonus: 5 },
  { value: 100, bonus: 15 },
  { value: 500, bonus: 100 },
  { value: 1000, bonus: 250 },
  { value: 2000, bonus: 600 },
])

const minRechargeAmount = computed(() => 1)
const maxRechargeAmount = computed(() => 10000)

const finalAmount = computed(() => {
  return selectedAmount.value > 0 ? selectedAmount.value : (customAmount.value || 0)
})

const bonusAmount = computed(() => {
  const preset = presetAmounts.value.find(p => p.value === selectedAmount.value)
  return preset?.bonus || 0
})

const canSubmitRecharge = computed(() => {
  return finalAmount.value >= minRechargeAmount.value
    && selectedPaymentMethod.value
    && !isProcessing.value
})

// 表格列定义
const historyColumns: TableColumnsType = [
  {
    title: '充值单号',
    dataIndex: 'id',
    key: 'id',
    width: 120,
    ellipsis: true,
  },
  {
    title: '充值金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 120,
    align: 'right',
  },
  {
    title: '支付方式',
    dataIndex: 'paymentMethod',
    key: 'paymentMethod',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 160,
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
  },
]

// 方法
function selectAmount(amount: number) {
  selectedAmount.value = amount
  customAmount.value = null

  // 自动选择默认支付方式
  if (!selectedPaymentMethod.value && paymentMethods.value.length > 0) {
    const enabledMethod = paymentMethods.value.find(m => m.enabled)
    if (enabledMethod) {
      selectedPaymentMethod.value = enabledMethod.code
    }
  }
}

function onCustomAmountChange(value: number | null) {
  if (value && value > 0) {
    selectedAmount.value = 0

    // 自动选择默认支付方式
    if (!selectedPaymentMethod.value && paymentMethods.value.length > 0) {
      const enabledMethod = paymentMethods.value.find((m: any) => m.enabled)
      if (enabledMethod) {
        selectedPaymentMethod.value = enabledMethod.code
      }
    }
  }
}

function scrollToRechargeForm() {
  if (rechargeFormRef.value) {
    rechargeFormRef.value.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    })
  }
}

async function submitRecharge() {
  if (!canSubmitRecharge.value) {
    return
  }

  isProcessing.value = true
  try {
    const rechargeData: RechargeRequest = {
      amount: finalAmount.value,
      paymentMethod: selectedPaymentMethod.value as 'ALIPAY' | 'WECHAT' | 'BANK_CARD' | 'OTHER',
      description: `充值 ${formatCurrency(finalAmount.value)}`,
    }

    await billingStore.submitRecharge(rechargeData)

    message.success('充值订单创建成功，请完成支付')

    // 重置表单
    selectedAmount.value = 0
    customAmount.value = null
    selectedPaymentMethod.value = ''

    // 刷新余额和记录
    await billingStore.refreshBalance()
    await loadRechargeHistory()

    // 如果是模拟支付，可以模拟支付成功
    if (process.env.NODE_ENV === 'development') {
      setTimeout(() => {
        message.success('模拟支付成功，余额已更新')
        billingStore.refreshBalance()
      }, 2000)
    }
  }
  catch (error: any) {
    console.error('充值失败:', error)
    message.error(error.message || '充值失败，请稍后重试')
  }
  finally {
    isProcessing.value = false
  }
}

async function loadRechargeHistory() {
  historyLoading.value = true
  try {
    const params = {
      page: historyPagination.value.current - 1,
      size: historyPagination.value.pageSize,
      status: (historyFilters.value.status as 'PENDING' | 'SUCCESS' | 'FAILED' | 'CANCELLED' | undefined) || undefined,
      startDate: historyFilters.value.dateRange?.[0]?.format('YYYY-MM-DD'),
      endDate: historyFilters.value.dateRange?.[1]?.format('YYYY-MM-DD'),
    }

    await billingStore.fetchRechargeRecords(params)
    rechargeHistory.value = billingStore.rechargeRecords
    historyPagination.value = { ...billingStore.rechargeRecordsPagination }
  }
  catch (error: any) {
    console.error('加载充值记录失败:', error)
    message.error('加载充值记录失败')
  }
  finally {
    historyLoading.value = false
  }
}

function refreshHistory() {
  loadRechargeHistory()
}

function onHistoryFilterChange() {
  historyPagination.value.current = 1
  loadRechargeHistory()
}

function resetHistoryFilters() {
  historyFilters.value.status = ''
  historyFilters.value.dateRange = null
  historyPagination.value.current = 1
  loadRechargeHistory()
}

function onHistoryTableChange({ current, pageSize }: any) {
  historyPagination.value.current = current
  historyPagination.value.pageSize = pageSize
  loadRechargeHistory()
}

function viewRechargeDetail(record: RechargeRecord) {
  selectedRecord.value = record
  detailDrawerVisible.value = true
}

function closeDetailDrawer() {
  detailDrawerVisible.value = false
  selectedRecord.value = null
}

function continuePayment(_record: RechargeRecord) {
  message.info('继续支付功能开发中...')
  // TODO: 实现继续支付逻辑
}

async function cancelRecharge(record: RechargeRecord) {
  try {
    await billingStore.cancelRecharge(record.id)
    message.success('充值订单已取消')
    await loadRechargeHistory()
  }
  catch (error: any) {
    console.error('取消充值失败:', error)
    message.error('取消充值失败')
  }
}

// 工具方法
function getPaymentIcon(code: string) {
  const iconMap: Record<string, any> = {
    alipay: AlipayOutlined,
    wechat: WechatOutlined,
  }
  return iconMap[code] || CreditCardOutlined
}

function getPaymentMethodName(code: string) {
  const method = paymentMethods.value.find(m => m.code === code)
  return method?.name || code
}

function getStatusColor(status: string) {
  const colorMap: Record<string, string> = {
    PENDING: 'orange',
    PAID: 'blue',
    COMPLETED: 'green',
    CANCELLED: 'default',
    FAILED: 'red',
  }
  return colorMap[status] || 'default'
}

function getStatusText(status: string) {
  const textMap: Record<string, string> = {
    PENDING: '待支付',
    PAID: '已支付',
    COMPLETED: '已完成',
    CANCELLED: '已取消',
    FAILED: '支付失败',
  }
  return textMap[status] || status
}

function formatDateTime(dateTime: string) {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(async () => {
  // 初始化数据
  await Promise.all([
    billingStore.fetchPaymentMethods(),
    loadRechargeHistory(),
  ])
})
</script>

<template>
  <div class="recharge-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">
        账户充值
      </h1>
      <p class="page-description">
        为您的账户充值，享受更多AI服务
      </p>
    </div>

    <div class="recharge-container">
      <!-- 当前余额展示 -->
      <div class="balance-section">
        <BalanceDisplay
          size="large"
          :auto-refresh="true"
          :refresh-interval="30"
          @recharge-click="scrollToRechargeForm"
        />
      </div>

      <!-- 充值表单 -->
      <div ref="rechargeFormRef" class="recharge-form-section">
        <a-card title="选择充值金额" class="recharge-card">
          <div class="amount-selection">
            <!-- 预设金额按钮 -->
            <div class="preset-amounts">
              <h3 class="section-title">
                推荐金额
              </h3>
              <div class="amount-buttons">
                <a-button
                  v-for="amount in presetAmounts"
                  :key="amount.value"
                  :type="selectedAmount === amount.value ? 'primary' : 'default'"
                  class="amount-btn" :class="[{ selected: selectedAmount === amount.value }]"
                  @click="selectAmount(amount.value)"
                >
                  <div class="amount-content">
                    <span class="amount-value">{{ formatCurrency(amount.value, currency) }}</span>
                    <span v-if="amount.bonus > 0" class="amount-bonus">
                      送{{ formatCurrency(amount.bonus, currency) }}
                    </span>
                  </div>
                </a-button>
              </div>
            </div>

            <!-- 自定义金额输入 -->
            <div class="custom-amount">
              <h3 class="section-title">
                自定义金额
              </h3>
              <a-input-number
                v-model:value="customAmount"
                :min="minRechargeAmount"
                :max="maxRechargeAmount"
                :precision="2"
                :step="1"
                placeholder="请输入充值金额"
                class="amount-input"
                @change="onCustomAmountChange"
              >
                <template #addonBefore>
                  {{ getCurrencySymbol(currency) }}
                </template>
              </a-input-number>
              <div class="amount-tips">
                <span class="tip-text">
                  最低充值 {{ formatCurrency(minRechargeAmount, currency) }}，
                  最高充值 {{ formatCurrency(maxRechargeAmount, currency) }}
                </span>
              </div>
            </div>
          </div>

          <!-- 充值信息确认 -->
          <div v-if="finalAmount > 0" class="recharge-summary">
            <a-divider />
            <div class="summary-content">
              <div class="summary-item">
                <span class="label">充值金额：</span>
                <span class="value primary">{{ formatCurrency(finalAmount, currency) }}</span>
              </div>
              <div v-if="bonusAmount > 0" class="summary-item">
                <span class="label">赠送金额：</span>
                <span class="value bonus">{{ formatCurrency(bonusAmount, currency) }}</span>
              </div>
              <div class="summary-item total">
                <span class="label">到账金额：</span>
                <span class="value">{{ formatCurrency(finalAmount + bonusAmount, currency) }}</span>
              </div>
            </div>
          </div>

          <!-- 支付方式选择 -->
          <div v-if="finalAmount > 0" class="payment-section">
            <a-divider />
            <h3 class="section-title">
              选择支付方式
            </h3>
            <a-radio-group v-model:value="selectedPaymentMethod" class="payment-methods">
              <a-radio
                v-for="method in paymentMethods"
                :key="method.code"
                :value="method.code"
                :disabled="!method.enabled"
                class="payment-method"
              >
                <div class="payment-content">
                  <div class="payment-icon">
                    <component :is="getPaymentIcon(method.code)" />
                  </div>
                  <span class="payment-name">{{ method.name }}</span>
                  <span v-if="!method.enabled" class="payment-disabled">(暂不可用)</span>
                </div>
              </a-radio>
            </a-radio-group>
          </div>

          <!-- 充值按钮 -->
          <div v-if="finalAmount > 0" class="recharge-actions">
            <a-button
              type="primary"
              size="large"
              :loading="isProcessing"
              :disabled="!canSubmitRecharge"
              class="recharge-btn"
              @click="submitRecharge"
            >
              <template #icon>
                <CreditCardOutlined />
              </template>
              确认充值 {{ formatCurrency(finalAmount, currency) }}
            </a-button>
          </div>
        </a-card>
      </div>

      <!-- 充值记录 -->
      <div class="recharge-history-section">
        <a-card title="充值记录" class="history-card">
          <template #extra>
            <a-button :loading="historyLoading" @click="refreshHistory">
              <template #icon>
                <ReloadOutlined />
              </template>
              刷新
            </a-button>
          </template>

          <!-- 筛选条件 -->
          <div class="history-filters">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-select
                  v-model:value="historyFilters.status"
                  placeholder="选择状态"
                  allow-clear
                  @change="onHistoryFilterChange"
                >
                  <a-select-option value="PENDING">
                    待支付
                  </a-select-option>
                  <a-select-option value="PAID">
                    已支付
                  </a-select-option>
                  <a-select-option value="COMPLETED">
                    已完成
                  </a-select-option>
                  <a-select-option value="CANCELLED">
                    已取消
                  </a-select-option>
                  <a-select-option value="FAILED">
                    支付失败
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :span="10">
                <a-range-picker
                  v-model:value="historyFilters.dateRange"
                  class="date-range"
                  @change="onHistoryFilterChange"
                />
              </a-col>
              <a-col :span="6">
                <a-button @click="resetHistoryFilters">
                  重置
                </a-button>
              </a-col>
            </a-row>
          </div>

          <!-- 充值记录表格 -->
          <a-table
            :columns="historyColumns"
            :data-source="rechargeHistory"
            :loading="historyLoading"
            :pagination="historyPagination"
            row-key="id"
            class="history-table"
            @change="onHistoryTableChange"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'amount'">
                <span class="amount-cell">
                  {{ formatCurrency(record.amount, record.currency) }}
                </span>
              </template>

              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </template>

              <template v-if="column.key === 'createdAt'">
                <span>{{ formatDateTime(record.createdAt) }}</span>
              </template>

              <template v-if="column.key === 'actions'">
                <a-space>
                  <a-button
                    type="link"
                    size="small"
                    @click="viewRechargeDetail(record)"
                  >
                    查看详情
                  </a-button>
                  <a-button
                    v-if="record.status === 'PENDING'"
                    type="link"
                    size="small"
                    @click="continuePayment(record)"
                  >
                    继续支付
                  </a-button>
                  <a-button
                    v-if="record.status === 'PENDING'"
                    type="link"
                    size="small"
                    danger
                    @click="cancelRecharge(record)"
                  >
                    取消
                  </a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>
      </div>
    </div>

    <!-- 充值详情抽屉 -->
    <a-drawer
      v-model:open="detailDrawerVisible"
      title="充值详情"
      placement="right"
      :width="400"
      @close="closeDetailDrawer"
    >
      <div v-if="selectedRecord" class="recharge-detail">
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="充值单号">
            {{ selectedRecord.id }}
          </a-descriptions-item>
          <a-descriptions-item label="充值金额">
            <span class="amount-value">
              {{ formatCurrency(selectedRecord.amount, selectedRecord.currency) }}
            </span>
          </a-descriptions-item>
          <a-descriptions-item label="支付方式">
            {{ getPaymentMethodName(selectedRecord.paymentMethod) }}
          </a-descriptions-item>
          <a-descriptions-item label="充值状态">
            <a-tag :color="getStatusColor(selectedRecord.status)">
              {{ getStatusText(selectedRecord.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatDateTime(selectedRecord.createdAt) }}
          </a-descriptions-item>
          <a-descriptions-item v-if="selectedRecord.completedAt" label="完成时间">
            {{ formatDateTime(selectedRecord.completedAt) }}
          </a-descriptions-item>
          <a-descriptions-item v-if="selectedRecord.remark" label="备注">
            {{ selectedRecord.remark }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-drawer>
  </div>
</template>

<style scoped lang="scss">
.recharge-page {
  max-width: 1200px;
  padding: 24px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 32px;
  text-align: center;

  .page-title {
    margin-bottom: 8px;
    font-size: 32px;
    font-weight: 600;
    color: var(--text-color, #262626);
  }

  .page-description {
    margin: 0;
    font-size: 16px;
    color: var(--text-color-secondary, #8c8c8c);
  }
}

.recharge-container {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.balance-section {
  display: flex;
  justify-content: center;
}

.recharge-card,
.history-card {
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
}

.amount-selection {
  .section-title {
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color, #262626);
  }

  .preset-amounts {
    margin-bottom: 32px;

    .amount-buttons {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
    }

    .amount-btn {
      height: auto;
      padding: 16px;
      border-radius: 8px;
      transition: all 0.3s ease;

      &.selected {
        border-color: var(--primary-color, #1890ff);
        box-shadow: 0 0 0 2px rgb(24 144 255 / 20%);
      }

      .amount-content {
        display: flex;
        flex-direction: column;
        gap: 4px;
        align-items: center;

        .amount-value {
          font-size: 18px;
          font-weight: 600;
        }

        .amount-bonus {
          font-size: 12px;
          color: var(--success-color, #52c41a);
        }
      }
    }
  }

  .custom-amount {
    .amount-input {
      width: 300px;
    }

    .amount-tips {
      margin-top: 8px;

      .tip-text {
        font-size: 12px;
        color: var(--text-color-secondary, #8c8c8c);
      }
    }
  }
}

.recharge-summary {
  .summary-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .summary-item {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .label {
      font-size: 14px;
      color: var(--text-color-secondary, #8c8c8c);
    }

    .value {
      font-size: 16px;
      font-weight: 500;

      &.primary {
        color: var(--primary-color, #1890ff);
      }

      &.bonus {
        color: var(--success-color, #52c41a);
      }
    }

    &.total {
      padding-top: 8px;
      border-top: 1px solid var(--border-color, #e8e8e8);

      .value {
        font-size: 18px;
        font-weight: 600;
        color: var(--primary-color, #1890ff);
      }
    }
  }
}

.payment-section {
  .payment-methods {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .payment-method {
    .payment-content {
      display: flex;
      gap: 12px;
      align-items: center;

      .payment-icon {
        font-size: 20px;
        color: var(--primary-color, #1890ff);
      }

      .payment-name {
        font-size: 14px;
        font-weight: 500;
      }

      .payment-disabled {
        font-size: 12px;
        color: var(--text-color-secondary, #8c8c8c);
      }
    }
  }
}

.recharge-actions {
  margin-top: 32px;
  text-align: center;

  .recharge-btn {
    height: 48px;
    padding: 0 32px;
    font-size: 16px;
    font-weight: 600;
  }
}

.history-filters {
  margin-bottom: 24px;

  .date-range {
    width: 100%;
  }
}

.history-table {
  .amount-cell {
    font-weight: 600;
    color: var(--primary-color, #1890ff);
  }
}

.recharge-detail {
  .amount-value {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color, #1890ff);
  }
}

// 响应式设计
@media (width <= 768px) {
  .recharge-page {
    padding: 16px;
  }

  .page-header {
    .page-title {
      font-size: 24px;
    }
  }

  .amount-buttons {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)) !important;
  }

  .amount-input {
    width: 100% !important;
  }

  .history-filters {
    .ant-row {
      flex-direction: column;
      gap: 16px;
    }
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .recharge-page {
    --text-color: #e8e8e8;
    --text-color-secondary: #a6a6a6;
    --border-color: #434343;
    --primary-color: #1890ff;
    --success-color: #52c41a;
  }
}
</style>
