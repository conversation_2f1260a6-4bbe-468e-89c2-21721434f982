<script setup lang="ts">
import type { AppealRequest, UsageRecord } from '@/types/billing'
import type { TableColumnsType } from 'ant-design-vue'
import { useBillingStore } from '@/stores/billing'
import { formatCurrency, formatNumber, getCurrencySymbol } from '@/utils/format'
import {
  BarChartOutlined,
  DownloadOutlined,
  DownOutlined,
  ReloadOutlined,
  SearchOutlined,
  TableOutlined,
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, nextTick, onMounted, ref, watch } from 'vue'

// Store
const billingStore = useBillingStore()

// 响应式引用
const loading = ref(false)
const statsLoading = ref(false)
const exportLoading = ref(false)
const viewMode = ref<'table' | 'chart'>('table')
const detailDrawerVisible = ref(false)
const appealModalVisible = ref(false)
const appealSubmitting = ref(false)
const selectedRecord = ref<UsageRecord | null>(null)

// 图表引用
const costTrendChart = ref<HTMLElement>()
const tokenDistributionChart = ref<HTMLElement>()
const serviceTypeChart = ref<HTMLElement>()
const modelUsageChart = ref<HTMLElement>()

// 数据
const usageRecords = ref<UsageRecord[]>([])
const todayStats = ref({
  totalCost: 0,
  changePercent: 0,
  changeType: 'increase' as 'increase' | 'decrease',
  totalTokens: 0,
  tokenChangePercent: 0,
  tokenChangeType: 'increase' as 'increase' | 'decrease',
})
const monthStats = ref({
  totalCost: 0,
  changePercent: 0,
  changeType: 'increase' as 'increase' | 'decrease',
  totalTokens: 0,
  tokenChangePercent: 0,
  tokenChangeType: 'increase' as 'increase' | 'decrease',
})

// 筛选条件
const filters = ref({
  dateRange: null as any,
  status: '',
  minCost: null as number | null,
  maxCost: null as number | null,
})

// 分页
const pagination = ref({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
})

// 申诉表单
const appealForm = ref({
  type: '',
  reason: '',
  contact: '',
})

// 计算属性
const currency = computed(() => billingStore.currency)

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '记录ID',
    dataIndex: 'id',
    key: 'id',
    width: 120,
    ellipsis: true,
    fixed: 'left',
  },
  {
    title: '会话ID',
    dataIndex: 'conversationId',
    key: 'conversationId',
    width: 120,
    ellipsis: true,
  },
  {
    title: '输入Token',
    dataIndex: 'inputTokens',
    key: 'inputTokens',
    width: 100,
    align: 'right',
  },
  {
    title: '输出Token',
    dataIndex: 'outputTokens',
    key: 'outputTokens',
    width: 100,
    align: 'right',
  },
  {
    title: '总Token',
    dataIndex: 'totalTokens',
    key: 'totalTokens',
    width: 100,
    align: 'right',
  },
  {
    title: '消费金额',
    dataIndex: 'cost',
    key: 'cost',
    width: 120,
    align: 'right',
  },
  {
    title: '使用时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 160,
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right',
  },
]

const billingDetailColumns: TableColumnsType = [
  {
    title: '计费项',
    dataIndex: 'item',
    key: 'item',
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    key: 'quantity',
    align: 'right',
  },
  {
    title: '单价',
    dataIndex: 'unitPrice',
    key: 'unitPrice',
    align: 'right',
  },
  {
    title: '费用',
    dataIndex: 'cost',
    key: 'cost',
    align: 'right',
  },
]

// 方法
async function loadUsageRecords() {
  loading.value = true
  try {
    const params = {
      page: pagination.value.current - 1,
      size: pagination.value.pageSize,
      minAmount: filters.value.minCost || undefined,
      maxAmount: filters.value.maxCost || undefined,
      startDate: filters.value.dateRange?.[0]?.format('YYYY-MM-DD'),
      endDate: filters.value.dateRange?.[1]?.format('YYYY-MM-DD'),
    }

    await billingStore.fetchUsageRecords(params)
    usageRecords.value = billingStore.usageRecords
    pagination.value.total = billingStore.usageRecordsPagination.total
  }
  catch (error: any) {
    console.error('加载消费记录失败:', error)
    message.error('加载消费记录失败')
  }
  finally {
    loading.value = false
  }
}

async function loadStats() {
  statsLoading.value = true
  try {
    await billingStore.fetchStatistics()
    if (billingStore.statistics) {
      todayStats.value = billingStore.statistics as any
      monthStats.value = billingStore.statistics as any
    }
  }
  catch (error: any) {
    console.error('加载统计数据失败:', error)
    message.error('加载统计数据失败')
  }
  finally {
    statsLoading.value = false
  }
}

function onFilter() {
  pagination.value.current = 1
  loadUsageRecords()
}

function resetFilters() {
  filters.value = {
    dateRange: null,
    status: '',
    minCost: null,
    maxCost: null,
  }
  pagination.value.current = 1
  loadUsageRecords()
}

function refreshData() {
  Promise.all([loadUsageRecords(), loadStats()])
}

function onTableChange({ current, pageSize }: any) {
  pagination.value.current = current
  pagination.value.pageSize = pageSize
  loadUsageRecords()
}

function onViewModeChange({ key }: { key: string }) {
  viewMode.value = key as 'table' | 'chart'
  if (key === 'chart') {
    nextTick(() => {
      initCharts()
    })
  }
}

function viewDetail(record: UsageRecord) {
  selectedRecord.value = record
  detailDrawerVisible.value = true
}

function closeDetailDrawer() {
  detailDrawerVisible.value = false
  selectedRecord.value = null
}

function createAppeal(record: UsageRecord) {
  selectedRecord.value = record
  appealForm.value = {
    type: '',
    reason: '',
    contact: '',
  }
  appealModalVisible.value = true
}

async function submitAppeal() {
  if (!selectedRecord.value || !appealForm.value.type || !appealForm.value.reason) {
    message.error('请填写完整的申诉信息')
    return
  }

  appealSubmitting.value = true
  try {
    const appealData: AppealRequest = {
      usageRecordId: selectedRecord.value.id.toString(),
      type: appealForm.value.type as 'BILLING_ERROR' | 'SERVICE_ISSUE' | 'REFUND_REQUEST' | 'OTHER',
      reason: appealForm.value.reason,
      description: appealForm.value.contact || `申诉联系方式：${appealForm.value.contact}`,
    }

    await billingStore.createAppeal(appealData)
    message.success('申诉提交成功，我们会尽快处理')

    appealModalVisible.value = false
    loadUsageRecords() // 刷新列表
  }
  catch (error: any) {
    console.error('提交申诉失败:', error)
    message.error('提交申诉失败，请稍后重试')
  }
  finally {
    appealSubmitting.value = false
  }
}

async function exportData() {
  exportLoading.value = true
  try {
    const params = {
      minCost: filters.value.minCost || undefined,
      maxCost: filters.value.maxCost || undefined,
      startDate: filters.value.dateRange?.[0]?.format('YYYY-MM-DD'),
      endDate: filters.value.dateRange?.[1]?.format('YYYY-MM-DD'),
    }

    // TODO: 实现导出功能
    console.log('导出参数:', params)
    message.success('导出成功，请查看下载文件')
  }
  catch (error: any) {
    console.error('导出失败:', error)
    message.error('导出失败，请稍后重试')
  }
  finally {
    exportLoading.value = false
  }
}

async function downloadReceipt(record: UsageRecord) {
  try {
    await billingStore.downloadReceipt(record.id.toString())
    message.success('凭证下载成功')
  }
  catch (error: any) {
    console.error('下载凭证失败:', error)
    message.error('下载凭证失败')
  }
}

function closeAppealModal() {
  appealModalVisible.value = false
}

async function initCharts() {
  // 这里应该初始化图表，需要引入图表库如 ECharts
  // 由于这是示例代码，只显示结构
  console.log('初始化图表')
}

// 工具方法
function getServiceTypeColor(type: string) {
  const colorMap: Record<string, string> = {
    CHAT: 'blue',
    IMAGE_GENERATION: 'purple',
    DOCUMENT_PROCESSING: 'green',
    CODE_GENERATION: 'orange',
    DATA_ANALYSIS: 'red',
  }
  return colorMap[type] || 'default'
}

function getServiceTypeName(type: string) {
  const nameMap: Record<string, string> = {
    CHAT: '聊天对话',
    IMAGE_GENERATION: '图像生成',
    DOCUMENT_PROCESSING: '文档处理',
    CODE_GENERATION: '代码生成',
    DATA_ANALYSIS: '数据分析',
  }
  return nameMap[type] || type
}

function formatDateTime(dateTime: string) {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 计费明细辅助方法
function getBillingDetails(record: UsageRecord | null) {
  if (!record) { return [] }
  // 假设 record.billingDetails 为计费明细数组，如无则返回空数组
  // 可根据实际数据结构调整
  return (record as any).billingDetails || []
}

// 生命周期
onMounted(async () => {
  await Promise.all([
    loadUsageRecords(),
    loadStats(),
  ])
})

// 监听视图模式变化
watch(viewMode, (newMode) => {
  if (newMode === 'chart') {
    nextTick(() => {
      initCharts()
    })
  }
})
</script>

<template>
  <div class="usage-history-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">
        消费记录
      </h1>
      <p class="page-description">
        查看您的AI服务使用记录和费用详情
      </p>
    </div>
    <div class="usage-container">
      <!-- 统计卡片 -->
      <div class="stats-section">
        <a-row :gutter="24">
          <a-col :span="6">
            <a-card class="stat-card">
              <a-statistic title="今日消费" :value="todayStats.totalCost" :precision="2" :loading="statsLoading">
                <template #prefix>
                  {{ getCurrencySymbol(currency) }}
                </template>
                <template #suffix>
                  <span class="stat-change" :class="todayStats.changeType">{{ todayStats.changePercent }}%</span>
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="stat-card">
              <a-statistic title="本月消费" :value="monthStats.totalCost" :precision="2" :loading="statsLoading">
                <template #prefix>
                  {{ getCurrencySymbol(currency) }}
                </template>
                <template #suffix>
                  <span class="stat-change" :class="monthStats.changeType">{{ monthStats.changePercent }}%</span>
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="stat-card">
              <a-statistic title="今日Token" :value="todayStats.totalTokens" :loading="statsLoading">
                <template #suffix>
                  <span class="stat-change" :class="todayStats.tokenChangeType">{{ todayStats.tokenChangePercent }}%</span>
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card class="stat-card">
              <a-statistic title="本月Token" :value="monthStats.totalTokens" :loading="statsLoading">
                <template #suffix>
                  <span class="stat-change" :class="monthStats.tokenChangeType">{{ monthStats.tokenChangePercent }}%</span>
                </template>
              </a-statistic>
            </a-card>
          </a-col>
        </a-row>
      </div>
      <!-- 筛选条件 -->
      <div class="filter-section">
        <a-card title="筛选条件" class="filter-card">
          <a-form layout="inline" :model="filters" @finish="onFilter">
            <a-row :gutter="24" style="width: 100%;">
              <a-col :span="6">
                <a-form-item label="时间范围">
                  <a-range-picker v-model:value="filters.dateRange" format="YYYY-MM-DD" :placeholder="['开始日期', '结束日期']" style="width: 100%;" />
                </a-form-item>
              </a-col>
              <a-col :span="5">
                <a-form-item label="最小费用">
                  <a-input-number v-model:value="filters.minCost" :min="0" :precision="2" placeholder="最小费用" style="width: 100%;" />
                </a-form-item>
              </a-col>
              <a-col :span="5">
                <a-form-item label="最大费用">
                  <a-input-number v-model:value="filters.maxCost" :min="0" :precision="2" placeholder="最大费用" style="width: 100%;" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16" style="margin-top: 16px;">
              <a-col>
                <a-button type="primary" html-type="submit" :loading="loading">
                  <template #icon>
                    <SearchOutlined />
                  </template>
                  查询
                </a-button>
              </a-col>
              <a-col>
                <a-button @click="resetFilters">
                  <template #icon>
                    <ReloadOutlined />
                  </template>
                  重置
                </a-button>
              </a-col>
              <a-col>
                <a-button :loading="exportLoading" @click="exportData">
                  <template #icon>
                    <DownloadOutlined />
                  </template>
                  导出
                </a-button>
              </a-col>
            </a-row>
          </a-form>
        </a-card>
      </div>
      <!-- 消费记录表格 -->
      <div class="table-section">
        <a-card title="消费记录" class="table-card">
          <template #extra>
            <a-space>
              <a-button :loading="loading" @click="refreshData">
                <template #icon>
                  <ReloadOutlined />
                </template>
                刷新
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="onViewModeChange">
                    <a-menu-item key="table">
                      <TableOutlined />表格视图
                    </a-menu-item>
                    <a-menu-item key="chart">
                      <BarChartOutlined />图表视图
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button>视图模式<DownOutlined /></a-button>
              </a-dropdown>
            </a-space>
          </template>
          <!-- 表格视图 -->
          <div v-if="viewMode === 'table'">
            <a-table :columns="columns" :data-source="usageRecords" :loading="loading" :pagination="pagination" row-key="id" :scroll="{ x: 1200 }" class="usage-table" @change="onTableChange">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'serviceType'">
                  <a-tag :color="getServiceTypeColor(record.serviceType)">
                    {{ getServiceTypeName(record.serviceType) }}
                  </a-tag>
                </template>
                <template v-if="column.key === 'model'">
                  <a-tag color="blue">
                    {{ record.model }}
                  </a-tag>
                </template>
                <template v-if="column.key === 'inputTokens'">
                  <span class="token-count">{{ formatNumber(record.inputTokens) }}</span>
                </template>
                <template v-if="column.key === 'outputTokens'">
                  <span class="token-count">{{ formatNumber(record.outputTokens) }}</span>
                </template>
                <template v-if="column.key === 'totalTokens'">
                  <span class="token-count total">{{ formatNumber(record.totalTokens) }}</span>
                </template>
                <template v-if="column.key === 'cost'">
                  <span class="cost-amount">{{ formatCurrency(record.cost, currency) }}</span>
                </template>
                <template v-if="column.key === 'createdAt'">
                  <span>{{ formatDateTime(record.createdAt) }}</span>
                </template>
                <template v-if="column.key === 'actions'">
                  <a-space>
                    <a-button type="link" size="small" @click="viewDetail(record)">
                      查看详情
                    </a-button>
                    <a-button v-if="record.canAppeal" type="link" size="small" @click="createAppeal(record)">
                      申诉
                    </a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </div>
          <!-- 图表视图 -->
          <div v-if="viewMode === 'chart'" class="chart-view">
            <a-row :gutter="24">
              <a-col :span="12">
                <div class="chart-container">
                  <h3>消费趋势</h3>
                  <div ref="costTrendChart" class="chart" />
                </div>
              </a-col>
              <a-col :span="12">
                <div class="chart-container">
                  <h3>Token分布</h3>
                  <div ref="tokenDistributionChart" class="chart" />
                </div>
              </a-col>
            </a-row>
            <a-row :gutter="24" style="margin-top: 24px;">
              <a-col :span="12">
                <div class="chart-container">
                  <h3>服务类型分布</h3>
                  <div ref="serviceTypeChart" class="chart" />
                </div>
              </a-col>
              <a-col :span="12">
                <div class="chart-container">
                  <h3>模型使用分布</h3>
                  <div ref="modelUsageChart" class="chart" />
                </div>
              </a-col>
            </a-row>
          </div>
        </a-card>
      </div>
      <!-- 详情抽屉 -->
      <a-drawer v-model:open="detailDrawerVisible" width="600" title="消费详情" :footer="null" @close="closeDetailDrawer">
        <div v-if="selectedRecord" class="usage-detail">
          <div class="detail-header">
            <span>会话ID：</span><span>{{ selectedRecord.conversationId }}</span>
            <span style="margin-left: 24px;">模型：</span><span>{{ selectedRecord.modelName }}</span>
          </div>
          <div class="detail-info">
            <div><span>输入Token：</span><span class="token-value">{{ selectedRecord.inputTokens }}</span></div>
            <div><span>输出Token：</span><span class="token-value">{{ selectedRecord.outputTokens }}</span></div>
            <div><span>总Token：</span><span class="token-value total">{{ selectedRecord.totalTokens }}</span></div>
            <div><span>消费金额：</span><span class="cost-value">{{ formatCurrency(selectedRecord.totalCost, currency) }}</span></div>
            <div><span>使用时间：</span><span>{{ formatDateTime(selectedRecord.createdAt) }}</span></div>
          </div>
          <div class="billing-details">
            <h4>计费明细</h4>
            <a-table :columns="billingDetailColumns" :data-source="getBillingDetails(selectedRecord)" :pagination="false" size="small" />
          </div>
          <div class="detail-actions">
            <a-button type="primary" @click="downloadReceipt(selectedRecord)">
              下载凭证
            </a-button>
          </div>
        </div>
      </a-drawer>
      <!-- 申诉弹窗 -->
      <a-modal v-model:open="appealModalVisible" title="费用申诉" :confirm-loading="appealSubmitting" @ok="submitAppeal" @cancel="closeAppealModal">
        <a-form :model="appealForm" layout="vertical">
          <a-form-item label="申诉类型" required>
            <a-select v-model:value="appealForm.type" placeholder="请选择申诉类型">
              <a-select-option value="BILLING_ERROR">
                计费错误
              </a-select-option>
              <a-select-option value="SERVICE_ISSUE">
                服务问题
              </a-select-option>
              <a-select-option value="REFUND_REQUEST">
                退款请求
              </a-select-option>
              <a-select-option value="OTHER">
                其他
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="申诉原因" required>
            <a-input v-model:value="appealForm.reason" placeholder="请填写申诉原因" />
          </a-form-item>
          <a-form-item label="联系方式">
            <a-input v-model:value="appealForm.contact" placeholder="邮箱/手机号（选填）" />
          </a-form-item>
        </a-form>
      </a-modal>
    </div>
  </div>
</template>

<style scoped lang="scss">
.usage-history-page {
  max-width: 1400px;
  padding: 24px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 32px;
  text-align: center;

  .page-title {
    margin-bottom: 8px;
    font-size: 32px;
    font-weight: 600;
    color: var(--text-color, #262626);
  }

  .page-description {
    margin: 0;
    font-size: 16px;
    color: var(--text-color-secondary, #8c8c8c);
  }
}

.usage-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.stats-section {
  .stat-card {
    text-align: center;
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);

    .stat-change {
      margin-left: 8px;
      font-size: 12px;

      &.increase {
        color: var(--error-color, #ff4d4f);
      }

      &.decrease {
        color: var(--success-color, #52c41a);
      }
    }
  }
}

.filter-card,
.table-card {
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
}

.usage-table {
  .token-count {
    font-family: Monaco, Menlo, monospace;
    font-size: 13px;

    &.total {
      font-weight: 600;
      color: var(--primary-color, #1890ff);
    }
  }

  .cost-amount {
    font-weight: 600;
    color: var(--primary-color, #1890ff);
  }
}

.chart-view {
  .chart-container {
    h3 {
      margin-bottom: 16px;
      color: var(--text-color, #262626);
      text-align: center;
    }

    .chart {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 300px;
      color: var(--text-color-secondary, #8c8c8c);
      background: var(--background-color-light, #fafafa);
      border-radius: 6px;
    }
  }
}

.usage-detail {
  .token-value {
    font-family: Monaco, Menlo, monospace;

    &.total {
      font-weight: 600;
      color: var(--primary-color, #1890ff);
    }
  }

  .cost-value {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color, #1890ff);
  }

  .billing-details {
    margin-top: 24px;

    h4 {
      margin-bottom: 16px;
      color: var(--text-color, #262626);
    }
  }

  .detail-actions {
    margin-top: 32px;
    text-align: center;
  }
}

// 响应式设计
@media (width <= 1200px) {
  .usage-history-page {
    padding: 16px;
  }

  .stats-section {
    .ant-col {
      margin-bottom: 16px;
    }
  }

  .filter-section {
    .ant-form-item {
      margin-bottom: 16px;
    }
  }
}

@media (width <= 768px) {
  .page-header {
    .page-title {
      font-size: 24px;
    }
  }

  .stats-section {
    .ant-col {
      flex: none !important;
      width: 100% !important;
    }
  }

  .filter-section {
    .ant-col {
      flex: none !important;
      width: 100% !important;
    }
  }

  .usage-table {
    :deep(.ant-table-body) {
      overflow-x: auto;
    }
  }

  .chart-view {
    .ant-col {
      flex: none !important;
      width: 100% !important;
      margin-bottom: 24px;
    }
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .usage-history-page {
    --text-color: #e8e8e8;
    --text-color-secondary: #a6a6a6;
    --background-color-light: #1f1f1f;
    --primary-color: #1890ff;
    --success-color: #52c41a;
    --error-color: #ff4d4f;
  }
}
</style>
