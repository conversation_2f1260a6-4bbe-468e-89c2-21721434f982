<script setup lang="ts">
import type { AppealRecord, AppealRequest, UsageRecord } from '@/types/billing'
import type { TableColumnsType, UploadChangeParam } from 'ant-design-vue'
import { useBillingStore } from '@/stores/billing'
import { formatCurrency, formatDateTime, formatFileSize } from '@/utils/format'
import {
  BugOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  ExclamationCircleOutlined,
  FileTextOutlined,
  FormOutlined,
  QuestionCircleOutlined,
  ReloadOutlined,
  SendOutlined,
  UploadOutlined,
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, onMounted, reactive, ref } from 'vue'

// Store
const billingStore = useBillingStore()

// 响应式引用
const submitting = ref(false)
const recordsLoading = ref(false)
const searchLoading = ref(false)
const detailDrawerVisible = ref(false)
const selectedAppeal = ref<AppealRecord | null>(null)
const selectedAppealType = ref<string>('')
const appealFormRef = ref()

// 数据
const usageRecords = ref<UsageRecord[]>([])
const appealRecords = ref<AppealRecord[]>([])
const attachments = ref<any[]>([])

// 申诉表单
const appealForm = reactive({
  usageRecordId: '',
  reason: '',
  userDescription: '',
  type: 'BILLING_ERROR' as 'BILLING_ERROR' | 'SERVICE_ISSUE' | 'REFUND_REQUEST' | 'OTHER',
  title: '',
  contact: '',
  priority: 'MEDIUM' as string,
  attachments: [] as string[],
})

// 记录筛选
const recordsFilter = ref({
  status: '',
  type: '',
  dateRange: null as any,
})

const recordsPagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
})

// 计算属性
const uploadUrl = computed(() => '/api/billing/appeal/upload')
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${localStorage.getItem('token')}`,
}))

// 申诉类型配置
const appealTypes = [
  {
    value: 'BILLING_ERROR',
    label: '计费错误',
    description: '计费金额或Token数量错误',
    icon: DollarOutlined,
    color: '#f50',
  },
  {
    value: 'SERVICE_ISSUE',
    label: '服务问题',
    description: '服务质量或功能异常',
    icon: BugOutlined,
    color: '#fa8c16',
  },
  {
    value: 'REFUND_REQUEST',
    label: '退款申请',
    description: '申请退还已扣费用',
    icon: ExclamationCircleOutlined,
    color: '#faad14',
  },
  {
    value: 'OTHER',
    label: '其他问题',
    description: '其他计费相关问题',
    icon: QuestionCircleOutlined,
    color: '#1890ff',
  },
]

// 表单验证规则
const appealRules = {
  usageRecordId: [{ required: true, message: '请选择使用记录' }],
  reason: [{ required: true, message: '请详细描述申诉原因' }],
}

// 表格列定义
const recordsColumns: TableColumnsType = [
  {
    title: '申诉ID',
    dataIndex: 'id',
    key: 'id',
    width: 120,
    ellipsis: true,
  },
  {
    title: '标题',
    dataIndex: 'title',
    key: 'title',
    ellipsis: true,
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '紧急程度',
    dataIndex: 'priority',
    key: 'priority',
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 160,
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
  },
]

// 方法
function selectAppealType(type: 'BILLING_ERROR' | 'SERVICE_ISSUE' | 'REFUND_REQUEST' | 'OTHER') {
  selectedAppealType.value = type
  appealForm.type = type
}

async function searchUsageRecords(keyword: string) {
  if (!keyword) { return }

  searchLoading.value = true
  try {
    await billingStore.fetchUsageRecords({ page: 0, size: 10 })
    usageRecords.value = billingStore.usageRecords
  }
  catch (error: any) {
    console.error('搜索消费记录失败:', error)
  }
  finally {
    searchLoading.value = false
  }
}

function filterUsageRecords(input: string, option: any) {
  return option.children.toLowerCase().includes(input.toLowerCase())
}

function beforeUpload(file: File) {
  const isValidType = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'text/plain'].includes(file.type)
  if (!isValidType) {
    message.error('只支持图片、PDF、文本文件')
    return false
  }

  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过 10MB')
    return false
  }

  return true
}

function onAttachmentChange(info: UploadChangeParam) {
  if (info.file.status === 'done') {
    message.success(`${info.file.name} 上传成功`)
    appealForm.attachments.push(info.file.response.fileId)
  }
  else if (info.file.status === 'error') {
    message.error(`${info.file.name} 上传失败`)
  }
}

async function submitAppeal() {
  submitting.value = true
  try {
    if (!appealForm.usageRecordId) {
      message.error('请选择使用记录')
      return
    }

    const appealData: AppealRequest = {
      type: appealForm.type,
      usageRecordId: String(appealForm.usageRecordId),
      reason: appealForm.reason,
      description: appealForm.userDescription,
    }

    await billingStore.createAppeal(appealData)
    message.success('申诉提交成功，我们会尽快为您处理')

    resetForm()
    loadAppealRecords()
  }
  catch (error: any) {
    console.error('提交申诉失败:', error)
    message.error(error.message || '提交申诉失败，请稍后重试')
  }
  finally {
    submitting.value = false
  }
}

function resetForm() {
  appealFormRef.value?.resetFields()
  attachments.value = []
  selectedAppealType.value = ''
  Object.assign(appealForm, {
    type: '',
    usageRecordId: '',
    title: '',
    reason: '',
    contact: '',
    priority: 'MEDIUM',
    attachments: [],
  })
}

async function loadAppealRecords(params?: any) {
  recordsLoading.value = true
  try {
    await billingStore.fetchAppealRecords(params)
    appealRecords.value = billingStore.appealRecords
    recordsPagination.value = billingStore.appealRecordsPagination
  }
  catch (error: any) {
    console.error('加载申诉记录失败:', error)
    message.error('加载申诉记录失败')
  }
  finally {
    recordsLoading.value = false
  }
}

function refreshRecords() {
  loadAppealRecords()
}

function onFilterChange() {
  recordsPagination.value.current = 1
  loadAppealRecords()
}

function resetFilter() {
  recordsFilter.value = {
    status: '',
    type: '',
    dateRange: null,
  }
  recordsPagination.value.current = 1
  loadAppealRecords()
}

function onTableChange({ current, pageSize }: any) {
  recordsPagination.value.current = current
  recordsPagination.value.pageSize = pageSize
  loadAppealRecords()
}

function viewDetail(record: AppealRecord) {
  selectedAppeal.value = record
  detailDrawerVisible.value = true
}

function closeDetailDrawer() {
  detailDrawerVisible.value = false
  selectedAppeal.value = null
}

function editAppeal(record: AppealRecord) {
  // 填充表单进行编辑
  Object.assign(appealForm, {
    type: record.type,
    usageRecordId: record.usageRecordId || '',
    title: record.title,
    reason: record.reason,
    contact: record.contact || '',
    priority: record.priority,
  })
  selectedAppealType.value = record.type

  // 滚动到表单区域
  document.querySelector('.form-section')?.scrollIntoView({ behavior: 'smooth' })
}

async function cancelAppeal(record: AppealRecord) {
  try {
    await billingStore.cancelAppeal(record.id)
    message.success('申诉已撤销')
    loadAppealRecords()
  }
  catch (error: any) {
    console.error('撤销申诉失败:', error)
    message.error('撤销申诉失败')
  }
}

async function downloadAttachment(attachment: any) {
  try {
    await billingStore.downloadAttachment(attachment.id)
    message.success('文件下载成功')
  }
  catch (error: any) {
    console.error('下载文件失败:', error)
    message.error('下载文件失败')
  }
}

function previewAttachment(attachment: any) {
  // 预览文件功能
  window.open(attachment.url, '_blank')
}

// 工具方法
function getAppealTypeColor(type: string) {
  const typeConfig = appealTypes.find(t => t.value === type)
  return typeConfig?.color || 'default'
}

function getAppealTypeName(type: string) {
  const typeConfig = appealTypes.find(t => t.value === type)
  return typeConfig?.label || type
}

function getStatusColor(status: string) {
  const colorMap: Record<string, string> = {
    PENDING: 'orange',
    PROCESSING: 'blue',
    RESOLVED: 'green',
    REJECTED: 'red',
  }
  return colorMap[status] || 'default'
}

function getStatusText(status: string) {
  const textMap: Record<string, string> = {
    PENDING: '待处理',
    PROCESSING: '处理中',
    RESOLVED: '已解决',
    REJECTED: '已拒绝',
  }
  return textMap[status] || status
}

function getPriorityColor(priority: string) {
  const colorMap: Record<string, string> = {
    LOW: 'green',
    MEDIUM: 'orange',
    HIGH: 'red',
  }
  return colorMap[priority] || 'default'
}

function getPriorityText(priority: string) {
  const textMap: Record<string, string> = {
    LOW: '一般',
    MEDIUM: '较急',
    HIGH: '紧急',
  }
  return textMap[priority] || priority
}

function getTimelineColor(action: string) {
  const colorMap: Record<string, string> = {
    提交申诉: 'blue',
    开始处理: 'orange',
    处理完成: 'green',
    申诉拒绝: 'red',
  }
  return colorMap[action] || 'blue'
}

// 生命周期
onMounted(async () => {
  await loadAppealRecords()
})
</script>

<template>
  <div class="appeal-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">
        费用申诉
      </h1>
      <p class="page-description">
        对消费记录有疑问？提交申诉，我们会尽快为您处理
      </p>
    </div>

    <div class="appeal-container">
      <!-- 申诉指南 -->
      <div class="guide-section">
        <a-card title="申诉指南" class="guide-card">
          <div class="guide-content">
            <a-row :gutter="24">
              <a-col :span="8">
                <div class="guide-item">
                  <div class="guide-icon">
                    <FileTextOutlined />
                  </div>
                  <h3>准备材料</h3>
                  <p>准备相关的消费记录、截图等证明材料，详细描述问题</p>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="guide-item">
                  <div class="guide-icon">
                    <FormOutlined />
                  </div>
                  <h3>提交申诉</h3>
                  <p>填写申诉表单，选择合适的申诉类型，提供详细说明</p>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="guide-item">
                  <div class="guide-icon">
                    <ClockCircleOutlined />
                  </div>
                  <h3>等待处理</h3>
                  <p>我们会在1-3个工作日内审核并回复您的申诉</p>
                </div>
              </a-col>
            </a-row>
          </div>
        </a-card>
      </div>

      <!-- 申诉类型卡片 -->
      <div class="appeal-types-section">
        <a-card title="常见申诉类型" class="types-card">
          <a-row :gutter="16">
            <a-col v-for="type in appealTypes" :key="type.value" :span="6">
              <a-card
                class="type-card" :class="[{ selected: selectedAppealType === type.value }]"
                hoverable
                @click="selectAppealType(type.value)"
              >
                <div class="type-content">
                  <div class="type-icon" :style="{ color: type.color }">
                    <component :is="type.icon" />
                  </div>
                  <h4 class="type-title">
                    {{ type.label }}
                  </h4>
                  <p class="type-desc">
                    {{ type.description }}
                  </p>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </a-card>
      </div>

      <!-- 申诉表单 -->
      <div class="form-section">
        <a-card title="提交申诉" class="form-card">
          <a-form
            ref="appealFormRef"
            :model="appealForm"
            :rules="appealRules"
            layout="vertical"
            @finish="submitAppeal"
          >
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="申诉类型" name="type" required>
                  <a-select
                    v-model:value="appealForm.type"
                    placeholder="请选择申诉类型"
                    size="large"
                  >
                    <a-select-option
                      v-for="type in appealTypes"
                      :key="type.value"
                      :value="type.value"
                    >
                      {{ type.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="关联消费记录" name="usageRecordId">
                  <a-select
                    v-model:value="appealForm.usageRecordId"
                    placeholder="选择要申诉的消费记录"
                    size="large"
                    show-search
                    :filter-option="filterUsageRecords"
                    :loading="searchLoading"
                    @search="searchUsageRecords"
                  >
                    <a-select-option
                      v-for="record in usageRecords"
                      :key="record.id"
                      :value="record.id"
                    >
                      {{ record.id }} - {{ formatCurrency(record.totalCost) }} - {{ formatDateTime(record.createdAt) }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <a-form-item label="申诉标题" name="title" required>
              <a-input
                v-model:value="appealForm.title"
                placeholder="请简要描述问题"
                size="large"
                :maxlength="100"
                show-count
              />
            </a-form-item>

            <a-form-item label="问题描述" name="reason" required>
              <a-textarea
                v-model:value="appealForm.reason"
                placeholder="请详细描述您遇到的问题，包括具体的时间、操作步骤、预期结果等"
                :rows="6"
                :maxlength="1000"
                show-count
              />
            </a-form-item>

            <a-form-item label="联系方式" name="contact">
              <a-input
                v-model:value="appealForm.contact"
                placeholder="请提供您的联系方式（邮箱、电话等），方便我们联系您"
                size="large"
              />
            </a-form-item>

            <a-form-item label="附件材料" name="attachments">
              <a-upload
                v-model:file-list="attachments"
                name="file"
                :action="uploadUrl"
                :headers="uploadHeaders"
                :before-upload="beforeUpload"
                multiple
                :max-count="5"
                @change="onAttachmentChange"
              >
                <a-button size="large">
                  <template #icon>
                    <UploadOutlined />
                  </template>
                  上传附件
                </a-button>
              </a-upload>
              <div class="upload-tips">
                支持图片、文档等格式，单个文件不超过10MB，最多上传5个文件
              </div>
            </a-form-item>

            <a-form-item label="紧急程度" name="priority">
              <a-radio-group v-model:value="appealForm.priority" size="large">
                <a-radio-button value="LOW">
                  一般
                </a-radio-button>
                <a-radio-button value="MEDIUM">
                  较急
                </a-radio-button>
                <a-radio-button value="HIGH">
                  紧急
                </a-radio-button>
              </a-radio-group>
            </a-form-item>

            <a-form-item>
              <a-space size="large">
                <a-button
                  type="primary"
                  html-type="submit"
                  size="large"
                  :loading="submitting"
                >
                  <template #icon>
                    <SendOutlined />
                  </template>
                  提交申诉
                </a-button>
                <a-button size="large" @click="resetForm">
                  <template #icon>
                    <ReloadOutlined />
                  </template>
                  重置表单
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </a-card>
      </div>

      <!-- 申诉记录 -->
      <div class="records-section">
        <a-card title="我的申诉记录" class="records-card">
          <template #extra>
            <a-button :loading="recordsLoading" @click="refreshRecords">
              <template #icon>
                <ReloadOutlined />
              </template>
              刷新
            </a-button>
          </template>

          <!-- 筛选条件 -->
          <div class="records-filters">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-select
                  v-model:value="recordsFilter.status"
                  placeholder="选择状态"
                  allow-clear
                  @change="onFilterChange"
                >
                  <a-select-option value="PENDING">
                    待处理
                  </a-select-option>
                  <a-select-option value="PROCESSING">
                    处理中
                  </a-select-option>
                  <a-select-option value="RESOLVED">
                    已解决
                  </a-select-option>
                  <a-select-option value="REJECTED">
                    已拒绝
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :span="8">
                <a-range-picker
                  v-model:value="recordsFilter.dateRange"
                  style="width: 100%;"
                  @change="onFilterChange"
                />
              </a-col>
              <a-col :span="6">
                <a-select
                  v-model:value="recordsFilter.type"
                  placeholder="申诉类型"
                  allow-clear
                  @change="onFilterChange"
                >
                  <a-select-option
                    v-for="type in appealTypes"
                    :key="type.value"
                    :value="type.value"
                  >
                    {{ type.label }}
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :span="4">
                <a-button @click="resetFilter">
                  重置
                </a-button>
              </a-col>
            </a-row>
          </div>

          <!-- 申诉记录表格 -->
          <a-table
            :columns="recordsColumns"
            :data-source="appealRecords"
            :loading="recordsLoading"
            :pagination="recordsPagination"
            row-key="id"
            class="records-table"
            @change="onTableChange"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'type'">
                <a-tag :color="getAppealTypeColor(record.type)">
                  {{ getAppealTypeName(record.type) }}
                </a-tag>
              </template>

              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </template>

              <template v-if="column.key === 'priority'">
                <a-tag :color="getPriorityColor(record.priority)">
                  {{ getPriorityText(record.priority) }}
                </a-tag>
              </template>

              <template v-if="column.key === 'createdAt'">
                <span>{{ formatDateTime(record.createdAt) }}</span>
              </template>

              <template v-if="column.key === 'actions'">
                <a-space>
                  <a-button type="link" size="small" @click="viewDetail(record)">
                    查看详情
                  </a-button>
                  <a-button
                    v-if="record.status === 'PENDING'"
                    type="link"
                    size="small"
                    @click="editAppeal(record)"
                  >
                    编辑
                  </a-button>
                  <a-button
                    v-if="record.status === 'PENDING'"
                    type="link"
                    size="small"
                    danger
                    @click="cancelAppeal(record)"
                  >
                    撤销
                  </a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>
      </div>
    </div>

    <!-- 申诉详情抽屉 -->
    <a-drawer
      v-model:open="detailDrawerVisible"
      title="申诉详情"
      placement="right"
      :width="600"
      @close="closeDetailDrawer"
    >
      <div v-if="selectedAppeal" class="appeal-detail">
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="申诉ID">
            {{ selectedAppeal.id }}
          </a-descriptions-item>
          <a-descriptions-item label="申诉标题">
            {{ selectedAppeal.title }}
          </a-descriptions-item>
          <a-descriptions-item label="申诉类型">
            <a-tag :color="getAppealTypeColor(selectedAppeal.type)">
              {{ getAppealTypeName(selectedAppeal.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="当前状态">
            <a-tag :color="getStatusColor(selectedAppeal.status)">
              {{ getStatusText(selectedAppeal.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="紧急程度">
            <a-tag :color="getPriorityColor(selectedAppeal.priority)">
              {{ getPriorityText(selectedAppeal.priority) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatDateTime(selectedAppeal.createdAt) }}
          </a-descriptions-item>
          <a-descriptions-item v-if="selectedAppeal.updatedAt" label="更新时间">
            {{ formatDateTime(selectedAppeal.updatedAt) }}
          </a-descriptions-item>
          <a-descriptions-item label="问题描述">
            <div class="reason-content">
              {{ selectedAppeal.reason }}
            </div>
          </a-descriptions-item>
          <a-descriptions-item v-if="selectedAppeal.contact" label="联系方式">
            {{ selectedAppeal.contact }}
          </a-descriptions-item>
        </a-descriptions>

        <!-- 处理记录 -->
        <div v-if="selectedAppeal.processRecords?.length" class="process-records">
          <h4>处理记录</h4>
          <a-timeline>
            <a-timeline-item
              v-for="(record, index) in selectedAppeal.processRecords"
              :key="index"
              :color="getTimelineColor(record.action)"
            >
              <div class="timeline-content">
                <div class="timeline-header">
                  <span class="action">{{ record.action }}</span>
                  <span class="time">{{ formatDateTime(record.createdAt) }}</span>
                </div>
                <div class="timeline-body">
                  {{ record.comment }}
                </div>
                <div v-if="record.operatorName" class="timeline-footer">
                  处理人：{{ record.operatorName }}
                </div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </div>

        <!-- 附件列表 -->
        <div v-if="selectedAppeal.attachments?.length" class="attachments">
          <h4>附件材料</h4>
          <a-list :data-source="selectedAppeal.attachments" size="small">
            <template #renderItem="{ item }">
              <a-list-item>
                <template #actions>
                  <a @click="downloadAttachment(item)">下载</a>
                </template>
                <a-list-item-meta>
                  <template #title>
                    <a @click="previewAttachment(item)">{{ item.name }}</a>
                  </template>
                  <template #description>
                    {{ formatFileSize(item.size) }} | {{ formatDateTime(item.uploadedAt) }}
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </div>
    </a-drawer>
  </div>
</template>

<style scoped lang="scss">
.appeal-page {
  max-width: 1200px;
  padding: 24px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 32px;
  text-align: center;

  .page-title {
    margin-bottom: 8px;
    font-size: 32px;
    font-weight: 600;
    color: var(--text-color, #262626);
  }

  .page-description {
    margin: 0;
    font-size: 16px;
    color: var(--text-color-secondary, #8c8c8c);
  }
}

.appeal-container {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.guide-card,
.types-card,
.form-card,
.records-card {
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
}

.guide-content {
  .guide-item {
    padding: 20px;
    text-align: center;

    .guide-icon {
      margin-bottom: 16px;
      font-size: 48px;
      color: var(--primary-color, #1890ff);
    }

    h3 {
      margin-bottom: 8px;
      font-size: 18px;
      font-weight: 600;
      color: var(--text-color, #262626);
    }

    p {
      margin: 0;
      color: var(--text-color-secondary, #8c8c8c);
    }
  }
}

.appeal-types-section {
  .type-card {
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--primary-color, #1890ff);
      box-shadow: 0 4px 12px rgb(24 144 255 / 15%);
      transform: translateY(-2px);
    }

    &.selected {
      background: rgb(24 144 255 / 5%);
      border-color: var(--primary-color, #1890ff);
    }

    .type-content {
      padding: 16px 8px;
      text-align: center;

      .type-icon {
        margin-bottom: 12px;
        font-size: 32px;
      }

      .type-title {
        margin-bottom: 8px;
        font-size: 16px;
        font-weight: 600;
        color: var(--text-color, #262626);
      }

      .type-desc {
        margin: 0;
        font-size: 12px;
        color: var(--text-color-secondary, #8c8c8c);
      }
    }
  }
}

.form-section {
  .upload-tips {
    margin-top: 8px;
    font-size: 12px;
    color: var(--text-color-secondary, #8c8c8c);
  }
}

.records-filters {
  margin-bottom: 24px;
}

.records-table {
  .reason-content {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.appeal-detail {
  .reason-content {
    line-height: 1.6;
    word-break: break-word;
    white-space: pre-wrap;
  }

  .process-records {
    margin-top: 32px;

    h4 {
      margin-bottom: 16px;
      color: var(--text-color, #262626);
    }

    .timeline-content {
      .timeline-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;

        .action {
          font-weight: 600;
          color: var(--text-color, #262626);
        }

        .time {
          font-size: 12px;
          color: var(--text-color-secondary, #8c8c8c);
        }
      }

      .timeline-body {
        margin-bottom: 4px;
        line-height: 1.5;
        color: var(--text-color-secondary, #8c8c8c);
      }

      .timeline-footer {
        font-size: 12px;
        color: var(--text-color-tertiary, #bfbfbf);
      }
    }
  }

  .attachments {
    margin-top: 32px;

    h4 {
      margin-bottom: 16px;
      color: var(--text-color, #262626);
    }
  }
}

// 响应式设计
@media (width <= 768px) {
  .appeal-page {
    padding: 16px;
  }

  .page-header {
    .page-title {
      font-size: 24px;
    }
  }

  .guide-content {
    .ant-col {
      flex: none !important;
      width: 100% !important;
      margin-bottom: 24px;
    }
  }

  .appeal-types-section {
    .ant-col {
      flex: none !important;
      width: 100% !important;
      margin-bottom: 16px;
    }
  }

  .form-section {
    .ant-col {
      flex: none !important;
      width: 100% !important;
    }
  }

  .records-filters {
    .ant-col {
      flex: none !important;
      width: 100% !important;
      margin-bottom: 16px;
    }
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .appeal-page {
    --text-color: #e8e8e8;
    --text-color-secondary: #a6a6a6;
    --text-color-tertiary: #737373;
    --primary-color: #1890ff;
  }
}
</style>
