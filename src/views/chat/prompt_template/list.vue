<route lang="yaml">
meta:
  title: 提示词模板
</route>

<script setup lang="ts">
// import api from '@/api/modules/chat_pTemplate'
import * as promptApi from '@/api/modules/prompt_template'
import eventBus from '@/utils/eventBus'
import dayjs from 'dayjs'
import { ElMessage, ElMessageBox } from 'element-plus'
import FormMode from './components/FormMode/index.vue'
import RelatedTemplateSelector from './components/RelatedTemplateSelector.vue'

defineOptions({
  name: 'ChatPromptTemplateList',
})

const router = useRouter()
const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()

// 表格是否自适应高度
const tableAutoHeight = ref(false)

/**
 * 详情展示模式
 * router 路由跳转
 * dialog 对话框
 * drawer 抽屉
 */
const formMode = ref<'router' | 'dialog' | 'drawer'>('dialog')

// 详情
const formModeProps = ref({
  visible: false,
  id: '',
})

// 关联管理
const relationModeProps = ref({
  visible: false,
  templateId: '',
  templateName: '',
})

// 当前关联关系
const currentRelations = ref<any[]>([])

// 搜索
const searchDefault = {
  title: '',
  name: '',
}
const search = ref({ ...searchDefault })
function searchReset() {
  Object.assign(search.value, searchDefault)
}

// 批量操作
const batch = ref({
  enable: true,
  selectionDataList: [],
})

// 列表
const loading = ref(false)
const dataList = ref([])

// 获取模板类型对应的标签颜色
function _getAgentTypeColor(type: string) {
  const typeColorMap: Record<string, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    system: 'primary',
    user: 'success',
    assistant: 'warning',
    function: 'danger',
    default: 'info',
  }
  return typeColorMap[type] || typeColorMap.default
}

// 计算槽位数量
function getSlotCount(row: any) {
  // 假设模板中的slots字段存储槽位信息
  if (row.slotDefinitions && Array.isArray(row.slotDefinitions)) {
    return row.slotDefinitions.length
  }
  // 如果没有slots字段，尝试从content中解析槽位（假设槽位使用{{slot}}格式）
  else if (row.content && typeof row.content === 'string') {
    const matches = row.content.match(/\{\{([^}]+)\}\}/g)
    return matches ? matches.length : 0
  }
  return 0
}

// 格式化日期时间
function formatDateTime(dateTime: string | number | Date) {
  if (!dateTime) {
    return '-'
  }
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

onMounted(() => {
  getDataList()
  if (formMode.value === 'router') {
    eventBus.on('get-data-list', () => {
      getDataList()
    })
  }
})

onBeforeUnmount(() => {
  if (formMode.value === 'router') {
    eventBus.off('get-data-list')
  }
})

function getDataList() {
  loading.value = true
  const _params = {
    ...getParams(),
    ...(search.value.title && { title: search.value.title }),
    ...(search.value.name && { name: search.value.name }),
  }

  // 使用提示词模板API获取列表
  promptApi.getPromptTemplates().then((res: any) => {
    loading.value = false
    dataList.value = Array.isArray(res) ? res : (res.data || [])
    pagination.value.total = dataList.value.length
  }).catch((error) => {
    loading.value = false
    console.error('获取提示词模板失败', error)
    ElMessage.error('获取提示词模板失败，请稍后重试')
    dataList.value = []
  })
}

// 每页数量切换
function sizeChange(size: number) {
  onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
  onCurrentChange(page).then(() => getDataList())
}

// 字段排序
function sortChange({ prop, order }: { prop: string, order: string }) {
  onSortChange(prop, order).then(() => getDataList())
}

function onCreate() {
  if (formMode.value === 'router') {
    router.push({
      name: 'ChatPromptTemplateDetail',
    })
  }
  else {
    formModeProps.value.id = ''
    formModeProps.value.visible = true
  }
}

function onEdit(row: any) {
  if (formMode.value === 'router') {
    router.push({
      name: 'ChatPromptTemplateDetail',
      params: {
        id: row.id,
      },
    })
  }
  else {
    formModeProps.value.id = row.id
    formModeProps.value.visible = true
  }
}

function onDel(row: any) {
  ElMessageBox.confirm(`确认删除「${row.name}」吗？`, '确认信息').then(() => {
    // 调用删除API
    promptApi.deletePromptTemplate(row.id).then(() => {
      ElMessage.success('删除成功')
      getDataList()
    }).catch((error) => {
      console.error('删除提示词模板失败', error)
      ElMessage.error('删除失败，请稍后重试')
    })
  }).catch(() => {})
}

// 管理关联关系
function onManageRelations(row: any) {
  relationModeProps.value.templateId = row.id
  relationModeProps.value.templateName = row.name
  relationModeProps.value.visible = true
  // 加载当前关联关系
  loadCurrentRelations(row.id)
}

// 加载当前关联关系
function loadCurrentRelations(templateId: string) {
  promptApi.getTemplateRelations(templateId).then((res: any) => {
    let relations = []

    // 处理API返回的数据格式
    if (Array.isArray(res)) {
      relations = res
    }
    else if (res && typeof res === 'object' && Array.isArray(res.data)) {
      relations = res.data
    }

    // 转换数据格式，将后端的字段名映射到前端期望的字段名
    currentRelations.value = relations.map((relation: any) => ({
      targetTemplateId: relation.id,
      targetTemplateName: relation.name,
      priority: relation.priority,
      relationId: relation.relationId, // 保留关联关系ID，用于删除操作
    }))
  }).catch((error) => {
    console.error('加载关联关系失败', error)
    currentRelations.value = []
  })
}

// 处理关联关系更新
function handleRelationsUpdate(relations: any[]) {
  currentRelations.value = relations
}

// 保存关联关系
function saveRelations() {
  const templateId = relationModeProps.value.templateId

  // 转换数据格式，添加必需的 sourceTemplateId 字段
  const relationsData = currentRelations.value.map((relation: any) => ({
    sourceTemplateId: templateId,
    targetTemplateId: relation.targetTemplateId,
    priority: relation.priority,
    // 可选字段
    targetTemplateName: relation.targetTemplateName,
  }))

  promptApi.updateTemplateRelations(templateId, relationsData).then(() => {
    ElMessage.success('关联关系保存成功')
    relationModeProps.value.visible = false
    getDataList() // 刷新列表
  }).catch((error) => {
    console.error('保存关联关系失败', error)
    ElMessage.error('保存关联关系失败，请稍后重试')
  })
}
</script>

<template>
  <div :class="{ 'absolute-container': tableAutoHeight }">
    <FaPageHeader title="提示词模板" class="mb-0" />
    <FaPageMain :class="{ 'flex-1 overflow-auto': tableAutoHeight }" :main-class="{ 'flex-1 flex flex-col overflow-auto': tableAutoHeight }">
      <FaSearchBar :show-toggle="false">
        <template #default="{ fold, toggle }">
          <ElForm :model="search" size="default" label-width="100px" inline-message inline class="search-form">
            <ElFormItem label="模板名称">
              <ElInput v-model="search.name" placeholder="请输入模板名称，支持模糊查询" clearable @keydown.enter="currentChange()" @clear="currentChange()" />
            </ElFormItem>
            <ElFormItem>
              <ElButton @click="searchReset(); currentChange()">
                重置
              </ElButton>
              <ElButton type="primary" @click="currentChange()">
                <template #icon>
                  <FaIcon name="i-ep:search" />
                </template>
                筛选
              </ElButton>
              <ElButton link disabled @click="toggle">
                <template #icon>
                  <FaIcon :name="fold ? 'i-ep:caret-bottom' : 'i-ep:caret-top' " />
                </template>
                {{ fold ? '展开' : '收起' }}
              </ElButton>
            </ElFormItem>
          </ElForm>
        </template>
      </FaSearchBar>
      <ElDivider border-style="dashed" />
      <ElSpace wrap>
        <ElButton type="primary" size="default" @click="onCreate">
          <template #icon>
            <FaIcon name="i-ep:plus" />
          </template>
          新增
        </ElButton>
      </ElSpace>
      <ElTable v-loading="loading" class="my-4" :data="dataList" stripe highlight-current-row border height="100%" @sort-change="sortChange" @selection-change="batch.selectionDataList = $event">
        <ElTableColumn v-if="batch.enable" type="selection" align="center" fixed />
        <ElTableColumn prop="name" label="名称" min-width="60" />
        <ElTableColumn prop="agentType" label="模板类型" min-width="60">
          <template #default="{ row }">
            <ElTag type="primary">
              {{ row.agentType }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn label="槽位数量" width="85">
          <template #default="{ row }">
            <ElTag type="primary">
              {{ getSlotCount(row) }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="description" label="描述" />
        <ElTableColumn prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </ElTableColumn>
        <ElTableColumn label="操作" width="140" align="center" fixed="right">
          <template #default="scope">
            <ElTooltip content="编辑" placement="top">
              <ElButton type="primary" size="small" circle plain @click="onEdit(scope.row)">
                <FaIcon name="i-ep:edit" />
              </ElButton>
            </ElTooltip>
            <ElTooltip content="管理关联" placement="top">
              <ElButton type="warning" size="small" circle plain @click="onManageRelations(scope.row)">
                <FaIcon name="i-ep:link" />
              </ElButton>
            </ElTooltip>
            <ElTooltip content="删除" placement="top">
              <ElButton type="danger" size="small" circle plain @click="onDel(scope.row)">
                <FaIcon name="i-ep:delete" />
              </ElButton>
            </ElTooltip>
          </template>
        </ElTableColumn>
      </ElTable>
      <ElPagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size" :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
    </FaPageMain>
    <FormMode v-if="formMode === 'dialog' || formMode === 'drawer'" :id="formModeProps.id" v-model:visible="formModeProps.visible" :mode="formMode" @success="getDataList" />

    <!-- 关联管理对话框 -->
    <ElDialog
      v-model="relationModeProps.visible"
      :title="`管理模板关联 - ${relationModeProps.templateName}`"
      width="800px"
      :close-on-click-modal="false"
    >
      <RelatedTemplateSelector
        v-if="relationModeProps.visible"
        v-model="currentRelations"
        :template-id="relationModeProps.templateId"
        @update:model-value="handleRelationsUpdate"
      />
      <template #footer>
        <div class="dialog-footer">
          <ElButton @click="relationModeProps.visible = false">
            取消
          </ElButton>
          <ElButton type="primary" @click="saveRelations">
            保存
          </ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<style scoped>
.absolute-container {
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.search-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(330px, 1fr));
  margin-bottom: -18px;

  :deep(.el-form-item) {
    grid-column: auto / span 1;

    &:last-child {
      grid-column-end: -1;

      .el-form-item__content {
        justify-content: flex-end;
      }
    }
  }
}

.el-divider {
  width: calc(100% + 40px);
  margin-inline: -20px;
}
</style>
