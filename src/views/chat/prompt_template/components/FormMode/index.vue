<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import { request } from '@/api'
import * as promptApi from '@/api/modules/prompt_template'
import { ElButton, ElDialog, ElForm, ElFormItem, ElInput, ElMessage, ElOption, ElSelect } from 'element-plus'
import { computed, reactive, ref } from 'vue'
import RelatedTemplateSelector from '../RelatedTemplateSelector.vue'

// 组件属性
const props = defineProps({
  id: {
    type: [String, Number],
    default: '',
  },
  visible: {
    type: Boolean,
    default: false,
  },
  mode: {
    type: String,
    default: 'dialog',
  },
})

// 组件事件
const emit = defineEmits(['update:visible', 'success'])

// API 基础路径
const API_BASE_URL = '/api'

// 定义槽位类型
interface SlotOption {
  label: string
  value: string | number
}

interface SlotDefinition {
  name: string
  label: string
  type: 'TEXT' | 'TEXTAREA' | 'NUMBER' | 'DATE' | 'SELECT'
  required: boolean
  placeholder: string
  defaultValue: string
  options?: SlotOption[]
}

interface TemplateFormData {
  name: string
  description: string
  agent_type: string
  template_content: string
  slot_definitions: SlotDefinition[]
  related_templates: any[]
}

// 使用计算属性处理对话框可见状态
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value),
})

// 表单相关状态
const formLoading = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive<TemplateFormData>({
  name: '',
  description: '',
  agent_type: '',
  template_content: '',
  slot_definitions: [],
  related_templates: [],
})

// 槽位类型选项
const slotTypes = [
  { label: '文本', value: 'TEXT' },
  { label: '多行文本', value: 'TEXTAREA' },
  { label: '数字', value: 'NUMBER' },
  { label: '日期', value: 'DATE' },
  { label: '下拉选择', value: 'SELECT' },
]

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  template_content: [{ required: true, message: '请输入模板内容', trigger: 'blur' }],
  slot_definitions: [{ required: true, message: '请至少添加一个槽位定义', trigger: 'change' }],
}

// 创建空的槽位定义
function createEmptySlot(): SlotDefinition {
  return {
    name: '',
    label: '',
    type: 'TEXT',
    required: false,
    placeholder: '',
    defaultValue: '',
    options: [],
  }
}

// 检查槽位是否有错误
function hasSlotError(index: number): boolean {
  const slot = formData.slot_definitions[index]
  return !slot.name || !slot.label
}

// 对话框打开时重置或加载表单
function onDialogOpen() {
  if (props.id) {
    // 如果有ID，加载模板数据
    loadTemplateData(props.id)
  }
  else {
    // 否则重置表单数据
    resetForm()
  }
}

// 重置表单数据
function resetForm() {
  Object.assign(formData, {
    name: '',
    description: '',
    agent_type: '',
    template_content: '',
    slot_definitions: [createEmptySlot()], // 默认添加一个空槽位
    related_templates: [],
  })
}

// 加载模板数据
async function loadTemplateData(id: string | number) {
  formLoading.value = true
  try {
    const response = await promptApi.getPromptTemplateDetail(String(id))
    const templateData = response.data || response

    console.log('获取到的模板数据:', templateData)

    // 填充表单数据，处理驼峰和下划线命名差异
    Object.assign(formData, {
      name: templateData.name || '',
      description: templateData.description || '',
      agent_type: templateData.agentType || '', // 从 agentType 映射到 agent_type
      template_content: templateData.templateContent || '', // 从 templateContent 映射到 template_content
      slot_definitions: Array.isArray(templateData.slotDefinitions)
        ? templateData.slotDefinitions.map((slot: any) => ({
            name: slot.name || '',
            label: slot.label || '',
            type: slot.type || 'TEXT',
            required: Boolean(slot.required),
            placeholder: slot.placeholder || '',
            defaultValue: slot.defaultValue || '',
            options: Array.isArray(slot.options) ? [...slot.options] : [],
          }))
        : [createEmptySlot()],
    })
  }
  catch (error) {
    console.error('加载模板数据失败', error)
    ElMessage.error('加载模板数据失败，请稍后重试')
    resetForm()
  }
  finally {
    formLoading.value = false
  }
}

// 添加新的槽位定义
function addSlotDefinition() {
  formData.slot_definitions.push(createEmptySlot())
}

// 删除槽位定义
function removeSlotDefinition(index: number) {
  formData.slot_definitions.splice(index, 1)
  // 确保至少有一个槽位定义
  if (formData.slot_definitions.length === 0) {
    formData.slot_definitions.push(createEmptySlot())
  }
}

// 添加选项到SELECT类型的槽位
function addOption(slot: SlotDefinition) {
  if (!slot.options) {
    slot.options = []
  }
  slot.options.push({ label: '', value: '' })
}

// 删除选项
function removeOption(slot: SlotDefinition, index: number) {
  if (slot.options) {
    slot.options.splice(index, 1)
  }
}

// 关闭对话框
function closeDialog() {
  dialogVisible.value = false
}

// 提交表单前验证槽位
function validateSlots() {
  // 检查是否有无效的槽位
  const invalidSlots = formData.slot_definitions.filter(slot =>
    !slot.name || slot.name.trim() === '' || !slot.label || slot.label.trim() === '',
  )

  if (invalidSlots.length > 0) {
    ElMessage.warning('请完善所有槽位的名称和标签')
    return false
  }

  // 检查槽位名称是否重复
  const slotNames = formData.slot_definitions.map(slot => slot.name.trim())
  const uniqueNames = new Set(slotNames)

  if (uniqueNames.size !== slotNames.length) {
    ElMessage.warning('槽位名称不能重复')
    return false
  }

  return true
}

// 检查模板内容是否包含定义的槽位
function validateTemplateContent() {
  const content = formData.template_content
  if (!content) { return false }

  const validSlotNames = formData.slot_definitions
    .filter(slot => slot.name && slot.name.trim() !== '')
    .map(slot => slot.name.trim())

  // 提取模板中的槽位 {{name}}
  const templateSlots = content.match(/\{\{([^}]+)\}\}/g) || []
  const extractedSlots = templateSlots.map(slot => slot.replace(/\{\{|\}\}/g, '').trim())

  // 检查是否有未定义的槽位
  const undefinedSlots = extractedSlots.filter(slot => !validSlotNames.includes(slot))

  if (undefinedSlots.length > 0) {
    ElMessage.warning(`模板内容中使用了未定义的槽位: ${undefinedSlots.join(', ')}`)
    return false
  }

  return true
}

// 提交表单
async function submitForm() {
  if (!formRef.value) { return }

  try {
    const valid = await formRef.value.validate()
    if (!valid) { return }

    // 验证槽位
    if (!validateSlots()) {
      return
    }

    // 验证模板内容
    if (!validateTemplateContent()) {
      return
    }

    formLoading.value = true

    // 过滤掉无效的槽位定义（没有名称的槽位）
    const validSlotDefinitions = formData.slot_definitions.filter(slot =>
      slot.name && slot.name.trim() !== '' && slot.label && slot.label.trim() !== '',
    )

    // 如果没有有效的槽位定义，添加一个默认的
    if (validSlotDefinitions.length === 0) {
      ElMessage.warning('请至少添加一个有效的槽位定义')
      formLoading.value = false
      return
    }

    // 处理表单数据，确保格式正确
    const submitData = {
      ...formData,
      slot_definitions: validSlotDefinitions.map((slot) => {
        const result = { ...slot }
        // 如果不是SELECT类型，移除options
        if (slot.type !== 'SELECT') {
          delete result.options
        }
        // 确保所有字符串字段都经过trim处理
        if (typeof result.name === 'string') { result.name = result.name.trim() }
        if (typeof result.label === 'string') { result.label = result.label.trim() }
        if (typeof result.placeholder === 'string') { result.placeholder = result.placeholder.trim() }
        if (typeof result.defaultValue === 'string') { result.defaultValue = result.defaultValue.trim() }

        return result
      }),
    }

    console.log('提交数据:', JSON.stringify(submitData, null, 2))

    // 根据是否有ID决定是创建还是更新
    let response
    if (props.id) {
      // 更新已有模板 - 使用与创建相同的 API，但添加 id
      response = await updatePromptTemplate(props.id, submitData)
      ElMessage.success('更新模板成功')
    }
    else {
      // 创建新模板
      response = await promptApi.createPromptTemplate(submitData)
      ElMessage.success('创建模板成功')
    }

    console.log('操作成功，响应数据:', response)
    closeDialog()

    // 通知父组件操作成功
    emit('success')
  }
  catch (error: any) {
    console.error('操作失败', error)
    let errorMessage = '请稍后重试'
    if (error.message) {
      errorMessage = error.message
    }
    else if (error.response && error.response.data) {
      // 处理后端返回的错误信息
      const responseData = error.response.data
      if (typeof responseData === 'string') {
        errorMessage = responseData
      }
      else if (responseData.message) {
        errorMessage = responseData.message
      }
      else if (responseData.error) {
        errorMessage = responseData.error
      }
      else {
        errorMessage = JSON.stringify(responseData)
      }
    }

    ElMessage.error(`操作失败: ${errorMessage}`)
  }
  finally {
    formLoading.value = false
  }
}

// 更新提示词模板 (API 中没有此方法，需要添加)
function updatePromptTemplate(id: string | number, data: any) {
  // 确保数据格式符合后端要求，使用驼峰命名
  const requestData = {
    name: data.name,
    description: data.description || '',
    agentType: data.agent_type || '', // 转换为驼峰命名
    templateContent: data.template_content, // 转换为驼峰命名
    slotDefinitions: Array.isArray(data.slot_definitions)
      ? data.slot_definitions.filter((slot: any) => slot.name && slot.label).map((slot: any) => ({
          name: slot.name,
          label: slot.label,
          type: slot.type,
          required: !!slot.required,
          placeholder: slot.placeholder || '',
          defaultValue: slot.defaultValue || '',
          options: slot.options,
        }))
      : [],
  }

  return request({
    url: `${API_BASE_URL}/prompt/templates/${String(id)}`,
    method: 'PUT',
    data: requestData,
  }).catch((error: any) => {
    console.error('API请求错误详情:', error)
    // 将错误向上抛出，让组件处理
    throw error
  })
}

defineExpose({
  submit: submitForm,
})
</script>

<template>
  <ElDialog
    v-if="mode === 'dialog'" :model-value="dialogVisible" :title="id ? '编辑提示词模板' : '新增提示词模板'" width="650px"
    :close-on-click-modal="false" @update:model-value="dialogVisible = $event" @open="onDialogOpen"
  >
    <ElForm ref="formRef" :model="formData" :rules="rules" label-width="100px" label-position="top">
      <ElFormItem label="模板名称" prop="name">
        <ElInput v-model="formData.name" placeholder="请输入模板名称" />
      </ElFormItem>

      <ElFormItem label="模板描述" prop="description">
        <ElInput v-model="formData.description" type="textarea" :rows="3" placeholder="请输入模板描述" />
      </ElFormItem>

      <ElFormItem label="模板类型" prop="agent_type">
        <ElInput v-model="formData.agent_type" placeholder="请输入模板类型" />
      </ElFormItem>

      <ElFormItem label="模板内容" prop="template_content">
        <ElInput
          v-model="formData.template_content" type="textarea" :rows="5"
          placeholder="请输入模板内容，使用 {{slot_name}} 表示槽位"
        />
        <div class="mt-1 text-xs text-gray-500">
          * 使用双大括号包裹槽位名称，例如: &#123;&#123;名称&#125;&#125;、&#123;&#123;公司&#125;&#125;等
        </div>
      </ElFormItem>

      <div class="mb-4">
        <div class="mb-2 flex items-center justify-between">
          <div class="text-base font-medium">
            槽位定义
          </div>
          <FaButton variant="default" size="sm" @click="addSlotDefinition">
            添加槽位
          </FaButton>
        </div>

        <div
          v-for="(slot, index) in formData.slot_definitions" :key="index" class="mb-4 border rounded-md p-4"
          :class="{ 'border-red-300 bg-red-50': hasSlotError(index) }"
        >
          <div class="mb-2 flex items-center justify-between">
            <div class="font-medium">
              槽位 #{{ index + 1 }}
            </div>
            <FaButton
              variant="destructive" size="sm" :disabled="formData.slot_definitions.length <= 1"
              @click="removeSlotDefinition(index)"
            >
              删除
            </FaButton>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <ElFormItem
              label="槽位名称" :prop="`slot_definitions.${index}.name`"
              :rules="[{ required: true, message: '请输入槽位名称', trigger: 'blur' }]"
            >
              <ElInput v-model="slot.name" placeholder="请输入槽位名称，如 name" />
              <div class="mt-1 text-xs text-gray-500">
                * 此名称将用于模板内容中的 &#123;&#123;槽位名&#125;&#125; 占位符
              </div>
            </ElFormItem>

            <ElFormItem
              label="显示标签" :prop="`slot_definitions.${index}.label`"
              :rules="[{ required: true, message: '请输入显示标签', trigger: 'blur' }]"
            >
              <ElInput v-model="slot.label" placeholder="请输入显示标签，如 姓名" />
              <div class="mt-1 text-xs text-gray-500">
                * 此标签将显示给用户，用于输入引导
              </div>
            </ElFormItem>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <ElFormItem label="槽位类型" :prop="`slot_definitions.${index}.type`">
              <ElSelect v-model="slot.type" placeholder="请选择槽位类型">
                <ElOption v-for="type in slotTypes" :key="type.value" :label="type.label" :value="type.value" />
              </ElSelect>
            </ElFormItem>

            <ElFormItem label="是否必填">
              <ElSelect v-model="slot.required" placeholder="请选择是否必填">
                <ElOption label="是" :value="true" />
                <ElOption label="否" :value="false" />
              </ElSelect>
            </ElFormItem>
          </div>

          <ElFormItem label="占位提示" :prop="`slot_definitions.${index}.placeholder`">
            <ElInput v-model="slot.placeholder" placeholder="请输入占位提示" />
          </ElFormItem>

          <ElFormItem label="默认值" :prop="`slot_definitions.${index}.defaultValue`">
            <ElInput v-model="slot.defaultValue" placeholder="请输入默认值" />
          </ElFormItem>

          <!-- 选项列表 (仅当槽位类型为 SELECT 时显示) -->
          <template v-if="slot.type === 'SELECT'">
            <div class="mb-2 mt-4">
              <div class="flex items-center justify-between">
                <div class="font-medium">
                  选项列表
                </div>
                <FaButton variant="default" size="sm" @click="addOption(slot)">
                  添加选项
                </FaButton>
              </div>
            </div>

            <div
              v-for="(option, optionIndex) in slot.options" :key="optionIndex"
              class="grid grid-cols-2 mb-2 gap-4 border-t p-2 pt-3"
            >
              <ElFormItem
                :label="`选项标签 #${optionIndex + 1}`"
                :prop="`slot_definitions.${index}.options.${optionIndex}.label`"
              >
                <ElInput v-model="option.label" placeholder="请输入选项标签" />
              </ElFormItem>

              <div class="flex items-end gap-2">
                <ElFormItem label="选项值" :prop="`slot_definitions.${index}.options.${optionIndex}.value`" class="flex-1">
                  <ElInput v-model="option.value" placeholder="请输入选项值" />
                </ElFormItem>

                <FaButton variant="destructive" size="sm" class="mb-2" @click="removeOption(slot, optionIndex)">
                  删除
                </FaButton>
              </div>
            </div>

            <div v-if="!slot.options || slot.options.length === 0" class="mt-2 text-sm text-gray-500">
              请添加选项
            </div>
          </template>
        </div>
      </div>
    </ElForm>

    <template #footer>
      <div class="flex justify-end gap-2">
        <ElButton @click="closeDialog">
          取消
        </ElButton>
        <ElButton type="primary" :loading="formLoading" @click="submitForm">
          确认
        </ElButton>
      </div>
    </template>
  </ElDialog>

  <!-- 抽屉模式 -->
  <ElDrawer
    v-if="mode === 'drawer'" :model-value="dialogVisible" :title="id ? '编辑提示词模板' : '新增提示词模板'" size="650px"
    :close-on-click-modal="false" @update:model-value="dialogVisible = $event" @open="onDialogOpen"
  >
    <ElForm ref="formRef" :model="formData" :rules="rules" label-width="100px" label-position="top">
      <!-- 与对话框模式相同的表单内容 -->
      <ElFormItem label="模板名称" prop="name">
        <ElInput v-model="formData.name" placeholder="请输入模板名称" />
      </ElFormItem>

      <ElFormItem label="模板描述" prop="description">
        <ElInput v-model="formData.description" type="textarea" :rows="3" placeholder="请输入模板描述" />
      </ElFormItem>

      <ElFormItem label="模板类型" prop="agent_type">
        <ElInput v-model="formData.agent_type" placeholder="请输入模板类型" />
      </ElFormItem>

      <ElFormItem label="模板内容" prop="template_content">
        <ElInput
          v-model="formData.template_content" type="textarea" :rows="5"
          placeholder="请输入模板内容，使用 {{slot_name}} 表示槽位"
        />
        <div class="mt-1 text-xs text-gray-500">
          * 使用双大括号包裹槽位名称，例如: &#123;&#123;名称&#125;&#125;、&#123;&#123;公司&#125;&#125;等
        </div>
      </ElFormItem>

      <div class="mb-4">
        <!-- 与对话框模式相同的槽位定义 -->
        <div class="mb-2 flex items-center justify-between">
          <div class="text-base font-medium">
            槽位定义
          </div>
          <FaButton variant="default" size="sm" @click="addSlotDefinition">
            添加槽位
          </FaButton>
        </div>

        <div
          v-for="(slot, index) in formData.slot_definitions" :key="index" class="mb-4 border rounded-md p-4"
          :class="{ 'border-red-300 bg-red-50': hasSlotError(index) }"
        >
          <!-- 槽位的内容与对话框模式相同 -->
          <div class="mb-2 flex items-center justify-between">
            <div class="font-medium">
              槽位 #{{ index + 1 }}
            </div>
            <FaButton
              variant="destructive" size="sm" :disabled="formData.slot_definitions.length <= 1"
              @click="removeSlotDefinition(index)"
            >
              删除
            </FaButton>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <ElFormItem
              label="槽位名称" :prop="`slot_definitions.${index}.name`"
              :rules="[{ required: true, message: '请输入槽位名称', trigger: 'blur' }]"
            >
              <ElInput v-model="slot.name" placeholder="请输入槽位名称，如 name" />
              <div class="mt-1 text-xs text-gray-500">
                * 此名称将用于模板内容中的 &#123;&#123;槽位名&#125;&#125; 占位符
              </div>
            </ElFormItem>

            <ElFormItem
              label="显示标签" :prop="`slot_definitions.${index}.label`"
              :rules="[{ required: true, message: '请输入显示标签', trigger: 'blur' }]"
            >
              <ElInput v-model="slot.label" placeholder="请输入显示标签，如 姓名" />
              <div class="mt-1 text-xs text-gray-500">
                * 此标签将显示给用户，用于输入引导
              </div>
            </ElFormItem>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <ElFormItem label="槽位类型" :prop="`slot_definitions.${index}.type`">
              <ElSelect v-model="slot.type" placeholder="请选择槽位类型">
                <ElOption v-for="type in slotTypes" :key="type.value" :label="type.label" :value="type.value" />
              </ElSelect>
            </ElFormItem>

            <ElFormItem label="是否必填">
              <ElSelect v-model="slot.required" placeholder="请选择是否必填">
                <ElOption label="是" :value="true" />
                <ElOption label="否" :value="false" />
              </ElSelect>
            </ElFormItem>
          </div>

          <ElFormItem label="占位提示" :prop="`slot_definitions.${index}.placeholder`">
            <ElInput v-model="slot.placeholder" placeholder="请输入占位提示" />
          </ElFormItem>

          <ElFormItem label="默认值" :prop="`slot_definitions.${index}.defaultValue`">
            <ElInput v-model="slot.defaultValue" placeholder="请输入默认值" />
          </ElFormItem>

          <!-- 选项列表 (仅当槽位类型为 SELECT 时显示) -->
          <template v-if="slot.type === 'SELECT'">
            <div class="mb-2 mt-4">
              <div class="flex items-center justify-between">
                <div class="font-medium">
                  选项列表
                </div>
                <FaButton variant="default" size="sm" @click="addOption(slot)">
                  添加选项
                </FaButton>
              </div>
            </div>

            <div
              v-for="(option, optionIndex) in slot.options" :key="optionIndex"
              class="grid grid-cols-2 mb-2 gap-4 border-t p-2 pt-3"
            >
              <ElFormItem
                :label="`选项标签 #${optionIndex + 1}`"
                :prop="`slot_definitions.${index}.options.${optionIndex}.label`"
              >
                <ElInput v-model="option.label" placeholder="请输入选项标签" />
              </ElFormItem>

              <div class="flex items-end gap-2">
                <ElFormItem label="选项值" :prop="`slot_definitions.${index}.options.${optionIndex}.value`" class="flex-1">
                  <ElInput v-model="option.value" placeholder="请输入选项值" />
                </ElFormItem>

                <FaButton variant="destructive" size="sm" class="mb-2" @click="removeOption(slot, optionIndex)">
                  删除
                </FaButton>
              </div>
            </div>

            <div v-if="!slot.options || slot.options.length === 0" class="mt-2 text-sm text-gray-500">
              请添加选项
            </div>
          </template>
        </div>
      </div>
    </ElForm>

    <div class="mt-4 flex justify-end gap-2">
      <ElButton @click="closeDialog">
        取消
      </ElButton>
      <ElButton type="primary" :loading="formLoading" @click="submitForm">
        确认
      </ElButton>
    </div>
  </ElDrawer>
</template>
