<script setup lang="ts">
import * as promptApi from '@/api/modules/prompt_template'
import { ElButton, ElCheckbox, ElMessage, ElTable, ElTableColumn } from 'element-plus'
import { computed, onMounted, ref, watch } from 'vue'

// 定义关联模板类型
interface RelatedTemplate {
  targetTemplateId: string
  targetTemplateName: string
  priority: number
}

// 定义可用模板类型
interface AvailableTemplate {
  id: string
  name: string
  description?: string
}

// 组件属性
const props = defineProps({
  templateId: {
    type: String,
    required: true,
  },
  modelValue: {
    type: [Array, Object] as any,
    default: () => [],
  },
})

// 组件事件
const emit = defineEmits(['update:modelValue'])

// 状态管理
const loading = ref(false)
const availableTemplates = ref<AvailableTemplate[]>([])
const nextPriority = ref(0)

// 计算属性
const relatedTemplates = computed({
  get: () => {
    // 处理API返回的数据格式 {status: 1, data: Array}
    if (Array.isArray(props.modelValue)) {
      return props.modelValue
    }
    if (props.modelValue && typeof props.modelValue === 'object' && Array.isArray(props.modelValue.data)) {
      return props.modelValue.data
    }
    return []
  },
  set: value => emit('update:modelValue', value),
})

// 监听模板ID变化
watch(() => props.templateId, (newId) => {
  if (newId) {
    loadAvailableTemplates()
  }
}, { immediate: true })

// 加载可用的模板列表
async function loadAvailableTemplates() {
  if (!props.templateId) {
    return
  }

  loading.value = true
  try {
    const response = await promptApi.getAvailableTemplatesForRelation(props.templateId)
    availableTemplates.value = Array.isArray(response) ? response : (response.data || [])
  }
  catch (error) {
    console.error('获取可关联模板失败', error)
    ElMessage.error('获取可关联模板失败，请稍后重试')
    availableTemplates.value = []
  }
  finally {
    loading.value = false
  }
}

// 检查模板是否已关联
function isTemplateSelected(templateId: string): boolean {
  return relatedTemplates.value.some((rt: RelatedTemplate) => rt.targetTemplateId === templateId)
}

// 切换模板关联状态
function toggleTemplateSelection(template: AvailableTemplate, checked: boolean) {
  if (checked) {
    // 添加关联
    const newRelation: RelatedTemplate = {
      targetTemplateId: template.id,
      targetTemplateName: template.name,
      priority: nextPriority.value,
    }
    relatedTemplates.value = [...relatedTemplates.value, newRelation]
    nextPriority.value += 1
  }
  else {
    // 移除关联
    relatedTemplates.value = relatedTemplates.value.filter((rt: RelatedTemplate) => rt.targetTemplateId !== template.id)
  }
}

// 删除关联模板
function removeRelatedTemplate(index: number) {
  const newList = [...relatedTemplates.value]
  newList.splice(index, 1)
  relatedTemplates.value = newList
}

// 上移
function moveUp(index: number) {
  if (index === 0) {
    return
  }
  const newList = [...relatedTemplates.value]
  const temp = newList[index]
  newList[index] = newList[index - 1]
  newList[index - 1] = temp

  // 更新优先级
  ;(newList[index - 1] as RelatedTemplate).priority = index - 1
  ;(newList[index] as RelatedTemplate).priority = index

  relatedTemplates.value = newList
}

// 下移
function moveDown(index: number) {
  if (index === relatedTemplates.value.length - 1) {
    return
  }
  const newList = [...relatedTemplates.value]
  const temp = newList[index]
  newList[index] = newList[index + 1]
  newList[index + 1] = temp

  // 更新优先级
  ;(newList[index] as RelatedTemplate).priority = index
  ;(newList[index + 1] as RelatedTemplate).priority = index + 1

  relatedTemplates.value = newList
}

onMounted(() => {
  // 计算下一个优先级
  if (relatedTemplates.value.length > 0) {
    const maxPriority = Math.max(...relatedTemplates.value.map((rt: RelatedTemplate) => rt.priority || 0))
    nextPriority.value = maxPriority + 1
  }
})
</script>

<template>
  <div class="related-template-selector">
    <div class="mb-4">
      <h4 class="mb-2 text-sm font-medium">
        关联模板
      </h4>
      <p class="mb-3 text-xs text-gray-500">
        选择与当前模板相关的其他模板，用户完成当前模板后会看到这些推荐模板
      </p>

      <!-- 可选模板列表 -->
      <div class="mb-4">
        <h5 class="mb-2 text-sm font-medium">
          可选模板
        </h5>
        <div v-if="loading" class="py-4 text-center text-sm text-gray-500">
          加载中...
        </div>
        <div v-else-if="availableTemplates.length === 0" class="py-4 text-center text-sm text-gray-500">
          暂无可关联的模板
        </div>
        <div v-else class="overflow-y-auto border border-gray-200 rounded" style="max-height: 45rem;">
          <div
            v-for="template in availableTemplates"
            :key="template.id"
            class="flex cursor-pointer items-center border-b border-gray-100 p-3 transition-colors last:border-b-0 hover:bg-gray-50"
            @click="toggleTemplateSelection(template, !isTemplateSelected(template.id))"
          >
            <ElCheckbox
              :model-value="isTemplateSelected(template.id)"
              class="mr-4"
              @click.stop
              @change="(checked: any) => toggleTemplateSelection(template, !!checked)"
            />
            <div class="ml-4 flex-1">
              <div class="text-sm font-medium">
                {{ template.name }}
              </div>
              <div v-if="template.description" class="mt-1 text-xs text-gray-500">
                {{ template.description }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 关联模板列表 -->
    <div v-if="relatedTemplates.length > 0">
      <ElTable :data="relatedTemplates" size="small" class="w-full">
        <ElTableColumn label="模板名称" prop="targetTemplateName" />
        <ElTableColumn label="优先级" prop="priority" width="80" />
        <ElTableColumn label="操作" width="120">
          <template #default="{ $index }">
            <div class="flex gap-1">
              <ElButton
                size="small"
                link
                :disabled="$index === 0"
                @click="moveUp($index)"
              >
                ↑
              </ElButton>
              <ElButton
                size="small"
                link
                :disabled="$index === relatedTemplates.length - 1"
                @click="moveDown($index)"
              >
                ↓
              </ElButton>
              <ElButton
                size="small"
                link
                class="text-red-500"
                @click="removeRelatedTemplate($index)"
              >
                删除
              </ElButton>
            </div>
          </template>
        </ElTableColumn>
      </ElTable>
    </div>

    <!-- 空状态 -->
    <div v-else class="py-4 text-center text-sm text-gray-500">
      暂无关联模板，请在上方列表中勾选要关联的模板
    </div>
  </div>
</template>

<style scoped>
.related-template-selector {
  padding: 16px;
  background-color: #fafafa;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}
</style>
