<route lang="yaml">
meta:
  title: 提示词模板
</route>

<script setup lang="ts">
import useSettingsStore from '@/store/modules/settings'
import eventBus from '@/utils/eventBus'
import FormMode from './components/FormMode/index.vue'

defineOptions({
  name: 'ChatPromptTemplateDetail',
})

const route = useRoute()
const router = useRouter()
const tabbar = useTabbar()

const settingsStore = useSettingsStore()

// 表单相关状态
const formRef = useTemplateRef('formRef')
const formVisible = ref(true)

function onSubmit() {
  formRef.value?.submit().then(() => {
    eventBus.emit('get-data-list')
    goBack()
  })
}

function onCancel() {
  goBack()
}

// 返回列表页
function goBack() {
  if (settingsStore.settings.tabbar.enable && settingsStore.settings.tabbar.mergeTabsBy !== 'activeMenu') {
    tabbar.close({ name: 'ChatPromptTemplateList' })
  }
  else {
    router.push({ name: 'ChatPromptTemplateList' })
  }
}
</script>

<template>
  <div>
    <FaPageHeader :title="route.params.id ? '编辑提示词模板' : '新增提示词模板'">
      <ElButton size="default" round @click="goBack">
        <template #icon>
          <FaIcon name="i-ep:arrow-left" />
        </template>
        返回
      </ElButton>
    </FaPageHeader>
    <FaPageMain>
      <ElRow>
        <ElCol :md="24" :lg="16">
          <FormMode
            :id="route.params.id as string"
            ref="formRef"
            v-model:visible="formVisible"
            mode="dialog"
            @success="onSubmit"
          />
        </ElCol>
      </ElRow>
    </FaPageMain>
    <FaFixedActionBar>
      <ElButton type="primary" size="large" @click="onSubmit">
        提交
      </ElButton>
      <ElButton size="large" @click="onCancel">
        取消
      </ElButton>
    </FaFixedActionBar>
  </div>
</template>

<style scoped>
/* 样式 */
</style>
