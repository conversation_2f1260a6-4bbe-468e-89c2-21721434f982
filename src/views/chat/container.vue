<route lang="yaml">
  meta:
    enabled: false
  </route>

<script setup lang="tsx">
// import * as chatApi from '@/api/modules/chat'
import * as conversationApi from '@/api/modules/conversation'
import * as promptApi from '@/api/modules/prompt_template'
import ChatFunctionButtons from '@/components/chat/ChatFunctionButtons.vue'
import ChatMessageList from '@/components/chat/conversation/ChatMessageList.vue'
import ChatWelcome from '@/components/chat/conversation/ChatWelcome.vue'
import ConversationHistory from '@/components/chat/conversation/ConversationHistory.vue'
import PromptTemplates from '@/components/chat/prompt_template/PromptTemplates.vue'
import ChatSender from '@/components/chat/sender/ChatSender.vue'
import { ElMessage } from 'element-plus'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'

const vertical = ref(false)
const enableLeftSide = ref(false)
const enableRightSide = ref(false)
const enableTopSide = ref(false)
const enableBottomSide = ref(true)
const leftSideWidth = ref(300)
const rightSideWidth = ref(300)
const topSideHeight = ref(200)
const bottomSideHeight = ref(120)

// 聊天相关状态
const isLoading = ref(false)
const isHistoryLoading = ref(false)

// 历史会话相关
const activeConversation = ref('')
const conversationHistoryRef = ref()
const chatSenderRef = ref()

// 新增：底部容器引用和ResizeObserver
const bottomContainerRef = ref<HTMLElement>()
let resizeObserver: ResizeObserver | null = null

// 模板相关状态
const templateContent = ref('')
const templateName = ref('')
const templateId = ref('')
const slotDefinitions = ref<Record<string, any>>({})
const slotValues = ref<Record<string, any>>({})

// 聊天历史记录，初始为空数组
const chatHistory = ref<any[]>([])

// 新增：高亮缺失槽位的状态管理
const highlightMissingSlots = ref<string[]>([])
const showValidationMessage = ref(false)

// 新增：模板参数验证函数
function validateTemplateSlots(
  templateData: any,
  currentSlotValues: Record<string, any>,
): {
    isValid: boolean
    missingRequiredSlots: string[]
  } {
  const missingRequiredSlots: string[] = []

  if (templateData.slotDefinitions && Array.isArray(templateData.slotDefinitions)) {
    templateData.slotDefinitions.forEach((slotDef: any) => {
      if (slotDef.required) {
        const slotValue = currentSlotValues[slotDef.name]
        if (slotValue === undefined || slotValue === null || slotValue === '') {
          missingRequiredSlots.push(slotDef.name)
        }
      }
    })
  }

  return {
    isValid: missingRequiredSlots.length === 0,
    missingRequiredSlots,
  }
}

// 会话选择变更处理
async function handleConversationChange(key: string) {
  activeConversation.value = key

  // 加载选中会话的聊天记录
  isHistoryLoading.value = true
  try {
    // 从后端获取选中会话的聊天记录
    const response = await conversationApi.getConversationHistory(key)

    // 处理响应数据：接口直接返回数组而非包含data属性的对象
    const messagesData = Array.isArray(response)
      ? response
      : (response.data && Array.isArray(response.data) ? response.data : [])

    // 转换接口返回的消息数据为ChatMessageList组件需要的格式
    chatHistory.value = messagesData.map((msg: any) => {
      // 确保角色名称统一为小写，兼容大写格式（如"USER"和"ASSISTANT"）
      const role = (msg.role || '').toLowerCase()

      return {
        id: msg.id || `msg-${Date.now()}-${Math.random()}`,
        role: role === 'user' || role === 'assistant' ? role : (role === 'system' ? 'assistant' : role),
        content: msg.content || '',
        createdAt: msg.timestamp || msg.createdAt || new Date().toISOString(),
        intermediateSteps: msg.intermediateSteps || [],
        // 保留list字段，支持多类型消息内容
        list: msg.list || undefined,
        // 保留其他可能的字段
        loading: msg.loading || false,
        error: msg.error || false,
      }
    })

    // 如果没有消息，保持为空数组，让欢迎组件显示
    if (chatHistory.value.length === 0) {
      chatHistory.value = []
    }
  }
  catch (error) {
    console.error('获取会话消息失败', error)
    ElMessage.error('获取会话消息失败，请稍后重试')

    // 出错时显示提示信息
    chatHistory.value = [
      {
        id: `${key}-error`,
        role: 'assistant',
        content: '加载会话消息失败，请重试或选择其他会话。',
        createdAt: new Date().toISOString(),
      },
    ]
  }
  finally {
    isHistoryLoading.value = false
  }
}

// 处理消息发送完成事件
function handleMessageSent({
  userMessage,
  assistantMessage,
  isUpdate,
  loadingMessageId,
}: {
  userMessage?: any
  assistantMessage: any
  isLoading?: boolean
  isUpdate?: boolean
  loadingMessageId?: string
}) {
  // 如果有用户消息，添加到历史记录
  if (userMessage) {
    chatHistory.value.push(userMessage)
  }

  // 如果是更新现有的助手消息
  if (isUpdate && loadingMessageId) {
    const existingIndex = chatHistory.value.findIndex((msg: any) => msg.id === loadingMessageId)
    if (existingIndex !== -1) {
      chatHistory.value[existingIndex] = assistantMessage
    }
    else {
      chatHistory.value.push(assistantMessage)
    }
  }
  else {
    chatHistory.value.push(assistantMessage)
  }

  // 如果没有活跃会话，创建一个新会话并刷新会话列表
  if (!activeConversation.value && conversationHistoryRef.value) {
    // 刷新会话列表
    conversationHistoryRef.value.fetchConversations()
  }
}

// 中央区域 - 聊天消息列表或欢迎组件
const showWelcome = computed(() => {
  // 判断是否存在用户消息
  const hasUserMessages = chatHistory.value.some((msg: any) => msg.role === 'user')

  // 如果有用户消息，不显示欢迎组件
  if (hasUserMessages) {
    return false
  }

  // 当没有活跃会话且聊天历史为空时显示欢迎组件
  return !activeConversation.value && chatHistory.value.length === 0
})

// 处理示例问题选择
function handleSelectQuestion(question: string) {
  if (chatSenderRef.value) {
    chatSenderRef.value.setInputContent(question)
    chatSenderRef.value.sendCurrentInput()
  }
}

// 创建新会话
function handleCreateNewChat() {
  // 清除当前活跃会话
  activeConversation.value = ''

  // 清空聊天历史
  chatHistory.value = []
}

// 处理侧边栏宽度变化
function handleWidthChange(width: number) {
  leftSideWidth.value = width
}

// 处理右侧边栏宽度变化
function handleRightWidthChange(width: number) {
  rightSideWidth.value = width
}

// 选择提示词模板
function handleSelectTemplate(template: any) {
  // console.log('container.vue - handleSelectTemplate:', template)

  // 设置模板相关的状态
  templateContent.value = template.templateContent || template.content || ''
  templateName.value = template.name || '提示词模板'
  templateId.value = template.id || ''

  // 处理槽位定义 - 支持多种格式
  if (template.slotDefinitions) {
    slotDefinitions.value = template.slotDefinitions
  }
  else if (template.slots) {
    slotDefinitions.value = template.slots
  }
  else {
    slotDefinitions.value = {}
  }

  // 重置槽位值
  slotValues.value = {}

  // 从模板内容中解析槽位标记 {{slot_name}}
  const slotMatches = templateContent.value.match(/\{\{([^{}]+)\}\}/g) || []
  const slotNames = slotMatches.map((match: any) => match.replace(/[{}]/g, '').trim())

  // console.log('container.vue - 从模板内容解析出的槽位名称:', slotNames)

  // 处理不同格式的槽位定义
  if (Array.isArray(slotDefinitions.value)) {
    // 数组格式的槽位定义
    slotDefinitions.value.forEach((slot: any) => {
      if (slot && slot.name) {
        // 如果有默认值，使用默认值；否则使用空字符串
        slotValues.value[slot.name]
          = (slot.defaultValue !== undefined && slot.defaultValue !== null)
            ? slot.defaultValue
            : ''
      }
    })
  }
  else {
    // 对象格式的槽位定义
    Object.entries(slotDefinitions.value).forEach(([name, def]: [string, any]) => {
      // 如果有默认值，使用默认值；否则使用空字符串
      slotValues.value[name]
        = (def.defaultValue !== undefined && def.defaultValue !== null)
          ? def.defaultValue
          : ''
    })
  }

  // 确保从模板内容解析出的所有槽位都有对应的值
  slotNames.forEach((name: any) => {
    if (slotValues.value[name] === undefined) {
      slotValues.value[name] = ''
    }
  })

  // console.log('container.vue - 初始化后的槽位定义:', JSON.stringify(slotDefinitions.value))
  // console.log('container.vue - 初始化后的槽位值:', JSON.stringify(slotValues.value))
}

// 打开模板抽屉（如果右侧栏是关闭的，就打开它）
function handleOpenTemplateDrawer() {
  enableRightSide.value = true
}

// 处理槽位值变化
function handleSlotValueChange(slotName: string, value: any) {
  // console.log('container.vue - handleSlotValueChange:', slotName, value)
  // console.log('更新前 slotValues:', JSON.stringify(slotValues.value))

  // 创建新对象，保证响应式更新
  const newValues = { ...slotValues.value }
  newValues[slotName] = value === null ? '' : value // 避免null值
  slotValues.value = newValues

  // 新增：如果修改的槽位是高亮的缺失槽位，且现在有值了，就从高亮列表中移除
  if (highlightMissingSlots.value.includes(slotName) && value !== undefined && value !== null && value !== '') {
    highlightMissingSlots.value = highlightMissingSlots.value.filter(name => name !== slotName)

    // 如果没有缺失的槽位了，隐藏验证消息
    if (highlightMissingSlots.value.length === 0) {
      showValidationMessage.value = false
    }
  }

  // console.log('更新后 slotValues:', JSON.stringify(slotValues.value))
}

// 判断当前是否为新会话状态
const isNewChat = computed(() => {
  // 当没有活跃会话且聊天历史为空时视为新会话状态
  return !activeConversation.value && chatHistory.value.length === 0
})

// 处理 suggestion 点击事件
async function handleSuggestionClick(linkParams: any) {
  if (chatSenderRef.value && linkParams) {
    // 检查是否有模板相关参数
    if (linkParams.templateId && linkParams.slots) {
      try {
        // 获取模板详情
        const templateResponse = await promptApi.getPromptTemplateDetail(linkParams.templateId)
        const templateData = templateResponse.data || templateResponse

        // 设置模板相关状态
        templateId.value = linkParams.templateId
        templateContent.value = templateData.templateContent || linkParams.message || ''
        templateName.value = templateData.name || '提示词模板'
        slotValues.value = { ...linkParams.slots }

        // 设置槽位定义
        if (templateData.slotDefinitions) {
          slotDefinitions.value = templateData.slotDefinitions
        }
        else {
          slotDefinitions.value = {}
        }

        // 新增：验证模板参数
        const validationResult = validateTemplateSlots(templateData, linkParams.slots)

        if (!validationResult.isValid) {
          // 有必选项未填充，高亮显示并不发送消息
          highlightMissingSlots.value = validationResult.missingRequiredSlots
          showValidationMessage.value = true
          ElMessage.warning(`请完成模板中的必填参数：${validationResult.missingRequiredSlots.join(', ')}`)
          return
        }

        // 清除高亮状态
        highlightMissingSlots.value = []
        showValidationMessage.value = false

        // 直接调用模板发送方法，传递所有必要参数
        await chatSenderRef.value.sendTemplateMessage(
          linkParams.slots,
          linkParams.templateId,
          templateData.templateContent || linkParams.message || '',
        )
      }
      catch (error) {
        console.error('获取模板详情失败:', error)
        ElMessage.error('获取模板信息失败，使用普通文本发送')

        // 回退到普通文本发送
        if (linkParams.message) {
          chatSenderRef.value.setInputContent(linkParams.message)
          chatSenderRef.value.sendCurrentInput()
        }
      }
    }
    else {
      // 回退到普通文本发送
      if (linkParams.message) {
        chatSenderRef.value.setInputContent(linkParams.message)
        chatSenderRef.value.sendCurrentInput()
      }
    }
  }
}

// 新增：清除高亮状态的函数
function clearHighlightState() {
  highlightMissingSlots.value = []
  showValidationMessage.value = false
}

// 新增：处理加载状态更新
function handleUpdateIsLoading(loading: boolean) {
  isLoading.value = loading
}

// 清除模板
function handleClearTemplate() {
  templateContent.value = ''
  templateName.value = ''
  templateId.value = ''
  slotDefinitions.value = {}
  slotValues.value = {}
  // 新增：清除高亮状态
  clearHighlightState()
}

// 新增：动态调整底部高度的函数
function adjustBottomSideHeight() {
  if (bottomContainerRef.value) {
    const containerHeight = bottomContainerRef.value.scrollHeight
    // 添加容器的内边距：顶部16px + 底部24px = 40px
    const padding = 40
    const newHeight = Math.max(120, Math.min(400, containerHeight + padding))
    if (Math.abs(bottomSideHeight.value - newHeight) > 5) {
      bottomSideHeight.value = newHeight
    }
  }
}

// 新增：初始化ResizeObserver
function initResizeObserver() {
  if (typeof ResizeObserver !== 'undefined' && bottomContainerRef.value) {
    resizeObserver = new ResizeObserver(() => {
      nextTick(() => {
        adjustBottomSideHeight()
      })
    })
    resizeObserver.observe(bottomContainerRef.value)
  }
}

// 新增：清理ResizeObserver
function cleanupResizeObserver() {
  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }
}

// 组件挂载时初始化
onMounted(() => {
  nextTick(() => {
    initResizeObserver()
    adjustBottomSideHeight()
  })
})

// 组件卸载时清理
onUnmounted(() => {
  cleanupResizeObserver()
})

// 监听模板状态变化，重新调整高度
watch([() => templateContent.value, () => templateName.value], () => {
  nextTick(() => {
    adjustBottomSideHeight()
  })
}, { flush: 'post' })
</script>

<template>
  <div class="absolute size-full flex flex-col">
    <!-- <FaPageHeader title="布局容器" class="mb-0" /> -->
    <div class="relative flex-1">
      <FaLayoutContainer
        :vertical="vertical"
        :enable-left-side="enableLeftSide"
        :enable-right-side="enableRightSide"
        :enable-top-side="enableTopSide"
        :enable-bottom-side="enableBottomSide"
        :left-side-width="leftSideWidth"
        :right-side-width="rightSideWidth"
        :top-side-height="topSideHeight"
        :bottom-side-height="bottomSideHeight"
        bottom-side-class="p-0"
      >
        <template #leftSide>
          <ConversationHistory
            ref="conversationHistoryRef"
            :side-width="leftSideWidth"
            @conversation-change="handleConversationChange"
            @width-change="handleWidthChange"
          />
        </template>
        <template #rightSide>
          <PromptTemplates
            :side-width="rightSideWidth"
            @select-template="handleSelectTemplate"
            @width-change="handleRightWidthChange"
          />
        </template>
        <template #topSide>
          <div class="space-y-2" />
        </template>
        <template #bottomSide>
          <div class="h-full flex flex-col bg-white px-4 pb-4 pt-4 dark:bg-gray-900">
            <div ref="bottomContainerRef">
              <ChatSender
                ref="chatSenderRef"
                :active-conversation="activeConversation"
                :is-history-loading="isHistoryLoading"
                :template-content="templateContent"
                :template-name="templateName"
                :template-id="templateId"
                :slot-definitions="slotDefinitions"
                :slot-values="slotValues"
                :highlight-missing-slots="highlightMissingSlots"
                :show-validation-message="showValidationMessage"
                @message-sent="handleMessageSent"
                @update:is-loading="handleUpdateIsLoading"
                @open-template-drawer="handleOpenTemplateDrawer"
                @clear-template="handleClearTemplate"
                @slot-value-change="handleSlotValueChange"
              />
            </div>
          </div>
        </template>

        <!-- 中央区域 - 聊天消息列表 -->
        <div class="relative h-full flex flex-col">
          <!-- 右上角 - 功能按钮 -->
          <ChatFunctionButtons
            :is-new-chat="isNewChat"
            :enable-left-side="enableLeftSide"
            :enable-right-side="enableRightSide"
            @create-new-chat="handleCreateNewChat"
            @toggle-left-side="enableLeftSide = !enableLeftSide"
            @toggle-right-side="enableRightSide = !enableRightSide"
          />

          <!-- 中央区域 - 聊天内容，设置溢出滚动 -->
          <div class="flex-grow overflow-auto">
            <template v-if="showWelcome">
              <ChatWelcome
                @select-question="handleSelectQuestion"
              />
            </template>

            <!-- 中央区域 - 聊天消息列表 -->
            <template v-else>
              <ChatMessageList
                :chat-history="chatHistory"
                :is-loading="isLoading"
                :is-history-loading="isHistoryLoading"
                :styles="{}"
                class="flex-1"
                @suggestion-click="handleSuggestionClick"
              />
            </template>
          </div>
        </div>
      </FaLayoutContainer>
    </div>
  </div>
</template>
