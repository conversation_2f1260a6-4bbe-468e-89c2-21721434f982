<route lang="yaml">
meta:
  title: 主页
  icon: ant-design:home-twotone
</route>

<template>
  <div>
    <FaPageHeader>
      <template #title>
        <div class="flex items-center gap-4">
          欢迎使用 DIPS Pro AI
        </div>
      </template>
      <!-- <template #description>
        <div class="text-sm/6">
          <div>
            这是一款<b class="text-emphasis">开箱即用</b>的中后台框架，同时它也经历过数十个真实项目的技术沉淀，确保框架在开发中可落地、可使用、可维护
          </div>
          <div>
            注：在作者就职过的公司，本框架已在电商、直播、OA、ERP等多个不同领域的中后台系统中应用并稳定运行
          </div>
        </div>
      </template>
      <div class="flex gap-2">
        <FaButton variant="outline" @click="open('https://fantastic-admin.hurui.me')">
          <FaIcon name="i-ri:file-text-line" />
          开发文档
        </FaButton>
        <FaDropdown
          :items="[
            [
              { label: 'Github', icon: 'i-simple-icons:github', handle: () => open('https://github.com/fantastic-admin/basic') },
              { label: 'Gitee', icon: 'i-simple-icons:gitee', handle: () => open('https://gitee.com/fantastic-admin/basic') },
            ],
          ]"
        >
          <FaButton>
            <FaIcon name="i-ri:code-s-slash-line" />
            代码仓库
            <FaIcon name="i-ep:arrow-down" />
          </FaButton>
        </FaDropdown>
      </div> -->
    </FaPageHeader>
    <!-- <div class="w-full flex flex-col gap-4 px-4 xl-flex-row">
      <FaPageMain class="m-0 flex-1" title-class="flex flex-wrap items-center justify-between gap-4">
        <template #title>
          <div class="title-info">
            <img src="https://cn.vuejs.org/logo.svg">
            <div>
              <h1 class="c-[#41b883]">
                Fantastic-startkit
              </h1>
              <h2>一款简单好用的 Vue3 项目启动套件</h2>
            </div>
          </div>
          <div class="ms-auto">
            <FaButton @click="open('https://hooray.github.io/fantastic-startkit')">
              访问官网
            </FaButton>
          </div>
        </template>
        <ul class="m-0 list-disc px-8 text-sm leading-6 space-y-1">
          <li v-for="item in fantasticStartkitInfo.feature" :key="item">
            {{ item }}
          </li>
        </ul>
      </FaPageMain>
      <FaPageMain class="m-0 flex-1" title-class="flex flex-wrap items-center justify-between gap-4" main-class="flex-1">
        <template #title>
          <div class="title-info">
            <img src="https://fantastic-admin.hurui.me/logo.svg">
            <div>
              <h1 class="c-[#41b883]">
                Fantastic-admin
              </h1>
              <h2>一款开箱即用的 Vue 中后台管理系统框架</h2>
            </div>
          </div>
          <div class="ms-auto">
            <FaButton @click="open('https://fantastic-admin.hurui.me')">
              访问官网
            </FaButton>
          </div>
        </template>
        <div class="size-full flex items-center justify-center px-12">
          <FaCarousel
            :autoplay="{
              delay: 5000,
            }"
          >
            <img v-for="item in fantasticAdminInfo.data" :key="item" v-zoomable :src="item" class="size-full cursor-pointer overflow-hidden border rounded-xl object-cover">
          </FaCarousel>
        </div>
      </FaPageMain>
      <FaPageMain class="m-0 flex-1" title-class="flex flex-wrap items-center justify-between gap-4" main-class="flex-1">
        <template #title>
          <div class="title-info">
            <img src="https://one-step-admin.hurui.me/logo.png">
            <div>
              <h1 class="c-[#67c23a]">
                One-step-admin
              </h1>
              <h2>一款干啥都快人一步的 Vue 中后台系统框架</h2>
            </div>
          </div>
          <div class="ms-auto">
            <FaButton @click="open('https://one-step-admin.hurui.me')">
              访问官网
            </FaButton>
          </div>
        </template>
        <div class="size-full flex items-center justify-center px-12">
          <FaCarousel
            :autoplay="{
              delay: 5000,
            }"
          >
            <img v-for="item in oneStepAdminInfo.data" :key="item" v-zoomable :src="item" class="size-full cursor-pointer overflow-hidden border rounded-xl object-cover">
          </FaCarousel>
        </div>
      </FaPageMain>
    </div> -->
  </div>
</template>

<style scoped>
.text-emphasis {
  text-emphasis-style: "❤";
}

.title-info {
  --uno: flex items-center gap-4;

  img {
    --uno: block w-12 h-12;
  }

  h1 {
    --uno: m-0 text-2xl;
  }

  h2 {
    --uno: m-0 text-base text-secondary-foreground/50 font-normal;
  }
}
</style>
