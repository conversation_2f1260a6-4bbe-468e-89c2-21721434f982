<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import api from '@/api/modules/sys_role'
import { ElMessage } from 'element-plus'

export interface Props {
  id?: number | string
}
const props = withDefaults(
  defineProps<Props>(),
  {
    id: '',
  },
)

const loading = ref(false)
const formRef = useTemplateRef<FormInstance>('formRef')
const form = ref({
  id: props.id,
  tenantId: 1, // 默认租户ID
  roleCode: '',
  roleName: '',
  roleType: 'custom',
  parentId: null,
  level: 1,
  sortOrder: 0,
  status: 1,
  description: '',
})

const formRules = ref<FormRules>({
  roleCode: [
    { required: true, message: '请输入角色编码', trigger: 'blur' },
    { min: 2, max: 64, message: '角色编码长度在 2 到 64 个字符', trigger: 'blur' },
    { pattern: /^[\w-]+$/, message: '角色编码只能包含字母、数字、下划线和横线', trigger: 'blur' },
  ],
  roleName: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 128, message: '角色名称长度在 2 到 128 个字符', trigger: 'blur' },
  ],
  roleType: [
    { required: true, message: '请选择角色类型', trigger: 'change' },
  ],
  level: [
    { required: true, message: '请输入角色层级', trigger: 'blur' },
    { type: 'number', min: 1, max: 10, message: '角色层级必须在 1 到 10 之间', trigger: 'blur' },
  ],
  sortOrder: [
    { type: 'number', min: 0, max: 9999, message: '排序值必须在 0 到 9999 之间', trigger: 'blur' },
  ],
})

// 状态选项
const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 },
]

// 角色类型选项
const roleTypeOptions = [
  { label: '系统角色', value: 'system' },
  { label: '自定义角色', value: 'custom' },
]

// 父角色选项
const parentRoleOptions = ref<Array<{ label: string, value: number }>>([])

onMounted(() => {
  if (form.value.id !== '') {
    getInfo()
  }
  loadParentRoles()
})

function getInfo() {
  loading.value = true
  api.detail(form.value.id).then((res: any) => {
    loading.value = false
    const data = res.data
    Object.assign(form.value, {
      tenantId: data.tenantId,
      roleCode: data.roleCode,
      roleName: data.roleName,
      roleType: data.roleType,
      parentId: data.parentId,
      level: data.level,
      sortOrder: data.sortOrder,
      status: data.status,
      description: data.description,
    })
  })
}

// 加载父角色选项
function loadParentRoles() {
  api.getAllRoles(form.value.tenantId).then((res: any) => {
    parentRoleOptions.value = res.data
      .filter((role: any) => role.id !== form.value.id) // 排除自己
      .map((role: any) => ({
        label: role.roleName,
        value: role.id,
      }))
  })
}

// 检查角色编码是否存在
async function checkRoleCode() {
  if (!form.value.roleCode) { return }

  try {
    const res = await api.checkRoleCode(form.value.tenantId, form.value.roleCode, form.value.id || undefined)
    if (res.data) {
      ElMessage.warning('角色编码已存在')
      return false
    }
    return true
  }
  catch (error) {
    return true
  }
}

// 检查角色名称是否存在
async function checkRoleName() {
  if (!form.value.roleName) { return }

  try {
    const res = await api.checkRoleName(form.value.tenantId, form.value.roleName, form.value.id || undefined)
    if (res.data) {
      ElMessage.warning('角色名称已存在')
      return false
    }
    return true
  }
  catch (error) {
    return true
  }
}

defineExpose({
  submit() {
    return new Promise<void>((resolve) => {
      formRef.value?.validate(async (valid) => {
        if (valid) {
          // 检查角色编码和名称是否重复
          const codeValid = await checkRoleCode()
          const nameValid = await checkRoleName()

          if (!codeValid || !nameValid) {
            return
          }

          const submitData = { ...form.value }

          if (form.value.id === '') {
            api.create(submitData).then(() => {
              ElMessage.success({
                message: '新增角色成功',
                center: true,
              })
              resolve()
            })
          }
          else {
            api.edit(form.value.id, submitData).then(() => {
              ElMessage.success({
                message: '编辑角色成功',
                center: true,
              })
              resolve()
            })
          }
        }
      })
    })
  },
})
</script>

<template>
  <div v-loading="loading">
    <ElForm ref="formRef" :model="form" :rules="formRules" label-width="120px" label-suffix="：">
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="角色编码" prop="roleCode">
            <ElInput v-model="form.roleCode" placeholder="请输入角色编码" @blur="checkRoleCode" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="角色名称" prop="roleName">
            <ElInput v-model="form.roleName" placeholder="请输入角色名称" @blur="checkRoleName" />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="角色类型" prop="roleType">
            <ElSelect v-model="form.roleType" placeholder="请选择角色类型">
              <ElOption v-for="item in roleTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="父角色">
            <ElSelect v-model="form.parentId" placeholder="请选择父角色" clearable>
              <ElOption v-for="item in parentRoleOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="角色层级" prop="level">
            <ElInputNumber v-model="form.level" :min="1" :max="10" placeholder="请输入角色层级" style="width: 100%;" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="排序" prop="sortOrder">
            <ElInputNumber v-model="form.sortOrder" :min="0" :max="9999" placeholder="请输入排序值" style="width: 100%;" />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="状态">
            <ElSelect v-model="form.status" placeholder="请选择状态">
              <ElOption v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElFormItem label="角色描述">
        <ElInput v-model="form.description" type="textarea" :rows="4" placeholder="请输入角色描述" />
      </ElFormItem>

      <ElAlert title="角色编码和角色名称在同一租户下必须唯一" type="info" show-icon :closable="false" />
    </ElForm>
  </div>
</template>

<style scoped>
/* 样式 */
</style>
