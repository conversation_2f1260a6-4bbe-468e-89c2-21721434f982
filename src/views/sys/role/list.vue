<route lang="yaml">
meta:
  title: 角色管理
</route>

<script setup lang="ts">
import api from '@/api/modules/sys_role'
import eventBus from '@/utils/eventBus'
import { ElMessage, ElMessageBox } from 'element-plus'
import FormMode from './components/FormMode/index.vue'

defineOptions({
  name: 'SysRoleList',
})

const router = useRouter()
const { pagination, onSizeChange, onCurrentChange, onSortChange } = usePagination()

// 表格是否自适应高度
const tableAutoHeight = ref(false)

/**
 * 详情展示模式
 * router 路由跳转
 * dialog 对话框
 * drawer 抽屉
 */
const formMode = ref<'router' | 'dialog' | 'drawer'>('router')

// 详情
const formModeProps = ref({
  visible: false,
  id: '',
})

// 搜索
const searchDefault = {
  roleCode: '',
  roleName: '',
  roleType: '',
  status: '',
}
const search = ref({ ...searchDefault })
function searchReset() {
  Object.assign(search.value, searchDefault)
}

// 批量操作
const batch = ref({
  enable: true,
  selectionDataList: [],
})

// 列表
const loading = ref(false)
const dataList = ref([])

onMounted(() => {
  getDataList()
  if (formMode.value === 'router') {
    eventBus.on('get-data-list', () => {
      getDataList()
    })
  }
})

onBeforeUnmount(() => {
  if (formMode.value === 'router') {
    eventBus.off('get-data-list')
  }
})

function getDataList() {
  loading.value = true
  const params = {
    page: pagination.value.page,
    size: pagination.value.size,
    tenantId: 1, // 默认租户ID
    ...(search.value.roleCode && { roleCode: search.value.roleCode }),
    ...(search.value.roleName && { roleName: search.value.roleName }),
    ...(search.value.roleType && { roleType: search.value.roleType }),
    ...(search.value.status && { status: search.value.status }),
  }
  api.list(params).then((res: any) => {
    loading.value = false
    dataList.value = res.data.content
    pagination.value.total = res.data.totalElements
  })
}

// 每页数量切换
function sizeChange(size: number) {
  onSizeChange(size).then(() => getDataList())
}

// 当前页码切换（翻页）
function currentChange(page = 1) {
  onCurrentChange(page).then(() => getDataList())
}

// 字段排序
function sortChange({ prop, order }: { prop: string, order: string }) {
  onSortChange(prop, order).then(() => getDataList())
}

function onCreate() {
  if (formMode.value === 'router') {
    router.push({
      name: 'SysRoleDetail',
    })
  }
  else {
    formModeProps.value.id = ''
    formModeProps.value.visible = true
  }
}

function onEdit(row: any) {
  if (formMode.value === 'router') {
    router.push({
      name: 'SysRoleDetail',
      params: {
        id: row.id,
      },
    })
  }
  else {
    formModeProps.value.id = row.id
    formModeProps.value.visible = true
  }
}

function onDel(row: any) {
  ElMessageBox.confirm(`确认删除角色「${row.roleName}」吗？`, '确认信息').then(() => {
    api.delete(row.id).then(() => {
      getDataList()
      ElMessage.success({
        message: '删除成功',
        center: true,
      })
    })
  }).catch(() => {})
}

// 批量删除
function onBatchDelete() {
  if (batch.value.selectionDataList.length === 0) {
    ElMessage.warning('请选择要删除的角色')
    return
  }

  ElMessageBox.confirm(`确认删除选中的 ${batch.value.selectionDataList.length} 个角色吗？`, '确认信息').then(() => {
    const ids = batch.value.selectionDataList.map((item: any) => item.id)
    api.batchDelete(ids).then(() => {
      getDataList()
      ElMessage.success({
        message: '批量删除成功',
        center: true,
      })
    })
  }).catch(() => {})
}

// 更新状态
function onUpdateStatus(row: any, status: number) {
  const statusText = status === 1 ? '启用' : '禁用'
  ElMessageBox.confirm(`确认${statusText}角色「${row.roleName}」吗？`, '确认信息').then(() => {
    api.updateStatus(row.id, status).then(() => {
      row.status = status
      ElMessage.success({
        message: `${statusText}成功`,
        center: true,
      })
    })
  }).catch(() => {})
}

// 获取角色类型标签
function getRoleTypeTag(roleType: string) {
  const typeMap: Record<string, { text: string, type: 'success' | 'info' | 'warning' | 'danger' | 'primary' }> = {
    system: { text: '系统角色', type: 'danger' },
    custom: { text: '自定义角色', type: 'primary' },
  }
  return typeMap[roleType] || { text: '未知', type: 'info' }
}

// 获取状态标签
function getStatusTag(status: number) {
  const statusMap: Record<number, { text: string, type: 'success' | 'info' | 'warning' | 'danger' | 'primary' }> = {
    0: { text: '禁用', type: 'danger' },
    1: { text: '启用', type: 'success' },
  }
  return statusMap[status] || { text: '未知', type: 'info' }
}
</script>

<template>
  <div :class="{ 'absolute-container': tableAutoHeight }">
    <FaPageHeader title="角色管理" class="mb-0" />
    <FaPageMain :class="{ 'flex-1 overflow-auto': tableAutoHeight }" :main-class="{ 'flex-1 flex flex-col overflow-auto': tableAutoHeight }">
      <FaSearchBar :show-toggle="false">
        <template #default="{ fold, toggle }">
          <ElForm :model="search" size="default" label-width="100px" inline-message inline class="search-form">
            <ElFormItem label="角色编码">
              <ElInput v-model="search.roleCode" placeholder="请输入角色编码，支持模糊查询" clearable @keydown.enter="currentChange()" @clear="currentChange()" />
            </ElFormItem>
            <ElFormItem label="角色名称">
              <ElInput v-model="search.roleName" placeholder="请输入角色名称，支持模糊查询" clearable @keydown.enter="currentChange()" @clear="currentChange()" />
            </ElFormItem>
            <ElFormItem label="角色类型">
              <ElSelect v-model="search.roleType" placeholder="请选择角色类型" clearable @change="currentChange()">
                <ElOption label="系统角色" value="system" />
                <ElOption label="自定义角色" value="custom" />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="状态">
              <ElSelect v-model="search.status" placeholder="请选择状态" clearable @change="currentChange()">
                <ElOption label="启用" value="1" />
                <ElOption label="禁用" value="0" />
              </ElSelect>
            </ElFormItem>
            <ElFormItem>
              <ElButton @click="searchReset(); currentChange()">
                重置
              </ElButton>
              <ElButton type="primary" @click="currentChange()">
                <template #icon>
                  <FaIcon name="i-ep:search" />
                </template>
                筛选
              </ElButton>
              <ElButton link disabled @click="toggle">
                <template #icon>
                  <FaIcon :name="fold ? 'i-ep:caret-bottom' : 'i-ep:caret-top' " />
                </template>
                {{ fold ? '展开' : '收起' }}
              </ElButton>
            </ElFormItem>
          </ElForm>
        </template>
      </FaSearchBar>
      <ElDivider border-style="dashed" />
      <ElSpace wrap>
        <ElButton type="primary" size="default" @click="onCreate">
          <template #icon>
            <FaIcon name="i-ep:plus" />
          </template>
          新增角色
        </ElButton>
        <ElButton v-if="batch.enable" type="danger" size="default" :disabled="!batch.selectionDataList.length" @click="onBatchDelete">
          <template #icon>
            <FaIcon name="i-ep:delete" />
          </template>
          批量删除
        </ElButton>
      </ElSpace>
      <ElTable v-loading="loading" class="my-4" :data="dataList" stripe highlight-current-row border height="100%" @sort-change="sortChange" @selection-change="batch.selectionDataList = $event">
        <ElTableColumn v-if="batch.enable" type="selection" align="center" fixed />
        <ElTableColumn prop="roleCode" label="角色编码" width="150" />
        <ElTableColumn prop="roleName" label="角色名称" width="150" />
        <ElTableColumn prop="roleType" label="角色类型" width="120" align="center">
          <template #default="scope">
            <ElTag :type="getRoleTypeTag(scope.row.roleType).type">
              {{ getRoleTypeTag(scope.row.roleType).text }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="sortOrder" label="排序" width="80" align="center" />
        <ElTableColumn prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <ElTag :type="getStatusTag(scope.row.status).type">
              {{ getStatusTag(scope.row.status).text }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <ElTableColumn prop="createdTime" label="创建时间" width="180" />
        <ElTableColumn label="操作" width="70" align="center" fixed="right">
          <template #default="scope">
            <FaDropdown
              :items="[
                [
                  {
                    label: '编辑',
                    icon: 'i-ep:edit',
                    handle: () => onEdit(scope.row),
                  },
                  {
                    label: scope.row.status === 1 ? '禁用' : '启用',
                    icon: scope.row.status === 1 ? 'i-ep:close' : 'i-ep:check',
                    handle: () => onUpdateStatus(scope.row, scope.row.status === 1 ? 0 : 1),
                  },
                ],
                [
                  {
                    label: '删除',
                    icon: 'i-ep:delete',
                    handle: () => onDel(scope.row),
                  },
                ],
              ]"
            >
              <ElButton type="primary" size="small" plain>
                <template #icon>
                  <FaIcon name="i-ep:more" />
                </template>
              </ElButton>
            </FaDropdown>
          </template>
        </ElTableColumn>
      </ElTable>
      <ElPagination v-model:current-page="pagination.page" v-model:page-size="pagination.size" :total="pagination.total" :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
    </FaPageMain>
    <FormMode v-if="formMode === 'dialog' || formMode === 'drawer'" :id="formModeProps.id" v-model="formModeProps.visible" :mode="formMode" @success="getDataList" />
  </div>
</template>

<style scoped>
.absolute-container {
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.search-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(330px, 1fr));
  margin-bottom: -18px;

  :deep(.el-form-item) {
    grid-column: auto / span 1;

    &:last-child {
      grid-column-end: -1;

      .el-form-item__content {
        justify-content: flex-end;
      }
    }
  }
}

.el-divider {
  width: calc(100% + 40px);
  margin-inline: -20px;
}
</style>
