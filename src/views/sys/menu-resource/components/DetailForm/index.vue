<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import api from '@/api/modules/sys_menu_resource'
import { ElMessage } from 'element-plus'

export interface Props {
  id?: number | string
}
const props = withDefaults(
  defineProps<Props>(),
  {
    id: '',
  },
)

const loading = ref(false)
const formRef = useTemplateRef<FormInstance>('formRef')
const form = ref({
  id: props.id,
  menuCode: '',
  menuName: '',
  menuType: 'menu',
  parentId: undefined as number | undefined,
  level: 1,
  path: '',
  component: '',
  icon: '',
  sortOrder: 0,
  visible: 1,
  status: 1,
  externalLink: 0,
  cache: 0,
  description: '',
})

const formRules = ref<FormRules>({
  menuCode: [
    { required: true, message: '请输入菜单编码', trigger: 'blur' },
    { min: 2, max: 64, message: '菜单编码长度在 2 到 64 个字符', trigger: 'blur' },
  ],
  menuName: [
    { required: true, message: '请输入菜单名称', trigger: 'blur' },
    { min: 2, max: 128, message: '菜单名称长度在 2 到 128 个字符', trigger: 'blur' },
  ],
  menuType: [
    { required: true, message: '请选择菜单类型', trigger: 'change' },
  ],
  visible: [
    { required: true, message: '请选择是否可见', trigger: 'change' },
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' },
  ],
})

// 菜单类型选项
const menuTypeOptions = [
  { label: '目录', value: 'catalog' },
  { label: '菜单', value: 'menu' },
  { label: '按钮', value: 'button' },
]

// 可见性选项
const visibleOptions = [
  { label: '显示', value: 1 },
  { label: '隐藏', value: 0 },
]

// 状态选项
const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 },
]

// 外链选项
const externalLinkOptions = [
  { label: '是', value: 1 },
  { label: '否', value: 0 },
]

// 缓存选项
const cacheOptions = [
  { label: '是', value: 1 },
  { label: '否', value: 0 },
]

// 父菜单选项
const parentMenuOptions = ref<Array<{ label: string, value: number }>>([])

onMounted(() => {
  if (form.value.id !== '') {
    getInfo()
  }
  getParentMenuOptions()
})

function getInfo() {
  loading.value = true
  api.getMenuResourceDetail(form.value.id).then((res: any) => {
    loading.value = false
    const data = res.data
    Object.assign(form.value, {
      menuCode: data.menuCode,
      menuName: data.menuName,
      menuType: data.menuType,
      parentId: data.parentId,
      level: data.level,
      path: data.path,
      component: data.component,
      icon: data.icon,
      sortOrder: data.sortOrder,
      visible: data.visible,
      status: data.status,
      externalLink: data.externalLink,
      cache: data.cache,
      description: data.description,
    })
  })
}

function getParentMenuOptions() {
  api.getActiveMenuResources().then((res: any) => {
    const menus = res.data || []
    parentMenuOptions.value = menus
      .filter((menu: any) => menu.menuType !== 'button')
      .map((menu: any) => ({
        label: menu.menuName,
        value: menu.id,
      }))
  })
}

defineExpose({
  submit() {
    return new Promise<void>((resolve) => {
      formRef.value?.validate((valid) => {
        if (valid) {
          const submitData = { ...form.value }

          if (form.value.id === '') {
            api.createMenuResource(submitData).then(() => {
              ElMessage.success('新增菜单资源成功')
              resolve()
            })
          }
          else {
            api.updateMenuResource(submitData).then(() => {
              ElMessage.success('编辑菜单资源成功')
              resolve()
            })
          }
        }
      })
    })
  },
})
</script>

<template>
  <div v-loading="loading">
    <ElForm ref="formRef" :model="form" :rules="formRules" label-width="120px" label-suffix="：">
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="菜单编码" prop="menuCode">
            <ElInput v-model="form.menuCode" placeholder="请输入菜单编码" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="菜单名称" prop="menuName">
            <ElInput v-model="form.menuName" placeholder="请输入菜单名称" />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="菜单类型" prop="menuType">
            <ElSelect v-model="form.menuType" placeholder="请选择菜单类型">
              <ElOption v-for="item in menuTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="父菜单">
            <ElSelect v-model="form.parentId" placeholder="请选择父菜单" clearable>
              <ElOption v-for="item in parentMenuOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="层级">
            <ElInputNumber v-model="form.level" :min="1" :max="10" placeholder="请输入层级" style="width: 100%;" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="排序">
            <ElInputNumber v-model="form.sortOrder" :min="0" placeholder="请输入排序" style="width: 100%;" />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="路由路径">
            <ElInput v-model="form.path" placeholder="请输入路由路径" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="组件路径">
            <ElInput v-model="form.component" placeholder="请输入组件路径" />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="图标">
            <div class="icon-selector">
              <div class="icon-preview">
                <FaIcon v-if="form.icon" :name="form.icon" />
                <span v-else class="text-gray-400">无图标</span>
                <span class="icon-name">{{ form.icon || '未选择' }}</span>
              </div>
              <FaIconPicker v-model="form.icon" />
            </div>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="可见性" prop="visible">
            <ElSelect v-model="form.visible" placeholder="请选择可见性">
              <ElOption v-for="item in visibleOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="状态" prop="status">
            <ElSelect v-model="form.status" placeholder="请选择状态">
              <ElOption v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="是否外链">
            <ElSelect v-model="form.externalLink" placeholder="请选择是否外链">
              <ElOption v-for="item in externalLinkOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="是否缓存">
            <ElSelect v-model="form.cache" placeholder="请选择是否缓存">
              <ElOption v-for="item in cacheOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow>
        <ElCol :span="24">
          <ElFormItem label="描述">
            <ElInput v-model="form.description" type="textarea" :rows="3" placeholder="请输入描述" />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
</template>

<style lang="scss" scoped>
.icon-selector {
  .icon-preview {
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 8px;
    background-color: var(--el-fill-color-light);
    border-radius: 4px;

    .icon-name {
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
  }
}
</style>
