<route lang="yaml">
meta:
  title: 菜单资源管理
  icon: i-ep:menu
</route>

<script setup lang="ts">
import type { TagProps } from 'element-plus'
import api from '@/api/modules/sys_menu_resource'
import eventBus from '@/utils/eventBus'
// import dayjs from 'dayjs'
import { ElMessage, ElMessageBox } from 'element-plus'
import FormMode from './components/FormMode/index.vue'

defineOptions({
  name: 'SysMenuResourceList',
})

const router = useRouter()
const { pagination, getParams, onSortChange } = usePagination()

// 表格是否自适应高度
// const tableAutoHeight = ref(false)

/**
 * 详情展示模式
 * router 路由跳转
 * dialog 对话框
 * drawer 抽屉
 */
const formMode = ref<'router' | 'dialog' | 'drawer'>('dialog')

// 详情
const formModeProps = ref({
  visible: false,
  id: '',
})

// 搜索
const searchDefault = {
  menuCode: '',
  menuName: '',
  menuType: '',
  parentId: undefined as number | undefined,
  level: undefined as number | undefined,
  visible: undefined as number | undefined,
  status: undefined as number | undefined,
  externalLink: undefined as number | undefined,
}
const search = ref({ ...searchDefault })

function searchReset() {
  Object.assign(search.value, searchDefault)
}

// 批量操作
const batch = ref({
  enable: true,
  selectionDataList: [],
})

// 列表
const loading = ref(false)
const dataList = ref([])

// 菜单类型选项
const menuTypeOptions = [
  { label: '目录', value: 'catalog', type: 'primary' },
  { label: '菜单', value: 'menu', type: 'success' },
  { label: '按钮', value: 'button', type: 'warning' },
]

// 可见性选项
const visibleOptions = [
  { label: '显示', value: 1, type: 'success' },
  { label: '隐藏', value: 0, type: 'danger' },
]

// 状态选项
const statusOptions = [
  { label: '启用', value: 1, type: 'success' },
  { label: '禁用', value: 0, type: 'danger' },
]

// 外链选项
const externalLinkOptions = [
  { label: '是', value: 1, type: 'success' },
  { label: '否', value: 0, type: 'info' },
]

// 缓存选项
const cacheOptions = [
  { label: '是', value: 1, type: 'success' },
  { label: '否', value: 0, type: 'info' },
]

onMounted(() => {
  getDataList()
  if (formMode.value === 'router') {
    eventBus.on('get-data-list', () => {
      getDataList()
    })
  }
})

onBeforeUnmount(() => {
  if (formMode.value === 'router') {
    eventBus.off('get-data-list')
  }
})

function getDataList() {
  loading.value = true
  const params = {
    ...getParams(),
    ...search.value,
  }
  api.getMenuResourceList(params).then((res: any) => {
    loading.value = false
    dataList.value = res.data.content
    pagination.value.total = res.data.totalElements
  }).catch((error: any) => {
    console.error('请求失败:', error)
    loading.value = false
  })
}

// 每页数量切换
function sizeChange(_size: number) {
  getDataList()
}

// 当前页码切换（翻页）
function currentChange(_page = 1) {
  getDataList()
}

// 字段排序
function _sortChange({ prop, order }: { prop: string, order: string }) {
  onSortChange(prop, order).then(() => getDataList())
}

function onCreate() {
  if (formMode.value === 'router') {
    router.push({
      name: 'SysMenuResourceDetail',
    })
  }
  else {
    formModeProps.value.id = ''
    formModeProps.value.visible = true
  }
}

function onEdit(row: any) {
  if (formMode.value === 'router') {
    router.push({
      name: 'SysMenuResourceDetail',
      params: {
        id: row.id,
      },
    })
  }
  else {
    formModeProps.value.id = row.id
    formModeProps.value.visible = true
  }
}

function onDel(row: any) {
  ElMessageBox.confirm(`确认删除菜单资源「${row.menuName}」吗？`, '确认信息').then(() => {
    api.deleteMenuResource(row.id).then(() => {
      getDataList()
      ElMessage.success('删除成功')
    })
  }).catch(() => {})
}

// 批量删除
function onBatchDelete() {
  if (!batch.value.selectionDataList.length) {
    ElMessage.warning('请选择要删除的菜单资源')
    return
  }

  ElMessageBox.confirm(`确认删除选中的 ${batch.value.selectionDataList.length} 个菜单资源吗？`, '确认信息').then(() => {
    const ids = batch.value.selectionDataList.map((item: any) => item.id)
    api.deleteMenuResources(ids).then(() => {
      getDataList()
      ElMessage.success('批量删除成功')
    })
  }).catch(() => {})
}

// 切换菜单资源状态
function toggleStatus(row: any) {
  const newStatus = row.status === 1 ? 0 : 1
  const statusText = newStatus === 1 ? '启用' : '禁用'

  ElMessageBox.confirm(`确认${statusText}菜单资源「${row.menuName}」吗？`, '确认信息').then(() => {
    api.updateMenuResourceStatus(row.id, newStatus).then(() => {
      row.status = newStatus
      ElMessage.success(`${statusText}成功`)
    })
  }).catch(() => {})
}

// 获取菜单类型标签类型
function getMenuTypeTagType(menuType: string): TagProps['type'] {
  const option = menuTypeOptions.find(item => item.value === menuType)
  return option?.type as TagProps['type'] || 'info'
}

// 获取可见性标签类型
function getVisibleTagType(visible: number): TagProps['type'] {
  const option = visibleOptions.find(item => item.value === visible)
  return option?.type as TagProps['type'] || 'info'
}

// 获取状态标签类型
function getStatusTagType(status: number): TagProps['type'] {
  const option = statusOptions.find(item => item.value === status)
  return option?.type as TagProps['type'] || 'info'
}

// 获取外链标签类型
function getExternalLinkTagType(externalLink: number): TagProps['type'] {
  const option = externalLinkOptions.find(item => item.value === externalLink)
  return option?.type as TagProps['type'] || 'info'
}

// 获取缓存标签类型
function getCacheTagType(cache: number): TagProps['type'] {
  const option = cacheOptions.find(item => item.value === cache)
  return option?.type as TagProps['type'] || 'info'
}

// 格式化日期时间
function formatDateTime(dateTime: string | Date): string {
  if (!dateTime) {
    return '-'
  }

  const date = new Date(dateTime)
  if (Number.isNaN(date.getTime())) {
    return '-'
  }

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 获取操作菜单项
function getActionItems(row: any) {
  return [
    [
      {
        label: '编辑',
        icon: 'i-ep:edit',
        handle: () => onEdit(row),
      },
      {
        label: row.status === 1 ? '禁用' : '启用',
        icon: row.status === 1 ? 'i-ep:lock' : 'i-ep:unlock',
        handle: () => toggleStatus(row),
      },
    ],
    [
      {
        label: '删除',
        icon: 'i-ep:delete',
        handle: () => onDel(row),
      },
    ],
  ]
}
</script>

<template>
  <div>
    <FaPageHeader title="菜单资源管理" />
    <FaPageMain>
      <FaSearchBar
        :fold="false"
        :show-toggle="true"
      >
        <template #default="{ fold, toggle }">
          <ElForm
            :model="search"
            size="default"
            label-width="100px"
            inline
            class="search-form"
          >
            <ElFormItem label="菜单编码">
              <ElInput
                v-model="search.menuCode"
                placeholder="请输入菜单编码"
                clearable
                @keydown.enter="currentChange()"
                @clear="currentChange()"
              />
            </ElFormItem>
            <ElFormItem label="菜单名称">
              <ElInput
                v-model="search.menuName"
                placeholder="请输入菜单名称"
                clearable
                @keydown.enter="currentChange()"
                @clear="currentChange()"
              />
            </ElFormItem>
            <ElFormItem label="菜单类型">
              <ElSelect
                v-model="search.menuType"
                placeholder="请选择菜单类型"
                clearable
                @change="currentChange()"
              >
                <ElOption
                  v-for="item in menuTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </ElSelect>
            </ElFormItem>
            <template v-if="!fold">
              <ElFormItem label="层级">
                <ElInputNumber
                  v-model="search.level"
                  placeholder="请输入层级"
                  :min="1"
                  :max="10"
                  clearable
                  @change="currentChange()"
                />
              </ElFormItem>
              <ElFormItem label="可见性">
                <ElSelect
                  v-model="search.visible"
                  placeholder="请选择可见性"
                  clearable
                  @change="currentChange()"
                >
                  <ElOption
                    v-for="item in visibleOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </ElSelect>
              </ElFormItem>
              <ElFormItem label="状态">
                <ElSelect
                  v-model="search.status"
                  placeholder="请选择状态"
                  clearable
                  @change="currentChange()"
                >
                  <ElOption
                    v-for="item in statusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </ElSelect>
              </ElFormItem>
              <ElFormItem label="是否外链">
                <ElSelect
                  v-model="search.externalLink"
                  placeholder="请选择是否外链"
                  clearable
                  @change="currentChange()"
                >
                  <ElOption
                    v-for="item in externalLinkOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </ElSelect>
              </ElFormItem>
            </template>
            <ElFormItem>
              <ElButton
                type="primary"
                @click="currentChange()"
              >
                <template #icon>
                  <FaIcon name="i-ep:search" />
                </template>
                筛选
              </ElButton>
              <ElButton @click="searchReset">
                <template #icon>
                  <FaIcon name="i-ep:refresh" />
                </template>
                重置
              </ElButton>
              <ElButton
                link
                @click="toggle"
              >
                <template #icon>
                  <FaIcon :name="fold ? 'i-ep:caret-bottom' : 'i-ep:caret-top'" />
                </template>
                {{ fold ? '展开' : '收起' }}
              </ElButton>
            </ElFormItem>
          </ElForm>
        </template>
      </FaSearchBar>
      <ElSpace
        direction="vertical"
        alignment="stretch"
        :size="20"
        fill
      >
        <ElSpace wrap>
          <ElButton
            type="primary"
            size="default"
            @click="onCreate"
          >
            <template #icon>
              <FaIcon name="i-ep:plus" />
            </template>
            新增菜单资源
          </ElButton>
          <ElButton
            type="danger"
            size="default"
            :disabled="!batch.selectionDataList.length"
            @click="onBatchDelete"
          >
            <template #icon>
              <FaIcon name="i-ep:delete" />
            </template>
            批量删除
          </ElButton>
          <ElButton
            size="default"
            @click="getDataList"
          >
            <template #icon>
              <FaIcon name="i-ep:refresh" />
            </template>
            刷新
          </ElButton>
        </ElSpace>
        <ElTable
          v-loading="loading"
          :data="dataList"
          style="width: 100%;"
          @selection-change="batch.selectionDataList = $event"
        >
          <ElTableColumn type="selection" align="center" fixed="left" width="50" />
          <ElTableColumn prop="id" label="ID" width="80" sortable="custom" />
          <ElTableColumn prop="menuCode" label="菜单编码" min-width="150" sortable="custom" />
          <ElTableColumn prop="menuName" label="菜单名称" min-width="180" sortable="custom" />
          <ElTableColumn prop="menuType" label="菜单类型" width="100" align="center">
            <template #default="{ row }">
              <ElTag :type="getMenuTypeTagType(row.menuType)">
                {{ row.menuTypeText }}
              </ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="parentName" label="父菜单" min-width="120" />
          <ElTableColumn prop="level" label="层级" width="80" align="center" sortable="custom" />
          <ElTableColumn prop="icon" label="图标" width="120" align="center">
            <template #default="{ row }">
              <div class="icon-display">
                <FaIcon v-if="row.icon" :name="row.icon" />
                <span v-else class="text-gray-400">无图标</span>
              </div>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="sortOrder" label="排序" width="80" align="center" sortable="custom" />
          <ElTableColumn prop="path" label="路由路径" min-width="150" />
          <ElTableColumn prop="component" label="组件路径" min-width="150" />
          <!--
          <ElTableColumn prop="visible" label="可见性" width="80" align="center">
            <template #default="{ row }">
              <ElTag :type="getVisibleTagType(row.visible)">
                {{ row.visibleText }}
              </ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="status" label="状态" width="80" align="center">
            <template #default="{ row }">
              <ElTag :type="getStatusTagType(row.status)">
                {{ row.statusText }}
              </ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="externalLink" label="外链" width="80" align="center">
            <template #default="{ row }">
              <ElTag :type="getExternalLinkTagType(row.externalLink)">
                {{ row.externalLinkText }}
              </ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="cache" label="缓存" width="80" align="center">
            <template #default="{ row }">
              <ElTag :type="getCacheTagType(row.cache)">
                {{ row.cacheText }}
              </ElTag>
            </template>
          </ElTableColumn> -->
          <ElTableColumn prop="createdTime" label="创建时间" min-width="160" align="center">
            <template #default="{ row }">
              {{ row.createdTime ? formatDateTime(row.createdTime) : '-' }}
            </template>
          </ElTableColumn>
          <ElTableColumn label="操作" width="70" align="center" fixed="right">
            <template #default="scope">
              <FaDropdown
                :items="getActionItems(scope.row)"
                align="center"
                side="bottom"
              >
                <ElButton type="primary" size="small" plain>
                  <template #icon>
                    <FaIcon name="i-ep:more" />
                  </template>
                </ElButton>
              </FaDropdown>
            </template>
          </ElTableColumn>
        </ElTable>
        <ElPagination
          :current-page="pagination.page"
          :total="pagination.total"
          :page-size="pagination.size"
          :page-sizes="pagination.sizes"
          :layout="pagination.layout"
          :hide-on-single-page="false"
          class="pagination"
          background
          @size-change="sizeChange"
          @current-change="currentChange"
        />
      </ElSpace>
    </FaPageMain>
    <FormMode
      v-if="['dialog', 'drawer'].includes(formMode)"
      :id="formModeProps.id"
      v-model="formModeProps.visible"
      :mode="formMode"
      @success="getDataList"
    />
  </div>
</template>

<style lang="scss" scoped>
.search-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(330px, 1fr));
  gap: 18px;

  :deep(.el-form-item) {
    grid-column: span 1;
    margin-right: 0;
    margin-bottom: 0;

    .el-form-item__content {
      justify-content: flex-start;
      width: 100%;

      .el-input,
      .el-select {
        width: 100%;
      }
    }
  }

  :deep(.el-form-item:last-child) {
    grid-column-end: -1;

    .el-form-item__content {
      justify-content: flex-end;
    }
  }
}

.pagination {
  justify-content: flex-end;
}

// 确保表格容器占满100%宽度
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-space) {
  width: 100%;
}

// 确保页面主容器占满宽度
.fa-page-main {
  width: 100%;
}

// 确保表格外层容器占满宽度
:deep(.el-space--vertical) {
  width: 100%;
}

// 确保表格布局正确
:deep(.el-table__body-wrapper) {
  width: 100%;
}

:deep(.el-table__header-wrapper) {
  width: 100%;
}
</style>
