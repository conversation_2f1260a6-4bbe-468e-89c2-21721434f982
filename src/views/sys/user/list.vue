<route lang="yaml">
meta:
  title: 用户管理
  icon: i-ep:user
</route>

<script setup lang="ts">
import type { TagProps } from 'element-plus'
import api from '@/api/modules/sys_user'
import eventBus from '@/utils/eventBus'
import dayjs from 'dayjs'
import { ElMessage, ElMessageBox } from 'element-plus'
import FormMode from './components/FormMode/index.vue'

defineOptions({
  name: 'SysUserList',
})

const router = useRouter()
const { pagination, getParams, onSortChange } = usePagination()

// 表格是否自适应高度
const tableAutoHeight = ref(false)

/**
 * 详情展示模式
 * router 路由跳转
 * dialog 对话框
 * drawer 抽屉
 */
const formMode = ref<'router' | 'dialog' | 'drawer'>('dialog')

// 详情
const formModeProps = ref({
  visible: false,
  id: '',
})

// 搜索
const searchDefault = {
  username: '',
  email: '',
  phone: '',
  realName: '',
  status: '',
  userType: '',
}
const search = ref({ ...searchDefault })

function searchReset() {
  Object.assign(search.value, searchDefault)
}

// 批量操作
const batch = ref({
  enable: true,
  selectionDataList: [],
})

// 列表
const loading = ref(false)
const dataList = ref([])

// 状态选项
const statusOptions = [
  { label: '启用', value: 1, type: 'success' },
  { label: '禁用', value: 0, type: 'danger' },
  { label: '锁定', value: 2, type: 'warning' },
]

// 用户类型选项
const userTypeOptions = [
  { label: '管理员', value: 'admin', type: 'danger' },
  { label: '普通用户', value: 'normal', type: 'primary' },
  { label: '访客', value: 'guest', type: 'info' },
]

// 性别选项
const genderOptions = [
  { label: '未知', value: 0 },
  { label: '男', value: 1 },
  { label: '女', value: 2 },
]

onMounted(() => {
  getDataList()
  if (formMode.value === 'router') {
    eventBus.on('get-data-list', () => {
      getDataList()
    })
  }
})

onBeforeUnmount(() => {
  if (formMode.value === 'router') {
    eventBus.off('get-data-list')
  }
})

function getDataList() {
  loading.value = true
  const params = {
    ...getParams(),
    ...search.value,
  }
  api.list(params).then((res: any) => {
    loading.value = false
    dataList.value = res.data.content // Spring Data JPA 分页结构
    pagination.value.total = res.data.totalElements // Spring Data JPA 总数字段
  })
}

// 每页数量切换
function sizeChange(_size: number) {
  // v-model 会自动更新 pagination.size，这里直接重新加载数据
  getDataList()
}

// 当前页码切换（翻页）
function currentChange(_page = 1) {
  // v-model 会自动更新 pagination.page，这里直接重新加载数据
  getDataList()
}

// 字段排序
function sortChange({ prop, order }: { prop: string, order: string }) {
  onSortChange(prop, order).then(() => getDataList())
}

function onCreate() {
  if (formMode.value === 'router') {
    router.push({
      name: 'SysUserDetail',
    })
  }
  else {
    formModeProps.value.id = ''
    formModeProps.value.visible = true
  }
}

function onEdit(row: any) {
  if (formMode.value === 'router') {
    router.push({
      name: 'SysUserDetail',
      params: {
        id: row.id,
      },
    })
  }
  else {
    formModeProps.value.id = row.id
    formModeProps.value.visible = true
  }
}

function onDel(row: any) {
  ElMessageBox.confirm(`确认删除用户「${row.username}」吗？`, '确认信息').then(() => {
    api.delete(row.id).then(() => {
      getDataList()
      ElMessage.success('删除成功')
    })
  }).catch(() => {})
}

// 批量删除
function onBatchDelete() {
  if (!batch.value.selectionDataList.length) {
    ElMessage.warning('请选择要删除的用户')
    return
  }

  ElMessageBox.confirm(`确认删除选中的 ${batch.value.selectionDataList.length} 个用户吗？`, '确认信息').then(() => {
    const ids = batch.value.selectionDataList.map((item: any) => item.id)
    api.batchDelete(ids).then(() => {
      getDataList()
      ElMessage.success('批量删除成功')
    })
  }).catch(() => {})
}

// 切换用户状态
function toggleStatus(row: any) {
  const newStatus = row.status === 1 ? 0 : 1
  const statusText = newStatus === 1 ? '启用' : '禁用'

  ElMessageBox.confirm(`确认${statusText}用户「${row.username}」吗？`, '确认信息').then(() => {
    api.updateStatus(row.id, newStatus).then(() => {
      row.status = newStatus
      ElMessage.success(`${statusText}成功`)
    })
  }).catch(() => {})
}

// 重置密码
function resetPassword(row: any) {
  ElMessageBox.confirm(`确认重置用户「${row.username}」的密码吗？`, '确认信息').then(() => {
    api.resetPassword(row.id).then(() => {
      ElMessage.success('密码重置成功，新密码已发送至用户邮箱')
    })
  }).catch(() => {})
}

// 获取状态标签类型
function getStatusType(status: number) {
  const option = statusOptions.find(item => item.value === status)
  return (option?.type || 'info') as 'success' | 'danger' | 'warning' | 'primary' | 'info'
}

// 获取状态标签文本
function getStatusText(status: number) {
  const option = statusOptions.find(item => item.value === status)
  return option?.label || '未知'
}

// 获取用户类型标签类型
function getUserTypeType(userType: string) {
  const option = userTypeOptions.find(item => item.value === userType)
  return (option?.type || 'info') as 'success' | 'danger' | 'warning' | 'primary' | 'info'
}

// 获取用户类型标签文本
function getUserTypeText(userType: string) {
  const option = userTypeOptions.find(item => item.value === userType)
  return option?.label || '未知'
}

// 获取性别文本
function getGenderText(gender: number) {
  const option = genderOptions.find(item => item.value === gender)
  return option?.label || '未知'
}

// 获取操作菜单项
function getActionItems(row: any) {
  return [
    [
      {
        label: '编辑',
        icon: 'i-ep:edit',
        handle: () => onEdit(row),
      },
      {
        label: row.status === 1 ? '禁用' : '启用',
        icon: row.status === 1 ? 'i-ep:lock' : 'i-ep:unlock',
        handle: () => toggleStatus(row),
      },
      {
        label: '重置密码',
        icon: 'i-ep:key',
        handle: () => resetPassword(row),
      },
    ],
    [
      {
        label: '删除',
        icon: 'i-ep:delete',
        handle: () => onDel(row),
      },
    ],
  ]
}
</script>

<template>
  <div :class="{ 'absolute-container': tableAutoHeight }">
    <FaPageHeader title="用户管理" class="mb-0" />
    <FaPageMain :class="{ 'flex-1 overflow-auto': tableAutoHeight }" :main-class="{ 'flex-1 flex flex-col overflow-auto': tableAutoHeight }">
      <FaSearchBar :show-toggle="true">
        <template #default="{ fold, toggle }">
          <ElForm :model="search" size="default" label-width="100px" inline-message inline class="search-form">
            <ElFormItem label="用户名">
              <ElInput v-model="search.username" placeholder="请输入用户名" clearable @keydown.enter="currentChange()" @clear="currentChange()" />
            </ElFormItem>
            <ElFormItem label="邮箱">
              <ElInput v-model="search.email" placeholder="请输入邮箱" clearable @keydown.enter="currentChange()" @clear="currentChange()" />
            </ElFormItem>
            <ElFormItem label="手机号">
              <ElInput v-model="search.phone" placeholder="请输入手机号" clearable @keydown.enter="currentChange()" @clear="currentChange()" />
            </ElFormItem>
            <ElFormItem v-show="!fold" label="真实姓名">
              <ElInput v-model="search.realName" placeholder="请输入真实姓名" clearable @keydown.enter="currentChange()" @clear="currentChange()" />
            </ElFormItem>
            <ElFormItem v-show="!fold" label="状态">
              <ElSelect v-model="search.status" placeholder="请选择状态" clearable>
                <ElOption v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
              </ElSelect>
            </ElFormItem>
            <ElFormItem v-show="!fold" label="用户类型">
              <ElSelect v-model="search.userType" placeholder="请选择用户类型" clearable>
                <ElOption v-for="item in userTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </ElSelect>
            </ElFormItem>
            <ElFormItem>
              <ElButton @click="searchReset(); currentChange()">
                重置
              </ElButton>
              <ElButton type="primary" @click="currentChange()">
                <template #icon>
                  <FaIcon name="i-ep:search" />
                </template>
                筛选
              </ElButton>
              <ElButton link @click="toggle">
                <template #icon>
                  <FaIcon :name="fold ? 'i-ep:caret-bottom' : 'i-ep:caret-top' " />
                </template>
                {{ fold ? '展开' : '收起' }}
              </ElButton>
            </ElFormItem>
          </ElForm>
        </template>
      </FaSearchBar>
      <ElDivider border-style="dashed" />
      <ElSpace wrap>
        <ElButton type="primary" size="default" @click="onCreate">
          <template #icon>
            <FaIcon name="i-ep:plus" />
          </template>
          新增用户
        </ElButton>
        <ElButton v-if="batch.enable" type="danger" size="default" :disabled="!batch.selectionDataList.length" @click="onBatchDelete">
          <template #icon>
            <FaIcon name="i-ep:delete" />
          </template>
          批量删除
        </ElButton>
        <ElButton size="default" @click="getDataList">
          <template #icon>
            <FaIcon name="i-ep:refresh" />
          </template>
          刷新
        </ElButton>
      </ElSpace>
      <ElTable v-loading="loading" class="my-4" :data="dataList" stripe highlight-current-row border height="100%" @sort-change="sortChange" @selection-change="batch.selectionDataList = $event">
        <ElTableColumn v-if="batch.enable" type="selection" align="center" fixed />
        <ElTableColumn prop="id" label="ID" width="80" sortable />
        <ElTableColumn prop="username" label="用户名" width="120" sortable />
        <ElTableColumn prop="realName" label="真实姓名" width="120" />
        <ElTableColumn prop="nickname" label="昵称" width="120" />
        <ElTableColumn prop="email" label="邮箱" width="180" />
        <ElTableColumn prop="phone" label="手机号" width="130" />
        <ElTableColumn prop="gender" label="性别" width="80" align="center">
          <template #default="{ row }">
            {{ getGenderText(row.gender) }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <ElTag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="userType" label="用户类型" width="120" align="center">
          <template #default="{ row }">
            <ElTag :type="getUserTypeType(row.userType)" size="small">
              {{ getUserTypeText(row.userType) }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="lastLoginTime" label="最后登录" width="160" sortable>
          <template #default="{ row }">
            {{ row.lastLoginTime ? dayjs(row.lastLoginTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="createdTime" label="创建时间" width="160" sortable>
          <template #default="{ row }">
            {{ dayjs(row.createdTime).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </ElTableColumn>
        <ElTableColumn label="操作" width="70" align="center" fixed="right">
          <template #default="scope">
            <FaDropdown
              :items="getActionItems(scope.row)"
              align="center"
              side="bottom"
            >
              <ElButton type="primary" size="small" plain>
                <template #icon>
                  <FaIcon name="i-ep:more" />
                </template>
              </ElButton>
            </FaDropdown>
          </template>
        </ElTableColumn>
      </ElTable>
      <ElPagination v-model:current-page="pagination.page" v-model:page-size="pagination.size" :total="pagination.total" :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination" background @size-change="sizeChange" @current-change="currentChange" />
    </FaPageMain>
    <FormMode v-if="formMode === 'dialog' || formMode === 'drawer'" :id="formModeProps.id" v-model="formModeProps.visible" :mode="formMode" @success="getDataList" />
  </div>
</template>

<style scoped>
.absolute-container {
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.search-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(330px, 1fr));
  margin-bottom: -18px;

  :deep(.el-form-item) {
    grid-column: auto / span 1;

    &:last-child {
      grid-column-end: -1;

      .el-form-item__content {
        justify-content: flex-end;
      }
    }
  }
}

.el-divider {
  width: calc(100% + 40px);
  margin-inline: -20px;
}
</style>
