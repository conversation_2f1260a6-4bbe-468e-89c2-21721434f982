<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import api from '@/api/modules/sys_user'
import { toast } from 'vue-sonner'

export interface Props {
  id?: number | string
}
const props = withDefaults(
  defineProps<Props>(),
  {
    id: '',
  },
)

const loading = ref(false)
const formRef = useTemplateRef<FormInstance>('formRef')
const form = ref({
  id: props.id,
  username: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: '',
  realName: '',
  nickname: '',
  gender: 0,
  birthday: '',
  status: 1,
  userType: 'normal',
})

const formRules = ref<FormRules>({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (!value) {
          callback()
          return
        }
        // 清理手机号：去除空格、横线、括号、点号、下划线、加号、斜杠等分隔符和不可见字符
        const cleanPhone = value.replace(/[\s\-().+_/\u200B-\u200D\u202C\u202D\u202E]/g, '').trim()
        // 更新表单值为清理后的手机号
        if (cleanPhone !== value) {
          form.value.phone = cleanPhone
        }
        // 验证清理后的手机号格式
        const phonePattern = /^1[3-9]\d{9}$/
        if (!phonePattern.test(cleanPhone)) {
          callback(new Error('请输入正确的手机号'))
        }
        else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
  password: [
    { required: !props.id, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: !props.id, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value !== form.value.password) {
          callback(new Error('两次输入密码不一致'))
        }
        else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
  ],
})

// 状态选项
const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 },
  { label: '锁定', value: 2 },
]

// 用户类型选项
const userTypeOptions = [
  { label: '管理员', value: 'admin' },
  { label: '普通用户', value: 'normal' },
  { label: '访客', value: 'guest' },
]

// 性别选项
const genderOptions = [
  { label: '未知', value: 0 },
  { label: '男', value: 1 },
  { label: '女', value: 2 },
]

onMounted(() => {
  if (form.value.id !== '') {
    getInfo()
  }
})

function getInfo() {
  loading.value = true
  api.detail(form.value.id).then((res: any) => {
    loading.value = false
    const data = res.data
    Object.assign(form.value, {
      username: data.username,
      email: data.email,
      phone: data.phone,
      realName: data.realName,
      nickname: data.nickname,
      gender: data.gender,
      birthday: data.birthday,
      status: data.status,
      userType: data.userType,
    })
  }).catch((error) => {
    loading.value = false
    const errorMessage = error?.error || error?.message || '获取用户详情失败'
    toast.error(errorMessage)
  })
}

defineExpose({
  submit() {
    return new Promise<void>((resolve, reject) => {
      formRef.value?.validate((valid) => {
        if (valid) {
          const submitData = { ...form.value } as any
          // 编辑时不提交密码字段（除非有值）
          if (props.id && !submitData.password) {
            delete submitData.password
            delete submitData.confirmPassword
          }

          if (form.value.id === '') {
            api.create(submitData).then(() => {
              toast.success('新增用户成功')
              resolve()
            }).catch((error) => {
              // 显示后端返回的具体错误信息
              const errorMessage = error?.error || error?.message || '新增用户失败'
              toast.error(errorMessage)
              reject(error)
            })
          }
          else {
            api.edit(submitData).then(() => {
              toast.success('编辑用户成功')
              resolve()
            }).catch((error) => {
              // 显示后端返回的具体错误信息
              const errorMessage = error?.error || error?.message || '编辑用户失败'
              toast.error(errorMessage)
              reject(error)
            })
          }
        }
        else {
          reject(new Error('表单验证失败'))
        }
      })
    })
  },
})
</script>

<template>
  <div v-loading="loading">
    <ElForm ref="formRef" :model="form" :rules="formRules" label-width="120px" label-suffix="：">
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="用户名" prop="username">
            <ElInput v-model="form.username" placeholder="请输入用户名" :disabled="!!props.id" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="真实姓名" prop="realName">
            <ElInput v-model="form.realName" placeholder="请输入真实姓名" />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="邮箱" prop="email">
            <ElInput v-model="form.email" placeholder="请输入邮箱" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="手机号" prop="phone">
            <ElInput v-model="form.phone" placeholder="请输入手机号" />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="昵称">
            <ElInput v-model="form.nickname" placeholder="请输入昵称" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="性别">
            <ElSelect v-model="form.gender" placeholder="请选择性别">
              <ElOption v-for="item in genderOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="生日">
            <ElDatePicker v-model="form.birthday" type="date" placeholder="请选择生日" style="width: 100%;" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="状态">
            <ElSelect v-model="form.status" placeholder="请选择状态">
              <ElOption v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="用户类型">
            <ElSelect v-model="form.userType" placeholder="请选择用户类型">
              <ElOption v-for="item in userTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElDivider content-position="left">
        密码设置
      </ElDivider>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="密码" prop="password">
            <ElInput v-model="form.password" type="password" placeholder="请输入密码" show-password />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="确认密码" prop="confirmPassword">
            <ElInput v-model="form.confirmPassword" type="password" placeholder="请确认密码" show-password />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElAlert v-if="props.id" title="编辑用户时，如果不需要修改密码，请留空密码字段" type="info" show-icon :closable="false" />
    </ElForm>
  </div>
</template>

<style scoped>
/* 样式 */
</style>
