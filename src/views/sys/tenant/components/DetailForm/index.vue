<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import api from '@/api/modules/sys_tenant'
import { ElMessage } from 'element-plus'

export interface Props {
  id?: number | string
}
const props = withDefaults(
  defineProps<Props>(),
  {
    id: '',
  },
)

const loading = ref(false)
const formRef = useTemplateRef<FormInstance>('formRef')
const form = ref({
  id: props.id,
  tenantCode: '',
  tenantName: '',
  domain: '',
  status: 1,
  packageType: 'basic',
  expireTime: '',
  contactName: '',
  contactPhone: '',
  contactEmail: '',
  logoUrl: '',
  description: '',
})

const formRules = ref<FormRules>({
  tenantCode: [
    { required: true, message: '请输入租户编码', trigger: 'blur' },
    { min: 2, max: 64, message: '租户编码长度在 2 到 64 个字符', trigger: 'blur' },
    { pattern: /^[\w-]+$/, message: '租户编码只能包含字母、数字、下划线和横线', trigger: 'blur' },
    {
      validator: async (rule: any, value: string, callback: any) => {
        if (value) {
          try {
            const res = await api.checkTenantCode(value, props.id || undefined)
            if (res.data) {
              callback(new Error('租户编码已存在'))
            }
            else {
              callback()
            }
          }
          catch {
            callback()
          }
        }
        else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
  tenantName: [
    { required: true, message: '请输入租户名称', trigger: 'blur' },
    { min: 2, max: 128, message: '租户名称长度在 2 到 128 个字符', trigger: 'blur' },
    {
      validator: async (rule: any, value: string, callback: any) => {
        if (value) {
          try {
            const res = await api.checkTenantName(value, props.id || undefined)
            if (res.data) {
              callback(new Error('租户名称已存在'))
            }
            else {
              callback()
            }
          }
          catch {
            callback()
          }
        }
        else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
  domain: [
    { max: 128, message: '域名长度不能超过128个字符', trigger: 'blur' },
    { pattern: /^[a-z0-9.-]*$/i, message: '域名格式不正确', trigger: 'blur' },
    {
      validator: async (rule: any, value: string, callback: any) => {
        if (value) {
          try {
            const res = await api.checkDomain(value, props.id || undefined)
            if (res.data) {
              callback(new Error('域名已存在'))
            }
            else {
              callback()
            }
          }
          catch {
            callback()
          }
        }
        else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
  packageType: [
    { required: true, message: '请选择套餐类型', trigger: 'change' },
  ],
  contactPhone: [
    { pattern: /^[0-9-+()\\s]*$/, message: '联系人电话格式不正确', trigger: 'blur' },
    { max: 32, message: '联系人电话长度不能超过32个字符', trigger: 'blur' },
  ],
  contactEmail: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
    { max: 128, message: '联系人邮箱长度不能超过128个字符', trigger: 'blur' },
  ],
  contactName: [
    { max: 64, message: '联系人姓名长度不能超过64个字符', trigger: 'blur' },
  ],
  logoUrl: [
    { max: 512, message: 'Logo地址长度不能超过512个字符', trigger: 'blur' },
  ],
  description: [
    { max: 1000, message: '租户描述长度不能超过1000个字符', trigger: 'blur' },
  ],
})

// 状态选项
const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 },
  { label: '过期', value: 2 },
]

// 套餐类型选项
const packageTypeOptions = [
  { label: '基础版', value: 'basic' },
  { label: '标准版', value: 'standard' },
  { label: '专业版', value: 'premium' },
  { label: '企业版', value: 'enterprise' },
]

onMounted(() => {
  if (form.value.id !== '') {
    getInfo()
  }
})

function getInfo() {
  loading.value = true
  api.detail(form.value.id).then((res: any) => {
    loading.value = false
    const data = res.data
    Object.assign(form.value, {
      tenantCode: data.tenantCode,
      tenantName: data.tenantName,
      domain: data.domain,
      status: data.status,
      packageType: data.packageType,
      expireTime: data.expireTime,
      contactName: data.contactName,
      contactPhone: data.contactPhone,
      contactEmail: data.contactEmail,
      logoUrl: data.logoUrl,
      description: data.description,
    })
  })
}

defineExpose({
  submit() {
    return new Promise<void>((resolve) => {
      formRef.value?.validate((valid) => {
        if (valid) {
          const submitData = { ...form.value }

          if (form.value.id === '') {
            api.create(submitData).then(() => {
              ElMessage.success('新增租户成功')
              resolve()
            })
          }
          else {
            api.edit(submitData).then(() => {
              ElMessage.success('编辑租户成功')
              resolve()
            })
          }
        }
      })
    })
  },
})
</script>

<template>
  <div v-loading="loading">
    <ElForm ref="formRef" :model="form" :rules="formRules" label-width="120px" label-suffix="：">
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="租户编码" prop="tenantCode">
            <ElInput v-model="form.tenantCode" placeholder="请输入租户编码" :disabled="!!props.id" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="租户名称" prop="tenantName">
            <ElInput v-model="form.tenantName" placeholder="请输入租户名称" />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="域名" prop="domain">
            <ElInput v-model="form.domain" placeholder="请输入域名（可选）" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="状态" prop="status">
            <ElSelect v-model="form.status" placeholder="请选择状态">
              <ElOption v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="套餐类型" prop="packageType">
            <ElSelect v-model="form.packageType" placeholder="请选择套餐类型">
              <ElOption v-for="item in packageTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="到期时间">
            <ElDatePicker
              v-model="form.expireTime"
              type="datetime"
              placeholder="请选择到期时间（可选）"
              style="width: 100%;"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="联系人姓名" prop="contactName">
            <ElInput v-model="form.contactName" placeholder="请输入联系人姓名（可选）" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="联系人电话" prop="contactPhone">
            <ElInput v-model="form.contactPhone" placeholder="请输入联系人电话（可选）" />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="联系人邮箱" prop="contactEmail">
            <ElInput v-model="form.contactEmail" placeholder="请输入联系人邮箱（可选）" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="Logo地址" prop="logoUrl">
            <ElInput v-model="form.logoUrl" placeholder="请输入Logo地址（可选）" />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow>
        <ElCol :span="24">
          <ElFormItem label="租户描述" prop="description">
            <ElInput
              v-model="form.description"
              type="textarea"
              :rows="4"
              placeholder="请输入租户描述（可选）"
              maxlength="1000"
              show-word-limit
            />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
</template>

<style scoped>
/* 样式 */
</style>
