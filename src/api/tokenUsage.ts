/**
 * Token 用量查询 API 模块
 * 提供单个轮次查询和批量查询功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */

import { request } from '@/api'

/**
 * 轮次 Token 用量数据接口
 */
export interface RoundTokenUsage {
  roundId: string
  conversationId: string
  roundSequence: number
  inputTokens: number
  outputTokens: number
  totalTokens: number
  totalCost: string
  status: 'PENDING' | 'SUCCESS' | 'FAILED'
  calculatedAt?: string
}

/**
 * 批量查询请求项接口
 */
export interface RoundTokenUsageRequest {
  conversationId: string
  roundSequence: number
}

/**
 * Token 用量状态检查结果接口
 */
export interface TokenUsageStatusResult {
  isCompleted: boolean
  status: 'PENDING' | 'SUCCESS' | 'FAILED'
  data?: RoundTokenUsage
}

/**
 * 获取指定轮次的 Token 用量信息
 *
 * @param conversationId 对话ID
 * @param roundSequence 轮次序号
 * @returns Promise<RoundTokenUsage>
 */
export async function getRoundTokenUsage(
  conversationId: string,
  roundSequence: number,
): Promise<RoundTokenUsage> {
  const response = await request.get(
    `/api/conversations/${conversationId}/rounds/${roundSequence}/tokens`,
  )

  return response.data
}

/**
 * 批量获取多个轮次的 Token 用量信息
 *
 * @param requests 批量查询请求列表
 * @returns Promise<RoundTokenUsage[]>
 */
export async function getBatchRoundTokenUsage(
  requests: RoundTokenUsageRequest[],
): Promise<RoundTokenUsage[]> {
  const response = await request.post('/api/tokens/batch', {
    requests,
  })

  return response.data
}

/**
 * 检查 Token 用量计算状态
 * 用于轮询检查计算是否完成
 *
 * @param conversationId 对话ID
 * @param roundSequence 轮次序号
 * @returns Promise<TokenUsageStatusResult>
 */
export async function checkTokenUsageStatus(
  conversationId: string,
  roundSequence: number,
): Promise<TokenUsageStatusResult> {
  try {
    const tokenUsage = await getRoundTokenUsage(conversationId, roundSequence)

    return {
      isCompleted: tokenUsage.status !== 'PENDING',
      status: tokenUsage.status,
      data: tokenUsage.status === 'SUCCESS' ? tokenUsage : undefined,
    }
  }
  catch (error) {
    console.error('检查Token用量状态失败:', error)
    return {
      isCompleted: true,
      status: 'FAILED',
    }
  }
}

/**
 * 创建轮询器，定期检查 Token 用量状态
 *
 * @param conversationId 对话ID
 * @param roundSequence 轮次序号
 * @param onUpdate 状态更新回调
 * @param interval 轮询间隔（毫秒），默认5秒
 * @returns 返回停止轮询的函数
 */
export function createTokenUsagePoller(
  conversationId: string,
  roundSequence: number,
  onUpdate: (result: TokenUsageStatusResult) => void,
  interval: number = 5000,
): () => void {
  let timer: NodeJS.Timeout | null = null
  let retryCount = 0
  const maxRetries = 10

  // 先声明 stop 函数
  const stop = () => {
    if (timer) {
      clearInterval(timer)
      timer = null
    }
  }

  const poll = async () => {
    try {
      const result = await checkTokenUsageStatus(conversationId, roundSequence)
      onUpdate(result)

      if (result.isCompleted) {
        stop()
      }
      else {
        retryCount = 0 // 重置重试计数
      }
    }
    catch (error) {
      retryCount++
      console.error(`Token用量轮询失败 (${retryCount}/${maxRetries}):`, error)

      if (retryCount >= maxRetries) {
        console.error('Token用量轮询超过最大重试次数，停止轮询')
        onUpdate({
          isCompleted: true,
          status: 'FAILED',
        })
        stop()
      }
    }
  }

  const start = () => {
    if (timer) {
      clearInterval(timer)
    }
    timer = setInterval(poll, interval)
    // 立即执行一次
    poll()
  }

  // 自动开始轮询
  start()

  // 返回停止函数
  return stop
}
