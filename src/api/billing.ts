import type {
  ApiResponse,
  AppealRecord,
  AppealRecordQueryParams,
  AppealRequest,
  BalanceCheckResponse,
  BalanceWarningConfig,
  BillingAccount,
  BillingConfig,
  BillingStatistics,
  PagedResponse,
  PaymentMethod,
  RechargeRecord,
  RechargeRecordQueryParams,
  RechargeRequest,
  TokenCalculationRequest,
  TokenCalculationResponse,
  UsageRecord,
  UsageRecordQueryParams,
} from '@/types/billing'
import { request } from '@/api'

// 是否使用模拟数据 (开发环境)
const USE_MOCK_DATA = import.meta.env.DEV && import.meta.env.VITE_USE_MOCK_BILLING !== 'false'

// 模拟数据生成器
const mockData = {
  // 模拟账户数据
  account: (): BillingAccount => ({
    id: 'acc_1234567890',
    userId: 'user_test_001',
    balance: 158.75,
    currency: 'CNY',
    status: 'ACTIVE',
    createdAt: '2024-01-15T08:30:00Z',
    updatedAt: new Date().toISOString(),
  }),

  // 模拟计费配置 - 与后端 BillingConfigDto 对齐
  config: (): BillingConfig => ({
    userId: 1001,
    packageId: 1,
    packageName: '标准套餐',
    packageDescription: '适合个人用户的标准计费套餐',
    inputTokenPrice: 0.00003,
    outputTokenPrice: 0.00006,
    thoughtChainTokenPrice: 0.00004,
    totalBalance: 158.75,
    rechargedBalance: 150.00,
    giftBalance: 8.75,
    freeTokens: 10000,
    todayTokenUsage: 2500,
    dailyFreeTokens: 10000,
    isFrozen: false,
    isPackageActive: true,
    packageCreatedAt: '2024-01-10T00:00:00Z',
    lastUpdated: new Date().toISOString(),
    minRechargeAmount: 10.00,
  }),

  // 模拟余额警告配置
  balanceWarningConfig: (): BalanceWarningConfig => ({
    lowBalanceThreshold: 50.0,
    criticalBalanceThreshold: 10.0,
    enableNotifications: true,
  }),

  // 模拟Token计算 - 去掉 modelName 参数
  tokenCalculation: (request: TokenCalculationRequest): TokenCalculationResponse => {
    const inputTokens = request.inputTokens || 0
    const outputTokens = request.outputTokens || 0
    const thoughtChainTokens = request.thoughtChainTokens || 0

    // 使用固定价格计算费用
    const inputTokenPrice = 0.00003
    const outputTokenPrice = 0.00006
    const thoughtChainTokenPrice = 0.00004

    const inputCost = inputTokens * inputTokenPrice
    const outputCost = outputTokens * outputTokenPrice
    const thoughtChainCost = thoughtChainTokens * thoughtChainTokenPrice
    const totalCost = inputCost + outputCost + thoughtChainCost

    return {
      inputTokens,
      outputTokens,
      thoughtChainTokens,
      totalTokens: inputTokens + outputTokens + thoughtChainTokens,
      inputCost,
      outputCost,
      totalCost,
      currency: 'CNY',
      formattedCost: `¥${totalCost.toFixed(6)}`,
    }
  },

  // 模拟使用记录 - 去掉 modelName 字段
  usageRecords: (count = 20): UsageRecord[] => {
    const records: UsageRecord[] = []

    for (let i = 0; i < count; i++) {
      const inputTokens = Math.floor(Math.random() * 1000) + 100
      const outputTokens = Math.floor(Math.random() * 800) + 50
      const thoughtChainTokens = Math.floor(Math.random() * 200)

      const inputTokenPrice = 0.00003
      const outputTokenPrice = 0.00006

      const inputCost = inputTokens * inputTokenPrice
      const outputCost = outputTokens * outputTokenPrice
      const totalCost = inputCost + outputCost

      records.push({
        id: Date.now() + i,
        messageId: `msg_${Date.now()}_${i}`,
        conversationId: `conv_${Math.floor(Math.random() * 100)}`,
        inputTokens,
        outputTokens,
        thoughtChainTokens,
        totalTokens: inputTokens + outputTokens + thoughtChainTokens,
        inputCost,
        outputCost,
        totalCost,
        status: 'SUCCESS',
        billingStatus: 'BILLED',
        packageId: 1,
        inputTokenPrice,
        outputTokenPrice,
        isAppealed: false,
        appealStatus: '',
        requestTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
        responseTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
        durationMs: Math.floor(Math.random() * 5000) + 500,
        createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
      })
    }

    return records.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
  },

  // 模拟充值记录
  rechargeRecords: (count = 10): RechargeRecord[] => {
    const records: RechargeRecord[] = []
    const paymentMethods = ['ALIPAY', 'WECHAT', 'BANK_CARD'] as const
    const statuses = ['SUCCESS', 'SUCCESS', 'SUCCESS', 'PENDING', 'FAILED'] as const

    for (let i = 0; i < count; i++) {
      const amount = [50, 100, 200, 500][Math.floor(Math.random() * 4)]
      const paymentMethod = paymentMethods[Math.floor(Math.random() * paymentMethods.length)]
      const status = statuses[Math.floor(Math.random() * statuses.length)]

      records.push({
        id: `recharge_${Date.now()}_${i}`,
        userId: 'user_test_001',
        amount,
        currency: 'CNY',
        paymentMethod,
        paymentId: status === 'SUCCESS' ? `pay_${Date.now()}_${i}` : undefined,
        status,
        description: `${paymentMethod}充值${amount}元`,
        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString(),
      })
    }

    return records.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
  },

  // 模拟申诉记录
  appealRecords: (count = 5): AppealRecord[] => {
    const records: AppealRecord[] = []
    const types = ['BILLING_ERROR', 'SERVICE_ISSUE', 'REFUND_REQUEST', 'OTHER'] as const
    const statuses = ['PENDING', 'IN_REVIEW', 'APPROVED', 'REJECTED', 'RESOLVED'] as const
    const usageRecords = mockData.usageRecords(50)

    for (let i = 0; i < count; i++) {
      const type = types[Math.floor(Math.random() * types.length)]
      const status = statuses[Math.floor(Math.random() * statuses.length)]
      const usageRecord = usageRecords[Math.floor(Math.random() * usageRecords.length)]

      records.push({
        id: `appeal_${Date.now()}_${i}`,
        userId: 'user_test_001',
        usageRecordId: usageRecord.id.toString(),
        type,
        reason: `${type}问题`,
        description: `关于使用记录的${type}申诉`,
        status,
        reviewNote: status === 'RESOLVED' ? '已处理完成' : undefined,
        refundAmount: status === 'APPROVED' ? usageRecord.totalCost : undefined,
        createdAt: new Date(Date.now() - Math.random() * 15 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString(),
        resolvedAt: status === 'RESOLVED' ? new Date().toISOString() : undefined,
      })
    }

    return records.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
  },

  // 模拟统计数据 - 去掉 modelDistribution
  statistics: (): BillingStatistics => {
    const usageRecords = mockData.usageRecords(100)

    const totalCost = usageRecords.reduce((sum, record) => sum + record.totalCost, 0)
    const totalTokens = usageRecords.reduce((sum, record) => sum + record.totalTokens, 0)
    const totalInputTokens = usageRecords.reduce((sum, record) => sum + record.inputTokens, 0)
    const totalOutputTokens = usageRecords.reduce((sum, record) => sum + record.outputTokens, 0)
    const totalThoughtChainTokens = usageRecords.reduce((sum, record) => sum + record.thoughtChainTokens, 0)

    const conversationCount = new Set(usageRecords.map(r => r.conversationId)).size
    const successfulConversationCount = usageRecords.filter(r => r.status === 'SUCCESS').length
    const failedConversationCount = usageRecords.filter(r => r.status === 'FAILED').length

    // 生成日度趋势数据
    const dailyTrend = []
    for (let i = 6; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      const dateStr = date.toISOString().split('T')[0]

      const dayRecords = usageRecords.filter(r => r.createdAt.startsWith(dateStr))
      const dayTokens = dayRecords.reduce((sum, r) => sum + r.totalTokens, 0)
      const dayCost = dayRecords.reduce((sum, r) => sum + r.totalCost, 0)
      const dayConversations = new Set(dayRecords.map(r => r.conversationId)).size

      dailyTrend.push({
        date: dateStr,
        tokens: dayTokens,
        cost: dayCost,
        conversations: dayConversations,
      })
    }

    return {
      period: 'week',
      startTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      endTime: new Date().toISOString(),
      totalTokens,
      totalInputTokens,
      totalOutputTokens,
      totalThoughtChainTokens,
      totalCost,
      totalInputCost: usageRecords.reduce((sum, record) => sum + record.inputCost, 0),
      totalOutputCost: usageRecords.reduce((sum, record) => sum + record.outputCost, 0),
      conversationCount,
      successfulConversationCount,
      failedConversationCount,
      averageTokensPerConversation: conversationCount > 0 ? totalTokens / conversationCount : 0,
      averageCostPerConversation: conversationCount > 0 ? totalCost / conversationCount : 0,
      maxTokensPerConversation: Math.max(...usageRecords.map(r => r.totalTokens)),
      maxCostPerConversation: Math.max(...usageRecords.map(r => r.totalCost)),
      dailyTrend,
    }
  },
}

// 模拟延迟
const mockDelay = (ms = 300) => new Promise(resolve => setTimeout(resolve, ms))

/**
 * 计费 API 类 - 与后端 BillingController 接口路径对齐
 */
export class BillingAPI {
  /**
   * 获取用户计费账户信息
   */
  static async getAccount(): Promise<ApiResponse<BillingAccount>> {
    if (USE_MOCK_DATA) {
      await mockDelay()
      return {
        success: true,
        data: mockData.account(),
        message: '获取账户信息成功',
      }
    }
    return request.get('/api/billing/account')
  }

  /**
   * 获取计费配置信息 - 对应后端 /api/billing/configuration
   */
  static async getConfiguration(): Promise<ApiResponse<BillingConfig>> {
    if (USE_MOCK_DATA) {
      await mockDelay()
      return {
        success: true,
        data: mockData.config(),
        message: '获取计费配置成功',
      }
    }
    return request.get('/api/billing/configuration')
  }

  /**
   * 计算Token费用 - 对应后端 /api/billing/calculate-cost
   * @param data Token计算请求数据
   */
  static async calculateTokenCost(data: TokenCalculationRequest): Promise<ApiResponse<TokenCalculationResponse>> {
    if (USE_MOCK_DATA) {
      await mockDelay(100) // 更快的响应用于实时计算
      return {
        success: true,
        data: mockData.tokenCalculation(data),
        message: 'Token费用计算成功',
      }
    }
    return request.post('/api/billing/calculate-cost', data)
  }

  /**
   * 获取使用记录列表 - 对应后端 /api/billing/usage
   * @param params 查询参数
   */
  static async getUsageRecords(params?: UsageRecordQueryParams): Promise<ApiResponse<PagedResponse<UsageRecord>>> {
    if (USE_MOCK_DATA) {
      await mockDelay()
      const records = mockData.usageRecords(50)
      const page = (params?.page || 0)
      const size = (params?.size || 20)
      const start = page * size
      const end = start + size
      const content = records.slice(start, end)

      return {
        success: true,
        data: {
          content,
          totalElements: records.length,
          totalPages: Math.ceil(records.length / size),
          number: page,
          size,
          first: page === 0,
          last: end >= records.length,
        },
        message: '获取使用记录成功',
      }
    }
    return request.get('/api/billing/usage', { params })
  }

  /**
   * 获取指定使用记录详情 - 对应后端 /api/billing/usage/{id}
   * @param id 使用记录ID
   */
  static async getUsageRecord(id: string): Promise<ApiResponse<UsageRecord>> {
    if (USE_MOCK_DATA) {
      await mockDelay()
      const records = mockData.usageRecords(50)
      const record = records.find(r => r.id.toString() === id)
      if (record) {
        return {
          success: true,
          data: record,
          message: '获取使用记录详情成功',
        }
      }
      else {
        throw new Error('使用记录不存在')
      }
    }
    return request.get(`/api/billing/usage/${id}`)
  }

  /**
   * 获取使用统计数据 - 对应后端 /api/billing/usage/statistics
   * @param period 统计周期
   */
  static async getUsageStatistics(period = 'month'): Promise<ApiResponse<BillingStatistics>> {
    if (USE_MOCK_DATA) {
      await mockDelay()
      const statistics = mockData.statistics()
      statistics.period = period
      return {
        success: true,
        data: statistics,
        message: '获取统计数据成功',
      }
    }
    return request.get('/api/billing/usage/statistics', { params: { period } })
  }

  /**
   * 预检查计费 - 对应后端 /api/billing/precheck
   * @param inputTokens 输入Token数量
   * @param outputTokens 输出Token数量
   */
  static async precheckBilling(inputTokens: number, outputTokens: number): Promise<ApiResponse<boolean>> {
    if (USE_MOCK_DATA) {
      await mockDelay(100)
      const account = mockData.account()
      const config = mockData.config()
      const estimatedCost = (inputTokens * config.inputTokenPrice) + (outputTokens * config.outputTokenPrice)
      const sufficient = account.balance >= estimatedCost

      return {
        success: true,
        data: sufficient,
        message: sufficient ? '余额充足' : '余额不足',
      }
    }
    return request.get('/api/billing/precheck', { params: { inputTokens, outputTokens } })
  }

  /**
   * 获取充值记录列表
   * @param params 查询参数
   */
  static async getRechargeRecords(params?: RechargeRecordQueryParams): Promise<ApiResponse<PagedResponse<RechargeRecord>>> {
    if (USE_MOCK_DATA) {
      await mockDelay()
      const records = mockData.rechargeRecords(20)
      const page = (params?.page || 0)
      const size = (params?.size || 20)
      const start = page * size
      const end = start + size
      const content = records.slice(start, end)

      return {
        success: true,
        data: {
          content,
          totalElements: records.length,
          totalPages: Math.ceil(records.length / size),
          number: page,
          size,
          first: page === 0,
          last: end >= records.length,
        },
        message: '获取充值记录成功',
      }
    }
    return request.get('/api/billing/recharge-records', { params })
  }

  /**
   * 获取指定充值记录详情
   * @param id 充值记录ID
   */
  static async getRechargeRecord(id: string): Promise<ApiResponse<RechargeRecord>> {
    return request.get(`/api/billing/recharge-records/${id}`)
  }

  /**
   * 提交充值申请
   * @param data 充值请求数据
   */
  static async submitRecharge(data: RechargeRequest): Promise<ApiResponse<RechargeRecord>> {
    return request.post('/api/billing/recharge', data)
  }

  /**
   * 获取申诉记录列表
   * @param params 查询参数
   */
  static async getAppealRecords(params?: AppealRecordQueryParams): Promise<ApiResponse<PagedResponse<AppealRecord>>> {
    if (USE_MOCK_DATA) {
      await mockDelay()
      const records = mockData.appealRecords(10)
      const page = (params?.page || 0)
      const size = (params?.size || 20)
      const start = page * size
      const end = start + size
      const content = records.slice(start, end)

      return {
        success: true,
        data: {
          content,
          totalElements: records.length,
          totalPages: Math.ceil(records.length / size),
          number: page,
          size,
          first: page === 0,
          last: end >= records.length,
        },
        message: '获取申诉记录成功',
      }
    }
    return request.get('/api/billing/appeals', { params })
  }

  /**
   * 获取指定申诉记录详情
   * @param id 申诉记录ID
   */
  static async getAppealRecord(id: string): Promise<ApiResponse<AppealRecord>> {
    return request.get(`/api/billing/appeals/${id}`)
  }

  /**
   * 提交申诉
   * @param data 申诉请求数据
   */
  static async submitAppeal(data: AppealRequest): Promise<ApiResponse<AppealRecord>> {
    const formData = new FormData()
    formData.append('usageRecordId', data.usageRecordId)
    formData.append('type', data.type)
    formData.append('reason', data.reason)
    formData.append('description', data.description)

    // 处理附件
    if (data.attachments && data.attachments.length > 0) {
      data.attachments.forEach((file, index) => {
        formData.append(`attachments[${index}]`, file)
      })
    }

    return request.post('/api/billing/appeals', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }

  /**
   * 更新申诉记录
   * @param id 申诉记录ID
   * @param data 更新数据
   */
  static async updateAppeal(id: string, data: Partial<AppealRequest>): Promise<ApiResponse<AppealRecord>> {
    return request.put(`/api/billing/appeals/${id}`, data)
  }

  /**
   * 获取余额警告配置
   */
  static async getBalanceWarningConfig(): Promise<ApiResponse<BalanceWarningConfig>> {
    if (USE_MOCK_DATA) {
      await mockDelay()
      return {
        success: true,
        data: mockData.balanceWarningConfig(),
        message: '获取余额警告配置成功',
      }
    }
    return request.get('/api/billing/balance-warning-config')
  }

  /**
   * 更新余额警告配置
   * @param config 警告配置
   */
  static async updateBalanceWarningConfig(config: BalanceWarningConfig): Promise<ApiResponse<BalanceWarningConfig>> {
    return request.put('/api/billing/balance-warning-config', config)
  }

  /**
   * 刷新账户余额
   */
  static async refreshBalance(): Promise<ApiResponse<BillingAccount>> {
    if (USE_MOCK_DATA) {
      await mockDelay()
      // 模拟余额刷新，随机小幅变动
      const account = mockData.account()
      account.balance += Math.random() * 2 - 1 // 随机增减1元以内
      account.balance = Math.max(0, Number(account.balance.toFixed(2)))
      return {
        success: true,
        data: account,
        message: '余额刷新成功',
      }
    }
    return request.post('/api/billing/account/refresh')
  }

  /**
   * 导出使用记录
   * @param params 查询参数
   * @param format 导出格式 (excel, csv)
   */
  static async exportUsageRecords(params?: UsageRecordQueryParams, format: 'excel' | 'csv' = 'excel'): Promise<Blob> {
    const response = await request.get('/api/billing/usage-records/export', {
      params: { ...params, format },
      responseType: 'blob',
    })
    return response.data
  }

  /**
   * 导出充值记录
   * @param params 查询参数
   * @param format 导出格式 (excel, csv)
   */
  static async exportRechargeRecords(params?: RechargeRecordQueryParams, format: 'excel' | 'csv' = 'excel'): Promise<Blob> {
    const response = await request.get('/api/billing/recharge-records/export', {
      params: { ...params, format },
      responseType: 'blob',
    })
    return response.data
  }

  /**
   * 获取支付方式列表
   */
  static async getPaymentMethods(): Promise<ApiResponse<PaymentMethod[]>> {
    if (USE_MOCK_DATA) {
      await mockDelay()
      return {
        success: true,
        data: [
          {
            code: 'ALIPAY',
            name: '支付宝',
            enabled: true,
            icon: 'alipay',
            description: '使用支付宝支付',
          },
          {
            code: 'WECHAT',
            name: '微信支付',
            enabled: true,
            icon: 'wechat',
            description: '使用微信支付',
          },
          {
            code: 'BANK_CARD',
            name: '银行卡',
            enabled: false,
            icon: 'bank',
            description: '银行卡支付（暂不可用）',
          },
        ],
        message: '获取支付方式成功',
      }
    }
    return request.get('/api/billing/payment-methods')
  }

  /**
   * 验证支付状态
   * @param rechargeId 充值记录ID
   */
  static async verifyPaymentStatus(rechargeId: string): Promise<ApiResponse<RechargeRecord>> {
    return request.post(`/api/billing/recharge/${rechargeId}/verify`)
  }

  /**
   * 取消充值申请
   * @param rechargeId 充值记录ID
   */
  static async cancelRecharge(rechargeId: string): Promise<ApiResponse<RechargeRecord>> {
    return request.post(`/api/billing/recharge/${rechargeId}/cancel`)
  }

  /**
   * 检查余额是否充足
   * @param amount 需要的金额
   */
  static async checkBalance(amount: number): Promise<ApiResponse<BalanceCheckResponse>> {
    if (USE_MOCK_DATA) {
      await mockDelay()
      const account = mockData.account()
      const sufficient = account.balance >= amount
      const shortage = sufficient ? 0 : amount - account.balance

      return {
        success: true,
        data: {
          sufficient,
          current: account.balance,
          required: amount,
          shortage,
        },
        message: sufficient ? '余额充足' : '余额不足',
      }
    }
    return request.post('/api/billing/check-balance', { amount })
  }

  /**
   * 获取预设充值金额选项
   */
  static async getRechargePresets(): Promise<ApiResponse<number[]>> {
    return request.get('/api/billing/recharge-presets')
  }

  /**
   * 取消申诉
   * @param appealId 申诉记录ID
   */
  static async cancelAppeal(appealId: string): Promise<ApiResponse<AppealRecord>> {
    return request.post(`/api/billing/appeals/${appealId}/cancel`)
  }

  /**
   * 下载申诉附件
   * @param attachmentId 附件ID
   */
  static async downloadAttachment(attachmentId: string): Promise<ApiResponse<Blob>> {
    const response = await request.get(`/api/billing/appeals/attachments/${attachmentId}/download`, {
      responseType: 'blob',
    })
    return {
      success: true,
      data: response.data,
      message: '下载成功',
    }
  }

  /**
   * 下载使用记录收据
   * @param recordId 使用记录ID
   */
  static async downloadReceipt(recordId: string): Promise<ApiResponse<Blob>> {
    const response = await request.get(`/api/billing/usage-records/${recordId}/receipt`, {
      responseType: 'blob',
    })
    return {
      success: true,
      data: response.data,
      message: '下载成功',
    }
  }
}

export default BillingAPI
