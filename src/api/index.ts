import useSettingsStore from '@/store/modules/settings'
import useUserStore from '@/store/modules/user'
import axios from 'axios'
// import qs from 'qs'
import { toast } from 'vue-sonner'

const api = axios.create({
  baseURL: (import.meta.env.DEV && import.meta.env.VITE_OPEN_PROXY) ? '/proxy/' : import.meta.env.VITE_APP_API_BASEURL,
  timeout: 1000 * 60,
  responseType: 'json',
})

api.interceptors.request.use(
  (request) => {
    // 全局拦截请求发送前提交的参数
    const settingsStore = useSettingsStore()
    const userStore = useUserStore()

    // 设置请求头
    if (request.headers) {
      request.headers['Accept-Language'] = settingsStore.lang
      // 只使用JWT认证，移除Basic认证
      if (userStore.isLogin) {
        request.headers.Authorization = `Bearer ${userStore.token}`
      }
      // 对于未登录的请求（如登录、注册），不设置Authorization头
      // 这些接口在后端SecurityConfig中已配置为permitAll
    }
    // 是否将 POST 请求参数进行字符串化处理
    if (request.method === 'post') {
      // request.data = qs.stringify(request.data, {
      //   arrayFormat: 'brackets',
      // })
    }
    return request
  },
)

api.interceptors.response.use(
  (response) => {
    /**
     * 全局拦截请求发送后返回的数据，如果数据有报错则在这做全局的错误提示
     * 后端返回数据格式为：{ status: number, message: string, data: any, error: string }
     * 规则是当 status 为 1 时表示请求成功，为 0 时表示失败或需要重新登录
     */
    // 兼容处理：如果响应数据是数组或不包含status字段的对象，直接返回数据
    if (Array.isArray(response.data) || !('status' in response.data)) {
      return Promise.resolve(response.data)
    }

    if (response.data.status === 1) {
      return Promise.resolve(response.data)
    }
    else if (response.data.status === 0) {
      // status为0表示失败或需要登录
      if (response.data.error && response.data.error.includes('认证')) {
        useUserStore().requestLogout()
      }
      // 对于业务错误（status=0），不在拦截器中显示toast
      // 让具体的组件来处理错误显示，避免重复提示
      return Promise.reject(response.data)
    }
    else {
      // 其他错误状态
      if (response.data.message || response.data.error) {
        toast.error('Error', {
          description: response.data.message || response.data.error,
        })
      }
      return Promise.reject(response.data)
    }
  },
  (error) => {
    if (error.status === 401) {
      useUserStore().requestLogout()
      throw error
    }

    // 检查是否是HTTP 400错误且包含业务错误信息
    if (error.response && error.response.status === 400 && error.response.data) {
      const responseData = error.response.data
      // 如果响应数据包含我们的业务错误格式，则按业务错误处理
      if (responseData.status === 0 && (responseData.error || responseData.message)) {
        // 对于业务错误，不在拦截器中显示toast，让组件处理
        return Promise.reject(responseData)
      }
    }

    // 检查是否是HTTP 429错误（重复请求）
    if (error.response && error.response.status === 429 && error.response.data) {
      const responseData = error.response.data
      // 如果响应数据包含我们的业务错误格式
      if (responseData.status === 0 && responseData.data && responseData.data.list) {
        // 提取ChatResponse中的具体错误消息
        const chatResponse = responseData.data
        const errorMessage = chatResponse.list && chatResponse.list.length > 0
          ? chatResponse.list[0].content
          : (responseData.error || '请求过于频繁，请稍后再试')

        toast.error('请求限制', {
          description: errorMessage,
        })
        return Promise.reject(responseData)
      }
      // 如果是其他格式的429错误
      else if (responseData.error || responseData.message) {
        toast.error('请求限制', {
          description: responseData.error || responseData.message,
        })
        return Promise.reject(responseData)
      }
    }

    let message = error.message
    if (message === 'Network Error') {
      message = '后端网络故障'
    }
    else if (message.includes('timeout')) {
      message = '接口请求超时'
    }
    else if (message.includes('Request failed with status code')) {
      message = `接口${message.substr(message.length - 3)}异常`
    }
    toast.error('Error', {
      description: message,
    })
    return Promise.reject(error)
  },
)

// 导出 request 作为 api 的别名，用于兼容其他模块的导入
export const request = api

export default api
