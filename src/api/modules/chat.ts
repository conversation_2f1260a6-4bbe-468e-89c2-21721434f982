import { request } from '@/api'
// import { fillTemplate } from '@/utils/templateUtils'

// API 基础路径
const API_BASE_URL = '/api'

/**
 * 发送聊天消息（文本或基于模板）到后端
 * 注意：此接口需要用户登录，会自动使用JWT Bearer Token认证
 *
 * @param message 用户文本消息（使用模板时为null）
 * @param templateId 正在使用的模板ID（文本消息时为null）
 * @param slots 模板槽位的键值对（文本消息时为null）
 * @param conversationId 当前会话ID（新会话时为null）
 * @returns 返回后端响应对象的Promise
 */
export async function sendChatMessage(
  message: string | null,
  templateId: string | null = null,
  slots: Record<string, any> | null = null,
  conversationId: string | null = null,
) {
  const requestId = Math.random().toString(36).substring(2, 8)

  // 添加日志，跟踪原始槽位数据
  console.warn('=== API sendChatMessage 开始 ===')
  console.warn(`[${requestId}] api/modules/chat.ts - sendChatMessage:`)
  console.warn(`[${requestId}] - message:`, message)
  console.warn(`[${requestId}] - templateId:`, templateId)
  console.warn(`[${requestId}] - slots:`, JSON.stringify(slots))
  console.warn(`[${requestId}] - conversationId:`, conversationId)

  // 准备发送的数据
  const data: Record<string, any> = {}

  // 如果有message参数，将其添加到请求数据中
  if (message) {
    data.message = message
  }

  // 如果有templateId参数，将其添加到请求数据中
  if (templateId) {
    data.templateId = templateId

    // 确保slots字段不为null，如果是null则使用空对象
    data.slots = slots || {}

    // 如果此时message为null，尝试获取模板内容并填充，作为message字段
    if (!message) {
      try {
        // 如果有模板内容通过其他方式获取，可在此处进行填充
        // 此API模块不再负责获取模板内容，仅处理传入的参数
      }
      catch (error) {
        console.error('获取或填充模板内容失败:', error)
      }
    }
  }

  // 如果有conversationId参数，将其添加到请求数据中
  if (conversationId) {
    data.conversationId = conversationId
  }

  console.warn(`[${requestId}] 最终请求数据:`, JSON.stringify(data))

  // 发送请求到后端API（自动使用JWT认证）
  try {
    console.warn(`[${requestId}] 开始发送HTTP请求到: ${API_BASE_URL}/chat`)
    const response = await request({
      url: `${API_BASE_URL}/chat`,
      method: 'post',
      data,
    })

    console.warn(`[${requestId}] API响应成功:`, response)
    // 返回API响应
    return response
  }
  catch (error: any) {
    console.warn(`❌ [${requestId}] 发送聊天消息失败`, error)

    // 添加详细的错误信息
    if (error.response) {
      console.warn(`📋 [${requestId}] 错误详情:`)
      console.warn(`[${requestId}] - 状态码: ${error.response.status}`)
      console.warn(`[${requestId}] - 响应头: ${JSON.stringify(error.response.headers)}`)
      console.warn(`[${requestId}] - 响应数据: ${JSON.stringify(error.response.data)}`)
    }

    throw error
  }
  finally {
    console.warn(`=== [${requestId}] API sendChatMessage 结束 ===`)
  }
}
