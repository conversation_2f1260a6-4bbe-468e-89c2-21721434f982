import api from '../index'

const API_BASE_URL = '/api'

// 菜单资源创建DTO
export interface MenuResourceCreateDto {
  menuCode: string
  menuName: string
  menuType: string
  parentId?: number
  level?: number
  path?: string
  component?: string
  icon?: string
  sortOrder?: number
  visible: number
  status: number
  externalLink?: number
  cache?: number
  description?: string
}

// 菜单资源更新DTO
export interface MenuResourceUpdateDto {
  menuCode?: string
  menuName?: string
  menuType?: string
  parentId?: number
  level?: number
  path?: string
  component?: string
  icon?: string
  sortOrder?: number
  visible?: number
  status?: number
  externalLink?: number
  cache?: number
  description?: string
}

// 菜单资源查询DTO
export interface MenuResourceQueryDto {
  page?: number
  size?: number
  menuCode?: string
  menuName?: string
  menuType?: string
  parentId?: number
  level?: number
  visible?: number
  status?: number
  externalLink?: number
  sortField?: string
  sortOrder?: string
}

// 菜单资源响应DTO
export interface MenuResourceResponseDto {
  id: number
  tenantId?: number
  menuCode: string
  menuName: string
  menuType: string
  menuTypeText: string
  parentId?: number
  parentName?: string
  level: number
  path?: string
  component?: string
  icon?: string
  sortOrder: number
  visible: number
  visibleText: string
  status: number
  statusText: string
  externalLink: number
  externalLinkText: string
  cache: number
  cacheText: string
  description?: string
  createdTime: string
  updatedTime: string
  createdBy?: number
  updatedBy?: number
  children?: MenuResourceResponseDto[]
  hasChildren?: boolean
}

// 菜单资源管理API
export default {
  // 分页查询菜单资源列表
  getMenuResourceList: (data: MenuResourceQueryDto) => api.get(`${API_BASE_URL}/sys/menu-resource/list`, {
    params: data,
  }),

  // 获取菜单资源详情
  getMenuResourceDetail: (id: number | string) => api.get(`${API_BASE_URL}/sys/menu-resource/detail`, {
    params: { id },
  }),

  // 创建菜单资源
  createMenuResource: (data: MenuResourceCreateDto) => api.post(`${API_BASE_URL}/sys/menu-resource/create`, data),

  // 更新菜单资源
  updateMenuResource: (data: MenuResourceUpdateDto & { id: number | string }) => api.post(`${API_BASE_URL}/sys/menu-resource/update`, data, {
    params: { id: data.id },
  }),

  // 删除菜单资源
  deleteMenuResource: (id: number | string) => api.post(`${API_BASE_URL}/sys/menu-resource/delete`, null, {
    params: { id },
  }),

  // 批量删除菜单资源
  deleteMenuResources: (ids: (number | string)[]) => api.post(`${API_BASE_URL}/sys/menu-resource/batch-delete`, ids),

  // 更新菜单资源状态
  updateMenuResourceStatus: (id: number | string, status: number) => api.post(`${API_BASE_URL}/sys/menu-resource/update-status`, null, {
    params: { id, status },
  }),

  // 检查菜单编码是否存在
  checkMenuCode: (menuCode: string, excludeId?: number | string) => api.get(`${API_BASE_URL}/sys/menu-resource/check-menu-code`, {
    params: { menuCode, excludeId },
  }),

  // 获取所有启用的菜单资源
  getActiveMenuResources: () => api.get(`${API_BASE_URL}/sys/menu-resource/active`),

  // 获取菜单树形结构
  getMenuResourceTree: () => api.get(`${API_BASE_URL}/sys/menu-resource/tree`),

  // 根据菜单类型获取菜单列表
  getMenuResourcesByType: (menuType: string) => api.get(`${API_BASE_URL}/sys/menu-resource/by-type`, {
    params: { menuType },
  }),
}
