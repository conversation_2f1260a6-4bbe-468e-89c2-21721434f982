import api from '../index'

// 认证相关接口类型定义
export interface LoginRequest {
  loginId: string
  password: string
}

export interface LoginResponse {
  accessToken: string
  refreshToken: string
  userId: number
  username: string
  realName: string
  avatar: string
  tenantId: number
  tenantName: string
}

export interface RegisterRequest {
  tenantName: string
  username: string
  password: string
  email?: string
  phone?: string
  realName?: string
}

export interface ResetPasswordRequest {
  tenantName?: string
  loginId: string
  newPassword: string
  verificationCode?: string
}

export default {
  // 用户登录
  login: (data: LoginRequest) => api.post<{
    status: number
    message: string
    data: LoginResponse
  }>('/api/auth/login', data),

  // 用户注册
  register: (data: RegisterRequest) => api.post<{
    status: number
    message: string
    data: null
  }>('/api/auth/register', data),

  // 重置密码
  resetPassword: (data: ResetPasswordRequest) => api.post<{
    status: number
    message: string
    data: null
  }>('/api/auth/reset-password', data),

  // 刷新令牌
  refreshToken: (refreshToken: string) => api.post<{
    status: number
    message: string
    data: LoginResponse
  }>('/api/auth/refresh', {}, {
    headers: {
      Authorization: `Bearer ${refreshToken}`,
    },
  }),

  // 发送验证码（用于忘记密码）
  sendVerificationCode: (data: {
    tenantName?: string
    loginId: string
    type: 'email' | 'sms'
  }) => api.post<{
    status: number
    message: string
    data: null
  }>('/api/auth/send-verification-code', data),
}
