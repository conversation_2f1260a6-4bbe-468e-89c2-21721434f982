import api from '../index'

// API 基础路径
const API_BASE_URL = '/api'

export default {
  // 分页查询角色列表
  list: (data: {
    tenantId?: number
    roleCode?: string
    roleName?: string
    roleType?: string
    parentId?: number
    status?: string | number
    page: number
    size: number
  }) => api.get(`${API_BASE_URL}/sys/role/list`, {
    params: data,
  }),

  // 获取角色详情
  detail: (id: number | string) => api.get(`${API_BASE_URL}/sys/role/detail`, {
    params: {
      id,
    },
  }),

  // 创建角色
  create: (data: {
    tenantId: number
    roleCode: string
    roleName: string
    roleType?: string
    parentId?: number
    level?: number
    sortOrder?: number
    status: number
    description?: string
  }) => api.post(`${API_BASE_URL}/sys/role/create`, data),

  // 更新角色
  edit: (id: number | string, data: {
    roleCode?: string
    roleName?: string
    roleType?: string
    parentId?: number
    level?: number
    sortOrder?: number
    status?: number
    description?: string
  }) => api.post(`${API_BASE_URL}/sys/role/edit`, data, {
    params: {
      id,
    },
  }),

  // 删除角色
  delete: (id: number | string) => api.post(`${API_BASE_URL}/sys/role/delete`, null, {
    params: {
      id,
    },
  }),

  // 批量删除角色
  batchDelete: (ids: (number | string)[]) => api.post(`${API_BASE_URL}/sys/role/batch-delete`, ids),

  // 更新角色状态
  updateStatus: (id: number | string, status: number) => api.post(`${API_BASE_URL}/sys/role/update-status`, null, {
    params: {
      id,
      status,
    },
  }),

  // 检查角色编码是否存在
  checkRoleCode: (tenantId: number, roleCode: string, excludeId?: number | string) => api.get(`${API_BASE_URL}/sys/role/check-role-code`, {
    params: {
      tenantId,
      roleCode,
      excludeId,
    },
  }),

  // 检查角色名称是否存在
  checkRoleName: (tenantId: number, roleName: string, excludeId?: number | string) => api.get(`${API_BASE_URL}/sys/role/check-role-name`, {
    params: {
      tenantId,
      roleName,
      excludeId,
    },
  }),

  // 根据租户ID获取所有角色
  getAllRoles: (tenantId: number) => api.get(`${API_BASE_URL}/sys/role/all`, {
    params: {
      tenantId,
    },
  }),
}
