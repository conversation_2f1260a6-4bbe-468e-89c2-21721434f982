import {request} from "@/api";

export async function getRhVisual(
  mapId: string,
  shotId: string,
  viewPointId: string,
  nodeId: string,
  visualId: string
) {
  try {
    let url = `/api/route/itgvk/rh/visualJson?mapId=${mapId}&shotId=${shotId}&viewPointId=${viewPointId}&nodeId=${nodeId}&visualId=${visualId}`;
    let response = await request({
      url: url,
      method: 'get'
    });
    return response.data
  } catch (error: any) {
    console.error(`getRhVisual error: `, error)
  }
}

export async function getGptVisual(
  stencilId: string,
  shotId: string,
  vertexId: string,
  indexId: string,
  visualStencilId: string,
  dataPeriod: string
) {
  try {
    let url = `/api/route/sp/gpt/findVisualJson?stencilId=${stencilId}&shotId=${shotId}&vertexId=${vertexId}&indexId=${indexId}&visualStencilId=${visualStencilId}&dataPeriod=${dataPeriod}`;
    let response = await request({
      url: url,
      method: 'get'
    });
    return response.data
  } catch (error: any) {
    console.error(`getGptVisual error: `, error)
  }
}
