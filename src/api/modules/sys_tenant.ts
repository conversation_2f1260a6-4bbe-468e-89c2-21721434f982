import api from '../index'

const API_BASE_URL = '/api'

// 租户相关类型定义
export interface TenantCreateDto {
  tenantCode: string
  tenantName: string
  domain?: string
  status: number
  packageType: string
  expireTime?: string
  contactName?: string
  contactPhone?: string
  contactEmail?: string
  logoUrl?: string
  description?: string
}

export interface TenantUpdateDto {
  tenantCode?: string
  tenantName?: string
  domain?: string
  status?: number
  packageType?: string
  expireTime?: string
  contactName?: string
  contactPhone?: string
  contactEmail?: string
  logoUrl?: string
  description?: string
}

export interface TenantQueryDto {
  page?: number
  size?: number
  tenantCode?: string
  tenantName?: string
  status?: number
  packageType?: string
  contactName?: string
  contactPhone?: string
  contactEmail?: string
  sortField?: string
  sortOrder?: string
}

export interface TenantResponseDto {
  id: number
  tenantCode: string
  tenantName: string
  domain?: string
  status: number
  statusText: string
  packageType: string
  packageTypeText: string
  expireTime?: string
  contactName?: string
  contactPhone?: string
  contactEmail?: string
  logoUrl?: string
  description?: string
  createdTime: string
  updatedTime: string
  createdBy?: number
  updatedBy?: number
  expired: boolean
  remainingDays?: number
}

// 租户管理API
export default {
  // 分页查询租户列表
  getTenantList: (data: TenantQueryDto) => api.get(`${API_BASE_URL}/sys/tenant/list`, {
    params: data,
  }),

  // 获取租户详情
  getTenantDetail: (id: number | string) => api.get(`${API_BASE_URL}/sys/tenant/detail`, {
    params: { id },
  }),

  // 创建租户
  createTenant: (data: TenantCreateDto) => api.post(`${API_BASE_URL}/sys/tenant/create`, data),

  // 更新租户
  updateTenant: (data: TenantUpdateDto & { id: number | string }) => api.post(`${API_BASE_URL}/sys/tenant/edit`, data, {
    params: { id: data.id },
  }),

  // 删除租户
  deleteTenant: (id: number | string) => api.post(`${API_BASE_URL}/sys/tenant/delete`, null, {
    params: { id },
  }),

  // 批量删除租户
  deleteTenants: (ids: (number | string)[]) => api.post(`${API_BASE_URL}/sys/tenant/batch-delete`, ids),

  // 更新租户状态
  updateTenantStatus: (id: number | string, status: number) => api.post(`${API_BASE_URL}/sys/tenant/update-status`, null, {
    params: { id, status },
  }),

  // 检查租户编码是否存在
  checkTenantCode: (tenantCode: string, excludeId?: number | string) => {
    const params: any = { tenantCode }
    if (excludeId) {
      params.excludeId = excludeId
    }
    return api.get(`${API_BASE_URL}/sys/tenant/check-tenant-code`, { params })
  },

  // 检查租户名称是否存在
  checkTenantName: (tenantName: string, excludeId?: number | string) => {
    const params: any = { tenantName }
    if (excludeId) {
      params.excludeId = excludeId
    }
    return api.get(`${API_BASE_URL}/sys/tenant/check-tenant-name`, { params })
  },

  // 检查域名是否存在
  checkDomain: (domain: string, excludeId?: number | string) => {
    const params: any = { domain }
    if (excludeId) {
      params.excludeId = excludeId
    }
    return api.get(`${API_BASE_URL}/sys/tenant/check-domain`, { params })
  },

  // 获取所有启用的租户
  getActiveTenants: () => api.get(`${API_BASE_URL}/sys/tenant/active`),

  // 根据套餐类型获取租户列表
  getTenantsByPackageType: (packageType: string) => api.get(`${API_BASE_URL}/sys/tenant/by-package-type`, {
    params: { packageType },
  }),
}
