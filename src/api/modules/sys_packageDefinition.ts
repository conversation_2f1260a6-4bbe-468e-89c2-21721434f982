import api from '../index'

export default {
  list: (data: {
    title?: string
    from: number
    limit: number
  }) => api.get('sys/package_definition/list', {
    params: data,
    baseURL: '/mock/',
  }),

  detail: (id: number | string) => api.get('sys/package_definition/detail', {
    params: {
      id,
    },
    baseURL: '/mock/',
  }),

  create: (data: any) => api.post('sys/package_definition/create', data, {
    baseURL: '/mock/',
  }),

  edit: (data: any) => api.post('sys/package_definition/edit', data, {
    baseURL: '/mock/',
  }),

  delete: (id: number | string) => api.post('sys/package_definition/delete', {
    id,
  }, {
    baseURL: '/mock/',
  }),
}
