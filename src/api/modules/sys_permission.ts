import api from '../index'

export default {
  list: (data: {
    title?: string
    from: number
    limit: number
  }) => api.get('sys/permission/list', {
    params: data,
    baseURL: '/mock/',
  }),

  detail: (id: number | string) => api.get('sys/permission/detail', {
    params: {
      id,
    },
    baseURL: '/mock/',
  }),

  create: (data: any) => api.post('sys/permission/create', data, {
    baseURL: '/mock/',
  }),

  edit: (data: any) => api.post('sys/permission/edit', data, {
    baseURL: '/mock/',
  }),

  delete: (id: number | string) => api.post('sys/permission/delete', {
    id,
  }, {
    baseURL: '/mock/',
  }),
}
