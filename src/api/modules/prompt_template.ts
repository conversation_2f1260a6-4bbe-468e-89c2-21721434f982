import { request } from '@/api'

// API 基础路径
const API_BASE_URL = '/api'

/**
 * 获取提示词模板列表
 * @returns Promise<any> 模板列表
 */
export function getPromptTemplates() {
  return request({
    url: `${API_BASE_URL}/prompt/templates`,
    method: 'GET',
  })
}

/**
 * 获取提示词模板详情
 * @param id 模板ID
 * @returns Promise<any> 模板详情
 */
export function getPromptTemplateDetail(id: string) {
  return request({
    url: `${API_BASE_URL}/prompt/templates/${id}`,
    method: 'GET',
  })
}

/**
 * 创建提示词模板
 * @param data 模板数据
 * @returns Promise<any> 创建结果
 */
export function createPromptTemplate(data: any) {
  // 确保数据格式符合后端要求，使用驼峰命名
  const requestData = {
    name: data.name,
    description: data.description || '',
    agentType: data.agent_type || '', // 转换为驼峰命名
    templateContent: data.template_content, // 转换为驼峰命名
    slotDefinitions: Array.isArray(data.slot_definitions)
      ? data.slot_definitions.filter((slot: any) => slot.name && slot.label).map((slot: any) => ({
          name: slot.name,
          label: slot.label,
          type: slot.type,
          required: !!slot.required,
          placeholder: slot.placeholder || '',
          defaultValue: slot.defaultValue || '',
          options: slot.options,
        }))
      : [],
  }

  console.log('准备发送API请求:', JSON.stringify(requestData, null, 2))

  return request({
    url: `${API_BASE_URL}/prompt/templates`,
    method: 'POST',
    data: requestData,
  }).catch((error) => {
    console.error('API请求错误详情:', error)
    // 将错误向上抛出，让组件处理
    throw error
  })
}

/**
 * 删除提示词模板
 * @param id 模板ID
 * @returns Promise<any> 删除结果
 */
export function deletePromptTemplate(id: string) {
  return request({
    url: `${API_BASE_URL}/prompt/templates/${id}`,
    method: 'DELETE',
  })
}

// 关联关系管理 API

/**
 * 获取模板的关联关系列表
 * @param id 模板ID
 * @returns Promise<any> 关联模板列表
 */
export function getTemplateRelations(id: string) {
  return request({
    url: `${API_BASE_URL}/prompt/templates/${id}/relations`,
    method: 'GET',
  })
}

/**
 * 添加模板关联关系
 * @param id 源模板ID
 * @param data 关联关系数据
 * @returns Promise<any> 创建结果
 */
export function addTemplateRelation(id: string, data: any) {
  return request({
    url: `${API_BASE_URL}/prompt/templates/${id}/relations`,
    method: 'POST',
    data,
  })
}

/**
 * 批量更新模板的关联关系
 * @param id 模板ID
 * @param relations 关联关系列表
 * @returns Promise<any> 更新结果
 */
export function updateTemplateRelations(id: string, relations: any[]) {
  return request({
    url: `${API_BASE_URL}/prompt/templates/${id}/relations`,
    method: 'PUT',
    data: relations,
  })
}

/**
 * 删除模板关联关系
 * @param id 模板ID
 * @param relationId 关联关系ID
 * @returns Promise<any> 删除结果
 */
export function deleteTemplateRelation(id: string, relationId: string) {
  return request({
    url: `${API_BASE_URL}/prompt/templates/${id}/relations/${relationId}`,
    method: 'DELETE',
  })
}

/**
 * 获取可用于关联的模板列表
 * @param id 要排除的模板ID
 * @returns Promise<any> 可关联的模板列表
 */
export function getAvailableTemplatesForRelation(id: string) {
  return request({
    url: `${API_BASE_URL}/prompt/templates/${id}/available-relations`,
    method: 'GET',
  })
}
