import api from '../index'

export default {
  list: (data: {
    title?: string
    from: number
    limit: number
  }) => api.get('sys/api_resource/list', {
    params: data,
    baseURL: '/mock/',
  }),

  detail: (id: number | string) => api.get('sys/api_resource/detail', {
    params: {
      id,
    },
    baseURL: '/mock/',
  }),

  create: (data: any) => api.post('sys/api_resource/create', data, {
    baseURL: '/mock/',
  }),

  edit: (data: any) => api.post('sys/api_resource/edit', data, {
    baseURL: '/mock/',
  }),

  delete: (id: number | string) => api.post('sys/api_resource/delete', {
    id,
  }, {
    baseURL: '/mock/',
  }),
}
