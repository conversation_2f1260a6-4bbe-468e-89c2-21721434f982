import api from '../index'

export default {
  list: (data: {
    title?: string
    from: number
    limit: number
  }) => api.get('sys/tenant_config/list', {
    params: data,
    baseURL: '/mock/',
  }),

  detail: (id: number | string) => api.get('sys/tenant_config/detail', {
    params: {
      id,
    },
    baseURL: '/mock/',
  }),

  create: (data: any) => api.post('sys/tenant_config/create', data, {
    baseURL: '/mock/',
  }),

  edit: (data: any) => api.post('sys/tenant_config/edit', data, {
    baseURL: '/mock/',
  }),

  delete: (id: number | string) => api.post('sys/tenant_config/delete', {
    id,
  }, {
    baseURL: '/mock/',
  }),
}
