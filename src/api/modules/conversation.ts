import { request } from '@/api'

// API 基础路径
const API_BASE_URL = '/api'

/**
 * 获取会话摘要列表
 *
 * @returns 返回会话摘要对象数组的Promise
 */
export function getConversations() {
  return request({
    url: `${API_BASE_URL}/conversations`,
    method: 'GET',
  })
}

/**
 * 获取指定会话ID的聊天历史记录
 *
 * @param conversationId 会话ID
 * @returns 返回聊天消息对象数组的Promise
 */
export function getConversationHistory(conversationId: string) {
  return request({
    url: `${API_BASE_URL}/history/${conversationId}`,
    method: 'GET',
  })
}

/**
 * 发送消息到指定会话
 *
 * @param conversationId 会话ID
 * @param content 消息内容
 * @param role 消息角色，默认为 'user'
 * @returns 返回发送成功的消息对象的Promise
 */
export function sendMessage(conversationId: string, content: string, role: string = 'user') {
  return request({
    url: `${API_BASE_URL}/conversations/${conversationId}/messages`,
    method: 'POST',
    data: {
      content,
      role,
    },
  })
}

/**
 * 请求AI完成（获取AI回复）
 *
 * @param conversationId 会话ID
 * @param message 用户消息
 * @returns 返回AI回复的Promise
 */
export function getCompletion(conversationId: string, message: string) {
  return request({
    url: `${API_BASE_URL}/conversations/${conversationId}/completions`,
    method: 'POST',
    data: {
      message,
    },
  })
}

/**
 * 创建新会话
 *
 * @param title 会话标题
 * @returns 返回创建的会话对象的Promise
 */
export function createConversation(title: string) {
  return request({
    url: `${API_BASE_URL}/conversations`,
    method: 'POST',
    data: {
      title,
    },
  })
}

/**
 * 删除会话
 *
 * @param conversationId 会话ID
 * @returns 无返回值的Promise
 */
export function deleteConversation(conversationId: string) {
  return request({
    url: `${API_BASE_URL}/conversations/${conversationId}`,
    method: 'DELETE',
  })
}
