import type {
  AppealRecord,
  AppealRecordQueryParams,
  AppealRequest,
  BalanceWarningConfig,
  BillingAccount,
  BillingConfig,
  BillingStatistics,
  PaymentMethod,
  RechargeRecord,
  RechargeRecordQueryParams,
  RechargeRequest,
  TokenCalculationRequest,
  TokenCalculationResponse,
  UsageRecord,
  UsageRecordQueryParams,
} from '@/types/billing'
import BillingAPI from '@/api/billing'
import { message } from 'ant-design-vue'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

/**
 * 计费系统状态管理
 */
export const useBillingStore = defineStore('billing', () => {
  // 基础状态
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 账户信息
  const account = ref<BillingAccount | null>(null)
  const config = ref<BillingConfig | null>(null)
  const balanceWarningConfig = ref<BalanceWarningConfig | null>(null)

  // 记录数据
  const usageRecords = ref<UsageRecord[]>([])
  const rechargeRecords = ref<RechargeRecord[]>([])
  const appealRecords = ref<AppealRecord[]>([])

  // 支付方式
  const paymentMethods = ref<PaymentMethod[]>([])

  // 统计数据
  const statistics = ref<BillingStatistics | null>(null)
  const currentTokenCalculation = ref<TokenCalculationResponse | null>(null)

  // 分页信息
  const usageRecordsPagination = ref({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条`,
  })

  const rechargeRecordsPagination = ref({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条`,
  })

  const appealRecordsPagination = ref({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条`,
  })

  // 计算属性
  const balance = computed(() => account.value?.balance || 0)
  const currency = computed(() => account.value?.currency || 'CNY')
  const accountStatus = computed(() => account.value?.status || 'ACTIVE')

  // 余额状态
  const balanceStatus = computed(() => {
    if (!account.value || !balanceWarningConfig.value) {
      return 'normal'
    }

    const { balance } = account.value
    const { lowBalanceThreshold, criticalBalanceThreshold } = balanceWarningConfig.value

    if (balance <= criticalBalanceThreshold) {
      return 'critical'
    }
    if (balance <= lowBalanceThreshold) {
      return 'warning'
    }
    return 'normal'
  })

  const isBalanceSufficient = computed(() => balanceStatus.value !== 'critical')

  // 当前使用的计费配置
  const currentConfig = computed(() => {
    return config.value
  })

  // Actions

  /**
   * 设置加载状态
   */
  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  /**
   * 设置错误信息
   */
  const setError = (errorMessage: string | null) => {
    error.value = errorMessage
    if (errorMessage) {
      message.error(errorMessage)
    }
  }

  /**
   * 清除错误
   */
  const clearError = () => {
    error.value = null
  }

  /**
   * 获取账户信息
   */
  const fetchAccount = async () => {
    try {
      setLoading(true)
      clearError()

      const response = await BillingAPI.getAccount()
      if (response.success) {
        account.value = response.data
      }
      else {
        setError(response.message || '获取账户信息失败')
      }
    }
    catch (err: any) {
      setError(err.message || '获取账户信息失败')
    }
    finally {
      setLoading(false)
    }
  }

  /**
   * 刷新余额
   */
  const refreshBalance = async () => {
    try {
      const response = await BillingAPI.refreshBalance()
      if (response.success) {
        account.value = response.data
        message.success('余额已刷新')
      }
      else {
        setError(response.message || '刷新余额失败')
      }
    }
    catch (err: any) {
      setError(err.message || '刷新余额失败')
    }
  }

  /**
   * 获取计费配置
   */
  const fetchConfiguration = async () => {
    try {
      const response = await BillingAPI.getConfiguration()
      if (response.success) {
        config.value = response.data
      }
      else {
        setError(response.message || '获取计费配置失败')
      }
    }
    catch (err: any) {
      setError(err.message || '获取计费配置失败')
    }
  }

  /**
   * 获取余额警告配置
   */
  const fetchBalanceWarningConfig = async () => {
    try {
      const response = await BillingAPI.getBalanceWarningConfig()
      if (response.success) {
        balanceWarningConfig.value = response.data
      }
    }
    catch (err: any) {
      console.warn('获取余额警告配置失败:', err.message)
    }
  }

  /**
   * 更新余额警告配置
   */
  const updateBalanceWarningConfig = async (config: BalanceWarningConfig) => {
    try {
      const response = await BillingAPI.updateBalanceWarningConfig(config)
      if (response.success) {
        balanceWarningConfig.value = response.data
        message.success('余额警告配置已更新')
      }
      else {
        setError(response.message || '更新余额警告配置失败')
      }
    }
    catch (err: any) {
      setError(err.message || '更新余额警告配置失败')
    }
  }

  /**
   * 计算Token费用
   */
  const calculateTokenCost = async (data: TokenCalculationRequest) => {
    try {
      const response = await BillingAPI.calculateTokenCost(data)
      if (response.success) {
        currentTokenCalculation.value = response.data
        return response.data
      }
      else {
        setError(response.message || 'Token费用计算失败')
        return null
      }
    }
    catch (err: any) {
      setError(err.message || 'Token费用计算失败')
      return null
    }
  }

  /**
   * 获取使用记录
   */
  const fetchUsageRecords = async (params?: UsageRecordQueryParams) => {
    try {
      setLoading(true)
      clearError()

      const response = await BillingAPI.getUsageRecords(params)
      if (response.success) {
        usageRecords.value = response.data.content
        usageRecordsPagination.value = {
          current: response.data.number + 1,
          pageSize: response.data.size,
          total: response.data.totalElements,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total: number) => `共 ${total} 条`,
        }
      }
      else {
        setError(response.message || '获取使用记录失败')
      }
    }
    catch (err: any) {
      setError(err.message || '获取使用记录失败')
    }
    finally {
      setLoading(false)
    }
  }

  /**
   * 获取充值记录
   */
  const fetchRechargeRecords = async (params?: RechargeRecordQueryParams) => {
    try {
      setLoading(true)
      clearError()

      const response = await BillingAPI.getRechargeRecords(params)
      if (response.success) {
        rechargeRecords.value = response.data.content
        rechargeRecordsPagination.value = {
          current: response.data.number + 1,
          pageSize: response.data.size,
          total: response.data.totalElements,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total: number) => `共 ${total} 条`,
        }
      }
      else {
        setError(response.message || '获取充值记录失败')
      }
    }
    catch (err: any) {
      setError(err.message || '获取充值记录失败')
    }
    finally {
      setLoading(false)
    }
  }

  /**
   * 提交充值申请
   */
  const submitRecharge = async (data: RechargeRequest) => {
    try {
      setLoading(true)
      clearError()

      const response = await BillingAPI.submitRecharge(data)
      if (response.success) {
        message.success('充值申请已提交')
        // 刷新充值记录和账户信息
        await Promise.all([
          fetchRechargeRecords(),
          fetchAccount(),
        ])
        return response.data
      }
      else {
        setError(response.message || '提交充值申请失败')
        return null
      }
    }
    catch (err: any) {
      setError(err.message || '提交充值申请失败')
      return null
    }
    finally {
      setLoading(false)
    }
  }

  /**
   * 获取申诉记录
   */
  const fetchAppealRecords = async (params?: AppealRecordQueryParams) => {
    try {
      setLoading(true)
      clearError()

      const response = await BillingAPI.getAppealRecords(params)
      if (response.success) {
        appealRecords.value = response.data.content
        appealRecordsPagination.value = {
          current: response.data.number + 1,
          pageSize: response.data.size,
          total: response.data.totalElements,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total: number) => `共 ${total} 条`,
        }
      }
      else {
        setError(response.message || '获取申诉记录失败')
      }
    }
    catch (err: any) {
      setError(err.message || '获取申诉记录失败')
    }
    finally {
      setLoading(false)
    }
  }

  /**
   * 提交申诉
   */
  const submitAppeal = async (data: AppealRequest) => {
    try {
      setLoading(true)
      clearError()

      const response = await BillingAPI.submitAppeal(data)
      if (response.success) {
        message.success('申诉已提交')
        // 刷新申诉记录
        await fetchAppealRecords()
        return response.data
      }
      else {
        setError(response.message || '提交申诉失败')
        return null
      }
    }
    catch (err: any) {
      setError(err.message || '提交申诉失败')
      return null
    }
    finally {
      setLoading(false)
    }
  }

  /**
   * 获取统计数据
   */
  const fetchStatistics = async (period = 'month') => {
    try {
      const response = await BillingAPI.getUsageStatistics(period)
      if (response.success) {
        statistics.value = response.data
      }
      else {
        setError(response.message || '获取统计数据失败')
      }
    }
    catch (err: any) {
      setError(err.message || '获取统计数据失败')
    }
  }

  /**
   * 预检查计费
   */
  const precheckBilling = async (inputTokens: number, outputTokens: number) => {
    try {
      const response = await BillingAPI.precheckBilling(inputTokens, outputTokens)
      if (response.success) {
        return response.data
      }
      else {
        setError(response.message || '预检查计费失败')
        return false
      }
    }
    catch (err: any) {
      setError(err.message || '预检查计费失败')
      return false
    }
  }

  /**
   * 检查余额
   */
  const checkBalance = async (amount: number) => {
    try {
      const response = await BillingAPI.checkBalance(amount)
      if (response.success) {
        return response.data
      }
      else {
        setError(response.message || '检查余额失败')
        return null
      }
    }
    catch (err: any) {
      setError(err.message || '检查余额失败')
      return null
    }
  }

  /**
   * 获取支付方式列表
   */
  const fetchPaymentMethods = async () => {
    try {
      const response = await BillingAPI.getPaymentMethods()
      if (response.success) {
        paymentMethods.value = response.data
      }
      else {
        setError(response.message || '获取支付方式失败')
      }
    }
    catch (err: any) {
      setError(err.message || '获取支付方式失败')
    }
  }

  /**
   * 取消充值
   */
  const cancelRecharge = async (rechargeId: string) => {
    try {
      setLoading(true)
      clearError()

      const response = await BillingAPI.cancelRecharge(rechargeId)
      if (response.success) {
        message.success('充值已取消')
        // 刷新充值记录
        await fetchRechargeRecords()
        return response.data
      }
      else {
        setError(response.message || '取消充值失败')
        return null
      }
    }
    catch (err: any) {
      setError(err.message || '取消充值失败')
      return null
    }
    finally {
      setLoading(false)
    }
  }

  /**
   * 创建申诉 (别名方法，指向 submitAppeal)
   */
  const createAppeal = async (data: AppealRequest) => {
    return await submitAppeal(data)
  }

  /**
   * 取消申诉
   */
  const cancelAppeal = async (appealId: string) => {
    try {
      setLoading(true)
      clearError()

      const response = await BillingAPI.cancelAppeal(appealId)
      if (response.success) {
        message.success('申诉已取消')
        // 刷新申诉记录
        await fetchAppealRecords()
        return response.data
      }
      else {
        setError(response.message || '取消申诉失败')
        return null
      }
    }
    catch (err: any) {
      setError(err.message || '取消申诉失败')
      return null
    }
    finally {
      setLoading(false)
    }
  }

  /**
   * 下载申诉附件
   */
  const downloadAttachment = async (attachmentId: string) => {
    try {
      const response = await BillingAPI.downloadAttachment(attachmentId)
      if (response.success) {
        // 这里应该处理文件下载
        const blob = new Blob([response.data], { type: 'application/octet-stream' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `attachment_${attachmentId}`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        message.success('文件下载成功')
      }
      else {
        setError(response.message || '下载附件失败')
      }
    }
    catch (err: any) {
      setError(err.message || '下载附件失败')
    }
  }

  /**
   * 下载使用记录收据
   */
  const downloadReceipt = async (recordId: string) => {
    try {
      const response = await BillingAPI.downloadReceipt(recordId)
      if (response.success) {
        // 这里应该处理文件下载
        const blob = new Blob([response.data], { type: 'application/pdf' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `receipt_${recordId}.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        message.success('收据下载成功')
      }
      else {
        setError(response.message || '下载收据失败')
      }
    }
    catch (err: any) {
      setError(err.message || '下载收据失败')
    }
  }

  /**
   * 初始化数据
   */
  const initialize = async () => {
    await Promise.all([
      fetchAccount(),
      fetchConfiguration(),
      fetchBalanceWarningConfig(),
    ])
  }

  /**
   * 重置状态
   */
  const reset = () => {
    account.value = null
    config.value = null
    balanceWarningConfig.value = null
    usageRecords.value = []
    rechargeRecords.value = []
    appealRecords.value = []
    statistics.value = null
    currentTokenCalculation.value = null
    error.value = null
    isLoading.value = false

    // 重置分页
    usageRecordsPagination.value = {
      current: 1,
      pageSize: 20,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number) => `共 ${total} 条`,
    }
    rechargeRecordsPagination.value = {
      current: 1,
      pageSize: 20,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number) => `共 ${total} 条`,
    }
    appealRecordsPagination.value = {
      current: 1,
      pageSize: 20,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number) => `共 ${total} 条`,
    }
  }

  return {
    // 状态
    isLoading,
    error,
    account,
    config,
    balanceWarningConfig,
    usageRecords,
    rechargeRecords,
    appealRecords,
    statistics,
    currentTokenCalculation,
    paymentMethods,
    usageRecordsPagination,
    rechargeRecordsPagination,
    appealRecordsPagination,

    // 计算属性
    balance,
    currency,
    accountStatus,
    balanceStatus,
    isBalanceSufficient,
    currentConfig,

    // Actions
    setLoading,
    setError,
    clearError,
    fetchAccount,
    refreshBalance,
    fetchConfiguration,
    fetchBalanceWarningConfig,
    updateBalanceWarningConfig,
    calculateTokenCost,
    fetchUsageRecords,
    fetchRechargeRecords,
    submitRecharge,
    fetchAppealRecords,
    submitAppeal,
    fetchStatistics,
    precheckBilling,
    checkBalance,
    fetchPaymentMethods,
    cancelRecharge,
    createAppeal,
    cancelAppeal,
    downloadAttachment,
    downloadReceipt,
    initialize,
    reset,
  }
})

export default useBillingStore
