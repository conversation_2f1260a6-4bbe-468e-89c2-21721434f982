-- <editor-fold desc="模拟测试数据">
-- ========================================
-- 模拟测试数据 INSERT 语句
-- ========================================

-- ----------------------------
-- 1. 套餐定义数据
-- ----------------------------
INSERT INTO sys_package_definition (package_code, package_name, package_type, price, billing_cycle, user_limit, storage_limit, feature_list, status, sort_order, created_by, updated_by) VALUES
('BASIC', '基础版', 'basic', 99.00, 'monthly', 10, 1024, '["user_management", "basic_reports"]', 1, 1, 1, 1),
('STANDARD', '标准版', 'standard', 299.00, 'monthly', 50, 5120, '["user_management", "basic_reports", "advanced_reports", "api_access"]', 1, 2, 1, 1),
('PREMIUM', '高级版', 'premium', 599.00, 'monthly', 200, 20480, '["user_management", "basic_reports", "advanced_reports", "api_access", "custom_integration"]', 1, 3, 1, 1),
('ENTERPRISE', '企业版', 'enterprise', 1299.00, 'monthly', -1, -1, '["user_management", "basic_reports", "advanced_reports", "api_access", "custom_integration", "priority_support"]', 1, 4, 1, 1);

-- ----------------------------
-- 2. 租户数据
-- ----------------------------
INSERT INTO sys_tenant (tenant_code, tenant_name, domain, status, package_type, expire_time, contact_name, contact_phone, contact_email, logo_url, description, created_by, updated_by) VALUES
('SYSTEM', '系统租户', 'system.dipspro.com', 1, 'enterprise', '2025-12-31 23:59:59', '系统管理员', '************', '<EMAIL>', '/images/logo/system.png', '系统默认租户，用于管理整个平台', 1, 1),
('DEMO001', '演示企业A', 'demo001.dipspro.com', 1, 'premium', '2024-12-31 23:59:59', '张三', '138-0000-0001', '<EMAIL>', '/images/logo/demo001.png', '演示企业A，用于产品展示', 1, 1),
('DEMO002', '演示企业B', 'demo002.dipspro.com', 1, 'standard', '2024-12-31 23:59:59', '李四', '138-0000-0002', '<EMAIL>', '/images/logo/demo002.png', '演示企业B，标准版用户', 1, 1),
('TEST001', '测试公司', 'test001.dipspro.com', 1, 'basic', '2024-06-30 23:59:59', '王五', '138-0000-0003', '<EMAIL>', '/images/logo/test001.png', '测试公司，基础版用户', 1, 1);

-- ----------------------------
-- 3. 系统级权限数据
-- ----------------------------
INSERT INTO sys_permission (tenant_id, permission_code, permission_name, permission_type, parent_id, level, sort_order, status, description, created_by, updated_by) VALUES
-- 系统管理权限
(NULL, 'SYSTEM_MANAGE', '系统管理', 'menu', NULL, 1, 1, 1, '系统管理根权限', 1, 1),
(NULL, 'TENANT_MANAGE', '租户管理', 'menu', 1, 2, 1, 1, '租户管理权限', 1, 1),
(NULL, 'TENANT_CREATE', '创建租户', 'button', 2, 3, 1, 1, '创建租户权限', 1, 1),
(NULL, 'TENANT_UPDATE', '编辑租户', 'button', 2, 3, 2, 1, '编辑租户权限', 1, 1),
(NULL, 'TENANT_DELETE', '删除租户', 'button', 2, 3, 3, 1, '删除租户权限', 1, 1),
(NULL, 'TENANT_VIEW', '查看租户', 'button', 2, 3, 4, 1, '查看租户权限', 1, 1),

-- 用户管理权限
(NULL, 'USER_MANAGE', '用户管理', 'menu', NULL, 1, 2, 1, '用户管理根权限', 1, 1),
(NULL, 'USER_CREATE', '创建用户', 'button', 7, 2, 1, 1, '创建用户权限', 1, 1),
(NULL, 'USER_UPDATE', '编辑用户', 'button', 7, 2, 2, 1, '编辑用户权限', 1, 1),
(NULL, 'USER_DELETE', '删除用户', 'button', 7, 2, 3, 1, '删除用户权限', 1, 1),
(NULL, 'USER_VIEW', '查看用户', 'button', 7, 2, 4, 1, '查看用户权限', 1, 1),

-- 角色管理权限
(NULL, 'ROLE_MANAGE', '角色管理', 'menu', NULL, 1, 3, 1, '角色管理根权限', 1, 1),
(NULL, 'ROLE_CREATE', '创建角色', 'button', 12, 2, 1, 1, '创建角色权限', 1, 1),
(NULL, 'ROLE_UPDATE', '编辑角色', 'button', 12, 2, 2, 1, '编辑角色权限', 1, 1),
(NULL, 'ROLE_DELETE', '删除角色', 'button', 12, 2, 3, 1, '删除角色权限', 1, 1),
(NULL, 'ROLE_VIEW', '查看角色', 'button', 12, 2, 4, 1, '查看角色权限', 1, 1),

-- 业务管理权限
(NULL, 'BUSINESS_MANAGE', '业务管理', 'menu', NULL, 1, 4, 1, '业务管理根权限', 1, 1),
(NULL, 'DASHBOARD_VIEW', '仪表盘查看', 'menu', 17, 2, 1, 1, '仪表盘查看权限', 1, 1),
(NULL, 'REPORT_VIEW', '报表查看', 'menu', 17, 2, 2, 1, '报表查看权限', 1, 1),
(NULL, 'REPORT_EXPORT', '报表导出', 'button', 19, 3, 1, 1, '报表导出权限', 1, 1);

-- ----------------------------
-- 4. 系统级角色数据
-- ----------------------------
INSERT INTO sys_role (tenant_id, role_code, role_name, role_type, parent_id, level, sort_order, status, description, created_by, updated_by) VALUES
-- 系统级角色
(1, 'SUPER_ADMIN', '超级管理员', 'system', NULL, 1, 1, 1, '系统超级管理员，拥有所有权限', 1, 1),
(1, 'SYSTEM_ADMIN', '系统管理员', 'system', 1, 2, 2, 1, '系统管理员，负责系统维护', 1, 1),
(1, 'TENANT_ADMIN', '租户管理员', 'system', 1, 2, 3, 1, '租户管理员，负责租户管理', 1, 1),

-- 租户级角色模板
(2, 'ADMIN', '管理员', 'custom', NULL, 1, 1, 1, '租户管理员角色', 1, 1),
(2, 'MANAGER', '经理', 'custom', 4, 2, 2, 1, '部门经理角色', 1, 1),
(2, 'EMPLOYEE', '员工', 'custom', 4, 2, 3, 1, '普通员工角色', 1, 1),
(2, 'GUEST', '访客', 'custom', 4, 2, 4, 1, '访客角色，只读权限', 1, 1),

(3, 'ADMIN', '管理员', 'custom', NULL, 1, 1, 1, '租户管理员角色', 1, 1),
(3, 'MANAGER', '经理', 'custom', 8, 2, 2, 1, '部门经理角色', 1, 1),
(3, 'EMPLOYEE', '员工', 'custom', 8, 2, 3, 1, '普通员工角色', 1, 1),

(4, 'ADMIN', '管理员', 'custom', NULL, 1, 1, 1, '租户管理员角色', 1, 1),
(4, 'EMPLOYEE', '员工', 'custom', 11, 2, 2, 1, '普通员工角色', 1, 1);

-- ----------------------------
-- 5. 用户数据（密码统一为：123456，已加密）
-- ----------------------------
INSERT INTO sys_user (tenant_id, username, email, phone, password, salt, real_name, nickname, avatar, gender, birthday, status, user_type, last_login_time, last_login_ip, login_count, password_update_time, created_by, updated_by) VALUES
-- 系统租户用户
(1, 'superadmin', '<EMAIL>', '138-0000-1001', '$2a$10$diemg6Bifboappu66K60weWI9ppPD./6fvE44gabrEnCuz1HxkZGi', 'salt001', '超级管理员', '超管', '/images/avatar/superadmin.png', 1, '1990-01-01', 1, 'admin', '2024-01-15 10:30:00', '127.0.0.1', 100, '2024-01-01 00:00:00', 1, 1),
(1, 'sysadmin', '<EMAIL>', '138-0000-1002', '$2a$10$N.zmdr9k7uOCQb94VKbMKe6gkQoDAMI6VkYeZsQr1hi/fYt0XZqTy', 'salt002', '系统管理员', '系统管理', '/images/avatar/sysadmin.png', 1, '1985-05-15', 1, 'admin', '2024-01-15 09:15:00', '*************', 50, '2024-01-01 00:00:00', 1, 1),
(1, 'tenantadmin', '<EMAIL>', '138-0000-1003', '$2a$10$N.zmdr9k7uOCQb94VKbMKe6gkQoDAMI6VkYeZsQr1hi/fYt0XZqTy', 'salt003', '租户管理员', '租户管理', '/images/avatar/tenantadmin.png', 2, '1988-08-20', 1, 'admin', '2024-01-15 08:45:00', '*************', 30, '2024-01-01 00:00:00', 1, 1),

-- 演示企业A用户
(2, 'admin', '<EMAIL>', '138-0001-0001', '$2a$10$N.zmdr9k7uOCQb94VKbMKe6gkQoDAMI6VkYeZsQr1hi/fYt0XZqTy', 'salt004', '张三', '张总', '/images/avatar/zhangsan.png', 1, '1980-03-10', 1, 'admin', '2024-01-15 11:20:00', '192.168.2.100', 80, '2024-01-01 00:00:00', 1, 1),
(2, 'manager01', '<EMAIL>', '138-0001-0002', '$2a$10$N.zmdr9k7uOCQb94VKbMKe6gkQoDAMI6VkYeZsQr1hi/fYt0XZqTy', 'salt005', '李经理', '李经理', '/images/avatar/lijingli.png', 1, '1985-07-22', 1, 'normal', '2024-01-15 10:10:00', '192.168.2.101', 45, '2024-01-01 00:00:00', 4, 4),
(2, 'employee01', '<EMAIL>', '138-0001-0003', '$2a$10$N.zmdr9k7uOCQb94VKbMKe6gkQoDAMI6VkYeZsQr1hi/fYt0XZqTy', 'salt006', '王员工', '小王', '/images/avatar/wangyuangong.png', 2, '1992-11-05', 1, 'normal', '2024-01-15 09:30:00', '192.168.2.102', 25, '2024-01-01 00:00:00', 4, 4),
(2, 'employee02', '<EMAIL>', '138-0001-0004', '$2a$10$N.zmdr9k7uOCQb94VKbMKe6gkQoDAMI6VkYeZsQr1hi/fYt0XZqTy', 'salt007', '赵员工', '小赵', '/images/avatar/zhaoyuangong.png', 1, '1990-12-18', 1, 'normal', '2024-01-15 08:50:00', '192.168.2.103', 20, '2024-01-01 00:00:00', 4, 4),
(2, 'guest01', '<EMAIL>', '138-0001-0005', '$2a$10$N.zmdr9k7uOCQb94VKbMKe6gkQoDAMI6VkYeZsQr1hi/fYt0XZqTy', 'salt008', '访客用户', '访客', '/images/avatar/guest.png', 0, '1995-04-30', 1, 'guest', '2024-01-15 14:20:00', '192.168.2.104', 5, '2024-01-01 00:00:00', 4, 4),

-- 演示企业B用户
(3, 'admin', '<EMAIL>', '138-0002-0001', '$2a$10$N.zmdr9k7uOCQb94VKbMKe6gkQoDAMI6VkYeZsQr1hi/fYt0XZqTy', 'salt009', '李四', '李总', '/images/avatar/lisi.png', 1, '1982-06-15', 1, 'admin', '2024-01-15 12:30:00', '192.168.3.100', 60, '2024-01-01 00:00:00', 1, 1),
(3, 'manager01', '<EMAIL>', '138-0002-0002', '$2a$10$N.zmdr9k7uOCQb94VKbMKe6gkQoDAMI6VkYeZsQr1hi/fYt0XZqTy', 'salt010', '陈经理', '陈经理', '/images/avatar/chenjingli.png', 2, '1987-09-08', 1, 'normal', '2024-01-15 11:40:00', '192.168.3.101', 35, '2024-01-01 00:00:00', 9, 9),
(3, 'employee01', '<EMAIL>', '138-0002-0003', '$2a$10$N.zmdr9k7uOCQb94VKbMKe6gkQoDAMI6VkYeZsQr1hi/fYt0XZqTy', 'salt011', '孙员工', '小孙', '/images/avatar/sunyuangong.png', 1, '1993-02-14', 1, 'normal', '2024-01-15 10:20:00', '192.168.3.102', 18, '2024-01-01 00:00:00', 9, 9),

-- 测试公司用户
(4, 'admin', '<EMAIL>', '138-0003-0001', '$2a$10$N.zmdr9k7uOCQb94VKbMKe6gkQoDAMI6VkYeZsQr1hi/fYt0XZqTy', 'salt012', '王五', '王总', '/images/avatar/wangwu.png', 1, '1979-12-03', 1, 'admin', '2024-01-15 13:10:00', '192.168.4.100', 40, '2024-01-01 00:00:00', 1, 1),
(4, 'employee01', '<EMAIL>', '138-0003-0002', '$2a$10$N.zmdr9k7uOCQb94VKbMKe6gkQoDAMI6VkYeZsQr1hi/fYt0XZqTy', 'salt013', '周员工', '小周', '/images/avatar/zhouyuangong.png', 2, '1991-10-25', 1, 'normal', '2024-01-15 09:50:00', '*************', 12, '2024-01-01 00:00:00', 12, 12);

-- ----------------------------
-- 6. 部门数据
-- ----------------------------
INSERT INTO sys_department (tenant_id, dept_code, dept_name, parent_id, level, dept_path, leader_id, phone, email, address, sort_order, status, description, created_by, updated_by) VALUES
-- 演示企业A部门
(2, 'ROOT', '演示企业A', NULL, 1, '/ROOT', 4, '400-0001-000', '<EMAIL>', '北京市朝阳区演示大厦A座', 1, 1, '演示企业A根部门', 4, 4),
(2, 'TECH', '技术部', 1, 2, '/ROOT/TECH', 5, '400-0001-001', '<EMAIL>', '北京市朝阳区演示大厦A座10层', 1, 1, '技术研发部门', 4, 4),
(2, 'SALES', '销售部', 1, 2, '/ROOT/SALES', 6, '400-0001-002', '<EMAIL>', '北京市朝阳区演示大厦A座8层', 2, 1, '销售业务部门', 4, 4),
(2, 'HR', '人事部', 1, 2, '/ROOT/HR', 7, '400-0001-003', '<EMAIL>', '北京市朝阳区演示大厦A座6层', 3, 1, '人力资源部门', 4, 4),

-- 演示企业B部门
(3, 'ROOT', '演示企业B', NULL, 1, '/ROOT', 9, '400-0002-000', '<EMAIL>', '上海市浦东新区演示大厦B座', 1, 1, '演示企业B根部门', 9, 9),
(3, 'PRODUCT', '产品部', 5, 2, '/ROOT/PRODUCT', 10, '400-0002-001', '<EMAIL>', '上海市浦东新区演示大厦B座12层', 1, 1, '产品设计部门', 9, 9),
(3, 'MARKET', '市场部', 5, 2, '/ROOT/MARKET', 11, '400-0002-002', '<EMAIL>', '上海市浦东新区演示大厦B座9层', 2, 1, '市场营销部门', 9, 9),

-- 测试公司部门
(4, 'ROOT', '测试公司', NULL, 1, '/ROOT', 12, '400-0003-000', '<EMAIL>', '深圳市南山区测试大厦', 1, 1, '测试公司根部门', 12, 12),
(4, 'DEV', '开发部', 8, 2, '/ROOT/DEV', 13, '400-0003-001', '<EMAIL>', '深圳市南山区测试大厦15层', 1, 1, '软件开发部门', 12, 12);

-- ----------------------------
-- 7. 岗位数据
-- ----------------------------
INSERT INTO sys_position (tenant_id, position_code, position_name, position_level, dept_id, sort_order, status, responsibilities, requirements, description, created_by, updated_by) VALUES
-- 演示企业A岗位
(2, 'CEO', '首席执行官', 1, 1, 1, 1, '负责公司整体战略规划和运营管理', '10年以上管理经验，MBA优先', '公司最高管理职位', 4, 4),
(2, 'CTO', '首席技术官', 2, 2, 1, 1, '负责技术战略规划和技术团队管理', '8年以上技术管理经验', '技术部门最高职位', 4, 4),
(2, 'TECH_MANAGER', '技术经理', 3, 2, 2, 1, '负责技术团队日常管理和项目推进', '5年以上技术经验，2年管理经验', '技术部门管理职位', 4, 4),
(2, 'SENIOR_DEV', '高级开发工程师', 4, 2, 3, 1, '负责核心功能开发和技术难题攻关', '3年以上开发经验', '高级技术职位', 4, 4),
(2, 'JUNIOR_DEV', '初级开发工程师', 5, 2, 4, 1, '负责基础功能开发和维护', '1年以上开发经验', '初级技术职位', 4, 4),

-- 演示企业B岗位
(3, 'CEO', '首席执行官', 1, 5, 1, 1, '负责公司整体战略规划和运营管理', '10年以上管理经验', '公司最高管理职位', 9, 9),
(3, 'PRODUCT_MANAGER', '产品经理', 3, 6, 1, 1, '负责产品规划和需求管理', '3年以上产品经验', '产品部门核心职位', 9, 9),
(3, 'MARKET_MANAGER', '市场经理', 3, 7, 1, 1, '负责市场推广和品牌建设', '3年以上市场经验', '市场部门核心职位', 9, 9),

-- 测试公司岗位
(4, 'CEO', '首席执行官', 1, 8, 1, 1, '负责公司整体运营', '5年以上管理经验', '公司负责人', 12, 12),
(4, 'DEVELOPER', '开发工程师', 4, 9, 1, 1, '负责软件开发工作', '2年以上开发经验', '开发职位', 12, 12);

-- ----------------------------
-- 8. 用户角色关联数据
-- ----------------------------
INSERT INTO sys_user_role_relation (tenant_id, user_id, role_id, created_by) VALUES
-- 系统租户用户角色
(1, 1, 1, 1), -- superadmin -> SUPER_ADMIN
(1, 2, 2, 1), -- sysadmin -> SYSTEM_ADMIN
(1, 3, 3, 1), -- tenantadmin -> TENANT_ADMIN

-- 演示企业A用户角色
(2, 4, 4, 4), -- admin -> ADMIN
(2, 5, 5, 4), -- manager01 -> MANAGER
(2, 6, 6, 4), -- employee01 -> EMPLOYEE
(2, 7, 6, 4), -- employee02 -> EMPLOYEE
(2, 8, 7, 4), -- guest01 -> GUEST

-- 演示企业B用户角色
(3, 9, 8, 9),  -- admin -> ADMIN
(3, 10, 9, 9), -- manager01 -> MANAGER
(3, 11, 10, 9), -- employee01 -> EMPLOYEE

-- 测试公司用户角色
(4, 12, 11, 12), -- admin -> ADMIN
(4, 13, 12, 12); -- employee01 -> EMPLOYEE

-- ----------------------------
-- 9. 角色权限关联数据（系统级权限分配）
-- ----------------------------
INSERT INTO sys_role_permission_relation (tenant_id, role_id, permission_id, created_by) VALUES
-- 超级管理员拥有所有权限
(1, 1, 1, 1), (1, 1, 2, 1), (1, 1, 3, 1), (1, 1, 4, 1), (1, 1, 5, 1), (1, 1, 6, 1),
(1, 1, 7, 1), (1, 1, 8, 1), (1, 1, 9, 1), (1, 1, 10, 1), (1, 1, 11, 1),
(1, 1, 12, 1), (1, 1, 13, 1), (1, 1, 14, 1), (1, 1, 15, 1), (1, 1, 16, 1),
(1, 1, 17, 1), (1, 1, 18, 1), (1, 1, 19, 1), (1, 1, 20, 1),

-- 系统管理员权限
(1, 2, 7, 1), (1, 2, 8, 1), (1, 2, 9, 1), (1, 2, 10, 1), (1, 2, 11, 1),
(1, 2, 12, 1), (1, 2, 13, 1), (1, 2, 14, 1), (1, 2, 15, 1), (1, 2, 16, 1),
(1, 2, 17, 1), (1, 2, 18, 1), (1, 2, 19, 1), (1, 2, 20, 1),

-- 租户管理员权限
(1, 3, 1, 1), (1, 3, 2, 1), (1, 3, 3, 1), (1, 3, 4, 1), (1, 3, 5, 1), (1, 3, 6, 1);

-- ----------------------------
-- 10. 用户部门关联数据
-- ----------------------------
INSERT INTO sys_user_department_relation (tenant_id, user_id, dept_id, is_primary, join_time, created_by) VALUES
-- 演示企业A
(2, 4, 1, 1, '2024-01-01 09:00:00', 4), -- admin -> ROOT
(2, 5, 2, 1, '2024-01-01 09:00:00', 4), -- manager01 -> TECH
(2, 6, 2, 1, '2024-01-01 09:00:00', 4), -- employee01 -> TECH
(2, 7, 3, 1, '2024-01-01 09:00:00', 4), -- employee02 -> SALES
(2, 8, 1, 1, '2024-01-01 09:00:00', 4), -- guest01 -> ROOT

-- 演示企业B
(3, 9, 5, 1, '2024-01-01 09:00:00', 9),  -- admin -> ROOT
(3, 10, 6, 1, '2024-01-01 09:00:00', 9), -- manager01 -> PRODUCT
(3, 11, 7, 1, '2024-01-01 09:00:00', 9), -- employee01 -> MARKET

-- 测试公司
(4, 12, 8, 1, '2024-01-01 09:00:00', 12), -- admin -> ROOT
(4, 13, 9, 1, '2024-01-01 09:00:00', 12); -- employee01 -> DEV

-- ----------------------------
-- 11. 用户岗位关联数据
-- ----------------------------
INSERT INTO sys_user_position_relation (tenant_id, user_id, position_id, is_primary, start_time, created_by) VALUES
-- 演示企业A
(2, 4, 1, 1, '2024-01-01 09:00:00', 4), -- admin -> CEO
(2, 5, 3, 1, '2024-01-01 09:00:00', 4), -- manager01 -> TECH_MANAGER
(2, 6, 4, 1, '2024-01-01 09:00:00', 4), -- employee01 -> SENIOR_DEV
(2, 7, 5, 1, '2024-01-01 09:00:00', 4), -- employee02 -> JUNIOR_DEV

-- 演示企业B
(3, 9, 6, 1, '2024-01-01 09:00:00', 9),  -- admin -> CEO
(3, 10, 7, 1, '2024-01-01 09:00:00', 9), -- manager01 -> PRODUCT_MANAGER
(3, 11, 8, 1, '2024-01-01 09:00:00', 9), -- employee01 -> MARKET_MANAGER

-- 测试公司
(4, 12, 9, 1, '2024-01-01 09:00:00', 12),  -- admin -> CEO
(4, 13, 10, 1, '2024-01-01 09:00:00', 12); -- employee01 -> DEVELOPER

-- ----------------------------
-- 12. 租户配置数据
-- ----------------------------
INSERT INTO sys_tenant_config (tenant_id, config_key, config_value, config_type, description, created_by, updated_by) VALUES
-- 系统租户配置
(1, 'system.name', 'DIPS Pro 管理系统', 'system', '系统名称配置', 1, 1),
(1, 'system.version', '1.0.0', 'system', '系统版本配置', 1, 1),
(1, 'system.logo', '/images/logo/system.png', 'ui', '系统Logo配置', 1, 1),

-- 演示企业A配置
(2, 'company.name', '演示企业A', 'business', '公司名称', 4, 4),
(2, 'company.address', '北京市朝阳区演示大厦A座', 'business', '公司地址', 4, 4),
(2, 'ui.theme', 'blue', 'ui', '界面主题色', 4, 4),
(2, 'ui.logo', '/images/logo/demo001.png', 'ui', '公司Logo', 4, 4),

-- 演示企业B配置
(3, 'company.name', '演示企业B', 'business', '公司名称', 9, 9),
(3, 'company.address', '上海市浦东新区演示大厦B座', 'business', '公司地址', 9, 9),
(3, 'ui.theme', 'green', 'ui', '界面主题色', 9, 9),

-- 测试公司配置
(4, 'company.name', '测试公司', 'business', '公司名称', 12, 12),
(4, 'company.address', '深圳市南山区测试大厦', 'business', '公司地址', 12, 12),
(4, 'ui.theme', 'orange', 'ui', '界面主题色', 12, 12);

-- ----------------------------
-- 13. 租户使用量统计数据
-- ----------------------------
INSERT INTO sys_tenant_usage (tenant_id, stat_date, user_count, storage_used, api_calls, login_count) VALUES
-- 演示企业A使用量
(2, '2024-01-01', 5, 1024, 1500, 25),
(2, '2024-01-02', 5, 1100, 1800, 30),
(2, '2024-01-03', 5, 1200, 2000, 28),

-- 演示企业B使用量
(3, '2024-01-01', 3, 512, 800, 15),
(3, '2024-01-02', 3, 600, 950, 18),
(3, '2024-01-03', 3, 650, 1100, 20),

-- 测试公司使用量
(4, '2024-01-01', 2, 256, 300, 8),
(4, '2024-01-02', 2, 280, 350, 10),
(4, '2024-01-03', 2, 300, 400, 12);

-- ----------------------------
-- 14. 菜单资源测试数据 (PostgreSQL版本)
-- ----------------------------
-- PostgreSQL 菜单资源测试数据
-- 使用 ON CONFLICT 处理重复插入

INSERT INTO sys_menu_resource (
    id, tenant_id, menu_code, menu_name, menu_type, parent_id, level, 
    path, component, icon, sort_order, visible, status, external_link, 
    cache, created_time, updated_time, deleted
) VALUES 
-- 系统管理目录
(1, 1, 'system', '系统管理', 'catalog', NULL, 1, '/system', NULL, 'i-ep:setting', 1, 1, 1, 0, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0),

-- 用户管理菜单
(2, 1, 'system:user', '用户管理', 'menu', 1, 2, '/system/user', '@/views/sys/user/list.vue', 'i-ep:user', 1, 1, 1, 0, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0),

-- 用户管理按钮
(3, 1, 'system:user:add', '新增用户', 'button', 2, 3, NULL, NULL, NULL, 1, 1, 1, 0, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0),
(4, 1, 'system:user:edit', '编辑用户', 'button', 2, 3, NULL, NULL, NULL, 2, 1, 1, 0, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0),
(5, 1, 'system:user:delete', '删除用户', 'button', 2, 3, NULL, NULL, NULL, 3, 1, 1, 0, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0),

-- 菜单管理菜单
(6, 1, 'system:menu', '菜单管理', 'menu', 1, 2, '/system/menu-resource', '@/views/sys/menu-resource/list.vue', 'i-ep:menu', 2, 1, 1, 0, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0),

-- 菜单管理按钮
(7, 1, 'system:menu:add', '新增菜单', 'button', 6, 3, NULL, NULL, NULL, 1, 1, 1, 0, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0),
(8, 1, 'system:menu:edit', '编辑菜单', 'button', 6, 3, NULL, NULL, NULL, 2, 1, 1, 0, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0),
(9, 1, 'system:menu:delete', '删除菜单', 'button', 6, 3, NULL, NULL, NULL, 3, 1, 1, 0, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0),

-- 日志管理菜单
(10, 1, 'system:log', '日志管理', 'menu', 1, 2, '/system/log', '@/views/sys/log/list.vue', 'i-ep:document', 3, 1, 1, 0, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 0)

-- 处理主键冲突
ON CONFLICT (id) DO NOTHING;

-- 更新序列的当前值（PostgreSQL特有）
SELECT setval(pg_get_serial_sequence('sys_menu_resource', 'id'), (SELECT COALESCE(MAX(id), 1) FROM sys_menu_resource)); 

-- ========================================
-- 模拟测试数据 DELETE 语句
-- ========================================

-- ----------------------------
-- 清理所有模拟数据的DELETE语句
-- 注意：按照外键依赖关系的逆序删除
-- ----------------------------

-- 1. 删除租户使用量统计数据
DELETE FROM sys_tenant_usage WHERE tenant_id IN (1, 2, 3, 4);

-- 2. 删除租户配置数据
DELETE FROM sys_tenant_config WHERE tenant_id IN (1, 2, 3, 4);

-- 3. 删除用户岗位关联数据
DELETE FROM sys_user_position_relation WHERE tenant_id IN (2, 3, 4);

-- 4. 删除用户部门关联数据
DELETE FROM sys_user_department_relation WHERE tenant_id IN (2, 3, 4);

-- 5. 删除角色权限关联数据
DELETE FROM sys_role_permission_relation WHERE tenant_id IN (1, 2, 3, 4);

-- 6. 删除用户角色关联数据
DELETE FROM sys_user_role_relation WHERE tenant_id IN (1, 2, 3, 4);

-- 7. 删除岗位数据
DELETE FROM sys_position WHERE tenant_id IN (2, 3, 4);

-- 8. 删除部门数据
DELETE FROM sys_department WHERE tenant_id IN (2, 3, 4);

-- 9. 删除用户数据
DELETE FROM sys_user WHERE tenant_id IN (1, 2, 3, 4);

-- 10. 删除角色数据
DELETE FROM sys_role WHERE tenant_id IN (1, 2, 3, 4);

-- 11. 删除权限数据
DELETE FROM sys_permission WHERE tenant_id IS NULL OR tenant_id IN (1, 2, 3, 4);

-- 12. 删除租户数据
DELETE FROM sys_tenant WHERE tenant_code IN ('SYSTEM', 'DEMO001', 'DEMO002', 'TEST001');

-- 13. 删除套餐定义数据
DELETE FROM sys_package_definition WHERE package_code IN ('BASIC', 'STANDARD', 'PREMIUM', 'ENTERPRISE');

-- ========================================
-- 快速重置数据脚本
-- ========================================

-- 重置序列（如果需要重新开始ID编号）
-- ALTER SEQUENCE sys_tenant_id_seq RESTART WITH 1;
-- ALTER SEQUENCE sys_user_id_seq RESTART WITH 1;
-- ALTER SEQUENCE sys_role_id_seq RESTART WITH 1;
-- ALTER SEQUENCE sys_permission_id_seq RESTART WITH 1;
-- ALTER SEQUENCE sys_department_id_seq RESTART WITH 1;
-- ALTER SEQUENCE sys_position_id_seq RESTART WITH 1;
-- ALTER SEQUENCE sys_package_definition_id_seq RESTART WITH 1;

-- </editor-fold> 