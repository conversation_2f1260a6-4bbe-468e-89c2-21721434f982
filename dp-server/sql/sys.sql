-- <editor-fold desc="租户">
-- ----------------------------
-- 租户基础信息表
-- ----------------------------
DROP TABLE IF EXISTS `sys_tenant`;
CREATE TABLE `sys_tenant`
(
    `id`            BIGINT       NOT NULL AUTO_INCREMENT COMMENT '租户ID',
    `tenant_code`   VARCHAR(64)  NOT NULL COMMENT '租户编码',
    `tenant_name`   VARCHAR(128) NOT NULL COMMENT '租户名称',
    `domain`        VARCHAR(128) COMMENT '租户域名',
    `status`        TINYINT      NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用，2-过期',
    `package_type`  VARCHAR(32)  NOT NULL COMMENT '套餐类型：basic,standard,premium,enterprise',
    `expire_time`   DATETIME COMMENT '到期时间',
    `contact_name`  VARCHAR(64) COMMENT '联系人姓名',
    `contact_phone` VARCHAR(32) COMMENT '联系人电话',
    `contact_email` VARCHAR(128) COMMENT '联系人邮箱',
    `logo_url`      VARCHAR(512) COMMENT 'Logo地址',
    `description`   TEXT COMMENT '租户描述',
    `created_time`  DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time`  DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by`    BIGINT COMMENT '创建人',
    `updated_by`    BIGINT COMMENT '更新人',
    `deleted`       TINYINT      NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_code` (`tenant_code`),
    UNIQUE KEY `uk_domain` (`domain`),
    KEY             `idx_status` (`status`),
    KEY             `idx_package_type` (`package_type`),
    KEY             `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户基础信息表';

-- ----------------------------
-- 租户配置表
-- ----------------------------
DROP TABLE IF EXISTS `sys_tenant_config`;
CREATE TABLE `sys_tenant_config`
(
    `id`           BIGINT       NOT NULL AUTO_INCREMENT COMMENT '配置ID',
    `tenant_id`    BIGINT       NOT NULL COMMENT '租户ID',
    `config_key`   VARCHAR(128) NOT NULL COMMENT '配置键',
    `config_value` TEXT COMMENT '配置值',
    `config_type`  VARCHAR(32)  NOT NULL COMMENT '配置类型：system,business,ui',
    `description`  VARCHAR(512) COMMENT '配置描述',
    `created_time` DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by`   BIGINT COMMENT '创建人',
    `updated_by`   BIGINT COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_config` (`tenant_id`, `config_key`),
    KEY            `idx_tenant_id` (`tenant_id`),
    KEY            `idx_config_type` (`config_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户配置表';

-- ----------------------------
-- 套餐定义表
-- ----------------------------
DROP TABLE IF EXISTS `sys_package_definition`;
CREATE TABLE `sys_package_definition`
(
    `id`            BIGINT         NOT NULL AUTO_INCREMENT COMMENT '套餐ID',
    `package_code`  VARCHAR(64)    NOT NULL COMMENT '套餐编码',
    `package_name`  VARCHAR(128)   NOT NULL COMMENT '套餐名称',
    `package_type`  VARCHAR(32)    NOT NULL COMMENT '套餐类型',
    `price`         DECIMAL(10, 2) NOT NULL COMMENT '价格',
    `billing_cycle` VARCHAR(32)    NOT NULL COMMENT '计费周期：monthly,yearly',
    `user_limit`    INT COMMENT '用户数限制',
    `storage_limit` BIGINT COMMENT '存储限制(MB)',
    `feature_list`  JSON COMMENT '功能列表',
    `status`        TINYINT        NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `sort_order`    INT                     DEFAULT 0 COMMENT '排序',
    `created_time`  DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time`  DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by`    BIGINT COMMENT '创建人',
    `updated_by`    BIGINT COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_package_code` (`package_code`),
    KEY             `idx_package_type` (`package_type`),
    KEY             `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='套餐定义表';

-- ----------------------------
-- 租户使用量统计表
-- ----------------------------
DROP TABLE IF EXISTS `sys_tenant_usage`;
CREATE TABLE `sys_tenant_usage`
(
    `id`           BIGINT   NOT NULL AUTO_INCREMENT COMMENT '统计ID',
    `tenant_id`    BIGINT   NOT NULL COMMENT '租户ID',
    `stat_date`    DATE     NOT NULL COMMENT '统计日期',
    `user_count`   INT               DEFAULT 0 COMMENT '用户数量',
    `storage_used` BIGINT            DEFAULT 0 COMMENT '已用存储(MB)',
    `api_calls`    BIGINT            DEFAULT 0 COMMENT 'API调用次数',
    `login_count`  INT               DEFAULT 0 COMMENT '登录次数',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_date` (`tenant_id`, `stat_date`),
    KEY            `idx_tenant_id` (`tenant_id`),
    KEY            `idx_stat_date` (`stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户使用量统计表';
-- </editor-fold>

-- <editor-fold desc="权限管理体系（RBAC模型）">
-- ----------------------------
-- 用户表
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`
(
    `id`                   BIGINT       NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `tenant_id`            BIGINT       NOT NULL COMMENT '租户ID',
    `username`             VARCHAR(64)  NOT NULL COMMENT '用户名',
    `email`                VARCHAR(128) COMMENT '邮箱',
    `phone`                VARCHAR(32) COMMENT '手机号',
    `password`             VARCHAR(128) NOT NULL COMMENT '密码(加密)',
    `salt`                 VARCHAR(64)  NOT NULL COMMENT '密码盐值',
    `real_name`            VARCHAR(64) COMMENT '真实姓名',
    `nickname`             VARCHAR(64) COMMENT '昵称',
    `avatar`               VARCHAR(512) COMMENT '头像地址',
    `gender`               TINYINT COMMENT '性别：0-未知，1-男，2-女',
    `birthday`             DATE COMMENT '生日',
    `status`               TINYINT      NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用，2-锁定',
    `user_type`            VARCHAR(32)  NOT NULL DEFAULT 'normal' COMMENT '用户类型：admin,normal,guest',
    `last_login_time`      DATETIME COMMENT '最后登录时间',
    `last_login_ip`        VARCHAR(64) COMMENT '最后登录IP',
    `login_count`          INT                   DEFAULT 0 COMMENT '登录次数',
    `password_update_time` DATETIME COMMENT '密码更新时间',
    `created_time`         DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time`         DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by`           BIGINT COMMENT '创建人',
    `updated_by`           BIGINT COMMENT '更新人',
    `deleted`              TINYINT      NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_username` (`tenant_id`, `username`),
    UNIQUE KEY `uk_tenant_email` (`tenant_id`, `email`),
    UNIQUE KEY `uk_tenant_phone` (`tenant_id`, `phone`),
    KEY                    `idx_tenant_id` (`tenant_id`),
    KEY                    `idx_status` (`status`),
    KEY                    `idx_user_type` (`user_type`),
    KEY                    `idx_last_login_time` (`last_login_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- ----------------------------
-- 角色表
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`
(
    `id`           BIGINT       NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    `tenant_id`    BIGINT       NOT NULL COMMENT '租户ID',
    `role_code`    VARCHAR(64)  NOT NULL COMMENT '角色编码',
    `role_name`    VARCHAR(128) NOT NULL COMMENT '角色名称',
    `role_type`    VARCHAR(32)  NOT NULL DEFAULT 'custom' COMMENT '角色类型：system,custom',
    `parent_id`    BIGINT COMMENT '父角色ID',
    `level`        INT                   DEFAULT 1 COMMENT '角色层级',
    `sort_order`   INT                   DEFAULT 0 COMMENT '排序',
    `status`       TINYINT      NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `description`  VARCHAR(512) COMMENT '角色描述',
    `created_time` DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by`   BIGINT COMMENT '创建人',
    `updated_by`   BIGINT COMMENT '更新人',
    `deleted`      TINYINT      NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_role_code` (`tenant_id`, `role_code`),
    KEY            `idx_tenant_id` (`tenant_id`),
    KEY            `idx_parent_id` (`parent_id`),
    KEY            `idx_role_type` (`role_type`),
    KEY            `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- ----------------------------
-- 权限表
-- ----------------------------
DROP TABLE IF EXISTS `sys_permission`;
CREATE TABLE `sys_permission`
(
    `id`              BIGINT       NOT NULL AUTO_INCREMENT COMMENT '权限ID',
    `tenant_id`       BIGINT COMMENT '租户ID(为空表示系统级权限)',
    `permission_code` VARCHAR(128) NOT NULL COMMENT '权限编码',
    `permission_name` VARCHAR(128) NOT NULL COMMENT '权限名称',
    `permission_type` VARCHAR(32)  NOT NULL COMMENT '权限类型：menu,button,api,data',
    `resource_id`     BIGINT COMMENT '关联资源ID',
    `parent_id`       BIGINT COMMENT '父权限ID',
    `level`           INT                   DEFAULT 1 COMMENT '权限层级',
    `sort_order`      INT                   DEFAULT 0 COMMENT '排序',
    `status`          TINYINT      NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `description`     VARCHAR(512) COMMENT '权限描述',
    `created_time`    DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time`    DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by`      BIGINT COMMENT '创建人',
    `updated_by`      BIGINT COMMENT '更新人',
    `deleted`         TINYINT      NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_permission_code` (`permission_code`),
    KEY               `idx_tenant_id` (`tenant_id`),
    KEY               `idx_permission_type` (`permission_type`),
    KEY               `idx_resource_id` (`resource_id`),
    KEY               `idx_parent_id` (`parent_id`),
    KEY               `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- ----------------------------
-- 用户组表
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_group`;
CREATE TABLE `sys_user_group`
(
    `id`           BIGINT       NOT NULL AUTO_INCREMENT COMMENT '用户组ID',
    `tenant_id`    BIGINT       NOT NULL COMMENT '租户ID',
    `group_code`   VARCHAR(64)  NOT NULL COMMENT '用户组编码',
    `group_name`   VARCHAR(128) NOT NULL COMMENT '用户组名称',
    `group_type`   VARCHAR(32)  NOT NULL DEFAULT 'custom' COMMENT '用户组类型：system,custom',
    `parent_id`    BIGINT COMMENT '父用户组ID',
    `level`        INT                   DEFAULT 1 COMMENT '用户组层级',
    `sort_order`   INT                   DEFAULT 0 COMMENT '排序',
    `status`       TINYINT      NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `description`  VARCHAR(512) COMMENT '用户组描述',
    `created_time` DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by`   BIGINT COMMENT '创建人',
    `updated_by`   BIGINT COMMENT '更新人',
    `deleted`      TINYINT      NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_group_code` (`tenant_id`, `group_code`),
    KEY            `idx_tenant_id` (`tenant_id`),
    KEY            `idx_parent_id` (`parent_id`),
    KEY            `idx_group_type` (`group_type`),
    KEY            `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户组表';

-- ----------------------------
-- 菜单资源表
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu_resource`;
CREATE TABLE `sys_menu_resource`
(
    `id`            BIGINT       NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
    `tenant_id`     BIGINT COMMENT '租户ID(为空表示系统级菜单)',
    `menu_code`     VARCHAR(64)  NOT NULL COMMENT '菜单编码',
    `menu_name`     VARCHAR(128) NOT NULL COMMENT '菜单名称',
    `menu_type`     VARCHAR(32)  NOT NULL COMMENT '菜单类型：catalog,menu,button',
    `parent_id`     BIGINT COMMENT '父菜单ID',
    `level`         INT                   DEFAULT 1 COMMENT '菜单层级',
    `path`          VARCHAR(256) COMMENT '路由路径',
    `component`     VARCHAR(256) COMMENT '组件路径',
    `icon`          VARCHAR(128) COMMENT '菜单图标',
    `sort_order`    INT                   DEFAULT 0 COMMENT '排序',
    `visible`       TINYINT      NOT NULL DEFAULT 1 COMMENT '是否可见：0-隐藏，1-显示',
    `status`        TINYINT      NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `external_link` TINYINT               DEFAULT 0 COMMENT '是否外链：0-否，1-是',
    `cache`         TINYINT               DEFAULT 0 COMMENT '是否缓存：0-否，1-是',
    `description`   VARCHAR(512) COMMENT '菜单描述',
    `created_time`  DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time`  DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by`    BIGINT COMMENT '创建人',
    `updated_by`    BIGINT COMMENT '更新人',
    `deleted`       TINYINT      NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_menu_code` (`menu_code`),
    KEY             `idx_tenant_id` (`tenant_id`),
    KEY             `idx_parent_id` (`parent_id`),
    KEY             `idx_menu_type` (`menu_type`),
    KEY             `idx_status` (`status`),
    KEY             `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜单资源表';

-- ----------------------------
-- API资源表
-- ----------------------------
DROP TABLE IF EXISTS `sys_api_resource`;
CREATE TABLE `sys_api_resource`
(
    `id`             BIGINT       NOT NULL AUTO_INCREMENT COMMENT 'API资源ID',
    `tenant_id`      BIGINT COMMENT '租户ID(为空表示系统级API)',
    `api_code`       VARCHAR(128) NOT NULL COMMENT 'API编码',
    `api_name`       VARCHAR(128) NOT NULL COMMENT 'API名称',
    `api_path`       VARCHAR(256) NOT NULL COMMENT 'API路径',
    `http_method`    VARCHAR(16)  NOT NULL COMMENT 'HTTP方法：GET,POST,PUT,DELETE',
    `resource_group` VARCHAR(64) COMMENT '资源分组',
    `auth_required`  TINYINT      NOT NULL DEFAULT 1 COMMENT '是否需要认证：0-否，1-是',
    `rate_limit`     INT COMMENT '限流配置(次/分钟)',
    `status`         TINYINT      NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `description`    VARCHAR(512) COMMENT 'API描述',
    `created_time`   DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time`   DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by`     BIGINT COMMENT '创建人',
    `updated_by`     BIGINT COMMENT '更新人',
    `deleted`        TINYINT      NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_api_code` (`api_code`),
    UNIQUE KEY `uk_api_path_method` (`api_path`, `http_method`),
    KEY              `idx_tenant_id` (`tenant_id`),
    KEY              `idx_resource_group` (`resource_group`),
    KEY              `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API资源表';
-- </editor-fold>

-- <editor-fold desc="关联关系中间表">
-- ----------------------------
-- 用户角色关联表
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role_relation`;
CREATE TABLE `sys_user_role_relation`
(
    `id`           BIGINT   NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `tenant_id`    BIGINT   NOT NULL COMMENT '租户ID',
    `user_id`      BIGINT   NOT NULL COMMENT '用户ID',
    `role_id`      BIGINT   NOT NULL COMMENT '角色ID',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `created_by`   BIGINT COMMENT '创建人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_role` (`user_id`, `role_id`),
    KEY            `idx_tenant_id` (`tenant_id`),
    KEY            `idx_user_id` (`user_id`),
    KEY            `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- ----------------------------
-- 角色权限关联表
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_permission_relation`;
CREATE TABLE `sys_role_permission_relation`
(
    `id`            BIGINT   NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `tenant_id`     BIGINT   NOT NULL COMMENT '租户ID',
    `role_id`       BIGINT   NOT NULL COMMENT '角色ID',
    `permission_id` BIGINT   NOT NULL COMMENT '权限ID',
    `created_time`  DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `created_by`    BIGINT COMMENT '创建人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`),
    KEY             `idx_tenant_id` (`tenant_id`),
    KEY             `idx_role_id` (`role_id`),
    KEY             `idx_permission_id` (`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- ----------------------------
-- 用户组用户关联表
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_group_user_relation`;
CREATE TABLE `sys_user_group_user_relation`
(
    `id`            BIGINT   NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `tenant_id`     BIGINT   NOT NULL COMMENT '租户ID',
    `user_group_id` BIGINT   NOT NULL COMMENT '用户组ID',
    `user_id`       BIGINT   NOT NULL COMMENT '用户ID',
    `created_time`  DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `created_by`    BIGINT COMMENT '创建人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_group_user` (`user_group_id`, `user_id`),
    KEY             `idx_tenant_id` (`tenant_id`),
    KEY             `idx_user_group_id` (`user_group_id`),
    KEY             `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户组用户关联表';

-- ----------------------------
-- 用户组角色关联表
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_group_role_relation`;
CREATE TABLE `sys_user_group_role_relation`
(
    `id`            BIGINT   NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `tenant_id`     BIGINT   NOT NULL COMMENT '租户ID',
    `user_group_id` BIGINT   NOT NULL COMMENT '用户组ID',
    `role_id`       BIGINT   NOT NULL COMMENT '角色ID',
    `created_time`  DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `created_by`    BIGINT COMMENT '创建人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_group_role` (`user_group_id`, `role_id`),
    KEY             `idx_tenant_id` (`tenant_id`),
    KEY             `idx_user_group_id` (`user_group_id`),
    KEY             `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户组角色关联表';
-- </editor-fold>

-- <editor-fold desc="组织架构管理">

-- ----------------------------
-- 部门表
-- ----------------------------
DROP TABLE IF EXISTS `sys_department`;
CREATE TABLE `sys_department`
(
    `id`           BIGINT       NOT NULL AUTO_INCREMENT COMMENT '部门ID',
    `tenant_id`    BIGINT       NOT NULL COMMENT '租户ID',
    `dept_code`    VARCHAR(64)  NOT NULL COMMENT '部门编码',
    `dept_name`    VARCHAR(128) NOT NULL COMMENT '部门名称',
    `parent_id`    BIGINT COMMENT '父部门ID',
    `level`        INT                   DEFAULT 1 COMMENT '部门层级',
    `dept_path`    VARCHAR(512) COMMENT '部门路径',
    `leader_id`    BIGINT COMMENT '部门负责人ID',
    `phone`        VARCHAR(32) COMMENT '部门电话',
    `email`        VARCHAR(128) COMMENT '部门邮箱',
    `address`      VARCHAR(256) COMMENT '部门地址',
    `sort_order`   INT                   DEFAULT 0 COMMENT '排序',
    `status`       TINYINT      NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `description`  VARCHAR(512) COMMENT '部门描述',
    `created_time` DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by`   BIGINT COMMENT '创建人',
    `updated_by`   BIGINT COMMENT '更新人',
    `deleted`      TINYINT      NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_dept_code` (`tenant_id`, `dept_code`),
    KEY            `idx_tenant_id` (`tenant_id`),
    KEY            `idx_parent_id` (`parent_id`),
    KEY            `idx_leader_id` (`leader_id`),
    KEY            `idx_status` (`status`),
    KEY            `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';

-- ----------------------------
-- 岗位表
-- ----------------------------
DROP TABLE IF EXISTS `sys_position`;
CREATE TABLE `sys_position`
(
    `id`               BIGINT       NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
    `tenant_id`        BIGINT       NOT NULL COMMENT '租户ID',
    `position_code`    VARCHAR(64)  NOT NULL COMMENT '岗位编码',
    `position_name`    VARCHAR(128) NOT NULL COMMENT '岗位名称',
    `position_level`   INT COMMENT '岗位级别',
    `dept_id`          BIGINT COMMENT '所属部门ID',
    `sort_order`       INT                   DEFAULT 0 COMMENT '排序',
    `status`           TINYINT      NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `responsibilities` TEXT COMMENT '岗位职责',
    `requirements`     TEXT COMMENT '任职要求',
    `description`      VARCHAR(512) COMMENT '岗位描述',
    `created_time`     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time`     DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by`       BIGINT COMMENT '创建人',
    `updated_by`       BIGINT COMMENT '更新人',
    `deleted`          TINYINT      NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_position_code` (`tenant_id`, `position_code`),
    KEY                `idx_tenant_id` (`tenant_id`),
    KEY                `idx_dept_id` (`dept_id`),
    KEY                `idx_position_level` (`position_level`),
    KEY                `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='岗位表';

-- ----------------------------
-- 用户部门关联表
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_department_relation`;
CREATE TABLE `sys_user_department_relation`
(
    `id`           BIGINT   NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `tenant_id`    BIGINT   NOT NULL COMMENT '租户ID',
    `user_id`      BIGINT   NOT NULL COMMENT '用户ID',
    `dept_id`      BIGINT   NOT NULL COMMENT '部门ID',
    `is_primary`   TINYINT  NOT NULL DEFAULT 1 COMMENT '是否主部门：0-否，1-是',
    `join_time`    DATETIME COMMENT '加入时间',
    `leave_time`   DATETIME COMMENT '离开时间',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `created_by`   BIGINT COMMENT '创建人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_dept_primary` (`user_id`, `dept_id`, `is_primary`),
    KEY            `idx_tenant_id` (`tenant_id`),
    KEY            `idx_user_id` (`user_id`),
    KEY            `idx_dept_id` (`dept_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户部门关联表';

-- ----------------------------
-- 用户岗位关联表
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_position_relation`;
CREATE TABLE `sys_user_position_relation`
(
    `id`           BIGINT   NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `tenant_id`    BIGINT   NOT NULL COMMENT '租户ID',
    `user_id`      BIGINT   NOT NULL COMMENT '用户ID',
    `position_id`  BIGINT   NOT NULL COMMENT '岗位ID',
    `is_primary`   TINYINT  NOT NULL DEFAULT 1 COMMENT '是否主岗位：0-否，1-是',
    `start_time`   DATETIME COMMENT '开始时间',
    `end_time`     DATETIME COMMENT '结束时间',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `created_by`   BIGINT COMMENT '创建人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_position` (`user_id`, `position_id`),
    KEY            `idx_tenant_id` (`tenant_id`),
    KEY            `idx_user_id` (`user_id`),
    KEY            `idx_position_id` (`position_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户岗位关联表';
-- </editor-fold>

-- <editor-fold desc="系统日志和审计">
-- ----------------------------
-- 操作日志表
-- ----------------------------
DROP TABLE IF EXISTS `sys_operation_log`;
CREATE TABLE `sys_operation_log`
(
    `id`              BIGINT      NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `tenant_id`       BIGINT COMMENT '租户ID',
    `user_id`         BIGINT COMMENT '操作用户ID',
    `username`        VARCHAR(64) COMMENT '操作用户名',
    `operation_type`  VARCHAR(32) NOT NULL COMMENT '操作类型：CREATE,UPDATE,DELETE,LOGIN,LOGOUT',
    `module`          VARCHAR(64) COMMENT '操作模块',
    `business_type`   VARCHAR(64) COMMENT '业务类型',
    `business_id`     BIGINT COMMENT '业务ID',
    `operation_desc`  VARCHAR(512) COMMENT '操作描述',
    `request_method`  VARCHAR(16) COMMENT '请求方法',
    `request_url`     VARCHAR(256) COMMENT '请求URL',
    `request_params`  TEXT COMMENT '请求参数',
    `response_result` TEXT COMMENT '响应结果',
    `ip_address`      VARCHAR(64) COMMENT 'IP地址',
    `user_agent`      VARCHAR(512) COMMENT '用户代理',
    `status`          TINYINT     NOT NULL COMMENT '操作状态：0-失败，1-成功',
    `error_msg`       TEXT COMMENT '错误信息',
    `cost_time`       BIGINT COMMENT '耗时(毫秒)',
    `created_time`    DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY               `idx_tenant_id` (`tenant_id`),
    KEY               `idx_user_id` (`user_id`),
    KEY               `idx_operation_type` (`operation_type`),
    KEY               `idx_module` (`module`),
    KEY               `idx_business_type` (`business_type`),
    KEY               `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- ----------------------------
-- 登录日志表
-- ----------------------------
DROP TABLE IF EXISTS `sys_login_log`;
CREATE TABLE `sys_login_log`
(
    `id`          BIGINT      NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `tenant_id`   BIGINT COMMENT '租户ID',
    `user_id`     BIGINT COMMENT '用户ID',
    `username`    VARCHAR(64) COMMENT '用户名',
    `login_type`  VARCHAR(32) NOT NULL COMMENT '登录类型：password,sms,oauth',
    `ip_address`  VARCHAR(64) COMMENT 'IP地址',
    `location`    VARCHAR(128) COMMENT '登录地点',
    `user_agent`  VARCHAR(512) COMMENT '用户代理',
    `device_type` VARCHAR(32) COMMENT '设备类型：web,mobile,app',
    `status`      TINYINT     NOT NULL COMMENT '登录状态：0-失败，1-成功',
    `fail_reason` VARCHAR(256) COMMENT '失败原因',
    `login_time`  DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
    `logout_time` DATETIME COMMENT '登出时间',
    PRIMARY KEY (`id`),
    KEY           `idx_tenant_id` (`tenant_id`),
    KEY           `idx_user_id` (`user_id`),
    KEY           `idx_username` (`username`),
    KEY           `idx_login_type` (`login_type`),
    KEY           `idx_status` (`status`),
    KEY           `idx_login_time` (`login_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录日志表';
-- </editor-fold>