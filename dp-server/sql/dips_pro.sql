-- ------------------------------------------------------------
-- 会话
-- ------------------------------------------------------------
-- Drop table if it exists (for re-runnability)
DROP TABLE IF EXISTS conversations;

-- Create conversations table
CREATE TABLE conversations (
    id UUID PRIMARY KEY NOT NULL,       -- 主键，UUID 类型
    label VARCHAR(255),                 -- 会话标签/名称，可为空
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(), -- 创建时间
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()  -- 最后更新时间
    -- Add user_id column here if/when multi-user support is added
    -- user_id VARCHAR(32) NOT NULL
);

-- Add comments
COMMENT ON TABLE conversations IS '存储会话的基本信息';
COMMENT ON COLUMN conversations.id IS '会话的唯一标识符 (UUID)';
COMMENT ON COLUMN conversations.label IS '用户自定义的会话标签或自动生成的摘要';
COMMENT ON COLUMN conversations.created_at IS '会话创建时间';
COMMENT ON COLUMN conversations.updated_at IS '会话最后更新时间';

-- ------------------------------------------------------------
-- 会话表结构更新：添加用户ID字段支持多用户会话隔离
-- 执行时间：2024-12-21
-- 说明：实现会话消息按当前登录用户过滤功能
-- ------------------------------------------------------------

-- 给conversations表添加user_id字段
ALTER TABLE conversations ADD COLUMN user_id BIGINT;

-- 添加字段注释
COMMENT ON COLUMN conversations.user_id IS '会话所属用户ID，关联sys_user.id';

-- 创建索引提升查询性能
CREATE INDEX idx_conversations_user_id ON conversations (user_id);
CREATE INDEX idx_conversations_user_created ON conversations (user_id, created_at);

-- 为现有数据设置默认用户ID（可选，用于开发测试）
-- 如果有现有数据且需要分配给特定用户，取消下面注释并修改用户ID
-- UPDATE conversations SET user_id = 1 WHERE user_id IS NULL;

-- ------------------------------------------------------------
-- 会话消息
-- ------------------------------------------------------------
-- Drop table if it exists
DROP TABLE IF EXISTS chat_messages;

-- Create chat_messages table
CREATE TABLE chat_messages (
    id UUID PRIMARY KEY NOT NULL,                -- 主键，UUID 类型
    conversation_id UUID NOT NULL,             -- 会话 ID，UUID 类型，关联 conversations.id
    user_id VARCHAR(32),                       -- 用户 ID，字符串类型，最大长度 32，可为空
    role VARCHAR(10) NOT NULL,                 -- 角色 ('user' or 'assistant')，字符串类型，最大长度 10
    content TEXT NOT NULL,                     -- 消息内容，文本类型
    intermediate_steps_json JSONB NULL,        -- 思维链步骤，JSONB 类型
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(), -- 创建时间，带时区的时间戳，默认为当前时间
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()  -- 最后更新时间，带时区的时间戳，默认为当前时间
);

ALTER TABLE chat_messages ADD COLUMN round_sequence BIGINT;
ALTER TABLE chat_messages ADD COLUMN message_order INTEGER;

-- Add comments to the table and columns
COMMENT ON TABLE chat_messages IS '存储聊天消息记录';
COMMENT ON COLUMN chat_messages.id IS '消息的唯一标识符 (UUID)';
COMMENT ON COLUMN chat_messages.conversation_id IS '所属会话的唯一标识符 (UUID)';
COMMENT ON COLUMN chat_messages.user_id IS '发送消息的用户标识符 (可选)';
COMMENT ON COLUMN chat_messages.role IS '消息发送者的角色 (''user'' 或 ''assistant'')';
COMMENT ON COLUMN chat_messages.content IS '消息的具体内容 (文本)';
COMMENT ON COLUMN chat_messages.intermediate_steps_json IS '助手生成响应时的中间步骤 (JSONB 格式)';
COMMENT ON COLUMN chat_messages.created_at IS '消息创建时间 (带时区)';
COMMENT ON COLUMN chat_messages.updated_at IS '消息最后更新时间 (带时区)';
COMMENT ON COLUMN chat_messages.round_sequence IS '轮次序号：一个用户问题及其对应的所有助手回复构成一个轮次，从1开始递增';
COMMENT ON COLUMN chat_messages.message_order IS '轮次内消息顺序：在同一轮次内，用户消息为1，助手消息按出现顺序递增';

-- Create index on conversation_id for faster lookups
CREATE INDEX idx_conversation_id ON chat_messages (conversation_id);

-- Create index for round sequence
CREATE INDEX idx_chat_messages_conversation_round ON chat_messages(conversation_id, round_sequence);

-- Create index for round sequence and message order
CREATE INDEX idx_chat_messages_conversation_round_order ON chat_messages(conversation_id, round_sequence, message_order);

-- ------------------------------------------------------------
-- 会话模板
-- ------------------------------------------------------------
-- Drop table if it exists (optional, for easier development iteration)
DROP TABLE IF EXISTS prompt_templates;

-- Create the prompt_templates table
CREATE TABLE prompt_templates
(
    id               UUID PRIMARY KEY      DEFAULT gen_random_uuid(),
    name             VARCHAR(255) NOT NULL,
    description      TEXT,
    agent_type       VARCHAR(100),          -- Can be used to categorize templates by the agent they target
    template_content TEXT,                  -- Optional: The template string with placeholders like {{slot_name}}
    slot_definitions JSONB        NOT NULL, -- Stores the array of SlotDefinition objects as JSON
    created_at       TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
    updated_at       TIMESTAMPTZ  NOT NULL DEFAULT NOW()
);

-- Add comments to the table and columns
COMMENT ON TABLE prompt_templates IS '存储可重用的提示词模板及其槽位定义';
COMMENT ON COLUMN prompt_templates.id IS '提示词模板的唯一标识符 (UUID)';
COMMENT ON COLUMN prompt_templates.name IS '用户友好的模板名称';
COMMENT ON COLUMN prompt_templates.description IS '模板用途的详细描述';
COMMENT ON COLUMN prompt_templates.agent_type IS '此模板适用的 Agent 或 Agent 类型标识符';
COMMENT ON COLUMN prompt_templates.template_content IS '带有占位符的原始模板文本 (可选，主要用于显示/参考)';
COMMENT ON COLUMN prompt_templates.slot_definitions IS '包含每个槽位定义的 JSONB 数组 (名称、标签、类型、是否必需、选项等)';
COMMENT ON COLUMN prompt_templates.created_at IS '模板创建的时间戳 (带时区)';
COMMENT ON COLUMN prompt_templates.updated_at IS '模板最后更新的时间戳 (带时区)';

-- Create indexes for faster querying
CREATE INDEX idx_prompt_templates_agent_type ON prompt_templates (agent_type);

-- Trigger function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
RETURN NEW;
END;
$$ language 'plpgsql';

-- Drop trigger if it exists before creating
DROP TRIGGER IF EXISTS update_prompt_templates_updated_at ON prompt_templates;
-- Trigger to call the function before any update on prompt_templates
CREATE TRIGGER update_prompt_templates_updated_at
    BEFORE UPDATE ON prompt_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Drop trigger if it exists before creating
DROP TRIGGER IF EXISTS update_conversations_updated_at ON conversations;
-- Trigger to call the function before any update on conversations
CREATE TRIGGER update_conversations_updated_at
    BEFORE UPDATE ON conversations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Drop trigger if it exists before creating
DROP TRIGGER IF EXISTS update_chat_messages_updated_at ON chat_messages;
-- Trigger to call the function before any update on chat_messages
CREATE TRIGGER update_chat_messages_updated_at
    BEFORE UPDATE ON chat_messages
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ------------------------------------------------------------
-- 提示词模板关联关系
-- ------------------------------------------------------------
-- Drop table if it exists
DROP TABLE IF EXISTS prompt_template_relations;

-- Create the prompt_template_relations table
CREATE TABLE prompt_template_relations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    source_template_id UUID NOT NULL REFERENCES prompt_templates(id) ON DELETE CASCADE,
    target_template_id UUID NOT NULL REFERENCES prompt_templates(id) ON DELETE CASCADE,
    priority INTEGER NOT NULL DEFAULT 0, -- 优先级，数字越小优先级越高
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(source_template_id, target_template_id) -- 防止重复关联
);

-- Add comments to the table and columns
COMMENT ON TABLE prompt_template_relations IS '存储提示词模板之间的关联关系';
COMMENT ON COLUMN prompt_template_relations.id IS '关联关系的唯一标识符 (UUID)';
COMMENT ON COLUMN prompt_template_relations.source_template_id IS '源模板的唯一标识符 (UUID)';
COMMENT ON COLUMN prompt_template_relations.target_template_id IS '目标模板的唯一标识符 (UUID)';
COMMENT ON COLUMN prompt_template_relations.priority IS '关联关系的优先级，数字越小优先级越高';
COMMENT ON COLUMN prompt_template_relations.created_at IS '关联关系创建时间 (带时区)';
COMMENT ON COLUMN prompt_template_relations.updated_at IS '关联关系最后更新时间 (带时区)';

-- Create indexes for faster querying
CREATE INDEX idx_prompt_template_relations_source ON prompt_template_relations (source_template_id);
CREATE INDEX idx_prompt_template_relations_target ON prompt_template_relations (target_template_id);
CREATE INDEX idx_prompt_template_relations_priority ON prompt_template_relations (source_template_id, priority);

-- Add trigger for updated_at
DROP TRIGGER IF EXISTS update_prompt_template_relations_updated_at ON prompt_template_relations;
CREATE TRIGGER update_prompt_template_relations_updated_at
    BEFORE UPDATE ON prompt_template_relations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ------------------------------------------------------------
-- 计费模块表结构
-- ------------------------------------------------------------

-- ------------------------------------------------------------
-- 计费套餐配置表
-- ------------------------------------------------------------
-- Drop table if it exists
DROP TABLE IF EXISTS b_billing_packages CASCADE;

-- Create billing packages table
CREATE TABLE b_billing_packages (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL, -- 套餐名称
    description TEXT, -- 套餐描述
    input_token_price DECIMAL(10,6) NOT NULL, -- 输入Token单价（每千Token价格）
    output_token_price DECIMAL(10,6) NOT NULL, -- 输出Token单价（每千Token价格）
    free_tokens BIGINT DEFAULT 0, -- 新用户免费Token数量
    max_tokens_per_request BIGINT DEFAULT 0, -- 单次请求最大Token限制，0表示无限制
    daily_token_limit BIGINT DEFAULT 0, -- 每日Token使用限制，0表示无限制
    is_active BOOLEAN DEFAULT true, -- 是否激活
    sort_order INTEGER DEFAULT 0, -- 排序权重
    is_default BOOLEAN DEFAULT false, -- 是否为默认套餐
    features TEXT, -- 套餐特性描述
    limitations TEXT, -- 套餐限制说明
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX idx_b_billing_packages_active_sort ON b_billing_packages (is_active, sort_order);
CREATE INDEX idx_b_billing_packages_created_at ON b_billing_packages (created_at);

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS update_b_billing_packages_updated_at ON b_billing_packages;
CREATE TRIGGER update_b_billing_packages_updated_at
    BEFORE UPDATE ON b_billing_packages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add table comments
COMMENT ON TABLE b_billing_packages IS '计费套餐配置表';
COMMENT ON COLUMN b_billing_packages.id IS '主键ID';
COMMENT ON COLUMN b_billing_packages.name IS '套餐名称';
COMMENT ON COLUMN b_billing_packages.description IS '套餐描述';
COMMENT ON COLUMN b_billing_packages.input_token_price IS '输入Token单价（每千Token价格）';
COMMENT ON COLUMN b_billing_packages.output_token_price IS '输出Token单价（每千Token价格）';
COMMENT ON COLUMN b_billing_packages.free_tokens IS '新用户免费Token数量';
COMMENT ON COLUMN b_billing_packages.max_tokens_per_request IS '单次请求最大Token限制，0表示无限制';
COMMENT ON COLUMN b_billing_packages.daily_token_limit IS '每日Token使用限制，0表示无限制';
COMMENT ON COLUMN b_billing_packages.is_active IS '是否激活';
COMMENT ON COLUMN b_billing_packages.sort_order IS '排序权重';
COMMENT ON COLUMN b_billing_packages.is_default IS '是否为默认套餐';
COMMENT ON COLUMN b_billing_packages.features IS '套餐特性描述';
COMMENT ON COLUMN b_billing_packages.limitations IS '套餐限制说明';
COMMENT ON COLUMN b_billing_packages.created_at IS '创建时间';
COMMENT ON COLUMN b_billing_packages.updated_at IS '更新时间';

-- ------------------------------------------------------------
-- 用户余额表
-- ------------------------------------------------------------
-- Drop table if it exists
DROP TABLE IF EXISTS b_user_balances CASCADE;

-- Create user balances table
CREATE TABLE b_user_balances (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL, -- 用户ID，关联users.id
    package_id BIGINT, -- 当前套餐ID，关联b_billing_packages.id
    recharged_balance DECIMAL(15,2) DEFAULT 0, -- 充值余额（人民币）
    gift_balance DECIMAL(15,2) DEFAULT 0, -- 赠送余额（人民币）
    free_tokens BIGINT DEFAULT 0, -- 免费Token余额
    used_tokens_today BIGINT DEFAULT 0, -- 今日已使用Token数
    last_reset_date DATE, -- 上次重置日期
    total_recharged DECIMAL(15,2) DEFAULT 0, -- 累计充值金额
    total_consumed DECIMAL(15,2) DEFAULT 0, -- 累计消费金额
    frozen_balance DECIMAL(15,2) DEFAULT 0, -- 冻结余额
    last_transaction_at TIMESTAMP WITH TIME ZONE, -- 最后交易时间
    is_frozen BOOLEAN DEFAULT false, -- 账户是否被冻结
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create unique constraint and indexes
ALTER TABLE b_user_balances ADD CONSTRAINT uk_b_user_balances_user_id UNIQUE (user_id);
CREATE INDEX idx_b_user_balances_package_id ON b_user_balances (package_id);
CREATE INDEX idx_b_user_balances_updated_at ON b_user_balances (updated_at);

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS update_b_user_balances_updated_at ON b_user_balances;
CREATE TRIGGER update_b_user_balances_updated_at
    BEFORE UPDATE ON b_user_balances
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add table comments
COMMENT ON TABLE b_user_balances IS '用户余额表';
COMMENT ON COLUMN b_user_balances.id IS '主键ID';
COMMENT ON COLUMN b_user_balances.user_id IS '用户ID，关联users.id';
COMMENT ON COLUMN b_user_balances.package_id IS '当前套餐ID，关联b_billing_packages.id';
COMMENT ON COLUMN b_user_balances.recharged_balance IS '充值余额（人民币）';
COMMENT ON COLUMN b_user_balances.gift_balance IS '赠送余额（人民币）';
COMMENT ON COLUMN b_user_balances.free_tokens IS '免费Token余额';
COMMENT ON COLUMN b_user_balances.used_tokens_today IS '今日已使用Token数';
COMMENT ON COLUMN b_user_balances.last_reset_date IS '上次重置日期';
COMMENT ON COLUMN b_user_balances.total_recharged IS '累计充值金额';
COMMENT ON COLUMN b_user_balances.total_consumed IS '累计消费金额';
COMMENT ON COLUMN b_user_balances.frozen_balance IS '冻结余额';
COMMENT ON COLUMN b_user_balances.last_transaction_at IS '最后交易时间';
COMMENT ON COLUMN b_user_balances.is_frozen IS '账户是否被冻结';
COMMENT ON COLUMN b_user_balances.created_at IS '创建时间';
COMMENT ON COLUMN b_user_balances.updated_at IS '更新时间';

-- ------------------------------------------------------------
-- 计费使用记录表
-- ------------------------------------------------------------
-- Drop table if it exists
DROP TABLE IF EXISTS b_billing_usage_records CASCADE;

-- Create billing usage records table
CREATE TABLE b_billing_usage_records (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL, -- 用户ID，关联users.id
    message_id UUID NOT NULL, -- 消息ID，关联chat_messages.id
    conversation_id UUID, -- 对话ID
    message_role VARCHAR(20), -- 消息角色：USER, ASSISTANT
    model_name VARCHAR(50) NOT NULL, -- 使用的模型名称

    -- Token统计
    input_tokens BIGINT NOT NULL DEFAULT 0, -- 输入Token数量
    output_tokens BIGINT NOT NULL DEFAULT 0, -- 输出Token数量
    thought_chain_tokens BIGINT DEFAULT 0, -- 思维链Token数量
    total_tokens BIGINT NOT NULL DEFAULT 0, -- 总Token数量

    -- 费用计算
    input_cost DECIMAL(10,6) DEFAULT 0, -- 输入费用
    output_cost DECIMAL(10,6) DEFAULT 0, -- 输出费用
    total_cost DECIMAL(10,6) NOT NULL DEFAULT 0, -- 总费用

    -- 状态管理
    status VARCHAR(20) DEFAULT 'SUCCESS', -- 状态：SUCCESS, FAILED, PENDING
    billing_status VARCHAR(20) DEFAULT 'BILLED', -- 计费状态：BILLED, UNBILLED, REFUNDED

    -- 套餐信息
    package_id BIGINT, -- 使用的套餐ID，关联b_billing_packages.id
    input_token_price DECIMAL(10,6), -- 计费时的输入Token单价
    output_token_price DECIMAL(10,6), -- 计费时的输出Token单价

    -- 申诉相关
    is_appealed BOOLEAN DEFAULT false, -- 是否已申诉
    appeal_status VARCHAR(20), -- 申诉状态：PENDING, APPROVED, REJECTED

    -- 时间字段
    request_time TIMESTAMP WITH TIME ZONE, -- 请求时间
    response_time TIMESTAMP WITH TIME ZONE, -- 响应时间
    duration_ms BIGINT, -- 处理时长（毫秒）
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX idx_b_billing_usage_records_user_created ON b_billing_usage_records (user_id, created_at);
CREATE INDEX idx_b_billing_usage_records_message_id ON b_billing_usage_records (message_id);
CREATE INDEX idx_b_billing_usage_records_conversation_id ON b_billing_usage_records (conversation_id);
CREATE INDEX idx_b_billing_usage_records_message_role ON b_billing_usage_records (message_role);
CREATE INDEX idx_b_billing_usage_records_status ON b_billing_usage_records (status);
CREATE INDEX idx_b_billing_usage_records_billing_status ON b_billing_usage_records (billing_status);
CREATE INDEX idx_b_billing_usage_records_appeal ON b_billing_usage_records (is_appealed, appeal_status);
CREATE INDEX idx_b_billing_usage_records_package_id ON b_billing_usage_records (package_id);
CREATE INDEX idx_b_billing_usage_records_created_at ON b_billing_usage_records (created_at);

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS update_b_billing_usage_records_updated_at ON b_billing_usage_records;
CREATE TRIGGER update_b_billing_usage_records_updated_at
    BEFORE UPDATE ON b_billing_usage_records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add table comments
COMMENT ON TABLE b_billing_usage_records IS '计费使用记录表（以消息为单位）';
COMMENT ON COLUMN b_billing_usage_records.id IS '主键ID';
COMMENT ON COLUMN b_billing_usage_records.user_id IS '用户ID，关联users.id';
COMMENT ON COLUMN b_billing_usage_records.message_id IS '消息ID，关联chat_messages.id（消息内容通过此ID关联获取）';
COMMENT ON COLUMN b_billing_usage_records.conversation_id IS '对话ID';
COMMENT ON COLUMN b_billing_usage_records.message_role IS '消息角色：USER, ASSISTANT';
COMMENT ON COLUMN b_billing_usage_records.model_name IS '使用的模型名称';
COMMENT ON COLUMN b_billing_usage_records.input_tokens IS '输入Token数量';
COMMENT ON COLUMN b_billing_usage_records.output_tokens IS '输出Token数量';
COMMENT ON COLUMN b_billing_usage_records.thought_chain_tokens IS '思维链Token数量';
COMMENT ON COLUMN b_billing_usage_records.total_tokens IS '总Token数量';
COMMENT ON COLUMN b_billing_usage_records.input_cost IS '输入费用';
COMMENT ON COLUMN b_billing_usage_records.output_cost IS '输出费用';
COMMENT ON COLUMN b_billing_usage_records.total_cost IS '总费用';
COMMENT ON COLUMN b_billing_usage_records.status IS '状态：SUCCESS, FAILED, PENDING';
COMMENT ON COLUMN b_billing_usage_records.billing_status IS '计费状态：BILLED, UNBILLED, REFUNDED';
COMMENT ON COLUMN b_billing_usage_records.package_id IS '使用的套餐ID，关联b_billing_packages.id';
COMMENT ON COLUMN b_billing_usage_records.input_token_price IS '计费时的输入Token单价';
COMMENT ON COLUMN b_billing_usage_records.output_token_price IS '计费时的输出Token单价';
COMMENT ON COLUMN b_billing_usage_records.is_appealed IS '是否已申诉';
COMMENT ON COLUMN b_billing_usage_records.appeal_status IS '申诉状态：PENDING, APPROVED, REJECTED';
COMMENT ON COLUMN b_billing_usage_records.request_time IS '请求时间';
COMMENT ON COLUMN b_billing_usage_records.response_time IS '响应时间';
COMMENT ON COLUMN b_billing_usage_records.duration_ms IS '处理时长（毫秒）';
COMMENT ON COLUMN b_billing_usage_records.created_at IS '创建时间';
COMMENT ON COLUMN b_billing_usage_records.updated_at IS '更新时间';

-- ------------------------------------------------------------
-- 计费交易记录表
-- ------------------------------------------------------------
-- Drop table if it exists
DROP TABLE IF EXISTS b_billing_transactions CASCADE;

-- Create billing transactions table
CREATE TABLE b_billing_transactions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL, -- 用户ID，关联users.id
    transaction_no VARCHAR(100) NOT NULL, -- 交易流水号
    external_transaction_id VARCHAR(100), -- 外部交易ID
    type VARCHAR(20) NOT NULL, -- 交易类型：RECHARGE, DEDUCT, REFUND, GIFT
    amount DECIMAL(15,2) NOT NULL, -- 交易金额（正数为收入，负数为支出）
    balance_type VARCHAR(20) NOT NULL, -- 余额类型：RECHARGED, GIFT, FREE_TOKENS
    status VARCHAR(20) DEFAULT 'SUCCESS', -- 交易状态：PENDING, SUCCESS, FAILED, CANCELLED
    related_id BIGINT, -- 关联记录ID（通用）
    related_type VARCHAR(50), -- 关联记录类型
    payment_method VARCHAR(50), -- 支付方式

    -- 关联记录
    usage_record_id BIGINT, -- 关联的使用记录ID，关联b_billing_usage_records.id
    payment_record_id BIGINT, -- 关联的支付记录ID，关联b_payment_records.id
    appeal_id BIGINT, -- 关联的申诉记录ID，关联b_billing_appeals.id

    -- 余额快照
    balance_before DECIMAL(15,2), -- 交易前余额
    balance_after DECIMAL(15,2), -- 交易后余额

    description TEXT, -- 交易描述
    operator_id BIGINT, -- 操作员ID（管理员操作时），关联users.id

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create unique constraint and indexes
ALTER TABLE b_billing_transactions ADD CONSTRAINT uk_b_billing_transactions_transaction_no UNIQUE (transaction_no);
CREATE INDEX idx_b_billing_transactions_user_created ON b_billing_transactions (user_id, created_at);
CREATE INDEX idx_b_billing_transactions_type ON b_billing_transactions (type);
CREATE INDEX idx_b_billing_transactions_usage_record ON b_billing_transactions (usage_record_id);
CREATE INDEX idx_b_billing_transactions_payment_record ON b_billing_transactions (payment_record_id);
CREATE INDEX idx_b_billing_transactions_appeal_id ON b_billing_transactions (appeal_id);
CREATE INDEX idx_b_billing_transactions_operator_id ON b_billing_transactions (operator_id);
CREATE INDEX idx_b_billing_transactions_created_at ON b_billing_transactions (created_at);

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS update_b_billing_transactions_updated_at ON b_billing_transactions;
CREATE TRIGGER update_b_billing_transactions_updated_at
    BEFORE UPDATE ON b_billing_transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add table comments
COMMENT ON TABLE b_billing_transactions IS '计费交易记录表';
COMMENT ON COLUMN b_billing_transactions.id IS '主键ID';
COMMENT ON COLUMN b_billing_transactions.user_id IS '用户ID，关联users.id';
COMMENT ON COLUMN b_billing_transactions.transaction_no IS '交易流水号';
COMMENT ON COLUMN b_billing_transactions.external_transaction_id IS '外部交易ID';
COMMENT ON COLUMN b_billing_transactions.type IS '交易类型：RECHARGE, DEDUCT, REFUND, GIFT';
COMMENT ON COLUMN b_billing_transactions.amount IS '交易金额（正数为收入，负数为支出）';
COMMENT ON COLUMN b_billing_transactions.balance_type IS '余额类型：RECHARGED, GIFT, FREE_TOKENS';
COMMENT ON COLUMN b_billing_transactions.status IS '交易状态：PENDING, SUCCESS, FAILED, CANCELLED';
COMMENT ON COLUMN b_billing_transactions.related_id IS '关联记录ID（通用）';
COMMENT ON COLUMN b_billing_transactions.related_type IS '关联记录类型';
COMMENT ON COLUMN b_billing_transactions.payment_method IS '支付方式';
COMMENT ON COLUMN b_billing_transactions.usage_record_id IS '关联的使用记录ID，关联b_billing_usage_records.id';
COMMENT ON COLUMN b_billing_transactions.payment_record_id IS '关联的支付记录ID，关联b_payment_records.id';
COMMENT ON COLUMN b_billing_transactions.appeal_id IS '关联的申诉记录ID，关联b_billing_appeals.id';
COMMENT ON COLUMN b_billing_transactions.balance_before IS '交易前余额';
COMMENT ON COLUMN b_billing_transactions.balance_after IS '交易后余额';
COMMENT ON COLUMN b_billing_transactions.description IS '交易描述';
COMMENT ON COLUMN b_billing_transactions.operator_id IS '操作员ID（管理员操作时），关联users.id';
COMMENT ON COLUMN b_billing_transactions.created_at IS '创建时间';
COMMENT ON COLUMN b_billing_transactions.updated_at IS '更新时间';

-- ------------------------------------------------------------
-- 计费申诉记录表
-- ------------------------------------------------------------
-- Drop table if it exists
DROP TABLE IF EXISTS b_billing_appeals CASCADE;

-- Create billing appeals table
CREATE TABLE b_billing_appeals (
    id BIGSERIAL PRIMARY KEY,
    appeal_no VARCHAR(100) NOT NULL, -- 申诉单号
    user_id BIGINT NOT NULL, -- 申诉用户ID，关联users.id
    usage_record_id BIGINT NOT NULL, -- 申诉的使用记录ID，关联b_billing_usage_records.id

    -- 申诉内容
    reason TEXT NOT NULL, -- 申诉原因
    user_description TEXT, -- 用户详细描述
    evidence_urls TEXT, -- 证据文件URLs（JSON数组）

    -- 处理信息
    admin_id BIGINT, -- 处理管理员ID，关联users.id
    status VARCHAR(20) DEFAULT 'PENDING', -- 申诉状态：PENDING, APPROVED, REJECTED, CANCELLED
    admin_comment TEXT, -- 管理员处理意见

    -- 退费信息
    refund_amount DECIMAL(10,6), -- 退费金额
    refund_transaction_id BIGINT, -- 退费交易记录ID，关联b_billing_transactions.id

    -- 时间字段
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 提交时间
    processed_at TIMESTAMP WITH TIME ZONE, -- 处理时间
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create unique constraints and indexes
ALTER TABLE b_billing_appeals ADD CONSTRAINT uk_b_billing_appeals_appeal_no UNIQUE (appeal_no);
ALTER TABLE b_billing_appeals ADD CONSTRAINT uk_b_billing_appeals_usage_record UNIQUE (usage_record_id);
CREATE INDEX idx_b_billing_appeals_user_status ON b_billing_appeals (user_id, status);
CREATE INDEX idx_b_billing_appeals_admin_status ON b_billing_appeals (admin_id, status);
CREATE INDEX idx_b_billing_appeals_status_submitted ON b_billing_appeals (status, submitted_at);
CREATE INDEX idx_b_billing_appeals_refund_transaction ON b_billing_appeals (refund_transaction_id);
CREATE INDEX idx_b_billing_appeals_created_at ON b_billing_appeals (created_at);

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS update_b_billing_appeals_updated_at ON b_billing_appeals;
CREATE TRIGGER update_b_billing_appeals_updated_at
    BEFORE UPDATE ON b_billing_appeals
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add table comments
COMMENT ON TABLE b_billing_appeals IS '计费申诉记录表';
COMMENT ON COLUMN b_billing_appeals.id IS '主键ID';
COMMENT ON COLUMN b_billing_appeals.appeal_no IS '申诉单号';
COMMENT ON COLUMN b_billing_appeals.user_id IS '申诉用户ID，关联users.id';
COMMENT ON COLUMN b_billing_appeals.usage_record_id IS '申诉的使用记录ID，关联b_billing_usage_records.id';
COMMENT ON COLUMN b_billing_appeals.reason IS '申诉原因';
COMMENT ON COLUMN b_billing_appeals.user_description IS '用户详细描述';
COMMENT ON COLUMN b_billing_appeals.evidence_urls IS '证据文件URLs（JSON数组）';
COMMENT ON COLUMN b_billing_appeals.admin_id IS '处理管理员ID，关联users.id';
COMMENT ON COLUMN b_billing_appeals.status IS '申诉状态：PENDING, APPROVED, REJECTED, CANCELLED';
COMMENT ON COLUMN b_billing_appeals.admin_comment IS '管理员处理意见';
COMMENT ON COLUMN b_billing_appeals.refund_amount IS '退费金额';
COMMENT ON COLUMN b_billing_appeals.refund_transaction_id IS '退费交易记录ID，关联b_billing_transactions.id';
COMMENT ON COLUMN b_billing_appeals.submitted_at IS '提交时间';
COMMENT ON COLUMN b_billing_appeals.processed_at IS '处理时间';
COMMENT ON COLUMN b_billing_appeals.created_at IS '创建时间';
COMMENT ON COLUMN b_billing_appeals.updated_at IS '更新时间';

-- ------------------------------------------------------------
-- 支付记录表
-- ------------------------------------------------------------
-- Drop table if it exists
DROP TABLE IF EXISTS b_payment_records CASCADE;

-- Create payment records table
CREATE TABLE b_payment_records (
    id BIGSERIAL PRIMARY KEY,
    payment_no VARCHAR(100) NOT NULL, -- 支付单号
    user_id BIGINT NOT NULL, -- 用户ID，关联users.id

    -- 支付信息
    payment_method VARCHAR(20) NOT NULL, -- 支付方式：WECHAT, ALIPAY, ADMIN
    payment_platform VARCHAR(20), -- 支付平台：WECHAT_PAY, ALIPAY
    platform_order_no VARCHAR(100), -- 第三方平台订单号
    platform_transaction_no VARCHAR(100), -- 第三方平台交易号

    -- 金额信息
    amount DECIMAL(15,2) NOT NULL, -- 支付金额
    currency VARCHAR(3) DEFAULT 'CNY', -- 货币代码

    -- 状态信息
    status VARCHAR(20) DEFAULT 'PENDING', -- 支付状态：PENDING, SUCCESS, FAILED, CANCELLED, REFUNDED
    failure_reason TEXT, -- 失败原因
    cancel_reason TEXT, -- 取消原因
    refund_amount DECIMAL(15,2), -- 退款金额
    refund_time TIMESTAMP WITH TIME ZONE, -- 退款时间
    refund_reason TEXT, -- 退款原因

    -- 回调数据
    callback_data TEXT, -- 支付平台回调数据
    callback_time TIMESTAMP WITH TIME ZONE, -- 回调时间

    -- 客户端信息
    client_ip VARCHAR(45), -- 客户端IP（使用字符串存储以兼容IPv4和IPv6）
    user_agent TEXT, -- 用户代理

    -- 时间字段
    expired_at TIMESTAMP WITH TIME ZONE, -- 支付过期时间
    paid_at TIMESTAMP WITH TIME ZONE, -- 支付完成时间
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create unique constraints and indexes
ALTER TABLE b_payment_records ADD CONSTRAINT uk_b_payment_records_payment_no UNIQUE (payment_no);
ALTER TABLE b_payment_records ADD CONSTRAINT uk_b_payment_records_platform_order_no UNIQUE (platform_order_no);
CREATE INDEX idx_b_payment_records_user_status ON b_payment_records (user_id, status);
CREATE INDEX idx_b_payment_records_status_created ON b_payment_records (status, created_at);
CREATE INDEX idx_b_payment_records_platform_order ON b_payment_records (platform_order_no);
CREATE INDEX idx_b_payment_records_platform_transaction ON b_payment_records (platform_transaction_no);
CREATE INDEX idx_b_payment_records_created_at ON b_payment_records (created_at);

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS update_b_payment_records_updated_at ON b_payment_records;
CREATE TRIGGER update_b_payment_records_updated_at
    BEFORE UPDATE ON b_payment_records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add table comments
COMMENT ON TABLE b_payment_records IS '支付记录表';
COMMENT ON COLUMN b_payment_records.id IS '主键ID';
COMMENT ON COLUMN b_payment_records.payment_no IS '支付单号';
COMMENT ON COLUMN b_payment_records.user_id IS '用户ID，关联users.id';
COMMENT ON COLUMN b_payment_records.payment_method IS '支付方式：WECHAT, ALIPAY, ADMIN';
COMMENT ON COLUMN b_payment_records.payment_platform IS '支付平台：WECHAT_PAY, ALIPAY';
COMMENT ON COLUMN b_payment_records.platform_order_no IS '第三方平台订单号';
COMMENT ON COLUMN b_payment_records.platform_transaction_no IS '第三方平台交易号';
COMMENT ON COLUMN b_payment_records.amount IS '支付金额';
COMMENT ON COLUMN b_payment_records.currency IS '货币代码';
COMMENT ON COLUMN b_payment_records.status IS '支付状态：PENDING, SUCCESS, FAILED, CANCELLED, REFUNDED';
COMMENT ON COLUMN b_payment_records.failure_reason IS '失败原因';
COMMENT ON COLUMN b_payment_records.cancel_reason IS '取消原因';
COMMENT ON COLUMN b_payment_records.refund_amount IS '退款金额';
COMMENT ON COLUMN b_payment_records.refund_time IS '退款时间';
COMMENT ON COLUMN b_payment_records.refund_reason IS '退款原因';
COMMENT ON COLUMN b_payment_records.callback_data IS '支付平台回调数据';
COMMENT ON COLUMN b_payment_records.callback_time IS '回调时间';
COMMENT ON COLUMN b_payment_records.client_ip IS '客户端IP（使用字符串存储以兼容IPv4和IPv6）';
COMMENT ON COLUMN b_payment_records.user_agent IS '用户代理';
COMMENT ON COLUMN b_payment_records.expired_at IS '支付过期时间';
COMMENT ON COLUMN b_payment_records.paid_at IS '支付完成时间';
COMMENT ON COLUMN b_payment_records.created_at IS '创建时间';
COMMENT ON COLUMN b_payment_records.updated_at IS '更新时间';

-- ------------------------------------------------------------
-- 外键约束
-- ------------------------------------------------------------
-- 用户余额表外键
ALTER TABLE b_user_balances ADD CONSTRAINT fk_b_user_balances_package_id 
    FOREIGN KEY (package_id) REFERENCES b_billing_packages(id);

-- 使用记录表外键
ALTER TABLE b_billing_usage_records ADD CONSTRAINT fk_b_billing_usage_records_message_id 
    FOREIGN KEY (message_id) REFERENCES chat_messages(id);
ALTER TABLE b_billing_usage_records ADD CONSTRAINT fk_b_billing_usage_records_package_id 
    FOREIGN KEY (package_id) REFERENCES b_billing_packages(id);

-- 交易记录表外键
ALTER TABLE b_billing_transactions ADD CONSTRAINT fk_b_billing_transactions_usage_record_id 
    FOREIGN KEY (usage_record_id) REFERENCES b_billing_usage_records(id);
ALTER TABLE b_billing_transactions ADD CONSTRAINT fk_b_billing_transactions_payment_record_id 
    FOREIGN KEY (payment_record_id) REFERENCES b_payment_records(id);
ALTER TABLE b_billing_transactions ADD CONSTRAINT fk_b_billing_transactions_appeal_id 
    FOREIGN KEY (appeal_id) REFERENCES b_billing_appeals(id);

-- 申诉记录表外键
ALTER TABLE b_billing_appeals ADD CONSTRAINT fk_b_billing_appeals_usage_record_id 
    FOREIGN KEY (usage_record_id) REFERENCES b_billing_usage_records(id);
ALTER TABLE b_billing_appeals ADD CONSTRAINT fk_b_billing_appeals_refund_transaction_id 
    FOREIGN KEY (refund_transaction_id) REFERENCES b_billing_transactions(id);

-- ------------------------------------------------------------
-- 初始化默认数据
-- ------------------------------------------------------------
-- 插入默认套餐配置
INSERT INTO b_billing_packages (name, description, input_token_price, output_token_price, free_tokens, max_tokens_per_request, daily_token_limit, is_active, sort_order)
VALUES 
    ('免费套餐', '新用户免费使用套餐，包含10,000免费Token', 0.002000, 0.006000, 10000, 8000, 50000, true, 1),
    ('基础套餐', '适合个人用户的基础套餐', 0.001500, 0.004500, 5000, 16000, 100000, true, 2),
    ('专业套餐', '适合专业用户的高级套餐', 0.001200, 0.003600, 10000, 32000, 500000, true, 3),
    ('企业套餐', '适合企业用户的无限制套餐', 0.001000, 0.003000, 50000, 0, 0, true, 4)
ON CONFLICT DO NOTHING;

-- ------------------------------------------------------------
-- 提示词模板向量
-- ------------------------------------------------------------
-- Drop table if it exists
DROP TABLE IF EXISTS prompt_template_vectors;
-- Create the prompt_template_vectors table
CREATE TABLE prompt_template_vectors (
    _id             SERIAL UNIQUE NOT NULL, -- _id INT GENERATED BY DEFAULT AS IDENTITY UNIQUE NOT NULL
    id              UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    template_id     VARCHAR(64),
    typical_description VARCHAR(255),
    embedding       VECTOR(768),
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by      VARCHAR(64),
    updated_by      VARCHAR(64)
);

-- Add comments to the table and columns
COMMENT ON TABLE prompt_template_vectors IS '存储提示词模板的向量';
COMMENT ON COLUMN prompt_template_vectors._id IS '自增数字ID用于分批、排序';
COMMENT ON COLUMN prompt_template_vectors.id IS '向量的唯一标识符 (UUID)';
COMMENT ON COLUMN prompt_template_vectors.template_id IS '关联的模板 ID';
COMMENT ON COLUMN prompt_template_vectors.typical_description IS '模板的典型描述';
COMMENT ON COLUMN prompt_template_vectors.embedding IS '模板的向量表示';
COMMENT ON COLUMN prompt_template_vectors.created_at IS '向量创建时间';
COMMENT ON COLUMN prompt_template_vectors.updated_at IS '向量最后更新时间';
COMMENT ON COLUMN prompt_template_vectors.created_by IS '创建向量的用户';
COMMENT ON COLUMN prompt_template_vectors.updated_by IS '最后更新向量的用户';