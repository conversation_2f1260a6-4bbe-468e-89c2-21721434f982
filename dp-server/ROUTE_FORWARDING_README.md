# 路由转发功能说明

## 概述

本项目实现了基于Spring MVC的路由转发功能，支持多种认证方式的API服务转发。采用WebClient作为HTTP客户端，具有轻量级、高性能、易扩展的特点。

## 功能特性

- ✅ **多种认证方式支持**：NONE（无认证）、BASIC（基础认证）、TOKEN（令牌认证）、JWT（当前用户JWT）
- ✅ **路径重写**：支持灵活的路径重写规则
- ✅ **连接池管理**：优化的WebClient连接池配置
- ✅ **健康检查**：服务健康状态监控和缓存
- ✅ **统计监控**：路由服务统计信息
- ✅ **配置管理**：动态配置和管理界面
- ✅ **错误处理**：完善的错误处理和日志记录

## 架构设计

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   客户端请求     │───▶│   RouteController │───▶│   RouteService   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │  SecurityConfig   │    │   WebClient     │
                       └──────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │   目标服务       │
                                               └─────────────────┘
```

## 配置说明

### 1. 路由配置文件 (`application-route.yml`)

```yaml
route:
  services:
    # 服务名称
    service_name:
      enabled: true                    # 是否启用
      base-url: http://localhost:8081  # 目标服务地址
      auth-type: BASIC                 # 认证类型
      username: user                   # Basic认证用户名
      password: pass                   # Basic认证密码
      connect-timeout: 30000           # 连接超时(毫秒)
      read-timeout: 60000              # 读取超时(毫秒)
      path-rewrite:                    # 路径重写规则
        "^/api/route/service_name(/.*)": "$1"
```

### 2. 认证类型说明

| 认证类型 | 说明 | 配置项 |
|---------|------|--------|
| NONE | 无需认证 | 无 |
| BASIC | HTTP Basic认证 | username, password |
| TOKEN | OAuth2令牌认证 | token-url, client-id, client-secret |
| JWT | 使用当前用户JWT | 无（自动获取） |

### 3. 环境变量配置

```bash
# ANA服务配置
export ANA_SERVICE_URL=http://ana-service:8081
export ANA_USERNAME=ana_user
export ANA_PASSWORD=ana_pass

# ANA_DIPS服务配置
export ANA_DIPS_SERVICE_URL=http://ana-dips-service:8082
export ANA_DIPS_USERNAME=ana_dips_user
export ANA_DIPS_PASSWORD=ana_dips_pass

# DIPSRAG服务配置
export DIPSRAG_SERVICE_URL=http://dipsrag-service:8083

# JINMAO服务配置
export JINMAO_SERVICE_URL=http://jinmao-service:8084
export JINMAO_TOKEN_URL=http://jinmao-service:8084/oauth/token
export JINMAO_CLIENT_ID=jinmao_client
export JINMAO_CLIENT_SECRET=jinmao_secret
```

## API接口

### 1. 路由转发接口

```http
# 转发到ANA服务
POST /api/route/ana/v1/data
Content-Type: application/json

{
  "key": "value"
}
```

### 2. 健康检查接口

```http
# 检查所有服务健康状态
GET /api/route/health

# 检查指定服务健康状态
GET /api/route/health/ana
```

### 3. 管理接口（需要ADMIN权限）

```http
# 获取所有服务配置
GET /api/route/management/services

# 获取指定服务配置
GET /api/route/management/services/ana

# 获取路由统计信息
GET /api/route/management/statistics

# 清除健康检查缓存
DELETE /api/route/management/health/cache?serviceName=ana
```

## 使用示例

### 1. 调用ANA服务（Basic认证）

```bash
curl -X POST http://localhost:8080/api/route/ana/v1/analyze \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{"data": "test"}'
```

### 2. 调用DIPSRAG服务（无认证）

```bash
curl -X GET http://localhost:8080/api/route/dipsrag/v1/search?q=test
```

### 3. 调用JINMAO服务（Token认证）

```bash
curl -X POST http://localhost:8080/api/route/jinmao/v1/process \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{"request": "data"}'
```

## 扩展指南

### 1. 添加新服务

在 `application-route.yml` 中添加新的服务配置：

```yaml
route:
  services:
    new_service:
      enabled: true
      base-url: ${NEW_SERVICE_URL:http://localhost:8085}
      auth-type: BASIC
      username: ${NEW_SERVICE_USERNAME:user}
      password: ${NEW_SERVICE_PASSWORD:pass}
      connect-timeout: 30000
      read-timeout: 60000
      path-rewrite:
        "^/api/route/new_service(/.*)": "$1"
```

### 2. 自定义认证方式

1. 在 `RouteConfig.AuthType` 枚举中添加新的认证类型
2. 在 `RouteService.addAuthentication()` 方法中添加对应的认证逻辑

### 3. 自定义路径重写

在服务配置中添加更复杂的路径重写规则：

```yaml
path-rewrite:
  "^/api/route/service/v1(/.*)": "/api/v2$1"
  "^/api/route/service/old(/.*)": "/api/new$1"
```

## 监控和运维

### 1. 健康检查

系统会定期检查各个服务的健康状态，结果会缓存60秒。可以通过管理接口手动清除缓存。

### 2. 日志监控

关键操作都会记录日志，包括：
- 请求转发日志
- 认证失败日志
- 服务健康检查日志
- 错误处理日志

### 3. 性能监控

可以通过以下方式监控性能：
- WebClient连接池指标
- 请求响应时间
- 错误率统计

## 安全考虑

1. **认证信息保护**：敏感信息通过环境变量配置，不在代码中硬编码
2. **权限控制**：管理接口需要ADMIN权限
3. **请求验证**：对转发的请求进行基本验证
4. **错误信息**：避免在错误响应中泄露敏感信息

## 故障排查

### 1. 常见问题

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 404错误 | 路径重写规则错误 | 检查path-rewrite配置 |
| 401错误 | 认证信息错误 | 检查认证配置和凭据 |
| 超时错误 | 网络或服务响应慢 | 调整超时配置 |
| 连接拒绝 | 目标服务不可用 | 检查服务状态和网络 |

### 2. 调试技巧

1. 启用DEBUG日志：`logging.level.com.dipspro.service.RouteService=DEBUG`
2. 检查健康状态：`GET /api/route/health`
3. 查看统计信息：`GET /api/route/management/statistics`
4. 检查WebClient连接池状态

## 性能优化

1. **连接池调优**：根据并发量调整连接池大小
2. **缓存策略**：合理设置健康检查缓存时间
3. **超时配置**：根据服务特性设置合适的超时时间
4. **资源释放**：确保WebClient资源正确释放

## 版本历史

- v1.0.0：初始版本，支持基本的路由转发功能
- 后续版本将添加更多功能和优化