package com.dipspro.util;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;

import com.dipspro.util.ProjectNameParseUtil.ProjectNameParseResult;

/**
 * ProjectNameParseUtil 单元测试
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
class ProjectNameParseUtilTest {

    @Test
    void testParseProjectName_WithoutBrackets() {
        // 测试不带中括号的情况
        String input = "徐州时代之光";
        ProjectNameParseResult result = ProjectNameParseUtil.parseProjectName(input);

        assertEquals("徐州时代之光", result.getProjectName());
        assertEquals("", result.getCustomerName());
    }

    @Test
    void testParseProjectName_WithBrackets() {
        // 测试带中括号的情况
        String input = "[万科]徐州时代之光";
        ProjectNameParseResult result = ProjectNameParseUtil.parseProjectName(input);

        assertEquals("徐州时代之光", result.getProjectName());
        assertEquals("万科", result.getCustomerName());
    }

    @Test
    void testParseProjectName_WithBracketsAndSpaces() {
        // 测试带中括号和空格的情况
        String input = "[万科] 徐州时代之光";
        ProjectNameParseResult result = ProjectNameParseUtil.parseProjectName(input);

        assertEquals("徐州时代之光", result.getProjectName());
        assertEquals("万科", result.getCustomerName());
    }

    @Test
    void testParseProjectName_WithBracketsButNoProjectName() {
        // 测试只有中括号没有项目名称的情况
        String input = "[万科]";
        ProjectNameParseResult result = ProjectNameParseUtil.parseProjectName(input);

        assertEquals("[万科]", result.getProjectName());
        assertEquals("", result.getCustomerName());
    }

    @Test
    void testParseProjectName_WithEmptyBrackets() {
        // 测试空中括号的情况
        String input = "[]徐州时代之光";
        ProjectNameParseResult result = ProjectNameParseUtil.parseProjectName(input);

        assertEquals("[]徐州时代之光", result.getProjectName());
        assertEquals("", result.getCustomerName());
    }

    @Test
    void testParseProjectName_WithIncompleteBrackets() {
        // 测试不完整中括号的情况
        String input = "[万科徐州时代之光";
        ProjectNameParseResult result = ProjectNameParseUtil.parseProjectName(input);

        assertEquals("[万科徐州时代之光", result.getProjectName());
        assertEquals("", result.getCustomerName());
    }

    @Test
    void testParseProjectName_WithMultipleBrackets() {
        // 测试多个中括号的情况
        String input = "[万科][恒大]徐州时代之光";
        ProjectNameParseResult result = ProjectNameParseUtil.parseProjectName(input);

        assertEquals("[恒大]徐州时代之光", result.getProjectName());
        assertEquals("万科", result.getCustomerName());
    }

    @Test
    void testParseProjectName_WithNullInput() {
        // 测试null输入
        ProjectNameParseResult result = ProjectNameParseUtil.parseProjectName(null);

        assertEquals("", result.getProjectName());
        assertEquals("", result.getCustomerName());
    }

    @Test
    void testParseProjectName_WithEmptyInput() {
        // 测试空字符串输入
        ProjectNameParseResult result = ProjectNameParseUtil.parseProjectName("");

        assertEquals("", result.getProjectName());
        assertEquals("", result.getCustomerName());
    }

    @Test
    void testParseProjectName_WithWhitespaceInput() {
        // 测试只有空格的输入
        ProjectNameParseResult result = ProjectNameParseUtil.parseProjectName("   ");

        assertEquals("", result.getProjectName());
        assertEquals("", result.getCustomerName());
    }

    @Test
    void testParseProjectName_WithLeadingAndTrailingSpaces() {
        // 测试前后有空格的情况
        String input = "  [万科]徐州时代之光  ";
        ProjectNameParseResult result = ProjectNameParseUtil.parseProjectName(input);

        assertEquals("徐州时代之光", result.getProjectName());
        assertEquals("万科", result.getCustomerName());
    }

    @Test
    void testParseProjectName_WithComplexProjectName() {
        // 测试复杂项目名称
        String input = "[中海地产]北京中海汇智里二期";
        ProjectNameParseResult result = ProjectNameParseUtil.parseProjectName(input);

        assertEquals("北京中海汇智里二期", result.getProjectName());
        assertEquals("中海地产", result.getCustomerName());
    }
}