package com.dipspro.common.interceptor;

import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import com.dipspro.common.dto.ApiResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 全局响应拦截器
 * 统一包装返回格式
 */
@Slf4j
@RestControllerAdvice
public class ResponseInterceptor implements ResponseBodyAdvice<Object> {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        // 排除已经是ApiResponse格式的响应
        if (returnType.getParameterType().equals(ApiResponse.class)) {
            return false;
        }

        // 支持路由转发的Mono<ResponseEntity<String>>类型
        // 支持其他类型的响应
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
            Class<? extends HttpMessageConverter<?>> selectedConverterType,
            ServerHttpRequest request, ServerHttpResponse response) {

        String path = request.getURI().getPath();

        // 排除不需要包装的路径
        if (shouldExclude(path)) {
            return body;
        }

        // 如果已经是ApiResponse格式，直接返回
        if (body instanceof ApiResponse) {
            return body;
        }

        // 特殊处理路由转发的响应
        if (path.startsWith("/api/route/")) {
            return handleRouteResponse(body, returnType, response);
        }

        // 包装成功响应
        ApiResponse<Object> apiResponse = ApiResponse.success(body);

        // 处理String类型返回值的特殊情况
        if (returnType.getParameterType().equals(String.class)) {
            try {
                return objectMapper.writeValueAsString(apiResponse);
            } catch (JsonProcessingException e) {
                log.error("JSON序列化失败", e);
                return objectMapper.valueToTree(ApiResponse.error("响应序列化失败"));
            }
        }

        return apiResponse;
    }

    /**
     * 处理路由转发的响应
     */
    private Object handleRouteResponse(Object body, MethodParameter returnType, ServerHttpResponse response) {
        try {
            int statusCode = response.getHeaders().getFirst("Status") != null
                    ? Integer.parseInt(response.getHeaders().getFirst("Status"))
                    : 200;

            // 统一错误处理
            if (statusCode < 200 || statusCode >= 300) {
                return objectMapper.writeValueAsString(
                        ApiResponse.error("请求转发失败: HTTP " + statusCode));
            }

            // 成功响应统一处理逻辑
            Object responseData = body;

            // 字符串预处理：尝试解析JSON
            if (body instanceof String bodyStr) {
                if (bodyStr.trim().startsWith("{") || bodyStr.trim().startsWith("[")) {
                    try {
                        responseData = objectMapper.readValue(bodyStr, Object.class);
                    } catch (Exception e) {
                        // 保持原始字符串
                        responseData = bodyStr;
                    }
                }
            }

            // 统一包装成功响应
            ApiResponse<Object> apiResponse = ApiResponse.success(responseData, "请求转发成功");
            return objectMapper.writeValueAsString(apiResponse);

        } catch (Exception e) {
            log.error("处理路由响应失败", e);
            try {
                return objectMapper.writeValueAsString(
                        ApiResponse.error("响应处理失败: " + e.getMessage()));
            } catch (JsonProcessingException ex) {
                return "{\"status\":0,\"error\":\"严重错误: 序列化失败\"}";
            }
        }
    }

    /**
     * 判断是否需要排除包装
     */
    private boolean shouldExclude(String path) {
        return path.startsWith("/actuator") ||
                path.startsWith("/swagger-ui") ||
                path.startsWith("/v3/api-docs") ||
                path.startsWith("/error") ||
                path.startsWith("/api/asyncTasks/");
    }
}