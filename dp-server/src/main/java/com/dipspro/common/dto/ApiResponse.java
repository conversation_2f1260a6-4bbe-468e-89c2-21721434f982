package com.dipspro.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 统一API响应格式
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse<T> {

    /**
     * 状态码：1-成功，0-失败/需要登录
     */
    private Integer status;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 成功消息
     */
    private String message;

    /**
     * 错误信息
     */
    private String error;

    /**
     * 成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(1, data, null, null);
    }

    /**
     * 成功响应（带消息）
     */
    public static <T> ApiResponse<T> success(T data, String message) {
        return new ApiResponse<>(1, data, message, null);
    }

    /**
     * 成功响应（仅消息）
     */
    public static <T> ApiResponse<T> success(String message) {
        return new ApiResponse<>(1, null, message, null);
    }

    /**
     * 失败响应
     */
    public static <T> ApiResponse<T> error(String error) {
        return new ApiResponse<>(0, null, null, error);
    }

    /**
     * 失败响应（带数据）
     */
    public static <T> ApiResponse<T> error(T data, String error) {
        return new ApiResponse<>(0, data, null, error);
    }

    /**
     * 需要登录响应
     */
    public static <T> ApiResponse<T> unauthorized(String error) {
        return new ApiResponse<>(0, null, null, error);
    }
} 