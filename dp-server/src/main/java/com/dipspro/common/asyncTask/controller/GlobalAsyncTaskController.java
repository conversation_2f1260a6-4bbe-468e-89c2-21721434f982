package com.dipspro.common.asyncTask.controller;

import com.dipspro.common.asyncTask.service.GlobalAsyncTaskService;
import com.dipspro.common.dto.ApiResponse;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * 全局异步任务监控控制器
 * 提供系统级别的任务监控和管理功能
 */
@Slf4j
@RestController
@RequestMapping("/api/asyncTasks")
@RequiredArgsConstructor
public class GlobalAsyncTaskController {

    private final GlobalAsyncTaskService globalAsyncTaskService;

    /**
     * 查询指定任务状态
     */
    @GetMapping("/{taskId}")
    public ResponseEntity<ApiResponse<TaskStatusResponse>> getTaskStatus(@PathVariable String taskId) {
        try {
            GlobalAsyncTaskService.TaskInfo taskInfo = globalAsyncTaskService.getTaskInfo(taskId);

            if (taskInfo == null) {
                TaskStatusResponse response = TaskStatusResponse.notFound(taskId);
                return ResponseEntity.ok(ApiResponse.success(response));
            }

            TaskStatusResponse response = TaskStatusResponse.fromTaskInfo(taskInfo);
            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            log.error("查询任务状态失败，任务ID: {}", taskId, e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.<TaskStatusResponse>error("查询任务状态失败: " + e.getMessage()));
        }
    }

    /**
     * 取消指定任务
     */
    @DeleteMapping("/{taskId}")
    public ResponseEntity<ApiResponse<String>> cancelTask(@PathVariable String taskId) {
        try {
            boolean cancelled = globalAsyncTaskService.cancelTask(taskId);

            if (cancelled) {
                return ResponseEntity.ok(ApiResponse.success("任务已取消"));
            } else {
                return ResponseEntity.ok(ApiResponse.success("任务无法取消（可能已完成或不存在）"));
            }

        } catch (Exception e) {
            log.error("取消任务失败，任务ID: {}", taskId, e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.<String>error("取消任务失败: " + e.getMessage()));
        }
    }

    /**
     * 获取所有任务状态
     */
    @GetMapping
    public ResponseEntity<ApiResponse<Map<String, TaskStatusResponse>>> getAllTasks(
            @RequestParam(required = false) String module,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String type) {
        try {
            Map<String, GlobalAsyncTaskService.TaskInfo> allTasks;

            // 根据参数过滤任务
            if (module != null) {
                allTasks = globalAsyncTaskService.getTasksByModule(module);
            } else if (status != null) {
                GlobalAsyncTaskService.TaskStatus taskStatus = GlobalAsyncTaskService.TaskStatus
                        .valueOf(status.toUpperCase());
                allTasks = globalAsyncTaskService.getTasksByStatus(taskStatus);
            } else if (type != null) {
                GlobalAsyncTaskService.TaskType taskType = GlobalAsyncTaskService.TaskType.valueOf(type.toUpperCase());
                allTasks = globalAsyncTaskService.getTasksByType(taskType);
            } else {
                allTasks = globalAsyncTaskService.getAllTasks();
            }

            Map<String, TaskStatusResponse> response = allTasks.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> TaskStatusResponse.fromTaskInfo(entry.getValue())));

            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            log.error("获取任务列表失败", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.<Map<String, TaskStatusResponse>>error("获取任务列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取任务统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<ApiResponse<TaskStatisticsResponse>> getTaskStatistics() {
        try {
            GlobalAsyncTaskService.TaskStatistics statistics = globalAsyncTaskService.getTaskStatistics();
            TaskStatisticsResponse response = TaskStatisticsResponse.fromTaskStatistics(statistics);
            return ResponseEntity.ok(ApiResponse.success(response));

        } catch (Exception e) {
            log.error("获取任务统计信息失败", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.<TaskStatisticsResponse>error("获取任务统计信息失败: " + e.getMessage()));
        }
    }

    /**
     * 批量取消指定模块的任务
     */
    @DeleteMapping("/module/{module}")
    public ResponseEntity<ApiResponse<String>> cancelTasksByModule(@PathVariable String module) {
        try {
            int cancelledCount = globalAsyncTaskService.cancelTasksByModule(module);
            return ResponseEntity.ok(ApiResponse.success(
                    String.format("已取消 %d 个任务（模块: %s）", cancelledCount, module)));

        } catch (Exception e) {
            log.error("批量取消任务失败，模块: {}", module, e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.<String>error("批量取消任务失败: " + e.getMessage()));
        }
    }

    /**
     * 获取系统健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<SystemHealthResponse>> getSystemHealth() {
        try {
            GlobalAsyncTaskService.TaskStatistics statistics = globalAsyncTaskService.getTaskStatistics();

            SystemHealthResponse health = new SystemHealthResponse();
            health.setTotalTasks(statistics.getTotalCount());
            health.setRunningTasks(statistics.getRunningCount());
            health.setPendingTasks(statistics.getPendingCount());
            health.setFailedTasks(statistics.getFailedCount());
            health.setModuleStatistics(statistics.getModuleStatistics());

            // 判断系统健康状态
            if (statistics.getRunningCount() > 100) {
                health.setStatus("BUSY");
                health.setMessage("系统任务负载较高");
            } else if (statistics.getFailedCount() > statistics.getCompletedCount() * 0.1) {
                health.setStatus("WARNING");
                health.setMessage("任务失败率较高");
            } else {
                health.setStatus("HEALTHY");
                health.setMessage("系统运行正常");
            }

            return ResponseEntity.ok(ApiResponse.success(health));

        } catch (Exception e) {
            log.error("获取系统健康状态失败", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.<SystemHealthResponse>error("获取系统健康状态失败: " + e.getMessage()));
        }
    }

    /**
     * 任务状态响应
     */
    @Data
    public static class TaskStatusResponse {
        private String taskId;
        private String module;
        private String taskType;
        private String status;
        private java.time.LocalDateTime createTime;
        private java.time.LocalDateTime completeTime;
        // private Object result; // 任务结果，结果太大，不返回
        private String errorMessage;
        private Long processingTimeMs;
        private String description;
        private Map<String, Object> metadata;

        public static TaskStatusResponse fromTaskInfo(GlobalAsyncTaskService.TaskInfo taskInfo) {
            TaskStatusResponse response = new TaskStatusResponse();
            response.setTaskId(taskInfo.getTaskId());
            response.setModule(taskInfo.getModule());
            response.setTaskType(taskInfo.getTaskType().name());
            response.setStatus(taskInfo.getStatus().name());
            response.setCreateTime(taskInfo.getCreateTime());
            response.setCompleteTime(taskInfo.getCompleteTime());
            // response.setResult(taskInfo.getResult());
            response.setErrorMessage(taskInfo.getErrorMessage());
            response.setDescription(taskInfo.getDescription());
            response.setMetadata(taskInfo.getMetadata());

            // 计算处理时间
            if (taskInfo.getCompleteTime() != null) {
                response.setProcessingTimeMs(
                        java.time.Duration.between(taskInfo.getCreateTime(), taskInfo.getCompleteTime()).toMillis());
            }

            return response;
        }

        public static TaskStatusResponse notFound(String taskId) {
            TaskStatusResponse response = new TaskStatusResponse();
            response.setTaskId(taskId);
            response.setStatus("NOT_FOUND");
            response.setErrorMessage("任务不存在或已过期");
            return response;
        }
    }

    /**
     * 任务统计响应
     */
    @Data
    public static class TaskStatisticsResponse {
        private int pendingCount;
        private int runningCount;
        private int completedCount;
        private int failedCount;
        private int cancelledCount;
        private int totalCount;
        private Map<String, Integer> moduleStatistics;
        private Map<String, Integer> typeStatistics;

        public static TaskStatisticsResponse fromTaskStatistics(GlobalAsyncTaskService.TaskStatistics statistics) {
            TaskStatisticsResponse response = new TaskStatisticsResponse();
            response.setPendingCount(statistics.getPendingCount());
            response.setRunningCount(statistics.getRunningCount());
            response.setCompletedCount(statistics.getCompletedCount());
            response.setFailedCount(statistics.getFailedCount());
            response.setCancelledCount(statistics.getCancelledCount());
            response.setTotalCount(statistics.getTotalCount());
            response.setModuleStatistics(statistics.getModuleStatistics());
            response.setTypeStatistics(statistics.getTypeStatistics());
            return response;
        }
    }

    /**
     * 系统健康状态响应
     */
    @Data
    public static class SystemHealthResponse {
        private String status;
        private String message;
        private int totalTasks;
        private int runningTasks;
        private int pendingTasks;
        private int failedTasks;
        private Map<String, Integer> moduleStatistics;
    }
}