package com.dipspro.common.asyncTask.service;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 全局异步任务管理服务
 * 用于跟踪和管理整个系统中的所有异步任务
 */
@Slf4j
@Service
public class GlobalAsyncTaskService {

    private final Map<String, TaskInfo> taskMap = new ConcurrentHashMap<>();
    private final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor();

    public GlobalAsyncTaskService() {
        // 定期清理过期任务（每小时清理一次，清理24小时前的任务）
        cleanupExecutor.scheduleAtFixedRate(this::cleanupExpiredTasks, 1, 1, TimeUnit.HOURS);
    }

    /**
     * 任务信息
     */
    @Data
    public static class TaskInfo {
        private String taskId;
        private String module; // 模块名称（如：embedding, nlp, ocr等）
        private TaskType taskType;
        private TaskStatus status;
        private LocalDateTime createTime;
        private LocalDateTime completeTime;
        private Object result;
        private String errorMessage;
        private CompletableFuture<?> future;
        private String description; // 任务描述
        private Map<String, Object> metadata; // 任务元数据

        public TaskInfo(String taskId, String module, TaskType taskType, String description,
                CompletableFuture<?> future) {
            this.taskId = taskId;
            this.module = module;
            this.taskType = taskType;
            this.description = description;
            this.status = TaskStatus.RUNNING;
            this.createTime = LocalDateTime.now();
            this.future = future;
            this.metadata = new ConcurrentHashMap<>();
        }
    }

    /**
     * 任务类型
     */
    public enum TaskType {
        // Embedding 相关
        TEXT_ENCODE, // 文本编码
        BATCH_ENCODE, // 批量编码
        SIMILARITY_CALC, // 相似度计算

        // 添加更多的任务类型...
    }

    /**
     * 任务状态
     */
    public enum TaskStatus {
        PENDING, // 等待中
        RUNNING, // 运行中
        COMPLETED, // 已完成
        FAILED, // 失败
        CANCELLED // 已取消
    }

    /**
     * 提交异步任务（通用方法）
     */
    public <T> String submitTask(String module, TaskType taskType, String description, CompletableFuture<T> future) {
        return submitTask(module, taskType, description, future, null);
    }

    /**
     * 提交异步任务（带元数据）
     */
    public <T> String submitTask(String module, TaskType taskType, String description,
            CompletableFuture<T> future, Map<String, Object> metadata) {
        String taskId = generateTaskId(module);
        TaskInfo taskInfo = new TaskInfo(taskId, module, taskType, description, future);

        if (metadata != null) {
            taskInfo.getMetadata().putAll(metadata);
        }

        future.whenComplete((result, throwable) -> {
            if (throwable != null) {
                taskInfo.setStatus(TaskStatus.FAILED);
                taskInfo.setErrorMessage(throwable.getMessage());
                log.error("任务执行失败，任务ID: {}, 模块: {}, 类型: {}", taskId, module, taskType, throwable);
            } else {
                taskInfo.setStatus(TaskStatus.COMPLETED);
                taskInfo.setResult(result);
                log.info("任务执行完成，任务ID: {}, 模块: {}, 类型: {}", taskId, module, taskType);
            }
            taskInfo.setCompleteTime(LocalDateTime.now());
        });

        taskMap.put(taskId, taskInfo);
        log.info("提交异步任务，任务ID: {}, 模块: {}, 类型: {}, 描述: {}", taskId, module, taskType, description);
        return taskId;
    }

    /**
     * 查询任务状态
     */
    public TaskInfo getTaskInfo(String taskId) {
        return taskMap.get(taskId);
    }

    /**
     * 取消任务
     */
    public boolean cancelTask(String taskId) {
        TaskInfo taskInfo = taskMap.get(taskId);
        if (taskInfo != null
                && (taskInfo.getStatus() == TaskStatus.RUNNING || taskInfo.getStatus() == TaskStatus.PENDING)) {
            boolean cancelled = taskInfo.getFuture().cancel(true);
            if (cancelled) {
                taskInfo.setStatus(TaskStatus.CANCELLED);
                taskInfo.setCompleteTime(LocalDateTime.now());
                log.info("任务已取消，任务ID: {}, 模块: {}", taskId, taskInfo.getModule());
                return true;
            }
        }
        return false;
    }

    /**
     * 获取所有任务状态
     */
    public Map<String, TaskInfo> getAllTasks() {
        return new ConcurrentHashMap<>(taskMap);
    }

    /**
     * 按模块获取任务
     */
    public Map<String, TaskInfo> getTasksByModule(String module) {
        return taskMap.entrySet().stream()
                .filter(entry -> module.equals(entry.getValue().getModule()))
                .collect(Collectors.toConcurrentMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue));
    }

    /**
     * 按状态获取任务
     */
    public Map<String, TaskInfo> getTasksByStatus(TaskStatus status) {
        return taskMap.entrySet().stream()
                .filter(entry -> status.equals(entry.getValue().getStatus()))
                .collect(Collectors.toConcurrentMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue));
    }

    /**
     * 按任务类型获取任务
     */
    public Map<String, TaskInfo> getTasksByType(TaskType taskType) {
        return taskMap.entrySet().stream()
                .filter(entry -> taskType.equals(entry.getValue().getTaskType()))
                .collect(Collectors.toConcurrentMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue));
    }

    /**
     * 清理过期任务
     */
    private void cleanupExpiredTasks() {
        LocalDateTime expireTime = LocalDateTime.now().minusHours(24);
        int removedCount = 0;

        var iterator = taskMap.entrySet().iterator();
        while (iterator.hasNext()) {
            var entry = iterator.next();
            TaskInfo taskInfo = entry.getValue();
            LocalDateTime checkTime = taskInfo.getCompleteTime() != null ? taskInfo.getCompleteTime()
                    : taskInfo.getCreateTime();

            if (checkTime.isBefore(expireTime)) {
                iterator.remove();
                removedCount++;
            }
        }

        if (removedCount > 0) {
            log.info("清理过期任务完成，清理数量: {}, 当前任务数量: {}", removedCount, taskMap.size());
        }
    }

    /**
     * 生成任务ID
     */
    private String generateTaskId(String module) {
        return String.format("%s_task_%d_%s",
                module.toLowerCase(),
                System.currentTimeMillis(),
                Integer.toHexString((int) (Math.random() * 0x10000)));
    }

    /**
     * 获取任务统计信息
     */
    public TaskStatistics getTaskStatistics() {
        int pending = 0, running = 0, completed = 0, failed = 0, cancelled = 0;
        Map<String, Integer> moduleStats = new ConcurrentHashMap<>();
        Map<String, Integer> typeStats = new ConcurrentHashMap<>();

        for (TaskInfo taskInfo : taskMap.values()) {
            // 统计状态
            switch (taskInfo.getStatus()) {
                case PENDING -> pending++;
                case RUNNING -> running++;
                case COMPLETED -> completed++;
                case FAILED -> failed++;
                case CANCELLED -> cancelled++;
            }

            // 统计模块
            moduleStats.merge(taskInfo.getModule(), 1, Integer::sum);

            // 统计类型
            typeStats.merge(taskInfo.getTaskType().name(), 1, Integer::sum);
        }

        return new TaskStatistics(pending, running, completed, failed, cancelled,
                taskMap.size(), moduleStats, typeStats);
    }

    /**
     * 任务统计信息
     */
    @Data
    public static class TaskStatistics {
        private final int pendingCount;
        private final int runningCount;
        private final int completedCount;
        private final int failedCount;
        private final int cancelledCount;
        private final int totalCount;
        private final Map<String, Integer> moduleStatistics;
        private final Map<String, Integer> typeStatistics;

        public TaskStatistics(int pendingCount, int runningCount, int completedCount,
                int failedCount, int cancelledCount, int totalCount,
                Map<String, Integer> moduleStatistics, Map<String, Integer> typeStatistics) {
            this.pendingCount = pendingCount;
            this.runningCount = runningCount;
            this.completedCount = completedCount;
            this.failedCount = failedCount;
            this.cancelledCount = cancelledCount;
            this.totalCount = totalCount;
            this.moduleStatistics = moduleStatistics;
            this.typeStatistics = typeStatistics;
        }
    }

    /**
     * 更新任务元数据
     */
    public void updateTaskMetadata(String taskId, String key, Object value) {
        TaskInfo taskInfo = taskMap.get(taskId);
        if (taskInfo != null) {
            taskInfo.getMetadata().put(key, value);
        }
    }

    /**
     * 获取运行中的任务数量
     */
    public int getRunningTaskCount() {
        return (int) taskMap.values().stream()
                .filter(task -> task.getStatus() == TaskStatus.RUNNING)
                .count();
    }

    /**
     * 获取指定模块的运行中任务数量
     */
    public int getRunningTaskCount(String module) {
        return (int) taskMap.values().stream()
                .filter(task -> module.equals(task.getModule()) && task.getStatus() == TaskStatus.RUNNING)
                .count();
    }

    /**
     * 批量取消任务
     */
    public int cancelTasksByModule(String module) {
        int cancelledCount = 0;
        for (TaskInfo taskInfo : taskMap.values()) {
            if (module.equals(taskInfo.getModule()) &&
                    (taskInfo.getStatus() == TaskStatus.RUNNING || taskInfo.getStatus() == TaskStatus.PENDING)) {
                if (taskInfo.getFuture().cancel(true)) {
                    taskInfo.setStatus(TaskStatus.CANCELLED);
                    taskInfo.setCompleteTime(LocalDateTime.now());
                    cancelledCount++;
                }
            }
        }
        log.info("批量取消任务完成，模块: {}, 取消数量: {}", module, cancelledCount);
        return cancelledCount;
    }
}