package com.dipspro;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@SpringBootApplication
@EntityScan(basePackages = "com.dipspro.modules")
@EnableJpaRepositories(basePackages = "com.dipspro.modules")
public class DipsProApp {

	public static void main(String[] args) {
		log.info("启动 DIPS Pro 应用...");
		SpringApplication.run(DipsProApp.class, args);
		log.info("DIPS Pro 应用启动完成");
	}

}
