package com.dipspro.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import lombok.Data;
import java.util.Map;
import java.util.HashMap;

/**
 * 路由配置类
 * 配置各个API服务的路由信息和认证方式
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "route")
public class RouteConfig {
    
    /**
     * 路由服务配置映射
     * key: 路由前缀 (如 ana, ana_dips, dipsrag, jinmao)
     * value: 服务配置信息
     */
    private Map<String, ServiceConfig> services = new HashMap<>();
    
    /**
     * 服务配置信息
     */
    @Data
    public static class ServiceConfig {
        /**
         * 目标服务的基础URL
         */
        private String baseUrl;
        
        /**
         * 认证类型: NONE, BASIC, TOKEN, JWT
         */
        private AuthType authType = AuthType.NONE;
        
        /**
         * Basic认证用户名
         */
        private String username;
        
        /**
         * Basic认证密码
         */
        private String password;
        
        /**
         * Token认证的token获取URL
         */
        private String tokenUrl;
        
        /**
         * Token认证的客户端ID
         */
        private String clientId;
        
        /**
         * Token认证的客户端密钥
         */
        private String clientSecret;
        
        /**
         * 连接超时时间(毫秒)
         */
        private int connectTimeout = 30000;
        
        /**
         * 读取超时时间(毫秒)
         */
        private int readTimeout = 60000;
        
        /**
         * 是否启用该服务
         */
        private boolean enabled = true;
        
        /**
         * 路径重写规则
         * key: 原路径模式, value: 目标路径模式
         */
        private Map<String, String> pathRewrite = new HashMap<>();
    }
    
    /**
     * 认证类型枚举
     */
    public enum AuthType {
        NONE,    // 无认证
        BASIC,   // Basic认证
        TOKEN,   // Token认证(需要先获取token)
        JWT      // JWT认证(使用当前用户的JWT)
    }
}