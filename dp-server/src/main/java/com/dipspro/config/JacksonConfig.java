package com.dipspro.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;

/**
 * Jackson配置类，用于配置全局ObjectMapper
 */
@Configuration
@Slf4j
public class JacksonConfig {

    /**
     * 配置全局ObjectMapper
     * @return 配置好的ObjectMapper实例
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = Jackson2ObjectMapperBuilder.json()
            // 添加Java 8 时间模块支持
            .modules(new JavaTimeModule())
            // 日期格式配置，使用ISO格式而非时间戳
            .featuresToDisable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            // 忽略未知属性
            .featuresToDisable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
            // 如果前端发送空对象，不要失败
            .featuresToDisable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
            // 忽略null值，减少JSON体积
            .serializationInclusion(JsonInclude.Include.NON_NULL)
            .build();
        
        // 启用ACCEPT_CASE_INSENSITIVE_ENUMS，允许不区分大小写的枚举处理
        objectMapper.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS, true);
        
        // 允许单引号
        objectMapper.configure(com.fasterxml.jackson.core.JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        
        // 允许不带引号的字段名
        objectMapper.configure(com.fasterxml.jackson.core.JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        
        log.info("已配置全局ObjectMapper，启用了JSONB/JSON类型转换支持和标准化配置");
        
        return objectMapper;
    }
    
    @PostConstruct
    public void logConfig() {
        log.info("Jackson配置已加载，将用于JSON序列化和反序列化");
    }
} 