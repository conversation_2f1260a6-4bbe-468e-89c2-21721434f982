package com.dipspro.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 中文句子转向量服务配置
 */
@Configuration
@ConfigurationProperties(prefix = "embedding")
public class EmbeddingConfig {

    /**
     * 模型配置
     */
    private Model model = new Model();

    /**
     * 线程池配置
     */
    private ThreadPool threadPool = new ThreadPool();

    public Model getModel() {
        return model;
    }

    public void setModel(Model model) {
        this.model = model;
    }

    public ThreadPool getThreadPool() {
        return threadPool;
    }

    public void setThreadPool(ThreadPool threadPool) {
        this.threadPool = threadPool;
    }

    public static class Model {
        /**
         * ONNX模型路径
         */
        private String path = "models/text2vec-base-chinese.onnx";

        /**
         * 词汇表路径
         */
        private String vocabPath = "models/vocab.txt";

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        public String getVocabPath() {
            return vocabPath;
        }

        public void setVocabPath(String vocabPath) {
            this.vocabPath = vocabPath;
        }
    }

    public static class ThreadPool {
        /**
         * 线程池大小
         */
        private int size = 4;

        public int getSize() {
            return size;
        }

        public void setSize(int size) {
            this.size = size;
        }
    }
}