package com.dipspro.config;

import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * WebClient配置类
 * 配置HTTP客户端连接池和超时设置
 */
@Slf4j
@Configuration
public class WebClientConfig {
    
    /**
     * 配置WebClient Builder
     * 
     * @return WebClient.Builder
     */
    @Bean
    public WebClient.Builder webClientBuilder() {
        log.info("配置WebClient Builder");
        
        // 配置连接池
        ConnectionProvider connectionProvider = ConnectionProvider.builder("route-connection-pool")
                .maxConnections(200)  // 最大连接数
                .maxIdleTime(Duration.ofSeconds(30))  // 最大空闲时间
                .maxLifeTime(Duration.ofMinutes(5))   // 连接最大生存时间
                .pendingAcquireTimeout(Duration.ofSeconds(10))  // 获取连接超时时间
                .evictInBackground(Duration.ofSeconds(60))      // 后台清理间隔
                .build();
        
        // 配置HttpClient
        HttpClient httpClient = HttpClient.create(connectionProvider)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 30000)  // 连接超时30秒
                .doOnConnected(conn -> conn
                        .addHandlerLast(new ReadTimeoutHandler(60, TimeUnit.SECONDS))   // 读取超时60秒
                        .addHandlerLast(new WriteTimeoutHandler(60, TimeUnit.SECONDS))  // 写入超时60秒
                )
                .compress(true)  // 启用压缩
                .keepAlive(true) // 启用Keep-Alive
                .followRedirect(true); // 自动跟随重定向
        
        // 配置Exchange策略，增加内存缓冲区大小
        ExchangeStrategies strategies = ExchangeStrategies.builder()
                .codecs(configurer -> {
                    configurer.defaultCodecs().maxInMemorySize(16 * 1024 * 1024); // 16MB
                    configurer.defaultCodecs().enableLoggingRequestDetails(true);
                })
                .build();
        
        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .exchangeStrategies(strategies)
                .defaultHeader("User-Agent", "DIPS-Pro-Route-Service/1.0")
                .filter((request, next) -> {
                    // 请求日志过滤器
                    log.debug("发送请求: {} {}", request.method(), request.url());
                    return next.exchange(request)
                            .doOnNext(response -> log.debug("收到响应: {} - 状态码: {}", 
                                    request.url(), response.statusCode()))
                            .doOnError(error -> log.error("请求失败: {} - 错误: {}", 
                                    request.url(), error.getMessage()));
                });
    }
    
    /**
     * 默认WebClient实例
     * 
     * @param webClientBuilder WebClient构建器
     * @return WebClient实例
     */
    @Bean
    public WebClient webClient(WebClient.Builder webClientBuilder) {
        return webClientBuilder.build();
    }
}