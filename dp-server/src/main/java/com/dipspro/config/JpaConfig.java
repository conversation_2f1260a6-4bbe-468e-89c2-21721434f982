package com.dipspro.config;

import org.hibernate.boot.model.TypeContributor;
import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;

@Configuration
public class JpaConfig {

    /**
     * 使用HibernatePropertiesCustomizer替代已过时的TypeContributorList
     * 配置Hibernate使用自定义JsonBinaryType处理JSONB
     */
    @Bean
    public HibernatePropertiesCustomizer hibernatePropertiesCustomizer() {
        return hibernateProperties -> {
            hibernateProperties.put("hibernate.type_contributors",
                    (TypeContributor) (typeContributions, _) -> {
                        typeContributions.contributeType(new JsonBinaryType());
                    });
        };
    }
}