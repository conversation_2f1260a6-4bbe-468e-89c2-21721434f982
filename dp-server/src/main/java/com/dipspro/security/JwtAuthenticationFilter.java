package com.dipspro.security;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;

import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import com.dipspro.util.JwtUtil;

import jakarta.servlet.DispatcherType;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * JWT认证过滤器
 * 拦截每个请求，验证JWT令牌的有效性
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtUtil jwtUtil;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
            FilterChain filterChain) throws ServletException, IOException {

        String requestURI = request.getRequestURI();
        String method = request.getMethod();
        String threadName = Thread.currentThread().getName();

        log.debug("JWT过滤器处理请求: {} {}", method, requestURI);

        // 检查是否为异步调度
        boolean isAsyncDispatch = request.getDispatcherType() == DispatcherType.ASYNC;

        // 标记请求已经过JWT过滤器处理
        request.setAttribute("JWT_FILTER_PROCESSED", true);
        request.setAttribute("JWT_FILTER_THREAD", threadName);
        request.setAttribute("JWT_FILTER_TIMESTAMP", System.currentTimeMillis());
        request.setAttribute("JWT_FILTER_ASYNC", isAsyncDispatch);

        // 如果是异步调度，尝试从请求属性中恢复认证信息
        if (isAsyncDispatch) {
            log.debug("处理异步调度请求");
            String savedUsername = (String) request.getAttribute("username");
            Long savedUserId = (Long) request.getAttribute("userId");
            Long savedTenantId = (Long) request.getAttribute("tenantId");

            if (savedUsername != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                log.debug("从请求属性恢复认证信息 - 用户: {}, 用户ID: {}, 租户ID: {}", savedUsername, savedUserId, savedTenantId);

                // 创建认证对象
                UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                        savedUsername, null, Collections.emptyList());
                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                // 设置认证上下文
                SecurityContextHolder.getContext().setAuthentication(authentication);
                log.debug("异步调度认证恢复成功，用户: {}", savedUsername);

                // 直接继续处理，不需要重新验证JWT
                log.debug("=== JWT过滤器调用下一个过滤器(异步恢复): {} {} === 线程: {}", method, requestURI, threadName);
                filterChain.doFilter(request, response);
                log.debug("=== JWT过滤器处理完成(异步恢复): {} {} === 线程: {}", method, requestURI, threadName);
                return;
            } else if (savedUsername == null) {
                log.warn("异步调度请求缺少保存的用户信息");
            } else {
                log.debug("异步调度请求已有认证上下文");
            }
        }

        // 跳过认证接口和公开接口
        if (shouldSkipAuthentication(requestURI)) {
            log.debug("跳过认证检查: {} {} 线程: {}", method, requestURI, threadName);
            filterChain.doFilter(request, response);
            log.debug("=== JWT过滤器处理完成(跳过认证): {} {} === 线程: {}", method, requestURI, threadName);
            return;
        }

        String authHeader = request.getHeader("Authorization");
        String token = null;
        String username = null;

        log.debug("JWT过滤器处理请求: {} {}", method, requestURI);

        // 从请求头中提取JWT令牌
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            token = authHeader.substring(7);
            log.debug("提取到Token: {}...", token.length() > 20 ? token.substring(0, 20) + "..." : token);

            try {
                username = jwtUtil.getUsernameFromToken(token);
                log.debug("从Token中解析到用户名: {}", username);
            } catch (Exception e) {
                log.error("JWT令牌解析失败: {}", e.getMessage());
                log.debug("Token内容: {}", token.length() > 50 ? token.substring(0, 50) + "..." : token);
            }
        } else {
            log.warn("未找到Bearer Token，Authorization头: {}", authHeader);
        }

        // 验证令牌并设置认证信息
        if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
            log.debug("开始验证Token有效性，用户: {}", username);

            try {
                if (jwtUtil.validateAccessToken(token)) {
                    log.debug("Token验证成功，设置认证上下文，用户: {}", username);

                    // 创建认证对象
                    UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(username,
                            null, new ArrayList<>());
                    authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                    // 设置到安全上下文
                    SecurityContextHolder.getContext().setAuthentication(authToken);

                    // 将用户信息添加到请求属性中，方便后续使用
                    try {
                        request.setAttribute("userId", jwtUtil.getUserIdFromToken(token));
                        request.setAttribute("tenantId", jwtUtil.getTenantIdFromToken(token));
                        request.setAttribute("username", username);
                        log.debug("用户信息已添加到请求属性");
                    } catch (Exception e) {
                        log.warn("添加用户信息到请求属性失败: {}", e.getMessage());
                    }

                    log.debug("认证成功，用户: {}", username);
                } else {
                    log.warn("Token验证失败，用户: {}", username);
                }
            } catch (Exception e) {
                log.error("Token验证过程中发生异常，用户: {}, 错误: {}", username, e.getMessage());
            }
        } else if (username == null) {
            log.debug("未解析到用户名，跳过认证 线程: {}", threadName);
        } else {
            log.debug("认证上下文已存在，跳过认证 线程: {}", threadName);
        }

        log.debug("=== JWT过滤器调用下一个过滤器: {} {} === 线程: {}", method, requestURI, threadName);
        filterChain.doFilter(request, response);
        log.debug("=== JWT过滤器处理完成: {} {} === 线程: {}", method, requestURI, threadName);
    }

    /**
     * 判断是否应该跳过认证检查
     */
    private boolean shouldSkipAuthentication(String requestURI) {
        boolean shouldSkip = requestURI.startsWith("/api/auth/") ||
                requestURI.startsWith("/actuator/") ||
                requestURI.startsWith("/swagger-ui/") ||
                requestURI.startsWith("/v3/api-docs/") ||
                requestURI.startsWith("/static/") ||
                requestURI.startsWith("/public/") ||
                requestURI.startsWith("/webjars/");

        log.debug("shouldSkipAuthentication({}) = {}", requestURI, shouldSkip);
        return shouldSkip;
    }

    /**
     * 重写shouldNotFilter方法，确保过滤器不会被意外跳过
     */
    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        String requestURI = request.getRequestURI();
        String method = request.getMethod();
        String threadName = Thread.currentThread().getName();

        boolean shouldNotFilter = shouldSkipAuthentication(requestURI);

        log.debug("=== shouldNotFilter检查 === URI: {}, Method: {}, 线程: {}, 结果: {}",
                requestURI, method, threadName, shouldNotFilter);

        return shouldNotFilter;
    }

    /**
     * 重写shouldNotFilterAsyncDispatch方法，确保异步调度请求也会经过过滤器
     * OncePerRequestFilter默认会跳过异步调度，我们需要重写这个行为
     */
    @Override
    protected boolean shouldNotFilterAsyncDispatch() {
        log.debug("=== shouldNotFilterAsyncDispatch检查 === 结果: false (强制处理异步调度)");
        return false; // 返回false表示异步调度也要经过过滤器
    }
}