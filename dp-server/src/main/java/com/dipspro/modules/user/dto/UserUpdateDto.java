package com.dipspro.modules.user.dto;

import java.time.LocalDate;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 用户更新DTO
 */
@Data
public class UserUpdateDto {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long id;

    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确")
    @Size(max = 128, message = "邮箱长度不能超过128个字符")
    private String email;

    /**
     * 手机号
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /**
     * 密码（可选，为空则不更新）
     */
    @Size(min = 6, max = 20, message = "密码长度在6到20个字符")
    private String password;

    /**
     * 真实姓名
     */
    @Size(max = 64, message = "真实姓名长度不能超过64个字符")
    private String realName;

    /**
     * 昵称
     */
    @Size(max = 64, message = "昵称长度不能超过64个字符")
    private String nickname;

    /**
     * 性别：0-未知，1-男，2-女
     */
    private Integer gender;

    /**
     * 生日
     */
    private LocalDate birthday;

    /**
     * 状态：0-禁用，1-启用，2-锁定
     */
    private Integer status;

    /**
     * 用户类型：admin,normal,guest
     */
    private String userType;
} 