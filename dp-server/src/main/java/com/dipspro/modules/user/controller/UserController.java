package com.dipspro.modules.user.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.user.dto.UserCreateDto;
import com.dipspro.modules.user.dto.UserQueryDto;
import com.dipspro.modules.user.dto.UserResponseDto;
import com.dipspro.modules.user.dto.UserUpdateDto;
import com.dipspro.modules.user.service.UserService;

import lombok.extern.slf4j.Slf4j;

/**
 * 用户管理控制器
 */
@RestController
@RequestMapping("/api/sys/user")
@Slf4j
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 分页查询用户列表
     */
    @GetMapping("/list")
    public ApiResponse<Page<UserResponseDto>> getUserList(UserQueryDto queryDto) {
        log.info("查询用户列表，查询条件: {}", queryDto);
        Page<UserResponseDto> result = userService.getUserList(queryDto);
        return ApiResponse.success(result);
    }

    /**
     * 根据ID获取用户详情
     */
    @GetMapping("/detail")
    public ApiResponse<UserResponseDto> getUserDetail(@RequestParam Long id) {
        log.info("获取用户详情，用户ID: {}", id);
        UserResponseDto result = userService.getUserById(id);
        return ApiResponse.success(result);
    }

    /**
     * 创建用户
     */
    @PostMapping("/create")
    public ApiResponse<UserResponseDto> createUser(@Validated @RequestBody UserCreateDto createDto) {
        log.info("创建用户，用户信息: {}", createDto);
        UserResponseDto result = userService.createUser(createDto);
        return ApiResponse.success(result);
    }

    /**
     * 更新用户
     */
    @PostMapping("/edit")
    public ApiResponse<UserResponseDto> updateUser(@Validated @RequestBody UserUpdateDto updateDto) {
        log.info("更新用户，用户ID: {}", updateDto.getId());
        UserResponseDto result = userService.updateUser(updateDto);
        return ApiResponse.success(result);
    }

    /**
     * 删除用户
     */
    @PostMapping("/delete")
    public ApiResponse<String> deleteUser(@RequestParam Long id) {
        log.info("删除用户，用户ID: {}", id);
        userService.deleteUser(id);
        return ApiResponse.success("删除成功");
    }

    /**
     * 批量删除用户
     */
    @PostMapping("/batch-delete")
    public ApiResponse<String> batchDeleteUsers(@RequestBody List<Long> ids) {
        log.info("批量删除用户，用户IDs: {}", ids);
        userService.batchDeleteUsers(ids);
        return ApiResponse.success("批量删除成功");
    }

    /**
     * 更新用户状态
     */
    @PostMapping("/update-status")
    public ApiResponse<String> updateUserStatus(@RequestParam Long id, @RequestParam Integer status) {
        log.info("更新用户状态，用户ID: {}, 新状态: {}", id, status);
        userService.updateUserStatus(id, status);
        return ApiResponse.success("状态更新成功");
    }

    /**
     * 重置用户密码
     */
    @PostMapping("/reset-password")
    public ApiResponse<String> resetUserPassword(@RequestParam Long id) {
        log.info("重置用户密码，用户ID: {}", id);
        userService.resetUserPassword(id);
        return ApiResponse.success("密码重置成功");
    }

    /**
     * 检查用户名是否存在
     */
    @GetMapping("/check-username")
    public ApiResponse<Boolean> checkUsername(@RequestParam String username) {
        boolean exists = userService.existsByUsername(username);
        return ApiResponse.success(exists);
    }

    /**
     * 检查邮箱是否存在
     */
    @GetMapping("/check-email")
    public ApiResponse<Boolean> checkEmail(@RequestParam String email) {
        boolean exists = userService.existsByEmail(email);
        return ApiResponse.success(exists);
    }

    /**
     * 检查手机号是否存在
     */
    @GetMapping("/check-phone")
    public ApiResponse<Boolean> checkPhone(@RequestParam String phone) {
        boolean exists = userService.existsByPhone(phone);
        return ApiResponse.success(exists);
    }
} 