package com.dipspro.modules.user.repository;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.dipspro.modules.user.entity.User;

/**
 * 用户Repository接口
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {

    /**
     * 根据租户ID和用户名查找用户
     */
    Optional<User> findByTenantIdAndUsernameAndDeleted(Long tenantId, String username, Integer deleted);

    /**
     * 根据租户ID和邮箱查找用户
     */
    Optional<User> findByTenantIdAndEmailAndDeleted(Long tenantId, String email, Integer deleted);

    /**
     * 根据租户ID和手机号查找用户
     */
    Optional<User> findByTenantIdAndPhoneAndDeleted(Long tenantId, String phone, Integer deleted);

    /**
     * 检查用户名是否存在
     */
    boolean existsByTenantIdAndUsernameAndDeleted(Long tenantId, String username, Integer deleted);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByTenantIdAndEmailAndDeleted(Long tenantId, String email, Integer deleted);

    /**
     * 检查手机号是否存在
     */
    boolean existsByTenantIdAndPhoneAndDeleted(Long tenantId, String phone, Integer deleted);

    /**
     * 根据用户名或邮箱或手机号查找用户（用于登录）
     */
    @Query("SELECT u FROM User u WHERE u.tenantId = :tenantId AND u.deleted = 0 AND " +
           "(u.username = :loginId OR u.email = :loginId OR u.phone = :loginId)")
    Optional<User> findByTenantIdAndLoginId(@Param("tenantId") Long tenantId, @Param("loginId") String loginId);

    /**
     * 根据用户名或邮箱或手机号查找用户（不限租户，用于登录）
     */
    @Query("SELECT u FROM User u WHERE u.deleted = :deleted AND " +
           "(u.username = :loginId OR u.email = :loginId OR u.phone = :loginId)")
    Optional<User> findByLoginIdAndDeleted(@Param("loginId") String loginId, @Param("deleted") Integer deleted);

    /**
     * 检查用户名是否存在（全局检查）
     */
    boolean existsByUsernameAndDeleted(String username, Integer deleted);

    /**
     * 检查邮箱是否存在（全局检查）
     */
    boolean existsByEmailAndDeleted(String email, Integer deleted);

    /**
     * 检查手机号是否存在（全局检查）
     */
    boolean existsByPhoneAndDeleted(String phone, Integer deleted);
} 