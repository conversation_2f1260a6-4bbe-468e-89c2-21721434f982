package com.dipspro.modules.user.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.dipspro.modules.user.dto.UserCreateDto;
import com.dipspro.modules.user.dto.UserQueryDto;
import com.dipspro.modules.user.dto.UserResponseDto;
import com.dipspro.modules.user.dto.UserUpdateDto;
import com.dipspro.modules.user.entity.User;
import com.dipspro.modules.user.repository.UserRepository;
import com.dipspro.modules.user.service.UserService;
import com.dipspro.util.PasswordUtil;

import jakarta.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户服务实现类
 */
@Service
@Transactional
@Slf4j
public class UserServiceImpl implements UserService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private PasswordUtil passwordUtil;

    @Override
    public Page<UserResponseDto> getUserList(UserQueryDto queryDto) {
        log.debug("查询用户列表，查询参数: {}", queryDto);
        
        // 构建查询条件
        Specification<User> spec = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            
            // 未删除的用户
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deleted"), 0));
            
            // 用户名模糊查询
            if (StringUtils.hasText(queryDto.getUsername())) {
                predicate = criteriaBuilder.and(predicate, 
                    criteriaBuilder.like(root.get("username"), "%" + queryDto.getUsername() + "%"));
            }
            
            // 邮箱模糊查询
            if (StringUtils.hasText(queryDto.getEmail())) {
                predicate = criteriaBuilder.and(predicate, 
                    criteriaBuilder.like(root.get("email"), "%" + queryDto.getEmail() + "%"));
            }
            
            // 手机号模糊查询
            if (StringUtils.hasText(queryDto.getPhone())) {
                predicate = criteriaBuilder.and(predicate, 
                    criteriaBuilder.like(root.get("phone"), "%" + queryDto.getPhone() + "%"));
            }
            
            // 真实姓名模糊查询
            if (StringUtils.hasText(queryDto.getRealName())) {
                predicate = criteriaBuilder.and(predicate, 
                    criteriaBuilder.like(root.get("realName"), "%" + queryDto.getRealName() + "%"));
            }
            
            // 状态精确查询
            if (queryDto.getStatus() != null) {
                predicate = criteriaBuilder.and(predicate, 
                    criteriaBuilder.equal(root.get("status"), queryDto.getStatus()));
            }
            
            // 用户类型精确查询
            if (StringUtils.hasText(queryDto.getUserType())) {
                predicate = criteriaBuilder.and(predicate, 
                    criteriaBuilder.equal(root.get("userType"), queryDto.getUserType()));
            }
            
            return predicate;
        };
        
        // 分页和排序
        int pageNumber = queryDto.getPage() - 1; // 转换为从0开始（Spring Data JPA要求）
        int pageSize = queryDto.getSize();
        
        Pageable pageable = PageRequest.of(
            pageNumber, 
            pageSize, 
            Sort.by(Sort.Direction.DESC, "createdTime")
        );
        
        Page<User> userPage = userRepository.findAll(spec, pageable);
        log.info("查询结果：总数={}, 当前页数据量={}, 页码={}, 页大小={}", 
            userPage.getTotalElements(), userPage.getNumberOfElements(), 
            userPage.getNumber(), userPage.getSize());
        
        // 转换为DTO
        return userPage.map(this::convertToResponseDto);
    }

    @Override
    public UserResponseDto getUserById(Long id) {
        Optional<User> userOpt = userRepository.findById(id);
        if (userOpt.isEmpty() || userOpt.get().getDeleted() == 1) {
            throw new RuntimeException("用户不存在");
        }
        return convertToResponseDto(userOpt.get());
    }

    @Override
    public UserResponseDto createUser(UserCreateDto createDto) {
        // 检查用户名是否已存在
        if (userRepository.existsByUsernameAndDeleted(createDto.getUsername(), 0)) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (userRepository.existsByEmailAndDeleted(createDto.getEmail(), 0)) {
            throw new RuntimeException("邮箱已存在");
        }
        
        // 检查手机号是否已存在（如果提供了手机号）
        if (StringUtils.hasText(createDto.getPhone()) && 
            userRepository.existsByPhoneAndDeleted(createDto.getPhone(), 0)) {
            throw new RuntimeException("手机号已存在");
        }
        
        // 创建用户实体
        User user = new User();
        BeanUtils.copyProperties(createDto, user);
        
        // 生成盐值并加密密码
        String salt = passwordUtil.generateSalt();
        String encodedPassword = passwordUtil.encodePassword(createDto.getPassword(), salt);
        user.setPassword(encodedPassword);
        user.setSalt(salt);
        
        // 设置默认值
        user.setTenantId(1L); // 默认租户
        user.setDeleted(0);
        user.setLoginCount(0);
        user.setPasswordUpdateTime(LocalDateTime.now());
        user.setCreatedTime(LocalDateTime.now());
        user.setUpdatedTime(LocalDateTime.now());
        
        User savedUser = userRepository.save(user);
        log.info("创建用户成功，用户ID: {}, 用户名: {}", savedUser.getId(), savedUser.getUsername());
        
        return convertToResponseDto(savedUser);
    }

    @Override
    public UserResponseDto updateUser(UserUpdateDto updateDto) {
        Optional<User> userOpt = userRepository.findById(updateDto.getId());
        if (userOpt.isEmpty() || userOpt.get().getDeleted() == 1) {
            throw new RuntimeException("用户不存在");
        }
        
        User user = userOpt.get();
        
        // 检查邮箱是否已被其他用户使用
        if (StringUtils.hasText(updateDto.getEmail()) && 
            !updateDto.getEmail().equals(user.getEmail()) &&
            userRepository.existsByEmailAndDeleted(updateDto.getEmail(), 0)) {
            throw new RuntimeException("邮箱已被其他用户使用");
        }
        
        // 检查手机号是否已被其他用户使用
        if (StringUtils.hasText(updateDto.getPhone()) && 
            !updateDto.getPhone().equals(user.getPhone()) &&
            userRepository.existsByPhoneAndDeleted(updateDto.getPhone(), 0)) {
            throw new RuntimeException("手机号已被其他用户使用");
        }
        
        // 更新字段
        if (StringUtils.hasText(updateDto.getEmail())) {
            user.setEmail(updateDto.getEmail());
        }
        if (StringUtils.hasText(updateDto.getPhone())) {
            user.setPhone(updateDto.getPhone());
        }
        if (StringUtils.hasText(updateDto.getPassword())) {
            // 生成新的盐值并加密密码
            String salt = passwordUtil.generateSalt();
            String encodedPassword = passwordUtil.encodePassword(updateDto.getPassword(), salt);
            user.setPassword(encodedPassword);
            user.setSalt(salt);
            user.setPasswordUpdateTime(LocalDateTime.now());
        }
        if (StringUtils.hasText(updateDto.getRealName())) {
            user.setRealName(updateDto.getRealName());
        }
        if (StringUtils.hasText(updateDto.getNickname())) {
            user.setNickname(updateDto.getNickname());
        }
        if (updateDto.getGender() != null) {
            user.setGender(updateDto.getGender());
        }
        if (updateDto.getBirthday() != null) {
            user.setBirthday(updateDto.getBirthday());
        }
        if (updateDto.getStatus() != null) {
            user.setStatus(updateDto.getStatus());
        }
        if (StringUtils.hasText(updateDto.getUserType())) {
            user.setUserType(updateDto.getUserType());
        }
        
        user.setUpdatedTime(LocalDateTime.now());
        
        User savedUser = userRepository.save(user);
        log.info("更新用户成功，用户ID: {}, 用户名: {}", savedUser.getId(), savedUser.getUsername());
        
        return convertToResponseDto(savedUser);
    }

    @Override
    public void deleteUser(Long id) {
        Optional<User> userOpt = userRepository.findById(id);
        if (userOpt.isEmpty() || userOpt.get().getDeleted() == 1) {
            throw new RuntimeException("用户不存在");
        }
        
        User user = userOpt.get();
        user.setDeleted(1);
        user.setUpdatedTime(LocalDateTime.now());
        userRepository.save(user);
        
        log.info("删除用户成功，用户ID: {}, 用户名: {}", user.getId(), user.getUsername());
    }

    @Override
    public void batchDeleteUsers(List<Long> ids) {
        List<User> users = userRepository.findAllById(ids);
        for (User user : users) {
            if (user.getDeleted() == 0) {
                user.setDeleted(1);
                user.setUpdatedTime(LocalDateTime.now());
            }
        }
        userRepository.saveAll(users);
        
        log.info("批量删除用户成功，删除数量: {}", users.size());
    }

    @Override
    public void updateUserStatus(Long id, Integer status) {
        Optional<User> userOpt = userRepository.findById(id);
        if (userOpt.isEmpty() || userOpt.get().getDeleted() == 1) {
            throw new RuntimeException("用户不存在");
        }
        
        User user = userOpt.get();
        user.setStatus(status);
        user.setUpdatedTime(LocalDateTime.now());
        userRepository.save(user);
        
        log.info("更新用户状态成功，用户ID: {}, 新状态: {}", id, status);
    }

    @Override
    public void resetUserPassword(Long id) {
        Optional<User> userOpt = userRepository.findById(id);
        if (userOpt.isEmpty() || userOpt.get().getDeleted() == 1) {
            throw new RuntimeException("用户不存在");
        }
        
        User user = userOpt.get();
        // 重置为默认密码
        String defaultPassword = "123456";
        String salt = passwordUtil.generateSalt();
        String encodedPassword = passwordUtil.encodePassword(defaultPassword, salt);
        user.setPassword(encodedPassword);
        user.setSalt(salt);
        user.setPasswordUpdateTime(LocalDateTime.now());
        user.setUpdatedTime(LocalDateTime.now());
        userRepository.save(user);
        
        log.info("重置用户密码成功，用户ID: {}, 用户名: {}", id, user.getUsername());
    }

    @Override
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsernameAndDeleted(username, 0);
    }

    @Override
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmailAndDeleted(email, 0);
    }

    @Override
    public boolean existsByPhone(String phone) {
        return userRepository.existsByPhoneAndDeleted(phone, 0);
    }

    /**
     * 转换为响应DTO
     */
    private UserResponseDto convertToResponseDto(User user) {
        UserResponseDto dto = new UserResponseDto();
        BeanUtils.copyProperties(user, dto);
        return dto;
    }
} 