package com.dipspro.modules.user.dto;

import lombok.Data;

/**
 * 用户查询DTO
 */
@Data
public class UserQueryDto {

    /**
     * 用户名
     */
    private String username;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 状态：0-禁用，1-启用，2-锁定
     */
    private Integer status;

    /**
     * 用户类型：admin,normal,guest
     */
    private String userType;

    /**
     * 页码（从1开始）
     */
    private Integer page = 1;

    /**
     * 每页大小
     */
    private Integer size = 10;
} 