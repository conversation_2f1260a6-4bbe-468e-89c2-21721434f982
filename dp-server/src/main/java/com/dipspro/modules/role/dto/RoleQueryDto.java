package com.dipspro.modules.role.dto;

import lombok.Data;

/**
 * 角色查询DTO
 */
@Data
public class RoleQueryDto {

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色类型：system,custom
     */
    private String roleType;

    /**
     * 父角色ID
     */
    private Long parentId;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 页码
     */
    private Integer page = 1;

    /**
     * 每页大小
     */
    private Integer size = 10;

    /**
     * 排序字段
     */
    private String sortBy = "id";

    /**
     * 排序方向：asc,desc
     */
    private String sortDir = "desc";
} 