package com.dipspro.modules.role.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.dipspro.modules.role.entity.Role;

/**
 * 角色Repository
 */
@Repository
public interface RoleRepository extends JpaRepository<Role, Long>, JpaSpecificationExecutor<Role> {

    /**
     * 根据租户ID和角色编码查找角色
     */
    Optional<Role> findByTenantIdAndRoleCodeAndDeleted(Long tenantId, String roleCode, Integer deleted);

    /**
     * 根据租户ID和角色名称查找角色
     */
    Optional<Role> findByTenantIdAndRoleNameAndDeleted(Long tenantId, String roleName, Integer deleted);

    /**
     * 根据租户ID查找所有角色
     */
    List<Role> findByTenantIdAndDeletedOrderBySortOrderAscIdAsc(Long tenantId, Integer deleted);

    /**
     * 根据父角色ID查找子角色
     */
    List<Role> findByParentIdAndDeletedOrderBySortOrderAscIdAsc(Long parentId, Integer deleted);

    /**
     * 检查角色编码是否存在（排除指定ID）
     */
    @Query("SELECT COUNT(r) > 0 FROM Role r WHERE r.tenantId = :tenantId AND r.roleCode = :roleCode AND r.id != :excludeId AND r.deleted = 0")
    boolean existsByRoleCodeExcludingId(@Param("tenantId") Long tenantId, @Param("roleCode") String roleCode, @Param("excludeId") Long excludeId);

    /**
     * 检查角色名称是否存在（排除指定ID）
     */
    @Query("SELECT COUNT(r) > 0 FROM Role r WHERE r.tenantId = :tenantId AND r.roleName = :roleName AND r.id != :excludeId AND r.deleted = 0")
    boolean existsByRoleNameExcludingId(@Param("tenantId") Long tenantId, @Param("roleName") String roleName, @Param("excludeId") Long excludeId);

    /**
     * 统计租户下的角色数量
     */
    long countByTenantIdAndDeleted(Long tenantId, Integer deleted);
} 