package com.dipspro.modules.role.dto;

import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 角色更新DTO
 */
@Data
public class RoleUpdateDto {

    /**
     * 角色编码
     */
    @Size(max = 64, message = "角色编码长度不能超过64个字符")
    @Pattern(regexp = "^[a-zA-Z0-9_-]+$", message = "角色编码只能包含字母、数字、下划线和横线")
    private String roleCode;

    /**
     * 角色名称
     */
    @Size(max = 128, message = "角色名称长度不能超过128个字符")
    private String roleName;

    /**
     * 角色类型：system,custom
     */
    @Pattern(regexp = "^(system|custom)$", message = "角色类型只能是system或custom")
    private String roleType;

    /**
     * 父角色ID
     */
    private Long parentId;

    /**
     * 角色层级
     */
    private Integer level;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 角色描述
     */
    @Size(max = 512, message = "角色描述长度不能超过512个字符")
    private String description;
} 