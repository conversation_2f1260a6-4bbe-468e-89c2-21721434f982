package com.dipspro.modules.role.service;

import java.util.List;

import org.springframework.data.domain.Page;

import com.dipspro.modules.role.dto.RoleCreateDto;
import com.dipspro.modules.role.dto.RoleQueryDto;
import com.dipspro.modules.role.dto.RoleResponseDto;
import com.dipspro.modules.role.dto.RoleUpdateDto;

/**
 * 角色服务接口
 */
public interface RoleService {

    /**
     * 分页查询角色列表
     */
    Page<RoleResponseDto> getRoleList(RoleQueryDto queryDto);

    /**
     * 根据ID获取角色详情
     */
    RoleResponseDto getRoleById(Long id);

    /**
     * 创建角色
     */
    RoleResponseDto createRole(RoleCreateDto createDto);

    /**
     * 更新角色
     */
    RoleResponseDto updateRole(Long id, RoleUpdateDto updateDto);

    /**
     * 删除角色
     */
    void deleteRole(Long id);

    /**
     * 批量删除角色
     */
    void deleteRoles(List<Long> ids);

    /**
     * 更新角色状态
     */
    void updateRoleStatus(Long id, Integer status);

    /**
     * 检查角色编码是否存在
     */
    boolean checkRoleCodeExists(Long tenantId, String roleCode, Long excludeId);

    /**
     * 检查角色名称是否存在
     */
    boolean checkRoleNameExists(Long tenantId, String roleName, Long excludeId);

    /**
     * 根据租户ID获取所有角色
     */
    List<RoleResponseDto> getRolesByTenantId(Long tenantId);
} 