package com.dipspro.modules.role.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.role.dto.RoleCreateDto;
import com.dipspro.modules.role.dto.RoleQueryDto;
import com.dipspro.modules.role.dto.RoleResponseDto;
import com.dipspro.modules.role.dto.RoleUpdateDto;
import com.dipspro.modules.role.service.RoleService;

import lombok.extern.slf4j.Slf4j;

/**
 * 角色管理控制器
 */
@RestController
@RequestMapping("/api/sys/role")
@Slf4j
public class RoleController {

    @Autowired
    private RoleService roleService;

    /**
     * 分页查询角色列表
     */
    @GetMapping("/list")
    public ApiResponse<Page<RoleResponseDto>> getRoleList(RoleQueryDto queryDto) {
        log.info("查询角色列表，查询条件: {}", queryDto);
        Page<RoleResponseDto> result = roleService.getRoleList(queryDto);
        return ApiResponse.success(result);
    }

    /**
     * 根据ID获取角色详情
     */
    @GetMapping("/detail")
    public ApiResponse<RoleResponseDto> getRoleDetail(@RequestParam Long id) {
        log.info("获取角色详情，角色ID: {}", id);
        RoleResponseDto result = roleService.getRoleById(id);
        return ApiResponse.success(result);
    }

    /**
     * 创建角色
     */
    @PostMapping("/create")
    public ApiResponse<RoleResponseDto> createRole(@Validated @RequestBody RoleCreateDto createDto) {
        log.info("创建角色，角色信息: {}", createDto);
        RoleResponseDto result = roleService.createRole(createDto);
        return ApiResponse.success(result);
    }

    /**
     * 更新角色
     */
    @PostMapping("/edit")
    public ApiResponse<RoleResponseDto> updateRole(@RequestParam Long id, @Validated @RequestBody RoleUpdateDto updateDto) {
        log.info("更新角色，角色ID: {}", id);
        RoleResponseDto result = roleService.updateRole(id, updateDto);
        return ApiResponse.success(result);
    }

    /**
     * 删除角色
     */
    @PostMapping("/delete")
    public ApiResponse<String> deleteRole(@RequestParam Long id) {
        log.info("删除角色，角色ID: {}", id);
        roleService.deleteRole(id);
        return ApiResponse.success("删除成功");
    }

    /**
     * 批量删除角色
     */
    @PostMapping("/batch-delete")
    public ApiResponse<String> batchDeleteRoles(@RequestBody List<Long> ids) {
        log.info("批量删除角色，角色IDs: {}", ids);
        roleService.deleteRoles(ids);
        return ApiResponse.success("批量删除成功");
    }

    /**
     * 更新角色状态
     */
    @PostMapping("/update-status")
    public ApiResponse<String> updateRoleStatus(@RequestParam Long id, @RequestParam Integer status) {
        log.info("更新角色状态，角色ID: {}, 新状态: {}", id, status);
        roleService.updateRoleStatus(id, status);
        return ApiResponse.success("状态更新成功");
    }

    /**
     * 检查角色编码是否存在
     */
    @GetMapping("/check-role-code")
    public ApiResponse<Boolean> checkRoleCode(@RequestParam Long tenantId, @RequestParam String roleCode, @RequestParam(required = false) Long excludeId) {
        boolean exists = roleService.checkRoleCodeExists(tenantId, roleCode, excludeId);
        return ApiResponse.success(exists);
    }

    /**
     * 检查角色名称是否存在
     */
    @GetMapping("/check-role-name")
    public ApiResponse<Boolean> checkRoleName(@RequestParam Long tenantId, @RequestParam String roleName, @RequestParam(required = false) Long excludeId) {
        boolean exists = roleService.checkRoleNameExists(tenantId, roleName, excludeId);
        return ApiResponse.success(exists);
    }

    /**
     * 根据租户ID获取所有角色
     */
    @GetMapping("/all")
    public ApiResponse<List<RoleResponseDto>> getAllRoles(@RequestParam Long tenantId) {
        log.info("获取租户所有角色，租户ID: {}", tenantId);
        List<RoleResponseDto> result = roleService.getRolesByTenantId(tenantId);
        return ApiResponse.success(result);
    }
} 