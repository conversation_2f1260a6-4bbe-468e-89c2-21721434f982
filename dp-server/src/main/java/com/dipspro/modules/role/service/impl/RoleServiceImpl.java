package com.dipspro.modules.role.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.dipspro.modules.role.dto.RoleCreateDto;
import com.dipspro.modules.role.dto.RoleQueryDto;
import com.dipspro.modules.role.dto.RoleResponseDto;
import com.dipspro.modules.role.dto.RoleUpdateDto;
import com.dipspro.modules.role.entity.Role;
import com.dipspro.modules.role.repository.RoleRepository;
import com.dipspro.modules.role.service.RoleService;

import jakarta.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;

/**
 * 角色服务实现类
 */
@Service
@Transactional
@Slf4j
public class RoleServiceImpl implements RoleService {

    @Autowired
    private RoleRepository roleRepository;

    @Override
    public Page<RoleResponseDto> getRoleList(RoleQueryDto queryDto) {
        log.info("查询角色列表，查询参数: {}", queryDto);
        
        // 构建查询条件
        Specification<Role> spec = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            
            // 未删除的角色
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("deleted"), 0));
            
            // 租户ID查询
            if (queryDto.getTenantId() != null) {
                predicate = criteriaBuilder.and(predicate, 
                    criteriaBuilder.equal(root.get("tenantId"), queryDto.getTenantId()));
            }
            
            // 角色编码模糊查询
            if (StringUtils.hasText(queryDto.getRoleCode())) {
                predicate = criteriaBuilder.and(predicate, 
                    criteriaBuilder.like(root.get("roleCode"), "%" + queryDto.getRoleCode() + "%"));
            }
            
            // 角色名称模糊查询
            if (StringUtils.hasText(queryDto.getRoleName())) {
                predicate = criteriaBuilder.and(predicate, 
                    criteriaBuilder.like(root.get("roleName"), "%" + queryDto.getRoleName() + "%"));
            }
            
            // 角色类型精确查询
            if (StringUtils.hasText(queryDto.getRoleType())) {
                predicate = criteriaBuilder.and(predicate, 
                    criteriaBuilder.equal(root.get("roleType"), queryDto.getRoleType()));
            }
            
            // 父角色ID查询
            if (queryDto.getParentId() != null) {
                predicate = criteriaBuilder.and(predicate, 
                    criteriaBuilder.equal(root.get("parentId"), queryDto.getParentId()));
            }
            
            // 状态精确查询
            if (queryDto.getStatus() != null) {
                predicate = criteriaBuilder.and(predicate, 
                    criteriaBuilder.equal(root.get("status"), queryDto.getStatus()));
            }
            
            return predicate;
        };
        
        // 分页和排序
        int pageNumber = queryDto.getPage() - 1; // 转换为从0开始（Spring Data JPA要求）
        int pageSize = queryDto.getSize();
        
        Sort sort = Sort.by(Sort.Direction.ASC, "sortOrder").and(Sort.by(Sort.Direction.DESC, "createdTime"));
        Pageable pageable = PageRequest.of(pageNumber, pageSize, sort);
        
        Page<Role> rolePage = roleRepository.findAll(spec, pageable);
        log.info("查询结果：总数={}, 当前页数据量={}, 页码={}, 页大小={}", 
            rolePage.getTotalElements(), rolePage.getNumberOfElements(), 
            rolePage.getNumber(), rolePage.getSize());
        
        // 转换为DTO
        return rolePage.map(this::convertToResponseDto);
    }

    @Override
    public RoleResponseDto getRoleById(Long id) {
        Optional<Role> roleOpt = roleRepository.findById(id);
        if (roleOpt.isEmpty() || roleOpt.get().getDeleted() == 1) {
            throw new RuntimeException("角色不存在");
        }
        return convertToResponseDto(roleOpt.get());
    }

    @Override
    public RoleResponseDto createRole(RoleCreateDto createDto) {
        // 检查角色编码是否已存在
        if (roleRepository.findByTenantIdAndRoleCodeAndDeleted(createDto.getTenantId(), createDto.getRoleCode(), 0).isPresent()) {
            throw new RuntimeException("角色编码已存在");
        }
        
        // 检查角色名称是否已存在
        if (roleRepository.findByTenantIdAndRoleNameAndDeleted(createDto.getTenantId(), createDto.getRoleName(), 0).isPresent()) {
            throw new RuntimeException("角色名称已存在");
        }
        
        // 创建角色实体
        Role role = new Role();
        BeanUtils.copyProperties(createDto, role);
        
        // 设置默认值
        role.setDeleted(0);
        role.setCreatedTime(LocalDateTime.now());
        role.setUpdatedTime(LocalDateTime.now());
        
        Role savedRole = roleRepository.save(role);
        log.info("创建角色成功，角色ID: {}, 角色编码: {}", savedRole.getId(), savedRole.getRoleCode());
        
        return convertToResponseDto(savedRole);
    }

    @Override
    public RoleResponseDto updateRole(Long id, RoleUpdateDto updateDto) {
        Optional<Role> roleOpt = roleRepository.findById(id);
        if (roleOpt.isEmpty() || roleOpt.get().getDeleted() == 1) {
            throw new RuntimeException("角色不存在");
        }
        
        Role role = roleOpt.get();
        
        // 检查角色编码是否已被其他角色使用
        if (StringUtils.hasText(updateDto.getRoleCode()) && 
            !updateDto.getRoleCode().equals(role.getRoleCode()) &&
            roleRepository.existsByRoleCodeExcludingId(role.getTenantId(), updateDto.getRoleCode(), id)) {
            throw new RuntimeException("角色编码已被其他角色使用");
        }
        
        // 检查角色名称是否已被其他角色使用
        if (StringUtils.hasText(updateDto.getRoleName()) && 
            !updateDto.getRoleName().equals(role.getRoleName()) &&
            roleRepository.existsByRoleNameExcludingId(role.getTenantId(), updateDto.getRoleName(), id)) {
            throw new RuntimeException("角色名称已被其他角色使用");
        }
        
        // 更新字段
        if (StringUtils.hasText(updateDto.getRoleCode())) {
            role.setRoleCode(updateDto.getRoleCode());
        }
        if (StringUtils.hasText(updateDto.getRoleName())) {
            role.setRoleName(updateDto.getRoleName());
        }
        if (StringUtils.hasText(updateDto.getRoleType())) {
            role.setRoleType(updateDto.getRoleType());
        }
        if (updateDto.getParentId() != null) {
            role.setParentId(updateDto.getParentId());
        }
        if (updateDto.getLevel() != null) {
            role.setLevel(updateDto.getLevel());
        }
        if (updateDto.getSortOrder() != null) {
            role.setSortOrder(updateDto.getSortOrder());
        }
        if (updateDto.getStatus() != null) {
            role.setStatus(updateDto.getStatus());
        }
        if (updateDto.getDescription() != null) {
            role.setDescription(updateDto.getDescription());
        }
        
        role.setUpdatedTime(LocalDateTime.now());
        
        Role savedRole = roleRepository.save(role);
        log.info("更新角色成功，角色ID: {}, 角色编码: {}", savedRole.getId(), savedRole.getRoleCode());
        
        return convertToResponseDto(savedRole);
    }

    @Override
    public void deleteRole(Long id) {
        Optional<Role> roleOpt = roleRepository.findById(id);
        if (roleOpt.isEmpty() || roleOpt.get().getDeleted() == 1) {
            throw new RuntimeException("角色不存在");
        }
        
        Role role = roleOpt.get();
        role.setDeleted(1);
        role.setUpdatedTime(LocalDateTime.now());
        
        roleRepository.save(role);
        log.info("删除角色成功，角色ID: {}", id);
    }

    @Override
    public void deleteRoles(List<Long> ids) {
        List<Role> roles = roleRepository.findAllById(ids);
        roles.forEach(role -> {
            if (role.getDeleted() == 0) {
                role.setDeleted(1);
                role.setUpdatedTime(LocalDateTime.now());
            }
        });
        
        roleRepository.saveAll(roles);
        log.info("批量删除角色成功，角色数量: {}", roles.size());
    }

    @Override
    public void updateRoleStatus(Long id, Integer status) {
        Optional<Role> roleOpt = roleRepository.findById(id);
        if (roleOpt.isEmpty() || roleOpt.get().getDeleted() == 1) {
            throw new RuntimeException("角色不存在");
        }
        
        Role role = roleOpt.get();
        role.setStatus(status);
        role.setUpdatedTime(LocalDateTime.now());
        
        roleRepository.save(role);
        log.info("更新角色状态成功，角色ID: {}, 状态: {}", id, status);
    }

    @Override
    public boolean checkRoleCodeExists(Long tenantId, String roleCode, Long excludeId) {
        if (excludeId != null) {
            return roleRepository.existsByRoleCodeExcludingId(tenantId, roleCode, excludeId);
        } else {
            return roleRepository.findByTenantIdAndRoleCodeAndDeleted(tenantId, roleCode, 0).isPresent();
        }
    }

    @Override
    public boolean checkRoleNameExists(Long tenantId, String roleName, Long excludeId) {
        if (excludeId != null) {
            return roleRepository.existsByRoleNameExcludingId(tenantId, roleName, excludeId);
        } else {
            return roleRepository.findByTenantIdAndRoleNameAndDeleted(tenantId, roleName, 0).isPresent();
        }
    }

    @Override
    public List<RoleResponseDto> getRolesByTenantId(Long tenantId) {
        List<Role> roles = roleRepository.findByTenantIdAndDeletedOrderBySortOrderAscIdAsc(tenantId, 0);
        return roles.stream()
                .map(this::convertToResponseDto)
                .collect(Collectors.toList());
    }

    /**
     * 转换为响应DTO
     */
    private RoleResponseDto convertToResponseDto(Role role) {
        RoleResponseDto dto = new RoleResponseDto();
        BeanUtils.copyProperties(role, dto);
        return dto;
    }
} 