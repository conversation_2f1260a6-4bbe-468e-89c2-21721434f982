package com.dipspro.modules.auth.service;

import java.time.LocalDateTime;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dipspro.modules.auth.dto.LoginRequest;
import com.dipspro.modules.auth.dto.LoginResponse;
import com.dipspro.modules.auth.dto.RegisterRequest;
import com.dipspro.modules.auth.dto.ResetPasswordRequest;
import com.dipspro.modules.tenant.entity.Tenant;
import com.dipspro.modules.tenant.repository.TenantRepository;
import com.dipspro.modules.user.entity.User;
import com.dipspro.modules.user.repository.UserRepository;
import com.dipspro.util.JwtUtil;
import com.dipspro.util.PasswordUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 认证服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {

    private final UserRepository userRepository;
    private final TenantRepository tenantRepository;
    private final JwtUtil jwtUtil;
    private final PasswordUtil passwordUtil;

    /**
     * 用户登录
     */
    @Transactional
    public LoginResponse login(LoginRequest request) {
        // 1. 查找用户（通过用户名、邮箱或手机号）
        User user = userRepository.findByLoginIdAndDeleted(request.getLoginId(), 0)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        if (user.getStatus() != 1) {
            throw new RuntimeException("用户已被禁用或锁定");
        }

        // 2. 获取用户关联的租户
        Tenant tenant = tenantRepository.findByIdAndDeleted(user.getTenantId(), 0)
                .orElseThrow(() -> new RuntimeException("租户不存在"));

        if (tenant.getStatus() != 1) {
            throw new RuntimeException("租户已被禁用或过期");
        }

        // 3. 验证密码
        if (!passwordUtil.matches(request.getPassword(), user.getSalt(), user.getPassword())) {
            throw new RuntimeException("密码错误");
        }

        // 4. 更新登录信息
        user.setLastLoginTime(LocalDateTime.now());
        user.setLastLoginIp(request.getIpAddress());
        user.setLoginCount(user.getLoginCount() + 1);
        userRepository.save(user);

        // 5. 生成JWT令牌
        String accessToken = jwtUtil.generateAccessToken(user.getId(), user.getTenantId(), user.getUsername());
        String refreshToken = jwtUtil.generateRefreshToken(user.getId(), user.getTenantId(), user.getUsername());

        return LoginResponse.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .userId(user.getId())
                .username(user.getUsername())
                .realName(user.getRealName())
                .avatar(user.getAvatar())
                .tenantId(user.getTenantId())
                .tenantName(tenant.getTenantName())
                .build();
    }

    /**
     * 用户注册
     */
    @Transactional
    public void register(RegisterRequest request) {
        // 1. 查找或创建租户
        Tenant tenant = tenantRepository.findByTenantNameAndDeleted(request.getTenantName(), 0)
                .orElseGet(() -> {
                    // 租户不存在，创建新租户
                    Tenant newTenant = new Tenant();
                    newTenant.setTenantName(request.getTenantName());
                    // 生成租户编码（使用租户名称的拼音或简化版本）
                    newTenant.setTenantCode(generateTenantCode(request.getTenantName()));
                    newTenant.setStatus(1); // 启用状态
                    return tenantRepository.save(newTenant);
                });

        if (tenant.getStatus() != 1) {
            throw new RuntimeException("租户已被禁用或过期");
        }

        // 2. 检查用户名是否已存在（全局检查）
        if (userRepository.existsByUsernameAndDeleted(request.getUsername(), 0)) {
            throw new RuntimeException("用户名已存在");
        }

        // 3. 检查邮箱是否已存在（全局检查）
        if (request.getEmail() != null && 
            userRepository.existsByEmailAndDeleted(request.getEmail(), 0)) {
            throw new RuntimeException("邮箱已存在");
        }

        // 4. 检查手机号是否已存在（全局检查）
        if (request.getPhone() != null && 
            userRepository.existsByPhoneAndDeleted(request.getPhone(), 0)) {
            throw new RuntimeException("手机号已存在");
        }

        // 5. 验证密码强度
        if (!passwordUtil.isStrongPassword(request.getPassword())) {
            throw new RuntimeException("密码强度不够，需要包含大小写字母、数字和特殊字符，长度至少8位");
        }

        // 6. 创建用户
        String salt = passwordUtil.generateSalt();
        String encodedPassword = passwordUtil.encodePassword(request.getPassword(), salt);

        User user = new User();
        user.setTenantId(tenant.getId());
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPhone(request.getPhone());
        user.setPassword(encodedPassword);
        user.setSalt(salt);
        user.setRealName(request.getRealName());
        user.setNickname(request.getNickname());
        user.setPasswordUpdateTime(LocalDateTime.now());

        userRepository.save(user);
        log.info("用户注册成功: tenantId={}, tenantName={}, username={}", tenant.getId(), tenant.getTenantName(), request.getUsername());
    }

    /**
     * 生成租户编码
     */
    private String generateTenantCode(String tenantName) {
        // 简单的租户编码生成逻辑：租户名称 + 时间戳后4位
        String baseCode = tenantName.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "").toLowerCase();
        if (baseCode.length() > 10) {
            baseCode = baseCode.substring(0, 10);
        }
        String timestamp = String.valueOf(System.currentTimeMillis());
        return baseCode + "_" + timestamp.substring(timestamp.length() - 4);
    }

    /**
     * 重置密码
     */
    @Transactional
    public void resetPassword(ResetPasswordRequest request) {
        // 1. 查找用户
        User user = userRepository.findByLoginIdAndDeleted(request.getLoginId(), 0)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 2. 验证租户名称（可选验证）
        if (request.getTenantName() != null) {
            Tenant tenant = tenantRepository.findByIdAndDeleted(user.getTenantId(), 0)
                    .orElseThrow(() -> new RuntimeException("租户不存在"));
            
            if (!tenant.getTenantName().equals(request.getTenantName())) {
                throw new RuntimeException("租户信息不匹配");
            }
        }

        // 3. 验证密码强度
        if (!passwordUtil.isStrongPassword(request.getNewPassword())) {
            throw new RuntimeException("密码强度不够，需要包含大小写字母、数字和特殊字符，长度至少8位");
        }

        // 4. 更新密码
        String salt = passwordUtil.generateSalt();
        String encodedPassword = passwordUtil.encodePassword(request.getNewPassword(), salt);

        user.setPassword(encodedPassword);
        user.setSalt(salt);
        user.setPasswordUpdateTime(LocalDateTime.now());

        userRepository.save(user);
        log.info("密码重置成功: userId={}, tenantId={}", user.getId(), user.getTenantId());
    }

    /**
     * 刷新令牌
     */
    public LoginResponse refreshToken(String refreshToken) {
        if (!jwtUtil.validateRefreshToken(refreshToken)) {
            throw new RuntimeException("刷新令牌无效或已过期");
        }

        Long userId = jwtUtil.getUserIdFromToken(refreshToken);
        Long tenantId = jwtUtil.getTenantIdFromToken(refreshToken);
        String username = jwtUtil.getUsernameFromToken(refreshToken);

        // 验证用户是否仍然有效
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        if (user.getStatus() != 1 || user.getDeleted() == 1) {
            throw new RuntimeException("用户已被禁用或删除");
        }

        Tenant tenant = tenantRepository.findById(tenantId)
                .orElseThrow(() -> new RuntimeException("租户不存在"));

        if (tenant.getStatus() != 1 || tenant.getDeleted() == 1) {
            throw new RuntimeException("租户已被禁用或删除");
        }

        // 生成新的令牌
        String newAccessToken = jwtUtil.generateAccessToken(userId, tenantId, username);
        String newRefreshToken = jwtUtil.generateRefreshToken(userId, tenantId, username);

        return LoginResponse.builder()
                .accessToken(newAccessToken)
                .refreshToken(newRefreshToken)
                .userId(user.getId())
                .username(user.getUsername())
                .realName(user.getRealName())
                .avatar(user.getAvatar())
                .tenantId(user.getTenantId())
                .tenantName(tenant.getTenantName())
                .build();
    }
} 