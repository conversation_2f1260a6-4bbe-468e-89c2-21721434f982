package com.dipspro.modules.auth.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 重置密码请求DTO
 */
@Data
public class ResetPasswordRequest {

    @NotBlank(message = "租户名称不能为空")
    private String tenantName;

    @NotBlank(message = "登录标识不能为空")
    private String loginId; // 可以是用户名、邮箱或手机号

    @NotBlank(message = "新密码不能为空")
    private String newPassword;

    private String verificationCode; // 验证码（可选，用于邮箱或短信验证）
} 