package com.dipspro.modules.auth.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.dipspro.common.dto.ApiResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * 测试控制器 - 用于验证路由是否正常工作
 */
@Slf4j
@RestController
@RequestMapping("/api/test")
public class TestController {

    @GetMapping("/hello")
    public ApiResponse<String> hello() {
        log.info("测试接口被调用: /api/test/hello");
        return ApiResponse.success("Hello, DIPS Pro!");
    }

    @PostMapping("/echo")
    public ApiResponse<String> echo() {
        log.info("测试接口被调用: /api/test/echo");
        return ApiResponse.success("Echo from DIPS Pro!");
    }
} 