package com.dipspro.modules.tenant.dto;

import lombok.Data;

/**
 * 租户查询DTO
 */
@Data
public class TenantQueryDto {

    /**
     * 页码
     */
    private Integer page = 1;

    /**
     * 每页大小
     */
    private Integer size = 10;

    /**
     * 租户编码（模糊查询）
     */
    private String tenantCode;

    /**
     * 租户名称（模糊查询）
     */
    private String tenantName;

    /**
     * 状态：0-禁用，1-启用，2-过期
     */
    private Integer status;

    /**
     * 套餐类型：basic,standard,premium,enterprise
     */
    private String packageType;

    /**
     * 联系人姓名（模糊查询）
     */
    private String contactName;

    /**
     * 联系人电话（模糊查询）
     */
    private String contactPhone;

    /**
     * 联系人邮箱（模糊查询）
     */
    private String contactEmail;

    /**
     * 排序字段
     */
    private String sortField = "id";

    /**
     * 排序方向：asc,desc
     */
    private String sortOrder = "desc";
} 