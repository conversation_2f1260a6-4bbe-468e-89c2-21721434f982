package com.dipspro.modules.tenant.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.tenant.dto.TenantCreateDto;
import com.dipspro.modules.tenant.dto.TenantQueryDto;
import com.dipspro.modules.tenant.dto.TenantResponseDto;
import com.dipspro.modules.tenant.dto.TenantUpdateDto;
import com.dipspro.modules.tenant.service.TenantService;

import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;

/**
 * 租户管理控制器
 */
@RestController
@RequestMapping("/api/sys/tenant")
@Slf4j
public class TenantController {

    @Autowired
    private TenantService tenantService;

    /**
     * 分页查询租户列表
     */
    @GetMapping("/list")
    public ApiResponse<Page<TenantResponseDto>> getTenantList(TenantQueryDto queryDto) {
        try {
            Page<TenantResponseDto> result = tenantService.getTenantList(queryDto);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("查询租户列表失败", e);
            return ApiResponse.error("查询租户列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取租户详情
     */
    @GetMapping("/detail")
    public ApiResponse<TenantResponseDto> getTenantDetail(@RequestParam Long id) {
        try {
            TenantResponseDto result = tenantService.getTenantById(id);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取租户详情失败，ID: {}", id, e);
            return ApiResponse.error("获取租户详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建租户
     */
    @PostMapping("/create")
    public ApiResponse<TenantResponseDto> createTenant(@Valid @RequestBody TenantCreateDto createDto) {
        try {
            TenantResponseDto result = tenantService.createTenant(createDto);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("创建租户失败", e);
            return ApiResponse.error("创建租户失败: " + e.getMessage());
        }
    }

    /**
     * 更新租户
     */
    @PostMapping("/edit")
    public ApiResponse<TenantResponseDto> updateTenant(@RequestParam Long id, @Valid @RequestBody TenantUpdateDto updateDto) {
        try {
            TenantResponseDto result = tenantService.updateTenant(id, updateDto);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("更新租户失败，ID: {}", id, e);
            return ApiResponse.error("更新租户失败: " + e.getMessage());
        }
    }

    /**
     * 删除租户
     */
    @PostMapping("/delete")
    public ApiResponse<Void> deleteTenant(@RequestParam Long id) {
        try {
            tenantService.deleteTenant(id);
            return ApiResponse.success(null);
        } catch (Exception e) {
            log.error("删除租户失败，ID: {}", id, e);
            return ApiResponse.error("删除租户失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除租户
     */
    @PostMapping("/batch-delete")
    public ApiResponse<Void> deleteTenants(@RequestBody List<Long> ids) {
        try {
            tenantService.deleteTenants(ids);
            return ApiResponse.success(null);
        } catch (Exception e) {
            log.error("批量删除租户失败，IDs: {}", ids, e);
            return ApiResponse.error("批量删除租户失败: " + e.getMessage());
        }
    }

    /**
     * 更新租户状态
     */
    @PostMapping("/update-status")
    public ApiResponse<Void> updateTenantStatus(@RequestParam Long id, @RequestParam Integer status) {
        try {
            tenantService.updateTenantStatus(id, status);
            return ApiResponse.success(null);
        } catch (Exception e) {
            log.error("更新租户状态失败，ID: {}, 状态: {}", id, status, e);
            return ApiResponse.error("更新租户状态失败: " + e.getMessage());
        }
    }

    /**
     * 检查租户编码是否存在
     */
    @GetMapping("/check-tenant-code")
    public ApiResponse<Boolean> checkTenantCodeExists(@RequestParam String tenantCode, @RequestParam(required = false) Long excludeId) {
        try {
            boolean exists = tenantService.checkTenantCodeExists(tenantCode, excludeId);
            return ApiResponse.success(exists);
        } catch (Exception e) {
            log.error("检查租户编码失败，编码: {}", tenantCode, e);
            return ApiResponse.error("检查租户编码失败: " + e.getMessage());
        }
    }

    /**
     * 检查租户名称是否存在
     */
    @GetMapping("/check-tenant-name")
    public ApiResponse<Boolean> checkTenantNameExists(@RequestParam String tenantName, @RequestParam(required = false) Long excludeId) {
        try {
            boolean exists = tenantService.checkTenantNameExists(tenantName, excludeId);
            return ApiResponse.success(exists);
        } catch (Exception e) {
            log.error("检查租户名称失败，名称: {}", tenantName, e);
            return ApiResponse.error("检查租户名称失败: " + e.getMessage());
        }
    }

    /**
     * 检查域名是否存在
     */
    @GetMapping("/check-domain")
    public ApiResponse<Boolean> checkDomainExists(@RequestParam String domain, @RequestParam(required = false) Long excludeId) {
        try {
            boolean exists = tenantService.checkDomainExists(domain, excludeId);
            return ApiResponse.success(exists);
        } catch (Exception e) {
            log.error("检查域名失败，域名: {}", domain, e);
            return ApiResponse.error("检查域名失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有启用的租户
     */
    @GetMapping("/active")
    public ApiResponse<List<TenantResponseDto>> getAllActiveTenants() {
        try {
            List<TenantResponseDto> result = tenantService.getAllActiveTenants();
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取启用租户列表失败", e);
            return ApiResponse.error("获取启用租户列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据套餐类型获取租户列表
     */
    @GetMapping("/by-package-type")
    public ApiResponse<List<TenantResponseDto>> getTenantsByPackageType(@RequestParam String packageType) {
        try {
            List<TenantResponseDto> result = tenantService.getTenantsByPackageType(packageType);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("根据套餐类型获取租户列表失败，套餐类型: {}", packageType, e);
            return ApiResponse.error("根据套餐类型获取租户列表失败: " + e.getMessage());
        }
    }
} 