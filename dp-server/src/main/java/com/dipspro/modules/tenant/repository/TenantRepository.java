package com.dipspro.modules.tenant.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.dipspro.modules.tenant.entity.Tenant;

/**
 * 租户Repository接口
 */
@Repository
public interface TenantRepository extends JpaRepository<Tenant, Long>, JpaSpecificationExecutor<Tenant> {

    /**
     * 根据租户编码查找租户
     */
    Optional<Tenant> findByTenantCodeAndDeleted(String tenantCode, Integer deleted);

    /**
     * 根据租户名称查找租户
     */
    Optional<Tenant> findByTenantNameAndDeleted(String tenantName, Integer deleted);

    /**
     * 根据ID查找租户
     */
    Optional<Tenant> findByIdAndDeleted(Long id, Integer deleted);

    /**
     * 根据域名查找租户
     */
    Optional<Tenant> findByDomainAndDeleted(String domain, Integer deleted);

    /**
     * 检查租户编码是否存在
     */
    boolean existsByTenantCodeAndDeleted(String tenantCode, Integer deleted);

    /**
     * 检查租户名称是否存在
     */
    boolean existsByTenantNameAndDeleted(String tenantName, Integer deleted);

    /**
     * 检查域名是否存在
     */
    boolean existsByDomainAndDeleted(String domain, Integer deleted);

    /**
     * 检查租户编码是否存在（排除指定ID）
     */
    @Query("SELECT COUNT(t) > 0 FROM Tenant t WHERE t.tenantCode = :tenantCode AND t.id != :excludeId AND t.deleted = 0")
    boolean existsByTenantCodeExcludingId(@Param("tenantCode") String tenantCode, @Param("excludeId") Long excludeId);

    /**
     * 检查租户名称是否存在（排除指定ID）
     */
    @Query("SELECT COUNT(t) > 0 FROM Tenant t WHERE t.tenantName = :tenantName AND t.id != :excludeId AND t.deleted = 0")
    boolean existsByTenantNameExcludingId(@Param("tenantName") String tenantName, @Param("excludeId") Long excludeId);

    /**
     * 检查域名是否存在（排除指定ID）
     */
    @Query("SELECT COUNT(t) > 0 FROM Tenant t WHERE t.domain = :domain AND t.id != :excludeId AND t.deleted = 0")
    boolean existsByDomainExcludingId(@Param("domain") String domain, @Param("excludeId") Long excludeId);

    /**
     * 获取所有启用的租户
     */
    List<Tenant> findByStatusAndDeletedOrderByIdDesc(Integer status, Integer deleted);

    /**
     * 根据套餐类型查找租户
     */
    List<Tenant> findByPackageTypeAndDeletedOrderByIdDesc(String packageType, Integer deleted);

    /**
     * 统计租户数量
     */
    long countByDeleted(Integer deleted);

    /**
     * 统计指定状态的租户数量
     */
    long countByStatusAndDeleted(Integer status, Integer deleted);
} 