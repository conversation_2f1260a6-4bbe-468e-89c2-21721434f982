package com.dipspro.modules.tenant.service;

import java.util.List;

import org.springframework.data.domain.Page;

import com.dipspro.modules.tenant.dto.TenantCreateDto;
import com.dipspro.modules.tenant.dto.TenantQueryDto;
import com.dipspro.modules.tenant.dto.TenantResponseDto;
import com.dipspro.modules.tenant.dto.TenantUpdateDto;

/**
 * 租户服务接口
 */
public interface TenantService {

    /**
     * 分页查询租户列表
     */
    Page<TenantResponseDto> getTenantList(TenantQueryDto queryDto);

    /**
     * 根据ID获取租户详情
     */
    TenantResponseDto getTenantById(Long id);

    /**
     * 创建租户
     */
    TenantResponseDto createTenant(TenantCreateDto createDto);

    /**
     * 更新租户
     */
    TenantResponseDto updateTenant(Long id, TenantUpdateDto updateDto);

    /**
     * 删除租户
     */
    void deleteTenant(Long id);

    /**
     * 批量删除租户
     */
    void deleteTenants(List<Long> ids);

    /**
     * 更新租户状态
     */
    void updateTenantStatus(Long id, Integer status);

    /**
     * 检查租户编码是否存在
     */
    boolean checkTenantCodeExists(String tenantCode, Long excludeId);

    /**
     * 检查租户名称是否存在
     */
    boolean checkTenantNameExists(String tenantName, Long excludeId);

    /**
     * 检查域名是否存在
     */
    boolean checkDomainExists(String domain, Long excludeId);

    /**
     * 获取所有启用的租户
     */
    List<TenantResponseDto> getAllActiveTenants();

    /**
     * 根据套餐类型获取租户列表
     */
    List<TenantResponseDto> getTenantsByPackageType(String packageType);
} 