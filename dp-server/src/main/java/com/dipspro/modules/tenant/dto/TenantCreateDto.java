package com.dipspro.modules.tenant.dto;

import java.time.LocalDateTime;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 租户创建DTO
 */
@Data
public class TenantCreateDto {

    /**
     * 租户编码
     */
    @NotBlank(message = "租户编码不能为空")
    @Size(min = 2, max = 64, message = "租户编码长度必须在2-64个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_-]+$", message = "租户编码只能包含字母、数字、下划线和横线")
    private String tenantCode;

    /**
     * 租户名称
     */
    @NotBlank(message = "租户名称不能为空")
    @Size(min = 2, max = 128, message = "租户名称长度必须在2-128个字符之间")
    private String tenantName;

    /**
     * 租户域名
     */
    @Size(max = 128, message = "域名长度不能超过128个字符")
    @Pattern(regexp = "^[a-zA-Z0-9.-]*$", message = "域名格式不正确")
    private String domain;

    /**
     * 状态：0-禁用，1-启用，2-过期
     */
    @NotNull(message = "状态不能为空")
    private Integer status = 1;

    /**
     * 套餐类型：basic,standard,premium,enterprise
     */
    @NotBlank(message = "套餐类型不能为空")
    @Pattern(regexp = "^(basic|standard|premium|enterprise)$", message = "套餐类型必须是basic、standard、premium或enterprise")
    private String packageType;

    /**
     * 到期时间
     */
    private LocalDateTime expireTime;

    /**
     * 联系人姓名
     */
    @Size(max = 64, message = "联系人姓名长度不能超过64个字符")
    private String contactName;

    /**
     * 联系人电话
     */
    @Size(max = 32, message = "联系人电话长度不能超过32个字符")
    @Pattern(regexp = "^[0-9-+()\\s]*$", message = "联系人电话格式不正确")
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    @Size(max = 128, message = "联系人邮箱长度不能超过128个字符")
    @Email(message = "联系人邮箱格式不正确")
    private String contactEmail;

    /**
     * Logo地址
     */
    @Size(max = 512, message = "Logo地址长度不能超过512个字符")
    private String logoUrl;

    /**
     * 租户描述
     */
    @Size(max = 1000, message = "租户描述长度不能超过1000个字符")
    private String description;
} 