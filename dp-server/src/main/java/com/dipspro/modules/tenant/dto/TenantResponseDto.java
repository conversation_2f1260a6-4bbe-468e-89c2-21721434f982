package com.dipspro.modules.tenant.dto;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * 租户响应DTO
 */
@Data
public class TenantResponseDto {

    /**
     * 租户ID
     */
    private Long id;

    /**
     * 租户编码
     */
    private String tenantCode;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 租户域名
     */
    private String domain;

    /**
     * 状态：0-禁用，1-启用，2-过期
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusText;

    /**
     * 套餐类型：basic,standard,premium,enterprise
     */
    private String packageType;

    /**
     * 套餐类型描述
     */
    private String packageTypeText;

    /**
     * 到期时间
     */
    private LocalDateTime expireTime;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    private String contactEmail;

    /**
     * Logo地址
     */
    private String logoUrl;

    /**
     * 租户描述
     */
    private String description;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 创建人
     */
    private Long createdBy;

    /**
     * 更新人
     */
    private Long updatedBy;

    /**
     * 是否过期
     */
    private Boolean expired;

    /**
     * 剩余天数
     */
    private Long remainingDays;
} 