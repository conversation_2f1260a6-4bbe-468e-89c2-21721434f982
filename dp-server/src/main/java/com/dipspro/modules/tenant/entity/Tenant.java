package com.dipspro.modules.tenant.entity;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 租户基础信息实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "sys_tenant")
public class Tenant {

    /**
     * 租户ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 租户编码
     */
    @Column(name = "tenant_code", nullable = false, unique = true, length = 64)
    private String tenantCode;

    /**
     * 租户名称
     */
    @Column(name = "tenant_name", nullable = false, length = 128)
    private String tenantName;

    /**
     * 租户域名
     */
    @Column(name = "domain", length = 128)
    private String domain;

    /**
     * 状态：0-禁用，1-启用，2-过期
     */
    @Column(name = "status", nullable = false)
    private Integer status = 1;

    /**
     * 套餐类型：basic,standard,premium,enterprise
     */
    @Column(name = "package_type", nullable = false, length = 32)
    private String packageType;

    /**
     * 到期时间
     */
    @Column(name = "expire_time")
    private LocalDateTime expireTime;

    /**
     * 联系人姓名
     */
    @Column(name = "contact_name", length = 64)
    private String contactName;

    /**
     * 联系人电话
     */
    @Column(name = "contact_phone", length = 32)
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    @Column(name = "contact_email", length = 128)
    private String contactEmail;

    /**
     * Logo地址
     */
    @Column(name = "logo_url", length = 512)
    private String logoUrl;

    /**
     * 租户描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * 创建时间
     */
    @Column(name = "created_time", nullable = false, updatable = false)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Column(name = "updated_time", nullable = false)
    private LocalDateTime updatedTime;

    /**
     * 创建人
     */
    @Column(name = "created_by")
    private Long createdBy;

    /**
     * 更新人
     */
    @Column(name = "updated_by")
    private Long updatedBy;

    /**
     * 删除标记：0-未删除，1-已删除
     */
    @Column(name = "deleted", nullable = false)
    private Integer deleted = 0;

    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        createdTime = now;
        updatedTime = now;
    }

    @PreUpdate
    protected void onUpdate() {
        updatedTime = LocalDateTime.now();
    }
} 