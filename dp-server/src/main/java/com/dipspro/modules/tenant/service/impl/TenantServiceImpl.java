package com.dipspro.modules.tenant.service.impl;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.dipspro.modules.tenant.dto.TenantCreateDto;
import com.dipspro.modules.tenant.dto.TenantQueryDto;
import com.dipspro.modules.tenant.dto.TenantResponseDto;
import com.dipspro.modules.tenant.dto.TenantUpdateDto;
import com.dipspro.modules.tenant.entity.Tenant;
import com.dipspro.modules.tenant.repository.TenantRepository;
import com.dipspro.modules.tenant.service.TenantService;

import jakarta.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;

/**
 * 租户服务实现类
 */
@Service
@Transactional
@Slf4j
public class TenantServiceImpl implements TenantService {

    @Autowired
    private TenantRepository tenantRepository;

    @Override
    public Page<TenantResponseDto> getTenantList(TenantQueryDto queryDto) {
        // 构建分页参数
        Sort sort = Sort.by(
            "desc".equalsIgnoreCase(queryDto.getSortOrder()) ? Sort.Direction.DESC : Sort.Direction.ASC,
            queryDto.getSortField()
        );
        Pageable pageable = PageRequest.of(queryDto.getPage() - 1, queryDto.getSize(), sort);

        // 构建查询条件
        Specification<Tenant> spec = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.equal(root.get("deleted"), 0);

            if (StringUtils.hasText(queryDto.getTenantCode())) {
                predicate = criteriaBuilder.and(predicate,
                    criteriaBuilder.like(root.get("tenantCode"), "%" + queryDto.getTenantCode() + "%"));
            }

            if (StringUtils.hasText(queryDto.getTenantName())) {
                predicate = criteriaBuilder.and(predicate,
                    criteriaBuilder.like(root.get("tenantName"), "%" + queryDto.getTenantName() + "%"));
            }

            if (queryDto.getStatus() != null) {
                predicate = criteriaBuilder.and(predicate,
                    criteriaBuilder.equal(root.get("status"), queryDto.getStatus()));
            }

            if (StringUtils.hasText(queryDto.getPackageType())) {
                predicate = criteriaBuilder.and(predicate,
                    criteriaBuilder.equal(root.get("packageType"), queryDto.getPackageType()));
            }

            if (StringUtils.hasText(queryDto.getContactName())) {
                predicate = criteriaBuilder.and(predicate,
                    criteriaBuilder.like(root.get("contactName"), "%" + queryDto.getContactName() + "%"));
            }

            if (StringUtils.hasText(queryDto.getContactPhone())) {
                predicate = criteriaBuilder.and(predicate,
                    criteriaBuilder.like(root.get("contactPhone"), "%" + queryDto.getContactPhone() + "%"));
            }

            if (StringUtils.hasText(queryDto.getContactEmail())) {
                predicate = criteriaBuilder.and(predicate,
                    criteriaBuilder.like(root.get("contactEmail"), "%" + queryDto.getContactEmail() + "%"));
            }

            return predicate;
        };

        Page<Tenant> tenantPage = tenantRepository.findAll(spec, pageable);
        return tenantPage.map(this::convertToResponseDto);
    }

    @Override
    public TenantResponseDto getTenantById(Long id) {
        Tenant tenant = tenantRepository.findByIdAndDeleted(id, 0)
            .orElseThrow(() -> new RuntimeException("租户不存在"));
        return convertToResponseDto(tenant);
    }

    @Override
    public TenantResponseDto createTenant(TenantCreateDto createDto) {
        // 检查租户编码是否已存在
        if (tenantRepository.existsByTenantCodeAndDeleted(createDto.getTenantCode(), 0)) {
            throw new RuntimeException("租户编码已存在");
        }

        // 检查租户名称是否已存在
        if (tenantRepository.existsByTenantNameAndDeleted(createDto.getTenantName(), 0)) {
            throw new RuntimeException("租户名称已存在");
        }

        // 检查域名是否已存在
        if (StringUtils.hasText(createDto.getDomain()) && 
            tenantRepository.existsByDomainAndDeleted(createDto.getDomain(), 0)) {
            throw new RuntimeException("域名已存在");
        }

        Tenant tenant = new Tenant();
        BeanUtils.copyProperties(createDto, tenant);
        
        tenant = tenantRepository.save(tenant);
        log.info("创建租户成功，ID: {}, 编码: {}", tenant.getId(), tenant.getTenantCode());
        
        return convertToResponseDto(tenant);
    }

    @Override
    public TenantResponseDto updateTenant(Long id, TenantUpdateDto updateDto) {
        Tenant tenant = tenantRepository.findByIdAndDeleted(id, 0)
            .orElseThrow(() -> new RuntimeException("租户不存在"));

        // 检查租户编码是否已存在（排除当前租户）
        if (StringUtils.hasText(updateDto.getTenantCode()) && 
            tenantRepository.existsByTenantCodeExcludingId(updateDto.getTenantCode(), id)) {
            throw new RuntimeException("租户编码已存在");
        }

        // 检查租户名称是否已存在（排除当前租户）
        if (StringUtils.hasText(updateDto.getTenantName()) && 
            tenantRepository.existsByTenantNameExcludingId(updateDto.getTenantName(), id)) {
            throw new RuntimeException("租户名称已存在");
        }

        // 检查域名是否已存在（排除当前租户）
        if (StringUtils.hasText(updateDto.getDomain()) && 
            tenantRepository.existsByDomainExcludingId(updateDto.getDomain(), id)) {
            throw new RuntimeException("域名已存在");
        }

        // 更新字段
        if (StringUtils.hasText(updateDto.getTenantCode())) {
            tenant.setTenantCode(updateDto.getTenantCode());
        }
        if (StringUtils.hasText(updateDto.getTenantName())) {
            tenant.setTenantName(updateDto.getTenantName());
        }
        if (updateDto.getDomain() != null) {
            tenant.setDomain(updateDto.getDomain());
        }
        if (updateDto.getStatus() != null) {
            tenant.setStatus(updateDto.getStatus());
        }
        if (StringUtils.hasText(updateDto.getPackageType())) {
            tenant.setPackageType(updateDto.getPackageType());
        }
        if (updateDto.getExpireTime() != null) {
            tenant.setExpireTime(updateDto.getExpireTime());
        }
        if (updateDto.getContactName() != null) {
            tenant.setContactName(updateDto.getContactName());
        }
        if (updateDto.getContactPhone() != null) {
            tenant.setContactPhone(updateDto.getContactPhone());
        }
        if (updateDto.getContactEmail() != null) {
            tenant.setContactEmail(updateDto.getContactEmail());
        }
        if (updateDto.getLogoUrl() != null) {
            tenant.setLogoUrl(updateDto.getLogoUrl());
        }
        if (updateDto.getDescription() != null) {
            tenant.setDescription(updateDto.getDescription());
        }

        tenant = tenantRepository.save(tenant);
        log.info("更新租户成功，ID: {}, 编码: {}", tenant.getId(), tenant.getTenantCode());
        
        return convertToResponseDto(tenant);
    }

    @Override
    public void deleteTenant(Long id) {
        Tenant tenant = tenantRepository.findByIdAndDeleted(id, 0)
            .orElseThrow(() -> new RuntimeException("租户不存在"));
        
        tenant.setDeleted(1);
        tenantRepository.save(tenant);
        log.info("删除租户成功，ID: {}, 编码: {}", tenant.getId(), tenant.getTenantCode());
    }

    @Override
    public void deleteTenants(List<Long> ids) {
        List<Tenant> tenants = tenantRepository.findAllById(ids);
        tenants.forEach(tenant -> {
            if (tenant.getDeleted() == 0) {
                tenant.setDeleted(1);
            }
        });
        tenantRepository.saveAll(tenants);
        log.info("批量删除租户成功，数量: {}", tenants.size());
    }

    @Override
    public void updateTenantStatus(Long id, Integer status) {
        Tenant tenant = tenantRepository.findByIdAndDeleted(id, 0)
            .orElseThrow(() -> new RuntimeException("租户不存在"));
        
        tenant.setStatus(status);
        tenantRepository.save(tenant);
        log.info("更新租户状态成功，ID: {}, 状态: {}", tenant.getId(), status);
    }

    @Override
    public boolean checkTenantCodeExists(String tenantCode, Long excludeId) {
        if (excludeId != null) {
            return tenantRepository.existsByTenantCodeExcludingId(tenantCode, excludeId);
        }
        return tenantRepository.existsByTenantCodeAndDeleted(tenantCode, 0);
    }

    @Override
    public boolean checkTenantNameExists(String tenantName, Long excludeId) {
        if (excludeId != null) {
            return tenantRepository.existsByTenantNameExcludingId(tenantName, excludeId);
        }
        return tenantRepository.existsByTenantNameAndDeleted(tenantName, 0);
    }

    @Override
    public boolean checkDomainExists(String domain, Long excludeId) {
        if (excludeId != null) {
            return tenantRepository.existsByDomainExcludingId(domain, excludeId);
        }
        return tenantRepository.existsByDomainAndDeleted(domain, 0);
    }

    @Override
    public List<TenantResponseDto> getAllActiveTenants() {
        List<Tenant> tenants = tenantRepository.findByStatusAndDeletedOrderByIdDesc(1, 0);
        return tenants.stream()
            .map(this::convertToResponseDto)
            .collect(Collectors.toList());
    }

    @Override
    public List<TenantResponseDto> getTenantsByPackageType(String packageType) {
        List<Tenant> tenants = tenantRepository.findByPackageTypeAndDeletedOrderByIdDesc(packageType, 0);
        return tenants.stream()
            .map(this::convertToResponseDto)
            .collect(Collectors.toList());
    }

    private TenantResponseDto convertToResponseDto(Tenant tenant) {
        TenantResponseDto dto = new TenantResponseDto();
        BeanUtils.copyProperties(tenant, dto);
        
        // 设置状态描述
        switch (tenant.getStatus()) {
            case 0:
                dto.setStatusText("禁用");
                break;
            case 1:
                dto.setStatusText("启用");
                break;
            case 2:
                dto.setStatusText("过期");
                break;
            default:
                dto.setStatusText("未知");
        }
        
        // 设置套餐类型描述
        switch (tenant.getPackageType()) {
            case "basic":
                dto.setPackageTypeText("基础版");
                break;
            case "standard":
                dto.setPackageTypeText("标准版");
                break;
            case "premium":
                dto.setPackageTypeText("高级版");
                break;
            case "enterprise":
                dto.setPackageTypeText("企业版");
                break;
            default:
                dto.setPackageTypeText("未知");
        }
        
        // 计算是否过期和剩余天数
        if (tenant.getExpireTime() != null) {
            LocalDateTime now = LocalDateTime.now();
            dto.setExpired(tenant.getExpireTime().isBefore(now));
            dto.setRemainingDays(ChronoUnit.DAYS.between(now, tenant.getExpireTime()));
        } else {
            dto.setExpired(false);
            dto.setRemainingDays(null);
        }
        
        return dto;
    }
} 