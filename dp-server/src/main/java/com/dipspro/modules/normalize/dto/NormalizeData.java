package com.dipspro.modules.normalize.dto;

import java.io.Serializable;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 归一数据（包括名单 namelist和数据 dataset），包含实际的数据内容
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NormalizeData implements Serializable {
    private String tableName;
    private Long id;
    private Map<String, Object> data;

    @Override
    public String toString() {
        return "NameListDataDto{" +
                "tableName='" + tableName + '\'' +
                ", id=" + id +
                ", data=" + data +
                '}';
    }
} 