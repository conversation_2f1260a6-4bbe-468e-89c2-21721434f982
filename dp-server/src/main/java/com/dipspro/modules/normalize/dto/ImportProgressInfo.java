package com.dipspro.modules.normalize.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据导入Redis的进度信息
 */
@Data
@NoArgsConstructor
public class ImportProgressInfo implements Serializable {
    private boolean importInProgress;
    private int totalTablesCount;
    private int processedTablesCount;
    private long totalRecords;
    private long processedRecords;
    private List<String> processedTables = new ArrayList<>();
    private String currentTable;
    private long startTime;
    private long lastUpdateTime;
    
    // 新增字段，存储每个表的详细进度信息
    private Map<String, TableProgress> tableProgressMap = new HashMap<>();
    
    /**
     * 表级别进度详情
     */
    @Data
    @NoArgsConstructor
    public static class TableProgress implements Serializable {
        private String tableName;
        private long expectedRecordCount;
        private long actualProcessedCount;
        private long startTime;
        private long endTime;
        private boolean completed;
        private String errorMessage;
        
        public TableProgress(String tableName, long expectedRecordCount) {
            this.tableName = tableName;
            this.expectedRecordCount = expectedRecordCount;
            this.actualProcessedCount = 0;
            this.startTime = System.currentTimeMillis();
            this.completed = false;
        }
        
        public void markCompleted() {
            this.completed = true;
            this.endTime = System.currentTimeMillis();
        }
        
        public double getProgressPercentage() {
            if (expectedRecordCount == 0) return 0;
            return (double) actualProcessedCount / expectedRecordCount * 100;
        }
        
        public long getElapsedTime() {
            if (endTime > 0) {
                return endTime - startTime;
            } else {
                return System.currentTimeMillis() - startTime;
            }
        }
    }

    public ImportProgressInfo(boolean importInProgress) {
        this.importInProgress = importInProgress;
        this.totalRecords = 0;
        this.processedRecords = 0;
        this.startTime = System.currentTimeMillis();
        this.lastUpdateTime = System.currentTimeMillis();
    }

    public void initTableProgress(String tableName, long expectedRecordCount) {
        TableProgress progress = new TableProgress(tableName, expectedRecordCount);
        this.tableProgressMap.put(tableName, progress);
    }
    
    public void updateTableProgress(String tableName, long processedCount) {
        TableProgress progress = this.tableProgressMap.get(tableName);
        if (progress != null) {
            progress.setActualProcessedCount(processedCount);
        }
    }
    
    public void completeTableProgress(String tableName, long finalProcessedCount) {
        TableProgress progress = this.tableProgressMap.get(tableName);
        if (progress != null) {
            progress.setActualProcessedCount(finalProcessedCount);
            progress.markCompleted();
        }
    }
    
    public void setTableError(String tableName, String errorMessage) {
        TableProgress progress = this.tableProgressMap.get(tableName);
        if (progress != null) {
            progress.setErrorMessage(errorMessage);
        }
    }

    public double getProgressPercentage() {
        if (totalRecords == 0) return 0;
        return (double) processedRecords / totalRecords * 100;
    }
    
    public long getElapsedTime() {
        return System.currentTimeMillis() - startTime;
    }
    
    public long getEstimatedRemainingTime() {
        if (processedRecords == 0 || totalRecords == 0) {
            return -1; // 无法估计
        }
        
        double processedPercentage = (double) processedRecords / totalRecords;
        if (processedPercentage <= 0) {
            return -1; // 无法估计
        }
        
        long elapsedTime = getElapsedTime();
        long estimatedTotalTime = (long) (elapsedTime / processedPercentage);
        return estimatedTotalTime - elapsedTime;
    }
    
    public List<Map<String, Object>> getRecordDiscrepancyStats() {
        List<Map<String, Object>> stats = new ArrayList<>();
        for (TableProgress progress : tableProgressMap.values()) {
            if (progress.isCompleted()) {
                Map<String, Object> tableStats = new HashMap<>();
                tableStats.put("tableName", progress.getTableName());
                tableStats.put("expectedCount", progress.getExpectedRecordCount());
                tableStats.put("actualCount", progress.getActualProcessedCount());
                tableStats.put("discrepancy", progress.getExpectedRecordCount() - progress.getActualProcessedCount());
                stats.add(tableStats);
            }
        }
        return stats;
    }
} 