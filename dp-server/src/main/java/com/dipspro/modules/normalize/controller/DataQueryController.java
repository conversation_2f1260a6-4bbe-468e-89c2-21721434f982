package com.dipspro.modules.normalize.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.chat.dto.MessageContent;
import com.dipspro.modules.normalize.dto.BatchUserProfileRequest;
import com.dipspro.modules.normalize.dto.ProjectProfileRequest;
import com.dipspro.modules.normalize.service.DataQueryService;
import com.dipspro.modules.profile.dto.ProjectProfileDto;
import com.dipspro.modules.profile.dto.UserProfileDto;
import com.dipspro.util.MaskUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *         2025/5/11 07:57
 * @apiNote 数据查询接口
 */
@Slf4j
@RestController
@RequestMapping("/api/data-query")
public class DataQueryController {

    private final DataQueryService dataQueryService;

    @Autowired
    public DataQueryController(DataQueryService dataQueryService) {
        this.dataQueryService = dataQueryService;
    }

    /**
     * 根据手机号查询用户画像
     *
     * @param mobile 手机号
     * @return 用户画像信息
     */
    @GetMapping("/userProfile/{mobile}")
    public ApiResponse<List<MessageContent>> userProfile(@PathVariable String mobile) {
        try {
            log.info("接收到查询用户画像的请求，手机号: {}", MaskUtil.maskMobile(mobile));

            UserProfileDto result = dataQueryService.userProfile(mobile);
            return ApiResponse.success(result.getProfiles(), "查询用户画像成功");
        } catch (Exception e) {
            log.error("查询用户画像时发生错误", e);
            return ApiResponse.error("查询用户画像失败: " + e.getMessage());
        }
    }

    /**
     * 批量查询用户画像
     *
     * @param request 批量查询请求参数，包含客户特征字符串
     * @return 批量查询结果
     */
    @PostMapping("/userProfile/batch")
    public ApiResponse<List<MessageContent>> batchUserProfile(
            @RequestBody @Validated BatchUserProfileRequest request) {
        try {
            log.info("接收到批量查询用户画像的请求，客户特征字符串长度: {}", request.getCustomerFeatures().length());

            List<MessageContent> result = dataQueryService.batchUserProfile(request);
            return ApiResponse.success(result, "批量查询用户画像成功");
        } catch (Exception e) {
            log.error("批量查询用户画像时发生错误", e);
            return ApiResponse.error("批量查询用户画像失败: " + e.getMessage());
        }
    }

    /**
     * 根据名称查询楼盘画像
     *
     * @param request 楼盘画像请求参数
     * @return 楼盘画像信息
     */
    @PostMapping("/projectProfile")
    public ApiResponse<List<MessageContent>> projectProfile(@RequestBody ProjectProfileRequest request) {
        try {
            log.info("接收到查询楼盘画像的请求，楼盘名称: {}", request.getName());

            ProjectProfileDto result = dataQueryService.projectProfile(request);
            return ApiResponse.success(result.getProfiles(), "查询楼盘画像成功");
        } catch (Exception e) {
            log.error("查询楼盘画像时发生错误", e);
            return ApiResponse.error("查询楼盘画像失败: " + e.getMessage());
        }
    }

    /**
     * 根据名称查询楼盘画像 - 获取楼盘的营销相关信息
     *
     * @param request 楼盘画像请求参数
     * @return 楼盘画像信息
     */
    @PostMapping("/projectProfile/salesInfo")
    public ApiResponse<List<MessageContent>> projectProfileSalesInfo(@RequestBody ProjectProfileRequest request) {
        try {
            log.info("接收到获取楼盘的营销相关信息的请求，楼盘名称: {}", request.getName());

            ProjectProfileDto result = dataQueryService.projectProfileSalesInfo(request);
            return ApiResponse.success(result.getProfiles(), "获取楼盘的营销相关信息成功");
        } catch (Exception e) {
            log.error("获取楼盘的营销相关信息时发生错误", e);
            return ApiResponse.error("获取楼盘的营销相关信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据名称查询楼盘画像 - 了解楼盘周边直线距离 N 公里的其他楼盘情况
     *
     * @param request 楼盘画像请求参数
     * @return 楼盘画像信息
     */
    @PostMapping("/projectProfile/surrounding")
    public ApiResponse<List<MessageContent>> projectProfileSurrounding(@RequestBody ProjectProfileRequest request) {
        try {
            log.info("接收到了解楼盘周边直线距离 N 公里的其他楼盘情况的请求，楼盘名称: {}", request.getName());

            ProjectProfileDto result = dataQueryService.projectProfileSurrounding(request);
            return ApiResponse.success(result.getProfiles(), "了解楼盘周边直线距离 N 公里的其他楼盘情况成功");
        } catch (Exception e) {
            log.error("了解楼盘周边直线距离 N 公里的其他楼盘情况时发生错误", e);
            return ApiResponse.error("了解楼盘周边直线距离 N 公里的其他楼盘情况失败: " + e.getMessage());
        }
    }

    /**
     * 根据名称查询楼盘画像 - 了解楼盘时间范围、属性、指标得分
     *
     * @param request 楼盘画像请求参数
     * @return 楼盘画像信息
     */
    @PostMapping("/projectProfile/ana")
    public ApiResponse<List<MessageContent>> projectProfileAna(@RequestBody ProjectProfileRequest request) {
        try {
            log.info("接收到了解楼盘时间范围、属性、指标得分的请求，楼盘名称: {}", request.getName());

            ProjectProfileDto result = dataQueryService.projectProfileAna(request);
            return ApiResponse.success(result.getProfiles(), "了解楼盘时间范围、属性、指标得分成功");
        } catch (Exception e) {
            log.error("了解楼盘时间范围、属性、指标得分时发生错误", e);
            return ApiResponse.error("了解楼盘时间范围、属性、指标得分失败: " + e.getMessage());
        }
    }
}
