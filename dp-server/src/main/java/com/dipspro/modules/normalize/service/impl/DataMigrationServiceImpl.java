package com.dipspro.modules.normalize.service.impl;

import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.PriorityQueue;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.QueryTimeoutException;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.dipspro.modules.profile.dto.DataStatisticsResultDto;
import com.dipspro.modules.normalize.dto.ImportProgressInfo;
import com.dipspro.modules.normalize.dto.ImportProgressInfo.TableProgress;
import com.dipspro.modules.profile.dto.MobileCombinedStatsDTO;
import com.dipspro.modules.profile.dto.MobileDataRecord;
import com.dipspro.modules.profile.dto.MobileProjectCountDTO;
import com.dipspro.modules.profile.dto.MobileProjectCountResultDTO;
import com.dipspro.modules.profile.dto.MobileStatsResultDto;
import com.dipspro.modules.normalize.dto.NormalizeData;
import com.dipspro.modules.profile.dto.ProjectStatisticsDto;
import com.dipspro.modules.normalize.service.DataMigrationService;
import com.dipspro.util.MaskUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据迁移服务实现类
 */
@Slf4j
@Service
public class DataMigrationServiceImpl implements DataMigrationService {

    // 需要处理的表名列表
    private static final List<String> TABLE_NAMES = Arrays.asList(
            "data_namelist_1",
            "data_namelist_1_24",
            "data_namelist_1_23",
            "data_namelist_1_22",
            "data_namelist_1_21",
            "data_namelist_1_20",
            "dataset_1",
            "dataset_1_24",
            "dataset_1_23",
            "dataset_1_22",
            "dataset_1_21",
            "dataset_1_19_20"
    );

    // 名单表名列表
    private static final List<String> NAMELIST_TABLE_NAMES = Arrays.asList(
            "data_namelist_1",
            "data_namelist_1_24",
            "data_namelist_1_23",
            "data_namelist_1_22",
            "data_namelist_1_21",
            "data_namelist_1_20"
    );

    // Redis key前缀 - 移除前缀，直接使用手机号
    private static final String REDIS_KEY_PREFIX = "";

    // 注入MySQL数据源的JdbcTemplate
    @Autowired
    @Qualifier("mysqlJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    // 导入进度信息
    private final ImportProgressInfo progressInfo = new ImportProgressInfo();

    // 存储每个表的记录数
    private final Map<String, Long> tableRecordCounts = new HashMap<>();

    // 存储每个表实际处理记录数
    private final Map<String, Long> tableProcessedCounts = new HashMap<>();

    // 数据查询条件
    private static final String MOBILE_QUERY_CONDITION =
            "d_mobile is not null and d_mobile<>'' and d_mobile<>'0' and d_mobile<>'****' " +
                    "AND LEFT(d_mobile, 4) <> '0085' " +
                    "AND LEFT(d_mobile, 2) <> '01' AND LEFT(d_mobile, 2) <> '02' " +
                    "AND LEFT(d_mobile, 2) <> '03' AND LEFT(d_mobile, 2) <> '04' " +
                    "AND LEFT(d_mobile, 2) <> '05' AND LEFT(d_mobile, 2) <> '06' " +
                    "AND LEFT(d_mobile, 2) <> '07' AND LEFT(d_mobile, 2) <> '08' " +
                    "AND LEFT(d_mobile, 2) <> '09'"
            // 排除异常分期 @王颐姝
            + " AND data_period<'2030-01'";

    // 在类顶部添加线程池
    private final ExecutorService executorService = Executors.newFixedThreadPool(
            Math.max(2, Runtime.getRuntime().availableProcessors()));

    /**
     * ID范围内部类，用于存储表中符合条件的记录的最小和最大ID
     */
    private static class MinMaxIds {
        private final Long minId;
        private final Long maxId;

        public MinMaxIds(Long minId, Long maxId) {
            this.minId = minId;
            this.maxId = maxId;
        }

        public Long getMinId() {
            return minId;
        }

        public Long getMaxId() {
            return maxId;
        }

        public boolean isValid() {
            return minId != null && maxId != null && minId <= maxId;
        }
    }

    @Override
    public void importDataToRedis(int batchSize) {
        // 如果已有导入进程在运行，直接返回
        if (progressInfo.isImportInProgress()) {
            log.warn("数据导入已在进行中，忽略此次请求。当前进度: {}%",
                    String.format("%.2f", progressInfo.getProgressPercentage()));
            return;
        }

        log.info("准备开始数据导入任务，批次大小: {}，待处理表: {}", batchSize, TABLE_NAMES);

        // 立即设置进度信息为进行中状态，确保状态及时更新
        synchronized (progressInfo) {
            progressInfo.setImportInProgress(true);
            progressInfo.setStartTime(System.currentTimeMillis());
            progressInfo.setLastUpdateTime(System.currentTimeMillis());
        }

        // 使用executorService执行异步任务，而不是默认的ForkJoinPool
        CompletableFuture.runAsync(() -> {
            try {
                log.info("开始异步数据导入任务...");
                // 使用单独的try-catch块处理数据导入过程
                try {
                    processImport(batchSize);
                    log.info("数据导入任务执行完成");
                } catch (Exception e) {
                    log.error("数据导入过程中发生错误: {}", e.getMessage(), e);
                    // 出错时也要重置进度信息状态
                    synchronized (progressInfo) {
                        progressInfo.setImportInProgress(false);
                    }
                }
            } catch (Exception e) {
                log.error("数据导入异步任务执行失败: {}", e.getMessage(), e);
                synchronized (progressInfo) {
                    progressInfo.setImportInProgress(false);
                }
            } finally {
                // 确保任务结束后进度状态被正确设置
                synchronized (progressInfo) {
                    if (progressInfo.isImportInProgress()) {
                        log.warn("导入任务已完成但进度状态仍为进行中，强制设置为已完成");
                        progressInfo.setImportInProgress(false);
                    }
                }
            }
        }, executorService);

        log.info("数据导入异步任务已启动，可通过/api/data-migration/import-progress接口查询进度");

        // 等待一小段时间，确保异步任务已经开始执行
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    @Override
    public List<MobileDataRecord> queryByMobile(String mobile) {
        log.info("开始查询手机号: {}", MaskUtil.maskMobile(mobile));
        // 直接使用手机号作为key
        String key = mobile;

        try {
            // 检查Redis连接
            boolean isConnected = redisTemplate.getConnectionFactory().getConnection().ping() != null;
            log.info("Redis连接状态: {}", isConnected ? "已连接" : "未连接");
        } catch (Exception e) {
            log.error("检查Redis连接状态失败: {}", e.getMessage());
        }

        // 获取存储的字符串数组
        String[] values = redisTemplate.opsForValue().get(key) != null ? 
                redisTemplate.opsForValue().get(key).split(",") : null;

        if (values == null || values.length == 0) {
            log.info("手机号 {} 在Redis中没有对应数据", mobile);
            return new ArrayList<>();
        }

        try {
            log.info("获取到Redis数据: {} 条记录", values.length);
            List<MobileDataRecord> records = new ArrayList<>();
            
            // 将每个"tableName:id"字符串转换为MobileDataRecord对象
            for (String value : values) {
                if (value != null && !value.isEmpty()) {
                    String[] parts = value.split(":");
                    if (parts.length == 2) {
                        String tableName = parts[0];
                        try {
                            Long id = Long.parseLong(parts[1]);
                            records.add(new MobileDataRecord(tableName, id));
                        } catch (NumberFormatException e) {
                            log.error("解析ID失败: {}", parts[1]);
                        }
                    }
                }
            }

            // 对获取到的数据进行去重
            Map<String, MobileDataRecord> uniqueRecords = new HashMap<>();
            for (MobileDataRecord record : records) {
                uniqueRecords.put(record.getTableName() + ":" + record.getId(), record);
            }
            List<MobileDataRecord> uniqueList = new ArrayList<>(uniqueRecords.values());

            log.info("手机号 {} 在Redis中找到 {} 条记录，去重后 {} 条",
                    MaskUtil.maskMobile(mobile), records.size(), uniqueList.size());
            if (uniqueList.size() > 0) {
                log.info("第一条记录: 表={}, ID={}", uniqueList.get(0).getTableName(), uniqueList.get(0).getId());
            }

            // 如果发现重复数据，自动修复
            if (records.size() != uniqueList.size()) {
                log.warn("检测到重复数据，自动修复");
                
                // 转换为字符串数组格式并存回Redis
                String updatedValue = uniqueList.stream()
                    .map(record -> record.getTableName() + ":" + record.getId())
                    .collect(Collectors.joining(","));
                    
                redisTemplate.opsForValue().set(key, updatedValue);
            }

            return uniqueList;

        } catch (Exception e) {
            log.error("解析Redis数据失败: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    @Override
    public ImportProgressInfo getImportProgress() {
        log.info("获取导入进度: 进行中={}, 进度={}%, 已处理记录={}, 总记录={}",
                progressInfo.isImportInProgress(),
                String.format("%.2f", progressInfo.getProgressPercentage()),
                progressInfo.getProcessedRecords(),
                progressInfo.getTotalRecords());
        return progressInfo;
    }

    /**
     * 实际处理数据导入的方法
     */
    private void processImport(int batchSize) {
        // 初始化进度信息
        initProgressInfo();

        log.info("数据导入过程开始，目标表数量: {}", TABLE_NAMES.size());

        // 计算记录总数
        calculateTotalRecords();

        // 记录已处理的表
        List<String> processedTables = new ArrayList<>();
        progressInfo.setProcessedTables(processedTables);

        long globalStartTime = System.currentTimeMillis();
        boolean hasProcessedAnyTable = false;

        // 按顺序处理每个表
        for (String tableName : TABLE_NAMES) {
            processTable(tableName, batchSize, processedTables);
            progressInfo.setProcessedTablesCount(progressInfo.getProcessedTablesCount() + 1);
            processedTables.add(tableName);
            hasProcessedAnyTable = true;

            // 更新最后更新时间，其他指标通过getter方法计算
            progressInfo.setLastUpdateTime(System.currentTimeMillis());
        }

        // 添加额外检查，确保所有表都已处理完成
        if (hasProcessedAnyTable) {
            log.info("所有表处理完成，共处理 {} 张表", TABLE_NAMES.size());
        } else {
            log.warn("没有处理任何表，可能存在数据问题");
        }

        // 设置最大超时保护，防止任务无限挂起
        long globalElapsed = System.currentTimeMillis() - globalStartTime;
        long maxAllowedTime = 4 * 60 * 60 * 1000; // 最多允许运行4小时
        
        if (globalElapsed > maxAllowedTime) {
            log.warn("导入任务执行时间已超过{}小时，强制结束", maxAllowedTime / (60 * 60 * 1000));
        }

        // 结束前输出处理记录统计信息
        logProcessingSummary();

        // 结束导入
        synchronized (progressInfo) {
            // 修复进度百分比超过100%的问题
            if (progressInfo.getProcessedRecords() > progressInfo.getTotalRecords()) {
                log.warn("处理记录数({})大于总记录数({}), 调整总记录数以确保进度正确",
                        progressInfo.getProcessedRecords(), progressInfo.getTotalRecords());
                progressInfo.setTotalRecords(progressInfo.getProcessedRecords());
            }
            
            // 强制设置进度状态为已完成
            progressInfo.setImportInProgress(false);
            log.info("导入任务状态已更新为已完成");
        }

        long elapsedTime = progressInfo.getElapsedTime();
        log.info("所有表数据导入完成，共处理 {} 条记录，耗时: {}分{}秒",
                progressInfo.getProcessedRecords(),
                elapsedTime / 60000,
                (elapsedTime % 60000) / 1000);
    }
    
    /**
     * 输出处理记录数的汇总信息
     */
    private void logProcessingSummary() {
        log.info("========== 数据处理汇总信息 ==========");
        log.info("总计划处理记录数: {}", progressInfo.getTotalRecords());
        log.info("总实际处理记录数: {}", progressInfo.getProcessedRecords());
        log.info("总体完成率: {}%", String.format("%.2f", progressInfo.getProgressPercentage()));
        
        List<Map<String, Object>> stats = progressInfo.getRecordDiscrepancyStats();
        
        for (Map<String, Object> tableStat : stats) {
            String tableName = (String) tableStat.get("tableName");
            long expectedCount = (long) tableStat.get("expectedCount");
            long actualCount = (long) tableStat.get("actualCount");
            long difference = (long) tableStat.get("difference");
            boolean completed = (boolean) tableStat.get("completed");
            long elapsedMs = (long) tableStat.get("elapsedTimeMs");
            
            StringBuilder logMessage = new StringBuilder();
            logMessage.append(String.format("表 %-20s 预期=%d 实际=%d 差异=%d 完成=%-5s 耗时=%ds", 
                    tableName, expectedCount, actualCount, difference, completed, elapsedMs/1000));
            
            if (tableStat.containsKey("error")) {
                logMessage.append(" 错误=").append(tableStat.get("error"));
                log.error(logMessage.toString());
            } else if (difference > 0 && expectedCount > 0) {
                double lossPercentage = (double) difference / expectedCount * 100;
                logMessage.append(String.format(" 数据丢失率=%.2f%%", lossPercentage));
                if (lossPercentage > 20) {
                    log.error(logMessage.toString());
                } else if (lossPercentage > 5) {
                    log.warn(logMessage.toString());
                } else {
                    log.info(logMessage.toString());
                }
            } else {
                log.info(logMessage.toString());
            }
        }
        
        log.info("===================================");
    }

    /**
     * 安全关闭线程池的方法
     * 从processImport中分离出来，以便可以在外部调用
     */
    private void shutdownExecutorService() {
        log.info("尝试关闭线程池");
        try {
            if (executorService != null && !executorService.isShutdown()) {
                executorService.shutdown();
                log.info("线程池已关闭");
            } else {
                log.info("线程池已经关闭或为null");
            }
        } catch (Exception e) {
            log.error("关闭线程池时出错: {}", e.getMessage(), e);
        }
    }

    /**
     * 实现接口的关闭线程池方法，释放资源
     */
    @Override
    public boolean shutdownImportService() {
        log.info("接收到手动关闭导入服务线程池的请求");

        // 先将进度设置为非运行状态
        synchronized (progressInfo) {
            if (progressInfo.isImportInProgress()) {
                log.warn("导入服务仍在进行中，但将被强制终止");
                progressInfo.setImportInProgress(false);
            }
        }

        try {
            // 关闭线程池
            if (executorService != null && !executorService.isShutdown()) {
                // 先尝试正常关闭
                executorService.shutdown();
                log.info("导入服务线程池已关闭");
                return true;
            } else {
                log.info("导入服务线程池已经关闭或为null");
                return true;
            }
        } catch (Exception e) {
            log.error("关闭导入服务线程池时出错: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 初始化进度信息
     */
    private void initProgressInfo() {
        // 使用同步块保护进度信息的更新
        synchronized (progressInfo) {
            // importInProgress在方法入口已设置，这里不再设置
            progressInfo.setTotalTablesCount(TABLE_NAMES.size());
            progressInfo.setProcessedTablesCount(0);
            progressInfo.setProcessedRecords(0);
            progressInfo.setTotalRecords(0); // 确保在计算前清零
            // startTime已在方法入口设置，这里不再设置

            // 清空表记录数缓存
            tableRecordCounts.clear();
        }

        log.info("进度信息已初始化: 开始时间={}", new java.util.Date(progressInfo.getStartTime()));
    }

    /**
     * 计算所有表的记录总数，并将每个表的记录数存入缓存
     */
    private void calculateTotalRecords() {
        AtomicLong totalRecords = new AtomicLong(0);

        log.info("开始计算符合条件的记录总数...");

        boolean hasValidTables = false;

        for (String tableName : TABLE_NAMES) {
            String countSql = "SELECT COUNT(*) FROM " + tableName + " WHERE " + MOBILE_QUERY_CONDITION;
            log.info("执行计数SQL: {}", countSql);

            try {
                Long count = jdbcTemplate.queryForObject(countSql, Long.class);
                if (count != null && count > 0) {
                    totalRecords.addAndGet(count);
                    // 缓存每个表的记录数
                    tableRecordCounts.put(tableName, count);
                    log.info("表 {} 符合条件的记录数: {} (已缓存)", tableName, count);
                    hasValidTables = true;
                } else {
                    log.warn("表 {} 计数返回null或0", tableName);
                    tableRecordCounts.put(tableName, 0L);
                }
            } catch (Exception e) {
                log.error("计算表 {} 记录总数时出错: {}", tableName, e.getMessage(), e);
                tableRecordCounts.put(tableName, 0L);
            }
        }

        // 使用同步块更新总记录数
        synchronized (progressInfo) {
            progressInfo.setTotalRecords(totalRecords.get());
        }

        if (!hasValidTables || totalRecords.get() == 0) {
            log.error("没有找到有效的表数据，导入过程终止");
            synchronized (progressInfo) {
                progressInfo.setImportInProgress(false);
            }
            return;
        }

        log.info("所有表符合条件的记录总数: {}，已缓存各表记录数，预计每秒处理1000条记录，大约需要 {} 分钟完成",
                totalRecords.get(), totalRecords.get() / 60000);
    }

    /**
     * 获取表中符合条件的记录的ID范围
     *
     * @param tableName 表名
     * @return 包含最小ID和最大ID的对象
     */
    private MinMaxIds getTableIdRange(String tableName) {
        long startTime = System.currentTimeMillis();
        String sql = "SELECT MIN(id) as min_id, MAX(id) as max_id FROM " + tableName +
                " WHERE " + MOBILE_QUERY_CONDITION;

        log.debug("执行表 {} ID范围查询", tableName);

        try {
            Map<String, Object> result = jdbcTemplate.queryForMap(sql);
            Long minId = result.get("min_id") instanceof Number ?
                    ((Number) result.get("min_id")).longValue() : null;
            Long maxId = result.get("max_id") instanceof Number ?
                    ((Number) result.get("max_id")).longValue() : null;

            long endTime = System.currentTimeMillis();
            MinMaxIds idRange = new MinMaxIds(minId, maxId);

            log.info("表 {} ID范围: {} - {}, 耗时: {}s",
                    tableName, minId, maxId, (endTime - startTime) / 1000.0);

            return idRange;
        } catch (Exception e) {
            log.error("获取表 {} ID范围失败: {}", tableName, e.getMessage(), e);
            return new MinMaxIds(null, null);
        }
    }

    /**
     * 根据ID范围查询一批数据
     *
     * @param tableName 表名
     * @param startId   起始ID (包含)
     * @param endId     结束ID (包含)
     * @return 查询结果记录列表
     */
    private List<MobileRecord> fetchDataForIdRange(String tableName, long startId, long endId) {
        long fetchStartTime = System.currentTimeMillis();

        String sql = "SELECT id, d_mobile FROM " + tableName +
                " WHERE " + MOBILE_QUERY_CONDITION +
                " AND id >= " + startId + " AND id <= " + endId +
                " ORDER BY id";

        // 将SQL日志改为DEBUG级别，仅记录基本查询信息
        log.debug("执行ID范围查询 (表={}, 范围: {} - {})", tableName, startId, endId);

        try {
//            long dbQueryStartTime = System.currentTimeMillis();
            List<MobileRecord> records = jdbcTemplate.query(sql, (rs, rowNum) ->
                    new MobileRecord(
                            rs.getLong("id"),
                            rs.getString("d_mobile"),
                            tableName
                    )
            );
            long dbQueryEndTime = System.currentTimeMillis();

            long totalElapsed = dbQueryEndTime - fetchStartTime;

            // 保留重要的结果统计信息，但使用DEBUG级别
            log.debug("ID范围查询完成 (表={}, 范围:{}-{})，获取 {} 条记录，耗时: {}ms",
                    tableName, startId, endId, records.size(), totalElapsed);

            // 仅在有记录且开启了DEBUG日志时才记录记录示例
            if (!records.isEmpty() && log.isDebugEnabled()) {
                log.debug("查询结果示例: 第一条[ID={}, Mobile={}], 最后一条[ID={}, Mobile={}]",
                        records.get(0).getId(), records.get(0).getMobile(),
                        records.get(records.size() - 1).getId(),
                        records.get(records.size() - 1).getMobile());
            }

            return records;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("执行ID范围查询失败 (表={}, 范围:{}-{}): {}，耗时: {}ms",
                    tableName, startId, endId, e.getMessage(), (endTime - fetchStartTime));
            return new ArrayList<>();
        }
    }

    /**
     * 处理单个表的数据
     */
    private void processTable(String tableName, int idRangeInterval, List<String> processedTables) {
        log.info("开始处理表: {}, ID范围间隔: {}", tableName, idRangeInterval);

        // 在processTable方法开头添加表处理状态记录
        synchronized (progressInfo) {
            progressInfo.setCurrentTable(tableName);
            // 更新表开始处理状态
            TableProgress tableProgress = progressInfo.getTableProgressMap().get(tableName);
            if (tableProgress != null) {
                tableProgress.setStartTime(System.currentTimeMillis());
            }
        }

        try {
            // 从缓存中获取表记录数，避免重复查询
            long totalCount = tableRecordCounts.getOrDefault(tableName, 0L);

            if (totalCount == 0) {
                log.warn("表 {} 的记录数为0或未找到缓存，跳过处理", tableName);
                // 记录跳过状态
                synchronized (progressInfo) {
                    TableProgress tableProgress = progressInfo.getTableProgressMap().get(tableName);
                    if (tableProgress != null) {
                        tableProgress.setErrorMessage("记录数为0或未找到缓存");
                        tableProgress.markCompleted();
                    }
                }
                return;
            }

            long processedCount = 0;
            long startTime = System.currentTimeMillis();

            log.info("表 {} 符合条件的记录总数: {} (从缓存获取)", tableName, totalCount);

            // 获取表的ID范围
            MinMaxIds idRange = getTableIdRange(tableName);
            if (!idRange.isValid()) {
                log.error("表 {} 未找到有效的ID范围，跳过处理", tableName);
                // 记录错误状态
                synchronized (progressInfo) {
                    TableProgress tableProgress = progressInfo.getTableProgressMap().get(tableName);
                    if (tableProgress != null) {
                        tableProgress.setErrorMessage("未找到有效的ID范围");
                        tableProgress.markCompleted();
                    }
                }
                return;
            }

            Long minId = idRange.getMinId();
            Long maxId = idRange.getMaxId();
            log.info("表 {} 的ID范围: {} - {}", tableName, minId, maxId);

            // 计算需要处理的ID范围数量
            long totalIdSpan = maxId - minId + 1;
            long numRanges = (totalIdSpan + idRangeInterval - 1) / idRangeInterval;

            // 限制最大并行任务数，避免创建过多线程
            int availableProcessors = Runtime.getRuntime().availableProcessors();
            int maxParallelTasks = Math.min(
                    availableProcessors * 2,
                    Math.min(8, (int) numRanges) // 限制最大并行任务数为处理器数量的2倍，且不超过8
            );

            log.info("表 {} 将被分为 {} 个ID范围，使用 {} 个并行任务处理",
                    tableName, numRanges, maxParallelTasks);

            // 增加定期进度检查，每30秒输出一次详细进度
            final long PROGRESS_CHECK_INTERVAL = 30000; // 30秒
            final long progressCheckStart = System.currentTimeMillis();

            // 创建定期检查线程
            Thread progressCheckThread = new Thread(() -> {
                try {
                    boolean running = true;
                    int lastProcessedCount = 0;
                    long lastCheckTime = System.currentTimeMillis();
                    
                    while (running) {
                        // 检查是否仍处于处理状态
                        synchronized (progressInfo) {
                            running = progressInfo.isImportInProgress() && 
                                     tableName.equals(progressInfo.getCurrentTable());
                        }
                        
                        if (running) {
                            // 睡眠一段时间
                            Thread.sleep(PROGRESS_CHECK_INTERVAL);
                            
                            // 输出当前处理进度
                            long progressCheckElapsed = System.currentTimeMillis() - progressCheckStart;
                            long currentProcessed = progressInfo.getProcessedRecords();
                            double percentage = (double) currentProcessed / totalCount * 100;
                            
                            // 计算处理速度
                            long now = System.currentTimeMillis();
                            int recordsDelta = (int)(currentProcessed - lastProcessedCount);
                            long timeDelta = now - lastCheckTime;
                            double recordsPerSecond = (timeDelta > 0) ? 
                                ((double)recordsDelta / timeDelta * 1000) : 0;
                            
                            // 更新表进度信息
                            synchronized (progressInfo) {
                                TableProgress tableProgress = progressInfo.getTableProgressMap().get(tableName);
                                if (tableProgress != null) {
                                    tableProgress.setActualProcessedCount(currentProcessed);
                                }
                            }
                            
                            log.info("表 {} 处理进度检查 - 已处理 {}/{} 条记录 ({}%)，已用时: {}秒，处理速度: {}/秒",
                                    tableName, currentProcessed, totalCount,
                                    String.format("%.2f", percentage),
                                    progressCheckElapsed / 1000,
                                    String.format("%.1f", recordsPerSecond));
                            
                            // 更新检查点数据，为下次计算速度做准备
                            lastProcessedCount = (int)currentProcessed;
                            lastCheckTime = now;
                            
                            // 检查处理进度是否长时间停滞
                            if (progressCheckElapsed > 300000 && percentage < 5) { // 5分钟内进度小于5%
                                log.warn("表 {} 处理进度异常缓慢 ({}%)，已用时: {}秒，请检查系统负载或网络问题",
                                    tableName, String.format("%.2f", percentage), progressCheckElapsed / 1000);
                            }
                            
                            // 检查持续低速处理
                            if (recordsPerSecond < 10 && progressCheckElapsed > 120000) { // 处理速度低于10条/秒且已运行超过2分钟
                                log.warn("表 {} 处理速度异常低 ({}/秒)，可能存在性能问题",
                                    tableName, String.format("%.1f", recordsPerSecond));
                            }
                        }
                    }
                    
                    log.info("表 {} 的进度检查线程结束", tableName);
                } catch (InterruptedException e) {
                    // 线程被中断，正常退出
                    Thread.currentThread().interrupt();
                    log.info("表 {} 的进度检查线程被中断", tableName);
                } catch (Exception e) {
                    log.error("表 {} 的进度检查线程发生错误: {}", tableName, e.getMessage(), e);
                }
            });
            
            // 设置为守护线程并启动
            progressCheckThread.setDaemon(true);
            progressCheckThread.setName("ProgressCheck-" + tableName);
            progressCheckThread.start();

            // 增加处理进度日志的间隔
            int logInterval = Math.max(10, (int) (numRanges / 10)); // 大约每处理10%的范围记录一次日志

            // 创建所有任务的Future列表
            List<CompletableFuture<Integer>> taskFutures = new ArrayList<>();
            
            // 创建一个跟踪已尝试的任务范围的集合，记录哪些ID范围已经被处理
            Set<Long> completedRangeIndexes = new HashSet<>();
            // 记录失败的任务，用于后续重试
            Map<Long, Integer> failedTaskRetries = new HashMap<>();
            // 最大重试次数
            final int MAX_TASK_RETRIES = 3;

            // 按ID范围创建任务
            for (long rangeIndex = 0; rangeIndex < numRanges; rangeIndex++) {
                final long startId = minId + (rangeIndex * idRangeInterval);
                final long endId = Math.min(maxId, startId + idRangeInterval - 1);
                final long taskIndex = rangeIndex;

                // 创建异步任务并添加到任务列表
                CompletableFuture<Integer> future = createTaskForIdRange(tableName, startId, endId, 
                        taskIndex, numRanges, logInterval);
                taskFutures.add(future);

                // 控制并行任务数量，当达到最大并行任务数时，等待一些任务完成后再继续
                if (taskFutures.size() >= maxParallelTasks && rangeIndex < numRanges - 1) {
                    log.info("已达到最大并行任务数 {}，等待部分任务完成后继续...", maxParallelTasks);
                    
                    try {
                        // 等待任务完成
                        waitForPartialTasksWithRetry(taskFutures, processedCount, totalCount, tableName, startTime);
                        
                        // 收集完成的任务和识别失败的任务
                        List<CompletableFuture<Integer>> completedTasks = new ArrayList<>();
                        List<Long> failedTaskIndexes = new ArrayList<>();
                        
                        for (int i = 0; i < taskFutures.size(); i++) {
                            CompletableFuture<Integer> task = taskFutures.get(i);
                            long taskRangeIndex = rangeIndex - taskFutures.size() + i + 1;
                            
                            if (task.isDone()) {
                                try {
                                    int count = task.get();
                                    processedCount += count;
                                    // 记录此范围已完成
                                    completedRangeIndexes.add(taskRangeIndex);
                                    completedTasks.add(task);
                                } catch (Exception e) {
                                    log.error("任务 {}/{} (范围: {}-{}) 执行失败: {}", 
                                            taskRangeIndex + 1, numRanges, 
                                            minId + taskRangeIndex * idRangeInterval, 
                                            Math.min(maxId, minId + (taskRangeIndex + 1) * idRangeInterval - 1),
                                            e.getMessage());
                                    
                                    // 将失败任务加入重试集合
                                    int retries = failedTaskRetries.getOrDefault(taskRangeIndex, 0);
                                    if (retries < MAX_TASK_RETRIES) {
                                        failedTaskRetries.put(taskRangeIndex, retries + 1);
                                        failedTaskIndexes.add(taskRangeIndex);
                                        log.info("任务 {}/{} 将被重试 ({}/{})", 
                                                taskRangeIndex + 1, numRanges, retries + 1, MAX_TASK_RETRIES);
                                    } else {
                                        log.warn("任务 {}/{} 已达到最大重试次数，将被跳过", 
                                                taskRangeIndex + 1, numRanges);
                                        // 标记为已完成，不再重试
                                        completedRangeIndexes.add(taskRangeIndex);
                                    }
                                    completedTasks.add(task);
                                }
                            }
                        }
                        
                        // 移除已完成的任务
                        taskFutures.removeAll(completedTasks);
                        
                        // 处理失败任务的重试
                        for (Long failedIndex : failedTaskIndexes) {
                            long failedStartId = minId + (failedIndex * idRangeInterval);
                            long failedEndId = Math.min(maxId, failedStartId + idRangeInterval - 1);
                            
                            log.info("重新创建任务 {}/{} (范围: {}-{})", 
                                    failedIndex + 1, numRanges, failedStartId, failedEndId);
                                    
                            CompletableFuture<Integer> retryTask = createTaskForIdRange(
                                    tableName, failedStartId, failedEndId, failedIndex, numRanges, logInterval);
                            taskFutures.add(retryTask);
                        }
                        
                        // 更新进度
                        synchronized (progressInfo) {
                            progressInfo.setProcessedRecords(progressInfo.getProcessedRecords() + processedCount);
                            progressInfo.setLastUpdateTime(System.currentTimeMillis());
                            processedCount = 0; // 重置计数，因为已经累加到全局计数中
                        }

                        // 记录进度
                        double percentage = (double) progressInfo.getProcessedRecords() / totalCount * 100;
                        long elapsed = System.currentTimeMillis() - startTime;
                        log.info("表 {} 处理进度: {}/{} 条记录 ({}%)，已用时: {}秒",
                                tableName, progressInfo.getProcessedRecords(), totalCount,
                                String.format("%.2f", percentage), elapsed / 1000);
                    } catch (Exception e) {
                        log.error("等待部分任务完成时出错: {}", e.getMessage(), e);
                        // 尝试继续处理，而不是直接中断流程
                        taskFutures.clear();
                    }
                }
            }

            // 等待所有剩余任务完成
            if (!taskFutures.isEmpty()) {
                try {
                    // 使用同样的增强等待方法处理最后一批任务
                    waitForPartialTasksWithRetry(taskFutures, processedCount, totalCount, tableName, startTime);
                    
                    // 收集最终批次任务的结果
                    int finalBatchRecords = 0;
                    List<Long> failedTaskIndexes = new ArrayList<>();
                    
                    for (int i = 0; i < taskFutures.size(); i++) {
                        CompletableFuture<Integer> task = taskFutures.get(i);
                        // 计算当前任务对应的范围索引
                        long lastStartIdx = numRanges - taskFutures.size();
                        long taskRangeIndex = lastStartIdx + i;
                        
                        if (task.isDone()) {
                            try {
                                int count = task.get();
                                finalBatchRecords += count;
                                // 记录已完成
                                completedRangeIndexes.add(taskRangeIndex);
                            } catch (Exception e) {
                                log.error("最终批次任务 {}/{} 执行失败: {}", 
                                        taskRangeIndex + 1, numRanges, e.getMessage());
                                
                                // 添加到重试列表
                                int retries = failedTaskRetries.getOrDefault(taskRangeIndex, 0);
                                if (retries < MAX_TASK_RETRIES) {
                                    failedTaskRetries.put(taskRangeIndex, retries + 1);
                                    failedTaskIndexes.add(taskRangeIndex);
                                } else {
                                    // 达到最大重试次数
                                    log.warn("任务 {}/{} 已达到最大重试次数，将被跳过", 
                                            taskRangeIndex + 1, numRanges);
                                    // 标记为已完成，不再尝试
                                    completedRangeIndexes.add(taskRangeIndex);
                                }
                            }
                        }
                    }
                    
                    // 处理失败任务的重试
                    taskFutures.clear(); // 清空已处理的任务
                    for (Long failedIndex : failedTaskIndexes) {
                        long failedStartId = minId + (failedIndex * idRangeInterval);
                        long failedEndId = Math.min(maxId, failedStartId + idRangeInterval - 1);
                        
                        log.info("重新创建最终批次任务 {}/{} (范围: {}-{})", 
                                failedIndex + 1, numRanges, failedStartId, failedEndId);
                                
                        CompletableFuture<Integer> retryTask = createTaskForIdRange(
                                tableName, failedStartId, failedEndId, failedIndex, numRanges, logInterval);
                        taskFutures.add(retryTask);
                    }
                    
                    // 如果有需要重试的任务，再次等待完成
                    if (!taskFutures.isEmpty()) {
                        log.info("等待 {} 个重试任务完成", taskFutures.size());
                        waitForPartialTasksWithRetry(taskFutures, 0, totalCount, tableName, startTime);
                        
                        // 计算重试任务的结果
                        for (CompletableFuture<Integer> task : taskFutures) {
                            try {
                                if (task.isDone()) {
                                    Integer count = task.get();
                                    finalBatchRecords += count != null ? count : 0;
                                }
                            } catch (Exception e) {
                                log.warn("获取重试任务结果时出错 (忽略): {}", e.getMessage());
                            }
                        }
                    }

                    // 更新最终进度
                    processedCount += finalBatchRecords;
                    synchronized (progressInfo) {
                        progressInfo.setProcessedRecords(progressInfo.getProcessedRecords() + finalBatchRecords);
                        progressInfo.setLastUpdateTime(System.currentTimeMillis());
                    }

                } catch (Exception e) {
                    log.error("等待最终任务完成时出错: {}", e.getMessage(), e);
                    // 继续执行收尾工作
                }
            }
            
            // 检查是否有未完成的ID范围
            Set<Long> allRanges = new HashSet<>();
            for (long i = 0; i < numRanges; i++) {
                allRanges.add(i);
            }
            allRanges.removeAll(completedRangeIndexes);
            
            if (!allRanges.isEmpty()) {
                log.warn("表 {} 有 {} 个ID范围未能成功处理，总范围数: {}", 
                        tableName, allRanges.size(), numRanges);
                
                // 处理未完成的范围
                log.info("开始处理遗漏的ID范围");
                List<CompletableFuture<Integer>> missedTaskFutures = new ArrayList<>();
                
                for (Long missedIndex : allRanges) {
                    long missedStartId = minId + (missedIndex * idRangeInterval);
                    long missedEndId = Math.min(maxId, missedStartId + idRangeInterval - 1);
                    
                    log.info("处理遗漏的ID范围 {}/{} (范围: {}-{})", 
                            missedIndex + 1, numRanges, missedStartId, missedEndId);
                    
                    CompletableFuture<Integer> task = createTaskForIdRange(
                            tableName, missedStartId, missedEndId, missedIndex, numRanges, logInterval);
                    missedTaskFutures.add(task);
                    
                    // 控制并行任务数
                    if (missedTaskFutures.size() >= maxParallelTasks) {
                        waitForPartialTasksWithRetry(missedTaskFutures, 0, totalCount, tableName, startTime);
                        
                        // 计算处理结果
                        int missedRecordsCount = 0;
                        for (CompletableFuture<Integer> t : missedTaskFutures) {
                            try {
                                if (t.isDone()) {
                                    Integer count = t.get();
                                    missedRecordsCount += count != null ? count : 0;
                                }
                            } catch (Exception e) {
                                log.warn("获取遗漏任务结果时出错: {}", e.getMessage());
                            }
                        }
                        
                        // 更新进度
                        synchronized (progressInfo) {
                            progressInfo.setProcessedRecords(progressInfo.getProcessedRecords() + missedRecordsCount);
                            progressInfo.setLastUpdateTime(System.currentTimeMillis());
                        }
                        
                        // 清空任务列表
                        missedTaskFutures.clear();
                    }
                }
                
                // 处理剩余的遗漏任务
                if (!missedTaskFutures.isEmpty()) {
                    waitForPartialTasksWithRetry(missedTaskFutures, 0, totalCount, tableName, startTime);
                    
                    // 计算结果
                    int remainingMissedCount = 0;
                    for (CompletableFuture<Integer> t : missedTaskFutures) {
                        try {
                            if (t.isDone()) {
                                Integer count = t.get();
                                remainingMissedCount += count != null ? count : 0;
                            }
                        } catch (Exception e) {
                            log.warn("获取最终遗漏任务结果时出错: {}", e.getMessage());
                        }
                    }
                    
                    // 更新进度
                    synchronized (progressInfo) {
                        progressInfo.setProcessedRecords(progressInfo.getProcessedRecords() + remainingMissedCount);
                        progressInfo.setLastUpdateTime(System.currentTimeMillis());
                    }
                }
            }

            // 进行最终检查，确保所有任务都已完成
            boolean allTasksCompleted = allRanges.isEmpty();
            if (!allTasksCompleted) {
                log.warn("表 {} 处理结束，但仍有 {} 个ID范围未能完成处理", tableName, allRanges.size());
            } else {
                log.info("表 {} 的所有 {} 个ID范围都已经成功处理", tableName, numRanges);
            }

            // 记录实际处理和预期总数的差异
            long actualProcessed = progressInfo.getProcessedRecords();
            long expectedCount = tableRecordCounts.getOrDefault(tableName, 0L);
            long diff = expectedCount - actualProcessed;
            
            // 更新表统计信息
            synchronized (progressInfo) {
                tableProcessedCounts.put(tableName, actualProcessed);
                progressInfo.completeTableProgress(tableName, actualProcessed);
            }
            
            log.info("表 {} 处理完成 - 预期记录数: {}, 实际处理记录数: {}, 差异: {}", 
                    tableName, expectedCount, actualProcessed, diff);

            long elapsed = System.currentTimeMillis() - startTime;
            double averageTimePerRecord = actualProcessed > 0 ? (double) elapsed / actualProcessed : 0;

            log.info("表 {} 处理完成，共处理 {} 条记录，总耗时: {}分{}秒，平均每条记录: {}ms",
                    tableName, progressInfo.getProcessedRecords(),
                    elapsed / 60000, (elapsed % 60000) / 1000,
                    String.format("%.2f", averageTimePerRecord));

        } catch (Exception e) {
            log.error("处理表 {} 时发生错误: {}", tableName, e.getMessage(), e);
        }
    }
    
    /**
     * 为ID范围创建异步任务
     */
    private CompletableFuture<Integer> createTaskForIdRange(String tableName, long startId, long endId, 
                                                         long taskIndex, long numRanges, int logInterval) {
        return CompletableFuture.supplyAsync(() -> {
            long taskStartTime = System.currentTimeMillis();
            log.debug("开始处理表 {} 的ID范围任务 {}/{}: {} - {}",
                    tableName, (taskIndex + 1), numRanges, startId, endId);

            // 获取指定ID范围内的数据
            List<MobileRecord> records = fetchDataForIdRange(tableName, startId, endId);
            int recordCount = records.size();

            // 如果有记录，导入到Redis
            if (!records.isEmpty()) {
                try {
                    long redisStartTime = System.currentTimeMillis();
                    importBatchToRedis(records);
                    long redisEndTime = System.currentTimeMillis();

                    log.debug("表 {} ID范围 {} - {} 的Redis导入完成，处理 {} 条记录，耗时: {}ms",
                            tableName, startId, endId, recordCount, (redisEndTime - redisStartTime));
                } catch (Exception e) {
                    // 捕获导入过程中的异常，但不中断流程
                    log.error("表 {} ID范围 {} - {} 导入Redis时出错: {}",
                            tableName, startId, endId, e.getMessage(), e);
                    // 返回处理的记录数，即使导入失败
                }
            } else {
                log.debug("表 {} ID范围 {} - {} 内没有符合条件的记录", tableName, startId, endId);
            }

            long taskEndTime = System.currentTimeMillis();
            // 仅在较大的ID段完成时使用INFO级别，小段使用DEBUG级别
            if (recordCount > 1000 || (taskIndex + 1) % logInterval == 0) {
                log.info("表 {} ID范围任务 {}/{} ({} - {}) 完成，处理 {} 条记录，总耗时: {}ms",
                        tableName, (taskIndex + 1), numRanges, startId, endId,
                        recordCount, (taskEndTime - taskStartTime));
            } else {
                log.debug("表 {} ID范围任务 {}/{} 完成，处理 {} 条记录，耗时: {}ms",
                        tableName, (taskIndex + 1), numRanges, recordCount, (taskEndTime - taskStartTime));
            }

            return recordCount;
        }, executorService).exceptionally(ex -> {
            // 处理任务执行过程中的异常
            log.error("表 {} ID范围任务 {}/{} ({} - {}) 执行失败: {}",
                    tableName, (taskIndex + 1), numRanges, startId, endId, ex.getMessage(), ex);
            // 重新抛出异常，以便外部处理重试
            throw new CompletionException(ex);
        });
    }

    /**
     * 增强的任务等待方法，带有重试和部分完成机制
     */
    private void waitForPartialTasksWithRetry(List<CompletableFuture<Integer>> tasks, 
                                             long currentProcessedCount, 
                                             long totalCount, 
                                             String tableName,
                                             long startTime) {
        // 最大等待时间和重试次数
        final long MAX_WAIT_TIME_MS = 600000; // 10分钟，之前是5分钟
        final int MAX_RETRIES = 5; // 增加到5次，之前是3次
        
        long waitStart = System.currentTimeMillis();
        int retryCount = 0;
        boolean allDone = false;
        
        log.debug("开始等待 {} 个任务完成，当前表: {}, 进度: {}/{}", 
                tasks.size(), tableName, currentProcessedCount, totalCount);
        
        // 先进行一次全量检查，避免在任务已全部完成的情况下仍进入等待循环
        allDone = tasks.stream().allMatch(CompletableFuture::isDone);
        if (allDone) {
            log.info("所有 {} 个任务已经完成，无需等待", tasks.size());
            return;
        }
        
        while (!allDone && (System.currentTimeMillis() - waitStart < MAX_WAIT_TIME_MS) && retryCount <= MAX_RETRIES) {
            try {
                // 获取所有已完成的任务
                List<CompletableFuture<Integer>> completedTasks = tasks.stream()
                        .filter(CompletableFuture::isDone)
                        .collect(Collectors.toList());
                List<CompletableFuture<Integer>> incompleteTasks = tasks.stream()
                        .filter(task -> !task.isDone())
                        .collect(Collectors.toList());
                
                // 记录完成情况
                int completedCount = completedTasks.size();
                int tasksTotal = tasks.size();
                log.info("已有 {}/{} 个任务完成，剩余 {} 个任务", 
                        completedCount, tasksTotal, incompleteTasks.size());
                
                // 如果所有任务都完成了，标记为全部完成
                if (completedCount == tasksTotal) {
                    allDone = true;
                    log.debug("所有 {} 个任务已完成", tasks.size());
                    break;
                }
                
                // 如果还有任务未完成，等待一小段时间
                if (!allDone) {
                    // 以递增的等待时间重试
                    int waitTime = 500 * (retryCount + 1); // 500ms, 1s, 1.5s...
                    log.debug("等待更多任务完成，等待 {}ms", waitTime);
                    try {
                        Thread.sleep(waitTime);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.warn("等待被中断");
                    }
                }
                
                // 再次检查是否所有任务都完成了
                allDone = tasks.stream().allMatch(CompletableFuture::isDone);
                
                if (!allDone) {
                    retryCount++;
                    // 记录长时间未完成的任务
                    if (retryCount >= 2) {
                        // 打印未完成任务的数量和状态
                        log.warn("有 {} 个任务在 {} 次等待后仍未完成，总任务数 {}，已完成任务数 {}", 
                                incompleteTasks.size(), retryCount, tasks.size(), completedTasks.size());
                    }
                }
                
            } catch (Exception e) {
                log.error("等待任务完成过程中出错: {}", e.getMessage(), e);
                retryCount++;
                
                if (retryCount >= MAX_RETRIES) {
                    log.warn("等待任务过程中错误次数过多，将继续等待完成");
                    // 不提前返回，而是继续等待
                }
            }
        }
        
        // 如果达到超时但仍有任务未完成，强制等待所有任务完成
        if (!allDone) {
            long elapsedWaitTime = System.currentTimeMillis() - waitStart;
            log.warn("等待任务超时或达到最大重试次数，耗时: {}ms, 重试: {}/{}，强制等待所有任务完成",
                    elapsedWaitTime, retryCount, MAX_RETRIES);
            
            try {
                // 使用CompletableFuture.allOf确保所有任务完成
                CompletableFuture.allOf(tasks.toArray(new CompletableFuture[0]))
                    .get(MAX_WAIT_TIME_MS, TimeUnit.MILLISECONDS);
                log.info("所有任务已全部完成");
            } catch (Exception e) {
                log.error("强制等待所有任务完成时发生错误: {}", e.getMessage(), e);
                // 即使出错也继续执行，不中断流程
            }
        }
    }

    /**
     * 将一批数据导入Redis
     */
    private void importBatchToRedis(List<MobileRecord> records) {
        long startTime = System.currentTimeMillis();
        log.debug("开始导入 {} 条记录到Redis", records.size());

        // 使用Map按手机号分组记录
        Map<String, Set<String>> recordsByMobile = new HashMap<>();

        // 分组计时
        long groupStartTime = System.currentTimeMillis();

        // 按手机号分组并构建tableName:id格式
        for (MobileRecord record : records) {
            // 处理可能包含多个手机号的情况（手机号中包含分号）
            String mobileString = record.getMobile();
            if (mobileString != null && mobileString.contains(";")) {
                // 如果包含分号，拆分为多个手机号单独处理
                String[] mobileParts = mobileString.split(";");
                log.debug("检测到分号分隔的多手机号: {}，拆分为 {} 个手机号", mobileString, mobileParts.length);

                // 使用Set对拆分后的手机号进行去重
                Set<String> uniqueMobiles = new HashSet<>();
                for (String part : mobileParts) {
                    if (part != null && !part.trim().isEmpty()) {
                        uniqueMobiles.add(part.trim());
                    }
                }

                // 遍历去重后的手机号
                for (String singleMobile : uniqueMobiles) {
                    // 直接使用手机号作为key
                    recordsByMobile.computeIfAbsent(singleMobile, k -> new HashSet<>())
                            .add(record.getTableName() + ":" + record.getId());
                }
            } else if (mobileString != null && !mobileString.trim().isEmpty()) {
                // 单个手机号的正常处理，直接使用手机号作为key
                recordsByMobile.computeIfAbsent(mobileString.trim(), k -> new HashSet<>())
                        .add(record.getTableName() + ":" + record.getId());
            } else {
                log.warn("记录 (表: {}, ID: {}) 的手机号为空或仅包含空格，跳过", record.getTableName(), record.getId());
            }
        }

        long groupEndTime = System.currentTimeMillis();
        log.debug("手机号分组完成，{} 条记录，{} 个不同手机号，耗时: {}ms",
                records.size(), recordsByMobile.size(), (groupEndTime - groupStartTime));

        // 批量获取现有数据
        List<String> keys = new ArrayList<>(recordsByMobile.keySet());
        Map<String, String> existingData = new HashMap<>();

        if (!keys.isEmpty()) {
            try {
                // 将键列表分成更小的批次，避免Redis超时
                final int BATCH_SIZE = 1000; // 每批处理1000个键
                int totalBatches = (keys.size() + BATCH_SIZE - 1) / BATCH_SIZE;
                
                log.debug("将 {} 个键分成 {} 批处理，每批最多 {} 个键", 
                        keys.size(), totalBatches, BATCH_SIZE);

                for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                    int fromIndex = batchIndex * BATCH_SIZE;
                    int toIndex = Math.min(fromIndex + BATCH_SIZE, keys.size());
                    List<String> batchKeys = keys.subList(fromIndex, toIndex);
                    
                    fetchExistingDataBatch(batchKeys, existingData, batchIndex, totalBatches);
                }

                log.debug("Redis已存在 {} 个键", existingData.size());
            } catch (Exception e) {
                log.error("批量获取Redis数据过程中遇到错误: {}", e.getMessage(), e);
            }
        }

        final AtomicInteger newKeysCount = new AtomicInteger(0);
        final AtomicInteger updatedKeysCount = new AtomicInteger(0);
        final AtomicInteger errorCount = new AtomicInteger(0);

        // 处理和写入Redis计时
        long processStartTime = System.currentTimeMillis();

        // 添加Redis管道操作的重试机制和批次拆分逻辑
        int maxRetries = 3; // 最大重试次数
        int retryCount = 0;
        boolean success = false;
        int currentSubBatchSize = keys.size(); // 初始子批次大小为全部键
        
        while (!success && retryCount <= maxRetries) {
            try {
                // 检查是否需要拆分批次
                if (retryCount > 0) {
                    // 每次重试，将批次大小减半
                    currentSubBatchSize = Math.max(100, currentSubBatchSize / 2);
                    log.info("Redis管道操作重试 {}/{}，减小批次大小至 {}",
                            retryCount, maxRetries, currentSubBatchSize);
                }
                
                // 将键分成更小的子批次处理
                int totalSubBatches = (keys.size() + currentSubBatchSize - 1) / currentSubBatchSize;
                log.debug("将 {} 个键拆分为 {} 个子批次，每批 {} 个键",
                        keys.size(), totalSubBatches, currentSubBatchSize);
                
                for (int subBatchIndex = 0; subBatchIndex < totalSubBatches; subBatchIndex++) {
                    int fromIndex = subBatchIndex * currentSubBatchSize;
                    int toIndex = Math.min(fromIndex + currentSubBatchSize, keys.size());
                    List<String> subBatchKeys = keys.subList(fromIndex, toIndex);
                    
                    if (subBatchIndex > 0 && subBatchIndex % 5 == 0) {
                        log.info("正在处理Redis写入子批次 {}/{}", subBatchIndex, totalSubBatches);
                    }
                    
                    processRedisPipelineBatch(subBatchKeys, recordsByMobile, existingData, 
                            newKeysCount, updatedKeysCount, errorCount);
                }
                
                // 所有子批次成功处理
                success = true;
                
            } catch (Exception e) {
                retryCount++;
                if (retryCount <= maxRetries) {
                    log.warn("Redis管道操作失败，准备第 {}/{} 次重试: {}", 
                            retryCount, maxRetries, e.getMessage());
                    try {
                        // 等待一段时间再重试，使用指数退避策略
                        int sleepTime = 1000 * retryCount;
                        log.info("等待 {}ms 后重试...", sleepTime);
                        Thread.sleep(sleepTime);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.warn("重试等待被中断", ie);
                    }
                } else {
                    log.error("Redis管道操作在 {} 次重试后仍然失败: {}", 
                            maxRetries, e.getMessage(), e);
                    errorCount.addAndGet(keys.size()); // 标记所有键处理失败
                }
            }
        }

        long elapsed = System.currentTimeMillis() - startTime;
        log.info("Redis导入完成: {} 条记录, {} 个手机号, 新键={}, 更新键={}, 错误={}, 总耗时={}ms",
                records.size(), recordsByMobile.size(), newKeysCount.get(),
                updatedKeysCount.get(), errorCount.get(), elapsed);
    }
    
    /**
     * 处理单个Redis管道子批次
     */
    private void processRedisPipelineBatch(List<String> batchKeys, 
                                          Map<String, Set<String>> recordsByMobile,
                                          Map<String, String> existingData,
                                          AtomicInteger newKeysCount,
                                          AtomicInteger updatedKeysCount,
                                          AtomicInteger errorCount) {
        
        if (batchKeys.isEmpty()) {
            return;
        }
        
        long batchStartTime = System.currentTimeMillis();
        log.debug("开始处理一个大小为 {} 的Redis管道子批次", batchKeys.size());
        
        try {
            // 使用管道批量更新Redis
            redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                for (String key : batchKeys) {
                    Set<String> newRecords = recordsByMobile.get(key);
                    String existingValue = existingData.get(key); // Fetched earlier via multiGet

                    try {
                        Set<String> combinedRecords = new HashSet<>();

                        // 1. 添加已经存在于Redis的记录
                        if (existingValue != null && !existingValue.isEmpty()) {
                            String[] existingRecords = existingValue.split(",");
                            combinedRecords.addAll(Arrays.asList(existingRecords));
                        }

                        // 2. 添加当前批次的新记录
                        if (newRecords != null && !newRecords.isEmpty()) {
                            combinedRecords.addAll(newRecords);
                        }

                        // 3. 判断是否需要更新Redis
                        boolean needsWriteToRedis = false;
                        String finalValueToWrite = "";

                        if (!combinedRecords.isEmpty()) {
                            // 将Set转换为逗号分隔的字符串
                            finalValueToWrite = String.join(",", combinedRecords);
                            
                            if (existingValue == null || existingValue.isEmpty()) {
                                // 新键或被视为空
                                needsWriteToRedis = true;
                                newKeysCount.incrementAndGet();
                            } else if (!existingValue.equals(finalValueToWrite)) {
                                // 已存在的键内容有变化
                                needsWriteToRedis = true;
                                updatedKeysCount.incrementAndGet();
                            }
                        }

                        // 4. 需要时写入Redis
                        if (needsWriteToRedis) {
                            connection.stringCommands().set(key.getBytes(), finalValueToWrite.getBytes());
                        }

                    } catch (Exception e) {
                        log.error("处理键 {} 时出错: {}", key, e.getMessage());
                        errorCount.incrementAndGet();
                    }
                }
                return null;
            });
            
            long batchEndTime = System.currentTimeMillis();
            log.debug("Redis管道子批次 ({} 个键) 处理完成，耗时: {}ms", 
                    batchKeys.size(), (batchEndTime - batchStartTime));
            
        } catch (Exception e) {
            log.error("处理Redis管道子批次时出错 ({} 个键): {}", 
                    batchKeys.size(), e.getMessage());
            // 向上传播异常，让调用者处理重试逻辑
            throw e;
        }
    }

    /**
     * 批量获取Redis现有数据，包含重试机制
     * 
     * @param batchKeys     当前批次的键列表
     * @param existingData  存放获取结果的映射
     * @param batchIndex    当前批次索引
     * @param totalBatches  总批次数
     */
    private void fetchExistingDataBatch(List<String> batchKeys, Map<String, String> existingData, 
                                         int batchIndex, int totalBatches) {
        int maxRetries = 3;
        int retryDelayMs = 500; // 初始延迟500毫秒
        
        for (int retry = 0; retry <= maxRetries; retry++) {
            try {
                // 批量获取现有值计时
                long redisGetStartTime = System.currentTimeMillis();

                // 批量获取现有值
                List<String> existingValues = redisTemplate.opsForValue().multiGet(batchKeys);

                long redisGetEndTime = System.currentTimeMillis();
                
                if (retry > 0) {
                    log.info("批次 {}/{} 重试成功 (第{}次尝试)，获取 {} 个键，耗时: {}ms",
                            (batchIndex + 1), totalBatches, retry, batchKeys.size(), 
                            (redisGetEndTime - redisGetStartTime));
                } else {
                    log.debug("批次 {}/{} Redis批量获取完成，查询 {} 个键，耗时: {}ms",
                            (batchIndex + 1), totalBatches, batchKeys.size(), 
                            (redisGetEndTime - redisGetStartTime));
                }

                if (existingValues != null) {
                    for (int i = 0; i < batchKeys.size(); i++) {
                        if (existingValues.get(i) != null) {
                            existingData.put(batchKeys.get(i), existingValues.get(i));
                        }
                    }
                }
                
                // 成功获取数据，退出重试循环
                return;
                
            } catch (QueryTimeoutException e) {
                // Redis超时错误特殊处理
                if (retry < maxRetries) {
                    int nextDelay = retryDelayMs * (retry + 1); // 递增延迟
                    log.warn("批次 {}/{} Redis批量获取超时 (尝试 {}/{}): {}. 将在 {}ms 后重试...",
                            (batchIndex + 1), totalBatches, retry + 1, maxRetries + 1, 
                            e.getMessage(), nextDelay);
                    
                    try {
                        Thread.sleep(nextDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.error("重试等待被中断", ie);
                    }
                } else {
                    // 超过最大重试次数，记录错误
                    log.error("批次 {}/{} Redis批量获取失败，已达到最大重试次数 ({}): {}",
                            (batchIndex + 1), totalBatches, maxRetries + 1, e.getMessage());
                    
                    // 降级为单键获取模式
                    log.info("批次 {}/{} 降级为单键获取模式", (batchIndex + 1), totalBatches);
                    fetchKeysIndividually(batchKeys, existingData);
                }
            } catch (Exception e) {
                // 其他类型错误处理
                log.error("批次 {}/{} 批量获取Redis数据失败: {}", 
                        (batchIndex + 1), totalBatches, e.getMessage(), e);
                
                // 对于非超时类错误，尝试降级为单键获取模式
                if (retry == maxRetries) {
                    log.info("批次 {}/{} 降级为单键获取模式", (batchIndex + 1), totalBatches);
                    fetchKeysIndividually(batchKeys, existingData);
                }
            }
        }
    }
    
    /**
     * 单键模式获取Redis数据（降级方案）
     * 
     * @param keys 键列表
     * @param existingData 存放结果的映射
     */
    private void fetchKeysIndividually(List<String> keys, Map<String, String> existingData) {
        long startTime = System.currentTimeMillis();
        AtomicInteger successCount = new AtomicInteger(0);
        
        // 为避免日志过多，计算日志间隔
        int logInterval = Math.max(100, keys.size() / 10);
        
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            try {
                String value = redisTemplate.opsForValue().get(key);
                if (value != null) {
                    existingData.put(key, value);
                    successCount.incrementAndGet();
                }
                
                // 每处理logInterval个键打印一次日志
                if ((i + 1) % logInterval == 0 || i == keys.size() - 1) {
                    log.debug("单键获取进度: {}/{}, 成功获取: {}", 
                            (i + 1), keys.size(), successCount.get());
                }
            } catch (Exception e) {
                log.warn("获取键 {} 失败: {}", key, e.getMessage());
            }
        }
        
        long endTime = System.currentTimeMillis();
        log.info("单键获取模式完成，处理 {} 个键，成功 {} 个，耗时: {}ms", 
                keys.size(), successCount.get(), (endTime - startTime));
    }

    /**
     * 内部类：表记录
     */
    private static class MobileRecord {
        private final Long id;
        private final String mobile;
        private final String tableName;

        public MobileRecord(Long id, String mobile, String tableName) {
            this.id = id;
            this.mobile = mobile;
            this.tableName = tableName;
        }

        public Long getId() {
            return id;
        }

        public String getMobile() {
            return mobile;
        }

        public String getTableName() {
            return tableName;
        }
    }

    @Override
    public List<NormalizeData> queryNameList(String mobile) {
        return queryDataByPrefix(mobile, "data_namelist_", "名单数据");
    }
    
    @Override
    public List<NormalizeData> queryDataset(String mobile) {
        return queryDataByPrefix(mobile, "dataset_", "调研数据");
    }
    
    @Override
    public DataStatisticsResultDto queryNameListWithStatistics(String mobile) {
        log.info("开始按项目统计名单数据，手机号: {}", mobile);
        List<NormalizeData> records = queryNameList(mobile);
        return generateStatisticsResult(records, "名单数据");
    }
    
    @Override
    public DataStatisticsResultDto queryDatasetWithStatistics(String mobile) {
        log.info("开始按项目统计调研数据，手机号: {}", mobile);
        List<NormalizeData> records = queryDataset(mobile);
        return generateStatisticsResult(records, "调研数据");
    }
    
    /**
     * 生成统计结果
     * 
     * @param records 原始记录列表
     * @param dataSourceDescription 数据来源描述
     * @return 统计结果对象
     */
    private DataStatisticsResultDto generateStatisticsResult(List<NormalizeData> records, String dataSourceDescription) {
        long startTime = System.currentTimeMillis();
        log.info("开始生成{}统计结果，记录数: {}", dataSourceDescription, records.size());
        
        // 创建结果对象
        DataStatisticsResultDto resultDto = new DataStatisticsResultDto(records, dataSourceDescription);
        
        if (records.isEmpty()) {
            log.info("没有记录，返回空的统计结果");
            return resultDto;
        }
        
        // 按项目分组的Map
        Map<String, ProjectStatisticsDto> projectStatsMap = new HashMap<>();
        
        // 遍历所有记录，按customer_project分组
        for (NormalizeData record : records) {
            try {
                Map<String, Object> data = record.getData();
                
                // 获取项目名称，如果不存在则设为"未知项目"
                String projectName = data.containsKey("customer_project") ? 
                        String.valueOf(data.get("customer_project")) : "未知项目";
                
                // 获取或创建该项目的统计对象
                ProjectStatisticsDto projectStats = projectStatsMap.computeIfAbsent(
                        projectName, name -> new ProjectStatisticsDto(name));
                
                // 添加记录到项目统计中
                projectStats.addRecord(record);
                
            } catch (Exception e) {
                log.error("处理记录时出错 (表={}, ID={}): {}", 
                        record.getTableName(), record.getId(), e.getMessage());
            }
        }
        
        // 将Map转换为List
        List<ProjectStatisticsDto> projectStatsList = new ArrayList<>(projectStatsMap.values());
        
        // 按记录数量降序排序
        projectStatsList.sort((p1, p2) -> Integer.compare(p2.getRecordCount(), p1.getRecordCount()));
        
        // 设置到结果对象
        resultDto.setProjectStatistics(projectStatsList);
        
        long endTime = System.currentTimeMillis();
        log.info("{}统计结果生成完成，共 {} 个项目，耗时 {}ms", 
                dataSourceDescription, projectStatsList.size(), (endTime - startTime));
        
        return resultDto;
    }
    
    /**
     * 根据表名前缀查询数据，用于支持名单数据和调研数据的查询
     * 
     * @param mobile 手机号
     * @param tablePrefix 表名前缀，用于过滤数据类型
     * @param dataTypeName 数据类型名称，用于日志输出
     * @return 查询结果列表
     */
    private List<NormalizeData> queryDataByPrefix(String mobile, String tablePrefix, String dataTypeName) {
        long startTime = System.currentTimeMillis();
        log.info("开始查询手机号的{}内容: {}", dataTypeName, MaskUtil.maskMobile(mobile));

        // 获取表名和ID信息
        long indexStartTime = System.currentTimeMillis();
        List<MobileDataRecord> records = queryByMobile(mobile);
        long indexEndTime = System.currentTimeMillis();

        if (records.isEmpty()) {
            log.info("手机号 {} 没有找到相关记录，耗时 {}ms", mobile, (System.currentTimeMillis() - startTime));
            return new ArrayList<>();
        }

        log.info("从Redis获取索引信息完成，找到记录 {} 条，耗时 {}ms", records.size(), (indexEndTime - indexStartTime));

        // 根据表名前缀过滤数据
        records = records.stream()
                .filter(record -> record.getTableName().startsWith(tablePrefix))
                .collect(Collectors.toList());
        
        log.info("过滤后的{}记录数: {}", dataTypeName, records.size());
        
        if (records.isEmpty()) {
            log.info("手机号 {} 没有找到{}记录，耗时 {}ms", mobile, dataTypeName, (System.currentTimeMillis() - startTime));
            return new ArrayList<>();
        }

        // 按表名分组
        Map<String, List<Long>> tableIdMap = records.stream()
                .collect(Collectors.groupingBy(
                        MobileDataRecord::getTableName,
                        Collectors.mapping(MobileDataRecord::getId, Collectors.toList())
                ));

        log.info("数据按表分组完成，共 {} 个表", tableIdMap.size());
        List<NormalizeData> resultList = new ArrayList<>();

        // 对每个表执行一次批量查询，如果ID列表过大，拆分成多次查询
        tableIdMap.forEach((tableName, idList) -> {
            long tableQueryStart = System.currentTimeMillis();

            // 为避免IN子句过长，每次最多查询1000个ID
            final int MAX_IDS_PER_QUERY = 1000;
            int totalIdsForTable = idList.size();
            int batchCount = (totalIdsForTable + MAX_IDS_PER_QUERY - 1) / MAX_IDS_PER_QUERY;

            log.info("表 {} 的ID总数为 {}，将分 {} 批次查询", tableName, totalIdsForTable, batchCount);

            for (int batch = 0; batch < batchCount; batch++) {
                int fromIndex = batch * MAX_IDS_PER_QUERY;
                int toIndex = Math.min(fromIndex + MAX_IDS_PER_QUERY, totalIdsForTable);
                List<Long> batchIdList = idList.subList(fromIndex, toIndex);

                String sql = "SELECT * FROM " + tableName + " WHERE id IN (" +
                        batchIdList.stream().map(String::valueOf).collect(Collectors.joining(",")) + ")";

                try {
                    log.info("执行SQL查询批次 {}/{}: {}", (batch + 1), batchCount,
                            sql.length() > 200 ? sql.substring(0, 200) + "..." : sql);

                    List<Map<String, Object>> results = jdbcTemplate.queryForList(sql);

                    for (Map<String, Object> row : results) {
                        Long id = ((Number) row.get("id")).longValue();
                        resultList.add(new NormalizeData(tableName, id, cleanupData(row)));
                    }

                    log.info("表 {} 查询批次 {}/{} 完成，获取了 {} 条记录",
                            tableName, (batch + 1), batchCount, results.size());

                } catch (Exception e) {
                    log.error("批量查询表 {} 数据批次 {}/{} 时发生错误: {}",
                            tableName, (batch + 1), batchCount, e.getMessage(), e);
                }
            }

            long tableQueryEnd = System.currentTimeMillis();
            int resultsForTable = (int) resultList.stream()
                    .filter(dto -> dto.getTableName().equals(tableName))
                    .count();

            log.info("表 {} 查询完成，共 {} 条记录，耗时 {}ms",
                    tableName, resultsForTable, (tableQueryEnd - tableQueryStart));
        });

        long endTime = System.currentTimeMillis();
        log.info("手机号 {} 查询完成，共 {} 条{}记录，总耗时 {}ms",
                MaskUtil.maskMobile(mobile), resultList.size(), dataTypeName, (endTime - startTime));

        return resultList;
    }

    // 清理数据，处理序列化问题
    private Map<String, Object> cleanupData(Map<String, Object> rawData) {
        Map<String, Object> cleanData = new HashMap<>();
        for (Map.Entry<String, Object> entry : rawData.entrySet()) {
            Object value = entry.getValue();
            if (value == null || value instanceof Number || value instanceof String ||
                    value instanceof Boolean || value instanceof java.util.Date) {
                cleanData.put(entry.getKey(), value);
            } else {
                cleanData.put(entry.getKey(), value.toString());
            }
        }
        return cleanData;
    }

    /**
     * 清空所有手机号数据
     *
     * @return 清除的键数量
     */
    @Override
    public int clearAllMobileData() {
        log.info("开始清除所有手机号数据");

        try {
            // 使用COUNT命令先获取键的估计数量（DBSIZE，避免使用阻塞的KEYS命令）
            Long estimatedKeySize = redisTemplate.execute((RedisCallback<Long>) connection ->
                    connection.serverCommands().dbSize());

            log.info("当前Redis数据库估计包含 {} 个键",
                    estimatedKeySize);

            // 我们需要更改查询模式，因为现在没有前缀
            // 这里需要谨慎处理，因为没有前缀可能会删除其他键
            // 使用正则表达式来匹配看起来像手机号的键
            
            // 注意：Redis的SCAN命令不支持完整的正则表达式，只支持简单的glob通配符
            // 我们使用1*来匹配以1开头的键，这样可以匹配中国的手机号
            String pattern = "1*"; // 匹配以1开头的键
            log.info("将尝试清除符合手机号模式的键 (pattern: {})", pattern);

            // 记录删除的总键数
            final AtomicInteger totalDeletedKeys = new AtomicInteger(0);

            // 使用SCAN命令批量获取并删除键，避免KEYS命令阻塞Redis
            long startTime = System.currentTimeMillis();
            final int batchSize = 100000; // 每批处理的键数量，考虑减小批量以降低服务器压力
            
            // 设置最大批次数限制，避免无限循环
            final int MAX_ITERATIONS = 2000; // 最多处理2000批

            // 使用游标迭代方式删除，避免一次性获取所有键
            redisTemplate.execute((RedisCallback<Void>) connection -> {
                try {
                    // 获取游标对象
                    ScanOptions options = ScanOptions.scanOptions().match(pattern).count(batchSize).build();
                    Cursor<byte[]> cursor = connection.keyCommands().scan(options);

                    // 分批处理，每批最多处理batchSize个键
                    List<byte[]> keyBatch = new ArrayList<>(batchSize);
                    int batchCount = 0;
                    int retryCount = 0;
                    boolean hasErrors = false;

                    // 设置最大处理时间，避免过长执行
                    long maxRunTime = 10 * 60 * 1000; // 最多运行10分钟
                    long timeoutTime = startTime + maxRunTime;

                    while (cursor.hasNext() && batchCount < MAX_ITERATIONS && System.currentTimeMillis() < timeoutTime) {
                        try {
                            keyBatch.add(cursor.next());

                            // 当积累了一批键或扫描完成时删除这批键
                            if (keyBatch.size() >= batchSize || !cursor.hasNext()) {
                                int currentBatchSize = keyBatch.size();
                                if (currentBatchSize > 0) {
                                    batchCount++;

                                    // 记录删除操作前的时间
                                    long batchStartTime = System.currentTimeMillis();
                                    log.info("开始删除第 {} 批键，本批键数: {}, 当前已删除: {}",
                                            batchCount, currentBatchSize, totalDeletedKeys.get());

                                    // 将byte[]转换为字符串用于日志
                                    if (log.isDebugEnabled() && currentBatchSize > 0) {
                                        String firstKey = new String(keyBatch.get(0));
                                        String lastKey = new String(keyBatch.get(currentBatchSize - 1));
                                        log.debug("第 {} 批键范围示例: [{}...{}]", batchCount, firstKey, lastKey);
                                    }

                                    try {
                                        // 批量删除键
                                        Long deleted = connection.keyCommands().del(keyBatch.toArray(new byte[0][]));
                                        totalDeletedKeys.addAndGet(deleted != null ? deleted.intValue() : 0);

                                        // 记录批次完成时间
                                        long batchEndTime = System.currentTimeMillis();
                                        log.info("完成第 {} 批键删除，删除了 {} 个键，耗时: {}ms, 总计已删除: {}",
                                                batchCount, deleted, (batchEndTime - batchStartTime), totalDeletedKeys.get());

                                        // 重置错误标记和重试计数
                                        hasErrors = false;
                                        retryCount = 0;
                                    } catch (Exception e) {
                                        // 删除操作出错
                                        log.error("删除第 {} 批键时出错: {}", batchCount, e.getMessage());

                                        // 增加重试计数
                                        retryCount++;
                                        hasErrors = true;

                                        // 如果重试超过3次，跳过当前批次
                                        if (retryCount > 3) {
                                            log.warn("第 {} 批键删除重试超过3次，跳过该批次", batchCount);
                                            retryCount = 0;
                                            hasErrors = false;
                                        } else {
                                            // 如果是第一次或第二次重试，等待一段时间后再尝试
                                            try {
                                                int waitTime = retryCount * 1000; // 递增等待时间
                                                log.info("等待 {}ms 后重试第 {} 批键删除", waitTime, batchCount);
                                                Thread.sleep(waitTime);

                                                // 重试当前批次，不清空keyBatch
                                                continue;
                                            } catch (InterruptedException ie) {
                                                Thread.currentThread().interrupt();
                                                break;
                                            }
                                        }
                                    }

                                    // 清空批次列表，准备下一批
                                    keyBatch.clear();

                                    // 短暂休息，避免对Redis造成过大压力
                                    // 每2批次休息一次，休息时间增加
                                    if (batchCount % 2 == 0) {
                                        try {
                                            int sleepTime = 200; // 增加休息时间到200ms
                                            log.debug("第 {} 批次后休息 {}ms", batchCount, sleepTime);
                                            Thread.sleep(sleepTime);
                                        } catch (InterruptedException e) {
                                            Thread.currentThread().interrupt();
                                            break;
                                        }
                                    }

                                    // 每100批次输出一次执行进度情况
                                    if (batchCount % 100 == 0) {
                                        long currentElapsed = System.currentTimeMillis() - startTime;
                                        log.info("已处理 {} 批次，共删除 {} 个键，已用时 {}秒",
                                                batchCount, totalDeletedKeys.get(), currentElapsed / 1000);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            // 处理SCAN命令的异常
                            log.error("SCAN操作第 {} 批次时出错: {}", batchCount, e.getMessage());

                            // 如果是游标操作错误，尝试关闭当前游标并重新打开
                            try {
                                if (cursor != null) {
                                    try {
                                        cursor.close();
                                    } catch (Exception closeEx) {
                                        log.warn("关闭游标失败: {}", closeEx.getMessage());
                                    }
                                }

                                // 等待一段时间后重新获取游标
                                Thread.sleep(2000);
                                log.info("尝试重新创建SCAN游标");
                                cursor = connection.keyCommands().scan(options);

                                // 如果重新创建游标失败，则跳出循环
                                if (cursor == null) {
                                    log.error("无法重新创建SCAN游标，终止清除操作");
                                    break;
                                }

                                // 重置键批次，继续处理
                                keyBatch.clear();
                            } catch (Exception reconnectEx) {
                                log.error("重新创建SCAN游标失败: {}", reconnectEx.getMessage());
                                break;
                            }
                        }
                    }

                    // 检查是否是因为达到最大批次数而退出
                    if (batchCount >= MAX_ITERATIONS) {
                        log.warn("已达到设定的最大批次数 {}，停止删除操作", MAX_ITERATIONS);
                    }

                    // 检查是否是因为超时而退出
                    if (System.currentTimeMillis() >= timeoutTime) {
                        log.warn("已达到最大执行时间 {} 分钟，停止删除操作", maxRunTime / (60 * 1000));
                    }

                    // 关闭游标
                    if (cursor != null) {
                        try {
                            cursor.close();
                        } catch (Exception e) {
                            log.warn("关闭游标失败: {}", e.getMessage());
                        }
                    }

                    long totalTime = System.currentTimeMillis() - startTime;
                    log.info("所有手机号数据清除完成，共删除 {} 个键，共 {} 个批次，总耗时: {}秒",
                            totalDeletedKeys.get(), batchCount, totalTime / 1000);

                } catch (Exception e) {
                    log.error("使用SCAN方式清除手机号数据时出错: {}", e.getMessage(), e);
                    // 遇到错误不中断，继续执行
                }
                return null;
            });

            return totalDeletedKeys.get();

        } catch (Exception e) {
            log.error("清除手机号数据时发生异常: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public MobileCombinedStatsDTO getCombinedStatsByMobile(String mobile) {
        log.info("开始获取手机号'{}' 的综合统计数据", mobile);
        
        // 1. 获取名单和数据集的统计信息
        DataStatisticsResultDto namelistStats = queryNameListWithStatistics(mobile);
        DataStatisticsResultDto datasetStats = queryDatasetWithStatistics(mobile);
        
        // 2. 创建综合统计对象
        MobileCombinedStatsDTO result = new MobileCombinedStatsDTO();
        result.setMobile(mobile);
        
        // 3. 设置名单统计信息
        MobileCombinedStatsDTO.NamelistStats namelist = new MobileCombinedStatsDTO.NamelistStats();
        namelist.setTotal(namelistStats.getTotalRecords());
        
        // 转换名单来源统计
        List<MobileCombinedStatsDTO.SourceCount> sourceCountList = new ArrayList<>();
        // TODO: 从namelistStats中提取来源统计，这需要根据实际数据结构添加代码
        namelist.setBySource(sourceCountList);
        
        // 转换名单项目统计
        List<MobileCombinedStatsDTO.CustomerProjectCount> projectCountList = new ArrayList<>();
        for (ProjectStatisticsDto project : namelistStats.getProjectStatistics()) {
            MobileCombinedStatsDTO.CustomerProjectCount projectCount = new MobileCombinedStatsDTO.CustomerProjectCount();
            projectCount.setCustomerProject(project.getProjectName());
            projectCount.setCount(project.getRecordCount());
            projectCountList.add(projectCount);
        }
        namelist.setByCustomerProject(projectCountList);
        
        // 设置时间范围
        MobileCombinedStatsDTO.TimeRange namelistTimeRange = new MobileCombinedStatsDTO.TimeRange();
        // TODO: 从namelistStats中提取时间范围，这需要根据实际数据结构添加代码
        namelist.setByCreateTime(namelistTimeRange);
        
        result.setNamelistStats(namelist);
        
        // 4. 设置数据集统计信息
        MobileCombinedStatsDTO.DatasetStats dataset = new MobileCombinedStatsDTO.DatasetStats();
        dataset.setTotal(datasetStats.getTotalRecords());
        
        // 转换数据集类型统计
        List<MobileCombinedStatsDTO.TypeCount> typeCountList = new ArrayList<>();
        // TODO: 从datasetStats中提取类型统计，这需要根据实际数据结构添加代码
        dataset.setByDatasetType(typeCountList);
        
        // 转换数据集项目统计
        List<MobileCombinedStatsDTO.CustomerProjectCount> datasetProjectCountList = new ArrayList<>();
        for (ProjectStatisticsDto project : datasetStats.getProjectStatistics()) {
            MobileCombinedStatsDTO.CustomerProjectCount projectCount = new MobileCombinedStatsDTO.CustomerProjectCount();
            projectCount.setCustomerProject(project.getProjectName());
            projectCount.setCount(project.getRecordCount());
            datasetProjectCountList.add(projectCount);
        }
        dataset.setByCustomerProject(datasetProjectCountList);
        
        // 设置时间范围
        MobileCombinedStatsDTO.TimeRange datasetTimeRange = new MobileCombinedStatsDTO.TimeRange();
        // TODO: 从datasetStats中提取时间范围，这需要根据实际数据结构添加代码
        dataset.setByCreateTime(datasetTimeRange);
        
        result.setDatasetStats(dataset);
        
        // 5. 构建汇总信息
        MobileCombinedStatsDTO.Summary summary = new MobileCombinedStatsDTO.Summary();
        summary.setTotalRecords(namelistStats.getTotalRecords() + datasetStats.getTotalRecords());
        
        // 处理客户项目统计
        Map<String, Integer> namelistByProject = new HashMap<>();
        for (MobileCombinedStatsDTO.CustomerProjectCount projectCount : namelist.getByCustomerProject()) {
            namelistByProject.put(projectCount.getCustomerProject(), projectCount.getCount());
        }
        
        Map<String, Integer> datasetByProject = new HashMap<>();
        for (MobileCombinedStatsDTO.CustomerProjectCount projectCount : dataset.getByCustomerProject()) {
            datasetByProject.put(projectCount.getCustomerProject(), projectCount.getCount());
        }
        
        // 合并项目统计
        Set<String> allProjects = new HashSet<>();
        allProjects.addAll(namelistByProject.keySet());
        allProjects.addAll(datasetByProject.keySet());
        
        // 创建项目名称到相关信息的映射，用于提取省份、城市和签约时间信息
        Map<String, Map<String, Object>> projectInfoMap = new HashMap<>();
        
        // 从名单数据中提取项目相关信息
        for (NormalizeData record : namelistStats.getOriginalRecords()) {
            Map<String, Object> data = record.getData();
            if (data != null) {
                Object projectObj = data.get("customer_project");
                String projectName = projectObj != null ? String.valueOf(projectObj) : "";
                
                if (projectName != null && !projectName.isEmpty() && !projectInfoMap.containsKey(projectName)) {
                    Map<String, Object> infoMap = new HashMap<>();
                    
                    // 提取省份信息
                    if (data.containsKey("province")) {
                        infoMap.put("province", data.get("province"));
                    }
                    
                    // 提取城市信息
                    if (data.containsKey("city")) {
                        infoMap.put("city", data.get("city"));
                    }
                    
                    // 提取签约时间信息
                    if (data.containsKey("d_sign_datetime")) {
                        infoMap.put("signDatetime", data.get("d_sign_datetime"));
                    }
                    
                    // 提取楼盘档次信息
                    if (data.containsKey("building_grade")) {
                        infoMap.put("buildingGrade", data.get("building_grade"));
                    }
                    
                    // 提取房屋类型信息
                    if (data.containsKey("d_house_type")) {
                        infoMap.put("houseType", data.get("d_house_type"));
                    }
                    
                    // 提取装修类型信息
                    if (data.containsKey("d_decoration_type")) {
                        infoMap.put("decorationType", data.get("d_decoration_type"));
                    }
                    
                    // 提取房号信息
                    if (data.containsKey("d_house_no")) {
                        infoMap.put("houseNo", data.get("d_house_no"));
                    }
                    
                    projectInfoMap.put(projectName, infoMap);
                }
            }
        }
        
        List<MobileCombinedStatsDTO.ProjectDetail> projectDetails = new ArrayList<>();
        for (String project : allProjects) {
            MobileCombinedStatsDTO.ProjectDetail detail = new MobileCombinedStatsDTO.ProjectDetail();
            detail.setCustomerProject(project);
            
            int namelistCount = namelistByProject.getOrDefault(project, 0);
            int datasetCount = datasetByProject.getOrDefault(project, 0);
            
            detail.setNamelistCount(namelistCount);
            detail.setDatasetCount(datasetCount);
            detail.setTotalCount(namelistCount + datasetCount);
            
            // 设置额外信息
            Map<String, Object> projectInfo = projectInfoMap.get(project);
            if (projectInfo != null) {
                if (projectInfo.containsKey("province")) {
                    detail.setProvince((String) projectInfo.get("province"));
                }
                
                if (projectInfo.containsKey("city")) {
                    detail.setCity((String) projectInfo.get("city"));
                }
                
                if (projectInfo.containsKey("signDatetime")) {
                    detail.setSignDatetime(String.valueOf(projectInfo.get("signDatetime")));
                }
                
                if (projectInfo.containsKey("buildingGrade")) {
                    detail.setBuildingGrade(String.valueOf(projectInfo.get("buildingGrade")));
                }
                
                if (projectInfo.containsKey("houseType")) {
                    detail.setHouseType(String.valueOf(projectInfo.get("houseType")));
                }
                
                if (projectInfo.containsKey("decorationType")) {
                    detail.setDecorationType(String.valueOf(projectInfo.get("decorationType")));
                }
                
                if (projectInfo.containsKey("houseNo")) {
                    detail.setHouseNo(String.valueOf(projectInfo.get("houseNo")));
                }
            }
            
            projectDetails.add(detail);
        }
        
        // 按总数降序排序
        projectDetails.sort((p1, p2) -> Integer.compare(p2.getTotalCount(), p1.getTotalCount()));
        
        MobileCombinedStatsDTO.CustomerProjectStats projectStats = new MobileCombinedStatsDTO.CustomerProjectStats();
        projectStats.setTotalProjects(allProjects.size());
        projectStats.setProjects(projectDetails);
        summary.setCustomerProjectStats(projectStats);
        
        // 设置整体时间范围
        MobileCombinedStatsDTO.TimeRange overallTimeRange = new MobileCombinedStatsDTO.TimeRange();
        // TODO: 合并两个时间范围，这需要根据实际数据结构添加代码
        summary.setTimeRange(overallTimeRange);
        
        result.setSummary(summary);
        
        return result;
    }

    /**
     * 处理单个分片的键扫描，支持早期终止
     */
    private Map<String, Integer> scanShardWithEarlyStop(
            String pattern, int shardIndex, int totalShards, String queryType, 
            AtomicInteger dynamicThreshold, AtomicBoolean earlyTerminationFlag, 
            int limit, int maxKeysPerShard) {
        
        // 使用优先队列保存最大的N个结果
        PriorityQueue<Map.Entry<String, Integer>> candidateQueue = 
                new PriorityQueue<>(Comparator.comparingInt(Map.Entry::getValue));
        
        AtomicInteger processedCount = new AtomicInteger(0);
        AtomicInteger totalScannedCount = new AtomicInteger(0);
        long startTime = System.currentTimeMillis();
        
        // 当前分片的本地最低阈值，使用原子引用解决作用域问题
        final AtomicInteger localThresholdRef = new AtomicInteger(dynamicThreshold.get());
        
        // 每个分片的目标候选记录数
        int targetCandidatesPerShard = Math.max(20, limit / totalShards * 3);
        
        log.info("分片 {}/{} 开始扫描，初始阈值: {}, 目标候选数: {}", 
                shardIndex + 1, totalShards, localThresholdRef.get(), targetCandidatesPerShard);
        
        // 提前终止检查间隔，减小为1000提高检查频率
        final int checkInterval = 1000;
        
        // 定义足够的候选记录数量，当所有分片合计记录达到此值时可提前终止
        final int sufficientCandidatesTotal = limit * 3;
        
        redisTemplate.execute((RedisCallback<Object>) connection -> {
            try (Cursor<byte[]> cursor = connection.keyCommands().scan(
                    ScanOptions.scanOptions().match(pattern).count(1000).build())) {
                
                List<byte[]> keyBatch = new ArrayList<>(1000);
                
                while (cursor.hasNext() && processedCount.get() < maxKeysPerShard) {
                    // 检查全局提前终止标志
                    if (earlyTerminationFlag.get()) {
                        log.info("分片 {}/{} 响应全局提前终止信号，当前已处理: {}/{} 键",
                                shardIndex + 1, totalShards, processedCount.get(), maxKeysPerShard);
                        break;
                    }
                    
                    // 定期检查本分片是否可以提前终止
                    if (processedCount.get() > 0 && processedCount.get() % checkInterval == 0) {
                        // 当前已处理占总处理量的比例
                        double processedRatio = (double)processedCount.get() / maxKeysPerShard;
                        
                        // 提前终止条件放宽：
                        // 1. 已处理比例从20%降低到10%
                        // 2. 候选记录质量要求从高于阈值1.5倍降低到1.3倍
                        if (candidateQueue.size() >= targetCandidatesPerShard && 
                            !candidateQueue.isEmpty() && 
                            candidateQueue.peek().getValue() > dynamicThreshold.get() * 1.3 && 
                            processedRatio >= 0.1) { // 至少处理10%的键
                            
                            log.info("分片 {}/{} 达到提前终止条件，已处理: {}/{} 键 ({}%), 已找到: {} 候选记录",
                                    shardIndex + 1, totalShards, processedCount.get(), maxKeysPerShard,
                                    String.format("%.1f", processedRatio * 100),
                                    candidateQueue.size());
                            
                            // 设置提前终止标志
                            earlyTerminationFlag.set(true);
                            break;
                        }
                        
                        // 新增: 检查所有分片总候选记录数是否足够 
                        // (假设每个分片候选队列大小相等，粗略估算)
                        int estimatedTotalCandidates = candidateQueue.size() * totalShards;
                        if (estimatedTotalCandidates >= sufficientCandidatesTotal && processedRatio >= 0.05) {
                            log.info("分片 {}/{} 估计总候选记录数({}*{})={}已足够，提前终止扫描",
                                    shardIndex + 1, totalShards, candidateQueue.size(), 
                                    totalShards, estimatedTotalCandidates);
                            earlyTerminationFlag.set(true);
                            break;
                        }
                    }
                    
                    byte[] key = cursor.next();
                    totalScannedCount.incrementAndGet();
                    
                    // 根据键的哈希值分片
                    int keyHash = Arrays.hashCode(key) % totalShards;
                    if (keyHash < 0) keyHash += totalShards;
                    
                    if (keyHash == shardIndex) {
                        keyBatch.add(key);
                        
                        if (keyBatch.size() >= 1000 || !cursor.hasNext()) {
                            // 处理前检查并更新阈值
                            int currentGlobalThreshold = dynamicThreshold.get();
                            if (currentGlobalThreshold > localThresholdRef.get()) {
                                localThresholdRef.set(currentGlobalThreshold);
                            }
                            
                            // 处理一批键
                            processKeyBatchWithPriority(keyBatch, candidateQueue, queryType, 
                                    localThresholdRef.get(), targetCandidatesPerShard * 2, dynamicThreshold);
                            
                            int newProcessed = processedCount.addAndGet(keyBatch.size());
                            
                            // 记录进度
                            if (processedCount.get() % 50000 == 0 || !cursor.hasNext()) {
                                log.info("分片 {}/{} 已处理 {}/{} 个键 ({}%), 匹配数量: {}, 当前阈值: {}, 耗时: {}ms",
                                        shardIndex + 1, totalShards, newProcessed, totalScannedCount.get(),
                                        String.format("%.1f", (double)newProcessed / maxKeysPerShard * 100),
                                        candidateQueue.size(), localThresholdRef.get(), 
                                        System.currentTimeMillis() - startTime);
                            }
                            
                            keyBatch.clear();
                        }
                    }
                }
                
                log.info("分片 {}/{} 扫描{}，共处理 {} 个键，总扫描数: {}, 匹配数量: {}, 最终阈值: {}, 总耗时: {}ms",
                        shardIndex + 1, totalShards, 
                        earlyTerminationFlag.get() ? "提前终止" : "完成",
                        processedCount.get(), totalScannedCount.get(), 
                        candidateQueue.size(), localThresholdRef.get(),
                        System.currentTimeMillis() - startTime);
                
            } catch (Exception e) {
                log.error("分片 {}/{} 扫描出错: {}", shardIndex + 1, totalShards, e.getMessage(), e);
            }
            return null;
        });
        
        // 将优先队列转换为Map返回
        Map<String, Integer> resultMap = new HashMap<>();
        while (!candidateQueue.isEmpty()) {
            Map.Entry<String, Integer> entry = candidateQueue.poll();
            resultMap.put(entry.getKey(), entry.getValue());
        }
        
        return resultMap;
    }

    /**
     * 使用优先队列处理批量键，只保留最大的N个结果
     */
    private void processKeyBatchWithPriority(List<byte[]> keyBatch, 
                                           PriorityQueue<Map.Entry<String, Integer>> topResults,
                                           String queryType, int localThreshold, 
                                           int maxQueueSize, AtomicInteger globalThreshold) {
        if (keyBatch.isEmpty()) {
            return;
        }
        
        // 转换字节数组键为字符串
        List<String> stringKeys = keyBatch.stream()
                .map(String::new)
                .collect(Collectors.toList());
        
        // 批量获取值
        List<String> values = redisTemplate.opsForValue().multiGet(stringKeys);
        
        if (values == null) {
            return;
        }
        
        for (int i = 0; i < stringKeys.size(); i++) {
            String key = stringKeys.get(i);
            String value = values.get(i);
            
            if (value == null || value.isEmpty()) {
                continue;
            }
            
            // 根据查询类型统计记录数
            String[] records = value.split(",");
            int namelistCount = 0;
            int datasetCount = 0;
            
            for (String record : records) {
                if (record.startsWith("data_namelist_")) {
                    namelistCount++;
                } else if (record.startsWith("dataset_")) {
                    datasetCount++;
                }
            }
            
            int totalCount = 0;
            switch (queryType) {
                case "ALL":
                    totalCount = namelistCount + datasetCount;
                    break;
                case "NAMELIST":
                    totalCount = namelistCount;
                    break;
                case "DATASET":
                    totalCount = datasetCount;
                    break;
            }
            
            // 动态阈值过滤 - 如果记录数低于当前阈值，跳过
            if (totalCount <= localThreshold) {
                continue;
            }
            
            // 记录数超过阈值，添加到优先队列
            AbstractMap.SimpleEntry<String, Integer> entry = 
                    new AbstractMap.SimpleEntry<>(key, totalCount);
            
            topResults.offer(entry);
            
            // 如果队列超出大小限制，移除最小的元素
            if (topResults.size() > maxQueueSize) {
                topResults.poll();
            }
            
            // 如果添加后队列已满，且当前最小记录值明显高于全局阈值，尝试提高全局阈值
            if (topResults.size() == maxQueueSize && !topResults.isEmpty()) {
                int currentSmallest = topResults.peek().getValue();
                int currentGlobal = globalThreshold.get();
                
                // 只有当队列最小值显著高于全局阈值时才更新
                if (currentSmallest > currentGlobal * 1.2) { // 增加20%的裕量确保值足够高
                    // 更新全局阈值
                    globalThreshold.updateAndGet(current -> Math.max(current, currentSmallest));
                }
            }
        }
    }

    @Override
    public MobileStatsResultDto queryTopMobilesByRecordCount(String queryType, int threshold, int limit) {
        log.info("开始查询记录数量大于{}的TOP{}个手机号，类型: {}", threshold, limit, queryType);
        long startTime = System.currentTimeMillis();
        
        // 估计总键数
        Long totalKeys = redisTemplate.execute((RedisCallback<Long>) connection -> 
            connection.serverCommands().dbSize());
        log.info("Redis数据库估计键数量: {}", totalKeys);
        
        // 限制最大扫描数量，防止操作过大导致性能问题
        final int maxScanCount = Math.min(totalKeys != null ? totalKeys.intValue() : 10000000, 10000000);
        log.info("设置最大扫描数量: {}", maxScanCount);
        
        // 引入并行处理机制
        final int parallelThreads = Math.min(4, Runtime.getRuntime().availableProcessors());
        log.info("使用 {} 个并行线程处理Redis扫描", parallelThreads);
        
        // 创建线程池
        ExecutorService executor = Executors.newFixedThreadPool(parallelThreads);
        
        // 早期终止优化：共享动态阈值，初始值为查询阈值
        final AtomicInteger dynamicThreshold = new AtomicInteger(threshold);
        
        // 早期终止优化：共享终止标志
        final AtomicBoolean earlyTerminationFlag = new AtomicBoolean(false);
        
        try {
            // 分片扫描键空间
            final int totalShards = parallelThreads * 2; // 将键空间分为 2 倍于线程数的分片
            List<CompletableFuture<Map<String, Integer>>> futures = new ArrayList<>();
            
            for (int shard = 0; shard < totalShards; shard++) {
                final int currentShard = shard;
                CompletableFuture<Map<String, Integer>> future = CompletableFuture.supplyAsync(() -> {
                    return scanShardWithEarlyStop(
                        "*", currentShard, totalShards, queryType, dynamicThreshold, 
                        earlyTerminationFlag, limit, maxScanCount / totalShards);
                }, executor);
                futures.add(future);
            }
            
            // 新增：创建定时检查任务，定期检查各分片已完成的结果
            // 如果发现已经有足够的高质量结果，可以提前终止所有任务
            ScheduledExecutorService scheduledExecutor = Executors.newSingleThreadScheduledExecutor();
            AtomicReference<String> terminationReason = new AtomicReference<>("正常完成");
            
            ScheduledFuture<?> scheduledFuture = scheduledExecutor.scheduleAtFixedRate(() -> {
                if (earlyTerminationFlag.get()) {
                    return; // 已经设置终止标志，不需要再检查
                }
                
                try {
                    // 检查已经完成的分片任务
                    int completedTasks = 0;
                    int totalCandidates = 0;
                    int completedShardsWithEnoughResults = 0;
                    
                    for (CompletableFuture<Map<String, Integer>> future : futures) {
                        if (future.isDone() && !future.isCompletedExceptionally()) {
                            completedTasks++;
                            try {
                                Map<String, Integer> result = future.get(50, TimeUnit.MILLISECONDS);
                                if (result != null) {
                                    totalCandidates += result.size();
                                    if (result.size() >= 5) { // 分片至少找到5个结果
                                        completedShardsWithEnoughResults++;
                                    }
                                }
                            } catch (Exception e) {
                                // 忽略获取结果时的异常
                            }
                        }
                    }
                    
                    // 检查终止条件：
                    // 1. 至少5个分片已完成且每个至少有5个结果
                    // 2. 总候选数超过目标数量的2倍
                    if (completedShardsWithEnoughResults >= 5 && totalCandidates >= limit * 2) {
                        String reason = String.format("已完成%d个分片，找到%d个候选记录（目标%d的%d倍）",
                                completedTasks, totalCandidates, limit, totalCandidates / limit);
                        terminationReason.set(reason);
                        
                        log.info("提前终止所有分片扫描：{}", reason);
                        earlyTerminationFlag.set(true);
                    }
                } catch (Exception e) {
                    log.warn("检查分片完成状态时出错: {}", e.getMessage());
                }
            }, 3, 3, TimeUnit.SECONDS); // 每3秒检查一次
            
            // 等待所有分片处理完成并合并结果
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            
            // 添加超时保护
            try {
                allFutures.get(15, TimeUnit.MINUTES);
            } catch (Exception e) {
                log.warn("等待分片处理超时或终止，将处理已完成的分片结果: {}", e.getMessage());
            } finally {
                // 停止定时检查任务
                scheduledFuture.cancel(true);
                scheduledExecutor.shutdownNow();
            }
            
            // 合并所有分片结果
            Map<String, Integer> mergedResults = new HashMap<>();
            AtomicInteger totalMatchedCount = new AtomicInteger(0);
            
            for (CompletableFuture<Map<String, Integer>> future : futures) {
                try {
                    Map<String, Integer> shardResult = future.isDone() ? future.get(1, TimeUnit.SECONDS) : new HashMap<>();
                    mergedResults.putAll(shardResult);
                    totalMatchedCount.addAndGet(shardResult.size());
                } catch (Exception e) {
                    log.error("获取分片结果时出错: {}", e.getMessage());
                }
            }
            
            // 按记录数量排序并限制返回数量
            List<Map.Entry<String, Integer>> sortedEntries = mergedResults.entrySet().stream()
                    .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                    .limit(limit)
                    .collect(Collectors.toList());
            
            // 提取排序后的手机号列表
            List<String> topMobiles = sortedEntries.stream()
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
            
            // 构建返回结果
            Map<String, Integer> topCountByMobile = new HashMap<>();
            sortedEntries.forEach(entry -> topCountByMobile.put(entry.getKey(), entry.getValue()));
            
            MobileStatsResultDto result = new MobileStatsResultDto();
            result.setQueryType(queryType);
            result.setMobileList(topMobiles);
            result.setCountByMobile(topCountByMobile);
            result.setThreshold(threshold);
            result.setLimit(limit);
            result.setTotalMobiles(totalMatchedCount.get());
            
            long endTime = System.currentTimeMillis();
            log.info("查询TOP手机号完成，共匹配 {} 个符合条件的手机号，返回前 {} 个，最终动态阈值: {}，终止原因: {}，总耗时: {}ms", 
                    totalMatchedCount.get(), topMobiles.size(), dynamicThreshold.get(), 
                    terminationReason.get(), (endTime - startTime));
            
            return result;
        } finally {
            // 关闭线程池
            try {
                executor.shutdown();
                if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 查询名单中同一个手机号项目数量大于N的记录
     *
     * @param threshold 项目数量阈值，查询项目数量大于该值的手机号
     * @return 查询结果，包含手机号、项目数量和项目集合
     */
    @Override
    public MobileProjectCountResultDTO queryMobilesByProjectCount(int threshold) {
        log.info("开始查询名单中项目数量大于{}的手机号", threshold);
        long startTime = System.currentTimeMillis();
        
        // 创建查询结果
        List<MobileProjectCountDTO> resultList = new ArrayList<>();
        // 设置一个最大结果数量阈值，当结果超过这个阈值时提前终止查询
        final int MAX_RESULTS = 1000;
        // 创建手机号到项目集合的映射
        Map<String, Set<String>> mobileToProjects = new HashMap<>();
        
        try {
            // 增加查询超时时间，设置为600秒
            int queryTimeoutSeconds = 600;
            jdbcTemplate.setQueryTimeout(queryTimeoutSeconds);
            
            // 创建项目到手机号的映射，用于统计每个项目下的手机号数量
            Map<String, Set<String>> projectToMobiles = new HashMap<>();
            
            // 遍历所有名单表
            for (String tableName : NAMELIST_TABLE_NAMES) {
                log.info("开始处理表: {}", tableName);
                
                // 获取表的ID范围
                MinMaxIds idRange = getTableIdRange(tableName);
                
                if (!idRange.isValid()) {
                    log.warn("表 {} 没有有效的ID范围，跳过", tableName);
                    continue;
                }
                
                Long minId = idRange.getMinId();
                Long maxId = idRange.getMaxId();
                log.info("表 {} 的ID范围: {} - {}", tableName, minId, maxId);
                
                // 批次大小：每次处理10万条记录
                final int BATCH_SIZE = 100000;
                
                // 计算需要处理的批次数
                long totalIdSpan = maxId - minId + 1;
                int totalBatches = (int)((totalIdSpan + BATCH_SIZE - 1) / BATCH_SIZE);
                
                log.info("表 {} 将分成 {} 个批次处理，每批次 {} 条记录", tableName, totalBatches, BATCH_SIZE);
                
                // 记录表处理开始时间
                long tableStartTime = System.currentTimeMillis();
                int totalProcessedForTable = 0;
                
                for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                    long batchStartId = minId + (batchIndex * (long)BATCH_SIZE);
                    long batchEndId = Math.min(maxId, batchStartId + BATCH_SIZE - 1);
                    
                    log.info("处理表 {} 的第 {}/{} 批次，ID范围: {} - {}", 
                            tableName, batchIndex + 1, totalBatches, batchStartId, batchEndId);
                    
                    long batchStartTime = System.currentTimeMillis();
                    
                    // 分批查询SQL
                    String sql = "SELECT d_mobile, customer_project FROM " + tableName + 
                               " WHERE " + MOBILE_QUERY_CONDITION + 
                               " AND customer_project IS NOT NULL AND customer_project <> ''" +
                               " AND id >= " + batchStartId + " AND id <= " + batchEndId;
                    
                    List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql);
                    
                    log.info("批次 {}/{} 查询完成，获取 {} 条记录，耗时: {}秒", 
                            batchIndex + 1, totalBatches, rows.size(), 
                            (System.currentTimeMillis() - batchStartTime) / 1000.0);
                    
                    totalProcessedForTable += rows.size();
                    
                    // 处理查询结果
                    for (Map<String, Object> row : rows) {
                        String mobile = (String) row.get("d_mobile");
                        String project = (String) row.get("customer_project");
                        
                        if (mobile != null && !mobile.isEmpty() && project != null && !project.isEmpty()) {
                            // 更新手机号到项目的映射
                            mobileToProjects.computeIfAbsent(mobile, k -> new HashSet<>()).add(project);
                            
                            // 更新项目到手机号的映射
                            projectToMobiles.computeIfAbsent(project, k -> new HashSet<>()).add(mobile);
                        }
                    }
                    
                    // 提前终止条件：如果已找到足够多的结果，可以提前停止
                    int potentialResults = 0;
                    for (Map.Entry<String, Set<String>> entry : mobileToProjects.entrySet()) {
                        if (entry.getValue().size() > threshold) {
                            potentialResults++;
                        }
                    }
                    
                    if (potentialResults >= MAX_RESULTS && mobileToProjects.size() > MAX_RESULTS * 3) {
                        log.info("已找到 {} 个潜在结果，大于阈值 {}，提前终止表 {} 的处理", 
                                potentialResults, MAX_RESULTS, tableName);
                        break;
                    }
                    
                    // 批次处理完成后记录进度
                    log.info("表 {} 批次 {}/{} 处理完成，当前已处理 {}/{} 条记录，累计获取手机号 {} 个",
                            tableName, batchIndex + 1, totalBatches, 
                            totalProcessedForTable, totalIdSpan, mobileToProjects.size());
                }
                
                // 记录表处理完成时间
                long tableEndTime = System.currentTimeMillis();
                log.info("表 {} 处理完成，共处理 {} 条记录，获得 {} 个手机号，耗时: {}秒",
                        tableName, totalProcessedForTable, mobileToProjects.size(),
                        (tableEndTime - tableStartTime) / 1000.0);
                
                // 筛选出项目数量超过阈值的项目
                Set<String> qualifiedProjects = new HashSet<>();
                for (Map.Entry<String, Set<String>> entry : projectToMobiles.entrySet()) {
                    if (entry.getValue().size() > threshold) {
                        qualifiedProjects.add(entry.getKey());
                    }
                }
                
                log.info("表 {} 中找到 {} 个满足条件的项目(手机号数量>{})",
                        tableName, qualifiedProjects.size(), threshold);
                
                // 提前终止条件：如果当前表中已找到足够多的满足条件的项目，可以停止处理后续表
                if (qualifiedProjects.size() >= 5) { // 当满足条件的项目数量超过5个时提前终止
                    log.info("表 {} 中找到 {} 个满足条件的项目(手机号数量>{})，达到提前退出阈值，不再处理后续表",
                            tableName, qualifiedProjects.size(), threshold);
                    break;
                }
                
                // 提前终止条件：如果已找到足够多的结果，可以停止处理后续表
                int potentialResults = 0;
                for (Map.Entry<String, Set<String>> entry : mobileToProjects.entrySet()) {
                    if (entry.getValue().size() > threshold) {
                        potentialResults++;
                    }
                }
                
                if (potentialResults >= MAX_RESULTS) {
                    log.info("已找到 {} 个潜在结果，大于阈值 {}，提前终止后续表的处理", 
                            potentialResults, MAX_RESULTS);
                    break;
                }
            }
            
            // 转换结果
            for (Map.Entry<String, Set<String>> entry : mobileToProjects.entrySet()) {
                String mobile = entry.getKey();
                Set<String> projects = entry.getValue();
                
                // 只保留项目数量大于阈值的结果
                if (projects.size() > threshold) {
                    MobileProjectCountDTO dto = new MobileProjectCountDTO();
                    dto.setMobile(mobile);
                    dto.setProjectCount(projects.size());
                    dto.setProjects(projects);
                    resultList.add(dto);
                    
                    // 如果结果数量已经超过阈值，可以提前终止
                    if (resultList.size() >= MAX_RESULTS) {
                        log.info("结果数量已达到 {}，提前终止处理", MAX_RESULTS);
                        break;
                    }
                }
            }
            
            // 按项目数量降序排序
            resultList.sort((o1, o2) -> Integer.compare(o2.getProjectCount(), o1.getProjectCount()));
            
            long endTime = System.currentTimeMillis();
            double duration = (endTime - startTime) / 1000.0;
            
            log.info("查询完成，满足条件的手机号共 {} 个，总耗时: {}秒",
                    resultList.size(), String.format("%.2f", duration));
            
            MobileProjectCountResultDTO result = new MobileProjectCountResultDTO();
            result.setTotal(resultList.size());
            result.setRecords(resultList);
            
            return result;
            
        } catch (Exception e) {
            log.error("查询项目数量大于{}的手机号时出错: {}", threshold, e.getMessage(), e);
            throw new RuntimeException("查询项目数量时出错", e);
        }
    }
} 