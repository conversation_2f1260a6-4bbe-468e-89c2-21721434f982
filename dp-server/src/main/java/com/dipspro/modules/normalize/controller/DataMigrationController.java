package com.dipspro.modules.normalize.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.normalize.service.DataMigrationService;
import com.dipspro.modules.profile.dto.MobileCombinedStatsDTO;
import com.dipspro.modules.profile.dto.MobileProjectCountResultDTO;
import com.dipspro.util.MaskUtil;

@RestController
@RequestMapping("/api/data-migration")
public class DataMigrationController {

    private static final Logger logger = LoggerFactory.getLogger(DataMigrationController.class);

    @Autowired
    private DataMigrationService dataMigrationService;

    /**
     * 触发数据导入到Redis的过程
     * 
     * @param batchSize 每批处理的数据量，默认10000
     * @return 导入结果信息
     */
    @PostMapping("/import-to-redis")
    public ApiResponse<String> importDataToRedis(
            @RequestParam(value = "batchSize", defaultValue = "20000") int batchSize) {
        
        logger.info("开始导入数据到Redis，批次大小: {}", batchSize);
        
        try {
            dataMigrationService.importDataToRedis(batchSize);
            return ApiResponse.success("数据导入过程已启动");
        } catch (Exception e) {
            logger.error("导入数据到Redis时发生错误", e);
            return ApiResponse.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 清空所有手机号数据
     * 
     * @return 清除结果信息
     */
    @PostMapping("/clear-mobile-data")
    public ApiResponse<String> clearMobileData() {
        logger.info("接收到清空所有手机号数据的请求");
        
        try {
            int count = dataMigrationService.clearAllMobileData();
            return ApiResponse.success(String.format("成功清除 %d 个手机号数据", count));
        } catch (Exception e) {
            logger.error("清除手机号数据时发生错误", e);
            return ApiResponse.error("清除失败: " + e.getMessage());
        }
    }

    /**
     * 根据手机号查询数据记录
     * 
     * @param mobile 手机号
     * @return 查询结果
     */
    @GetMapping("/query/{mobile}")
    public ApiResponse<Object> queryByMobile(@PathVariable String mobile) {
        try {
            Object result = dataMigrationService.queryByMobile(mobile);
            return ApiResponse.success(result, "查询成功");
        } catch (Exception e) {
            logger.error("查询数据时发生错误", e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据手机号查询名单数据（表名以data_namelist_开头的数据）
     * 
     * @param mobile 手机号
     * @return 名单数据内容
     */
    @GetMapping("/query-name-list/{mobile}")
    public ApiResponse<Object> queryNameList(@PathVariable String mobile) {
        try {
            logger.info("接收到查询名单数据的请求，手机号: {}", MaskUtil.maskMobile(mobile));
            Object result = dataMigrationService.queryNameList(mobile);
            return ApiResponse.success(result, "查询名单数据成功");
        } catch (Exception e) {
            logger.error("查询名单数据时发生错误", e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据手机号查询调研数据（表名以dataset_开头的数据）
     * 
     * @param mobile 手机号
     * @return 调研数据内容
     */
    @GetMapping("/query-dataset/{mobile}")
    public ApiResponse<Object> queryDataset(@PathVariable String mobile) {
        try {
            logger.info("接收到查询调研数据的请求，手机号: {}", MaskUtil.maskMobile(mobile));
            Object result = dataMigrationService.queryDataset(mobile);
            return ApiResponse.success(result, "查询调研数据成功");
        } catch (Exception e) {
            logger.error("查询调研数据时发生错误", e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据手机号查询名单数据并按项目分组统计
     * 
     * @param mobile 手机号
     * @return 名单数据内容及统计结果
     */
    @GetMapping("/query-name-list-stats/{mobile}")
    public ApiResponse<Object> queryNameListWithStatistics(@PathVariable String mobile) {
        try {
            logger.info("接收到查询名单数据(带统计)的请求，手机号: {}", MaskUtil.maskMobile(mobile));
            Object result = dataMigrationService.queryNameListWithStatistics(mobile);
            return ApiResponse.success(result, "查询名单数据(带统计)成功");
        } catch (Exception e) {
            logger.error("查询名单数据(带统计)时发生错误", e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据手机号查询调研数据并按项目分组统计
     * 
     * @param mobile 手机号
     * @return 调研数据内容及统计结果
     */
    @GetMapping("/query-dataset-stats/{mobile}")
    public ApiResponse<Object> queryDatasetWithStatistics(@PathVariable String mobile) {
        try {
            logger.info("接收到查询调研数据(带统计)的请求，手机号: {}", MaskUtil.maskMobile(mobile));
            Object result = dataMigrationService.queryDatasetWithStatistics(mobile);
            return ApiResponse.success(result, "查询调研数据(带统计)成功");
        } catch (Exception e) {
            logger.error("查询调研数据(带统计)时发生错误", e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取数据导入进度
     * 
     * @return 导入进度信息
     */
    @GetMapping("/import-progress")
    public ApiResponse<Object> getImportProgress() {
        try {
            Object result = dataMigrationService.getImportProgress();
            return ApiResponse.success(result, "获取导入进度成功");
        } catch (Exception e) {
            logger.error("获取导入进度时发生错误", e);
            return ApiResponse.error("获取进度失败: " + e.getMessage());
        }
    }
    
    /**
     * 关闭导入服务线程池
     * 
     * @return 关闭结果
     */
    @PostMapping("/shutdown-import")
    public ApiResponse<String> shutdownImportService() {
        logger.info("接收到关闭导入服务的请求");
        
        try {
            boolean success = dataMigrationService.shutdownImportService();
            if (success) {
                return ApiResponse.success("导入服务已成功关闭");
            } else {
                return ApiResponse.error("导入服务关闭失败，可能已经关闭或正在关闭中");
            }
        } catch (Exception e) {
            logger.error("关闭导入服务时发生错误", e);
            return ApiResponse.error("关闭失败: " + e.getMessage());
        }
    }

    /**
     * 根据手机号查询名单和数据集的综合统计信息
     * 
     * @param mobile 手机号
     * @return 综合统计信息
     */
    @GetMapping("/query-stats/{mobile}")
    public ApiResponse<MobileCombinedStatsDTO> queryCombinedStats(@PathVariable String mobile) {
        try {
            logger.info("接收到查询综合统计数据的请求，手机号: {}", MaskUtil.maskMobile(mobile));
            MobileCombinedStatsDTO result = dataMigrationService.getCombinedStatsByMobile(mobile);
            return ApiResponse.success(result, "查询综合统计数据成功");
        } catch (Exception e) {
            logger.error("查询综合统计数据时发生错误", e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询记录数量最多的手机号
     * 
     * @param queryType 查询类型：ALL(全部), NAMELIST(名单), DATASET(数据集)
     * @param threshold 记录数量阈值，默认10
     * @param limit 返回结果数量限制，默认20
     * @return 手机号及其记录数量列表
     */
    @GetMapping("/query-top-mobiles")
    public ApiResponse<Object> queryTopMobilesByRecordCount(
            @RequestParam(value = "type", defaultValue = "ALL") String queryType,
            @RequestParam(value = "threshold", defaultValue = "10") int threshold,
            @RequestParam(value = "limit", defaultValue = "20") int limit) {
        
        try {
            logger.info("接收到查询记录数量最多手机号的请求，类型: {}, 阈值: {}, 限制: {}", 
                       queryType, threshold, limit);
            
            Object result = dataMigrationService.queryTopMobilesByRecordCount(queryType, threshold, limit);
            return ApiResponse.success(result, "查询记录数量最多的手机号成功");
        } catch (Exception e) {
            logger.error("查询记录数量最多的手机号时发生错误", e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询参与项目数量达到阈值的手机号
     * 
     * @param threshold 项目数量阈值，默认2
     * @return 手机号及其参与的项目数量
     */
    @GetMapping("/query-project-count")
    public ApiResponse<MobileProjectCountResultDTO> queryMobilesByProjectCount(
            @RequestParam(value = "threshold", defaultValue = "2") int threshold) {
        
        try {
            logger.info("接收到查询参与项目数量达到阈值的手机号请求，阈值: {}", threshold);
            
            MobileProjectCountResultDTO result = dataMigrationService.queryMobilesByProjectCount(threshold);
            return ApiResponse.success(result, "查询参与项目数量达到阈值的手机号成功");
        } catch (Exception e) {
            logger.error("查询参与项目数量达到阈值的手机号时发生错误", e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }
} 