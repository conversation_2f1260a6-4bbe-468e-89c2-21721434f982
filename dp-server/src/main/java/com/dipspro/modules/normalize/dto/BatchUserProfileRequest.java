package com.dipspro.modules.normalize.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 批量用户画像查询请求DTO
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchUserProfileRequest {

    /**
     * 客户特征字符串，包含多个手机号，使用各种分隔符分隔
     * 支持的分隔符：空格、中英文逗号、中英文句号、中英文分号等
     */
    @NotBlank(message = "客户特征不能为空")
    @Size(max = 5000, message = "客户特征字符串长度不能超过5000个字符")
    private String customerFeatures;

    /**
     * 是否显示详细信息
     * 暂时代码中写死为false，后续通过角色权限控制
     */
    private Boolean showDetail = false;
}