package com.dipspro.modules.normalize.service.impl;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.stereotype.Service;
import org.yaml.snakeyaml.Yaml;

import com.dipspro.modules.chat.entity.IndexDefinition;
import com.dipspro.modules.normalize.service.IndexService;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;

/**
 * 指标服务实现类
 * 负责从配置文件加载指标定义并提供查询功能
 */
@Service
@Slf4j
public class IndexServiceImpl implements IndexService {

    // 缓存所有指标定义，初始化后不再变更，采用ConcurrentHashMap保证线程安全
    private final List<IndexDefinition> allIndexes = new ArrayList<>();
    private final Map<String, IndexDefinition> indexByPlanarCode = new ConcurrentHashMap<>();
    private final Map<String, IndexDefinition> indexByLegacyCode = new ConcurrentHashMap<>();
    private final Map<String, List<IndexDefinition>> subIndexesByMainCode = new ConcurrentHashMap<>();
    private final List<IndexDefinition> mainIndexes = new ArrayList<>();

    /**
     * 初始化指标缓存
     * 从配置文件加载指标定义并构建缓存
     */
    @PostConstruct
    @Override
    public void initIndexCache() {
        try {
            log.info("开始初始化指标缓存...");

            // 清空现有缓存
            allIndexes.clear();
            indexByPlanarCode.clear();
            indexByLegacyCode.clear();
            subIndexesByMainCode.clear();
            mainIndexes.clear();

            // 加载配置文件
            Yaml yaml = new Yaml();
            InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("index-definitions.yml");
            Map<String, Object> config = yaml.load(inputStream);
            
            if (config == null || !config.containsKey("indexes")) {
                log.warn("指标定义配置文件为空或格式不正确");
                return;
            }
            
            List<Map<String, Object>> indexesConfig = (List<Map<String, Object>>) config.get("indexes");
            
            // 解析指标定义
            for (Map<String, Object> indexConfig : indexesConfig) {
                IndexDefinition index = IndexDefinition.builder()
                        .indexGridName((String) indexConfig.get("indexGridName"))
                        .legacyIndexCode((String) indexConfig.get("legacyIndexCode"))
                        .planarIndexCode((String) indexConfig.get("planarIndexCode"))
                        .parentIndexCode((String) indexConfig.get("parentIndexCode"))
                        .isMainIndex((Boolean) indexConfig.getOrDefault("isMainIndex", false))
                        .build();
                
                // 添加到缓存
                allIndexes.add(index);
                indexByPlanarCode.put(index.getPlanarIndexCode(), index);
                indexByLegacyCode.put(index.getLegacyIndexCode(), index);
                
                // 处理主指标和子指标关系
                if (index.isMainIndex()) {
                    mainIndexes.add(index);
                } else if (index.getParentIndexCode() != null) {
                    subIndexesByMainCode.computeIfAbsent(index.getParentIndexCode(), k -> new ArrayList<>()).add(index);
                }
            }
            
            // 日志输出加载结果
            log.info("指标缓存初始化完成，共加载 {} 个指标（其中 {} 个主指标，{} 个子指标）",
                    allIndexes.size(), mainIndexes.size(), allIndexes.size() - mainIndexes.size());
            
        } catch (Exception e) {
            log.error("指标缓存初始化失败", e);
        }
    }

    @Override
    public List<IndexDefinition> getAllIndexDefinitions() {
        return Collections.unmodifiableList(allIndexes);
    }

    @Override
    public Optional<IndexDefinition> getIndexByPlanarCode(String planarIndexCode) {
        return Optional.ofNullable(indexByPlanarCode.get(planarIndexCode));
    }

    @Override
    public Optional<IndexDefinition> getIndexByLegacyCode(String legacyIndexCode) {
        return Optional.ofNullable(indexByLegacyCode.get(legacyIndexCode));
    }

    @Override
    public List<IndexDefinition> getAllMainIndexes() {
        return Collections.unmodifiableList(mainIndexes);
    }

    @Override
    public List<IndexDefinition> getSubIndexes(String mainIndexCode) {
        return Collections.unmodifiableList(
                subIndexesByMainCode.getOrDefault(mainIndexCode, Collections.emptyList())
        );
    }

    @Override
    public Map<String, List<IndexDefinition>> getMainToSubIndexMapping() {
        return Collections.unmodifiableMap(subIndexesByMainCode);
    }
} 