package com.dipspro.modules.normalize.dto;

import java.util.List;
import java.util.Map;

import com.dipspro.modules.chat.dto.MessageContent;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 批量用户画像查询响应DTO
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchUserProfileResponse {

    /**
     * 成功查询的用户画像结果
     * key: 手机号, value: 用户画像内容列表
     */
    private Map<String, List<MessageContent>> successResults;

    /**
     * 查询失败的手机号及失败原因
     * key: 手机号, value: 失败原因
     */
    private Map<String, String> failedResults;

    /**
     * 查询统计信息
     */
    private BatchQueryStats stats;

    /**
     * 批量查询统计信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BatchQueryStats {
        /**
         * 总查询数量
         */
        private int totalCount;

        /**
         * 成功数量
         */
        private int successCount;

        /**
         * 失败数量
         */
        private int failedCount;

        /**
         * 查询耗时（毫秒）
         */
        private long duration;
    }
}