package com.dipspro.modules.profile.dto;

import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 手机号统计结果DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MobileStatsResultDto {
    /**
     * 查询类型：ALL(统计名单+数据)、NAMELIST(仅统计名单)、DATASET(仅统计数据)
     */
    private String queryType;
    
    /**
     * 手机号列表，按记录数排序
     */
    private List<String> mobileList;
    
    /**
     * 每个手机号对应的记录数
     */
    private Map<String, Integer> countByMobile;
    
    /**
     * 阈值条件
     */
    private Integer threshold;
    
    /**
     * 限制返回条数
     */
    private Integer limit;
    
    /**
     * 总记录数
     */
    private long totalMobiles;
} 