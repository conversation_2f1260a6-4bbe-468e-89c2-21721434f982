package com.dipspro.modules.profile.dto;

import java.util.ArrayList;
import java.util.List;

import com.dipspro.modules.normalize.dto.NormalizeData;
import lombok.Data;

/**
 * 项目统计DTO，按customer_project分组统计数据
 */
@Data
public class ProjectStatisticsDto {
    
    /**
     * 项目名称(customer_project值)
     */
    private String projectName;
    
    /**
     * 该项目下的数据ID列表
     */
    private List<Long> dataIds = new ArrayList<>();
    
    /**
     * 该项目下的数据记录列表
     */
    private List<NormalizeData> records = new ArrayList<>();
    
    /**
     * 该项目数据记录数量
     */
    private int recordCount;
    
    /**
     * 表名列表，记录该项目涉及的所有表
     */
    private List<String> tableNames = new ArrayList<>();
    
    public ProjectStatisticsDto() {
    }
    
    public ProjectStatisticsDto(String projectName) {
        this.projectName = projectName;
    }
    
    public void addDataId(Long dataId) {
        this.dataIds.add(dataId);
    }
    
    public void addRecord(NormalizeData record) {
        this.records.add(record);
        this.recordCount = this.records.size();
        
        // 添加数据ID
        this.addDataId(record.getId());
        
        // 记录表名
        if (!this.tableNames.contains(record.getTableName())) {
            this.tableNames.add(record.getTableName());
        }
    }
    
    public void setRecords(List<NormalizeData> records) {
        this.records = records;
        this.recordCount = records.size();
    }
} 