package com.dipspro.modules.profile.dto;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.data.redis.core.StringRedisTemplate;

import com.dipspro.modules.chat.dto.MessageContent;
import com.dipspro.modules.profile.analyzer.MainUserProfileAnalyzer;
import com.dipspro.modules.profile.formatter.ProjectDetailFormatter;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *         2025/5/11 08:00
 * @apiNote 历史购买楼盘信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserProfileDto {

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 项目详情列表
     */
    private List<ProjectDetail> projects;

    /**
     * 综合统计数据
     */
    private MobileCombinedStatsDTO combinedStats;

    /**
     * 用户画像标签列表
     */
    private List<UserProfileTag> userProfileTags = new ArrayList<>();

    /**
     * 用户画像的描述
     */
    private List<MessageContent> profiles;

    /**
     * 分析用户画像标签
     * 根据历史购买项目数据生成用户标签
     */
    public void analyzeUserProfileTags(StringRedisTemplate redisTemplate) {
        if (projects == null || projects.isEmpty()) {
            return;
        }

        // 使用标签分析器分析标签
        MainUserProfileAnalyzer analyzer = new MainUserProfileAnalyzer(projects);
        analyzer.analyzeAllTags(redisTemplate);

        // 获取分析后的标签
        this.userProfileTags = analyzer.getUserProfileTags();
    }

    public void generateProjectDetail(boolean showDetail) {
        ProjectDetailFormatter formatter = new ProjectDetailFormatter(this, showDetail);
        setProfiles(formatter.formatStructured());
    }

    @Override
    public String toString() {
        return null == profiles ? "" : profiles.stream().map(Object::toString).collect(Collectors.joining("\n"));
    }
}
