package com.dipspro.modules.profile.formatter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import com.dipspro.modules.chat.dto.MessageContent;
import com.dipspro.modules.chat.dto.SuggestionItem;
import com.dipspro.modules.profile.dto.ProjectDetail;
import com.dipspro.modules.profile.dto.ProjectProfileDto;

import lombok.extern.slf4j.Slf4j;

/**
 * 楼盘画像格式化器
 *
 * <AUTHOR>
 *         2025/5/11 08:30
 * @apiNote 用于格式化楼盘画像信息为对话内容
 */
@Slf4j
public class ProjectProfileSalesInfoFormatter {

    private final ProjectProfileDto projectProfile;

    public ProjectProfileSalesInfoFormatter(ProjectProfileDto projectProfile) {
        this.projectProfile = projectProfile;
    }

    /**
     * 格式化楼盘画像为MessageContent列表
     *
     * @return 格式化后的消息内容列表
     */
    public List<MessageContent> formatProjectProfile() {
        List<MessageContent> contents = new ArrayList<>();

        // 根据查询状态生成不同的内容
        switch (projectProfile.getQueryStatus()) {
            case "exact":
                contents.addAll(formatExactMatchProfile());
                break;
            case "fuzzy":
                contents.addAll(formatFuzzyMatchProfile());
                break;
            case "multiple":
                contents.addAll(formatMultipleResultsProfile());
                break;
            case "not_found":
                contents.addAll(formatNotFoundProfile());
                break;
            default:
                contents.add(MessageContent.text("查询状态未知"));
        }

        return contents;
    }

    /**
     * 格式化精确匹配的楼盘画像
     *
     * @return 消息内容列表
     */
    private List<MessageContent> formatExactMatchProfile() {
        return new ArrayList<>(format());
    }

    /**
     * 格式化模糊匹配的楼盘画像
     *
     * @return 消息内容列表
     */
    private List<MessageContent> formatFuzzyMatchProfile() {
        List<MessageContent> contents = new ArrayList<>();

        contents.add(MessageContent.text("#### 楼盘画像（模糊匹配）：" + projectProfile.getProjectName()));
        contents.add(MessageContent.text("*注：通过模糊匹配找到的楼盘信息*"));

        // 复用精确匹配的格式化逻辑
        contents.addAll(formatExactMatchProfile().subList(1, formatExactMatchProfile().size()));

        return contents;
    }

    /**
     * 格式化多个匹配结果
     *
     * @return 消息内容列表
     */
    private List<MessageContent> formatMultipleResultsProfile() {
        List<MessageContent> contents = new ArrayList<>();

        contents.add(MessageContent.text("#### 找到多个匹配的楼盘"));
        contents.add(MessageContent.text("请从以下选项中选择您要查询的楼盘："));

        // 直接使用已经构建好的建议选项
        List<SuggestionItem> suggestionItems = projectProfile.getSuggestionItems();
        if (suggestionItems != null && !suggestionItems.isEmpty()) {
            contents.add(MessageContent.suggestion(suggestionItems.toArray(new SuggestionItem[0])));
        }

        return contents;
    }

    /**
     * 格式化未找到结果
     *
     * @return 消息内容列表
     */
    private List<MessageContent> formatNotFoundProfile() {
        List<MessageContent> contents = new ArrayList<>();

        contents.add(MessageContent.text("#### 未找到楼盘信息"));
        contents.add(MessageContent.text("抱歉，没有找到名为「" + projectProfile.getProjectName() + "」的楼盘信息。"));
        contents.add(MessageContent.text("请检查楼盘名称是否正确，或尝试使用其他关键词搜索。"));

        return contents;
    }

    /**
     * 格式化周边楼盘信息
     *
     * @return 消息内容列表
     */
    private List<MessageContent> format() {
        List<MessageContent> contents = new ArrayList<>();

        ProjectDetail projectDetail = projectProfile.getProjectDetail();
        /*
         * [北京保利中央公馆]位于[城市][行政区][地理位置]，以[building_type]为主，占地面积[area_covered]，建筑面积[
         * built_up_area]，规划户数[planed_number_of_households]户，产权[right_of_years]，售价约[
         * price]元/平。
         * 项目绿化率[greening_rate]，容积率[plot_ratio]，车位配比[parking_ratio]（[parking_lot]），物业费[
         * property_fee]（物业公司[property_company]）。
         * 户型：1室（建面[area]均价[price]万/套、建面[area]均价[price]万/套）；2室（建面[area]均价[price]万/套、建面[
         * area]均价[price]万/套）
         * 准业主满意度评价：2024年准业主[样框数量]户，满意度为[A1得分]，同城总体[A1得分]。客户年龄段主要分布在[年龄段]，较关注[开放题归纳]问题。
         */
        List<String> lines = new ArrayList<>();
        List<String> chunks = new ArrayList<>();
        chunks.add(String.format("%s位于%s%s%s", str(projectProfile.getProjectName()), str(projectDetail.getCity()),
                str(projectDetail.getDistrict()), str(projectDetail.getAddress())));
        if (StringUtils.isNotBlank(projectDetail.getLjXiaoquBuildType())) {
            chunks.add(String.format("以%s为主", projectDetail.getLjXiaoquBuildType()));
        }
        lines.add(String.join("，", chunks));

        Map<String, Object> ljLouPan = projectDetail.getLjLouPan();
        if (null != ljLouPan && !ljLouPan.isEmpty()) {
            chunks.clear();
            chunks.add(String.format("占地面积%s", ljLouPan.get("area_covered") + ""));
            chunks.add(String.format("建筑面积%s", ljLouPan.get("built_up_area") + ""));
            chunks.add(String.format("规划户数%s", ljLouPan.get("planed_number_of_households") + ""));
            chunks.add(String.format("产权%s", ljLouPan.get("right_of_years") + ""));
            chunks.add(String.format("售价约%s元/平", ljLouPan.get("price") + ""));
            lines.add(String.join("，", chunks));

            chunks.clear();
            chunks.add(String.format("项目绿化率%s", ljLouPan.get("greening_rate")));
            chunks.add(String.format("容积率%s", ljLouPan.get("plot_ratio")));
            chunks.add(String.format("车位配比%s(%s)", ljLouPan.get("parking_ratio"), ljLouPan.get("parking_lot")));
            chunks.add(String.format("物业费%s(物业公司%s)", ljLouPan.get("property_fee"), ljLouPan.get("property_company")));
            lines.add(String.join("，", chunks));
        }

        List<Map<String, Object>> ljLouPanLayout = projectDetail.getLjLouPanLayout();
        // 户型：1室（建面[area]均价[price]万/套、建面[area]均价[price]万/套）；2室（建面[area]均价[price]万/套、建面[area]均价[price]万/套）
        if (null != ljLouPanLayout && !ljLouPanLayout.isEmpty()) {
            chunks.clear();
            for (Map<String, Object> layout : ljLouPanLayout) {
                chunks.add(String.format("[%s %s，%s](%s)", layout.get("layout") + "", layout.get("area_desc") + "",
                        layout.get("price_desc") + "", layout.get("url")));
            }
            lines.add("户型：" + String.join("，", chunks));
        }

        Map<String, Object> totalSampleRawData = projectDetail.getTotalSampleRawData();
        if (null != totalSampleRawData && !totalSampleRawData.isEmpty()) {
            chunks.clear();
            Object zhunTotalSample = totalSampleRawData.get("准业主");
            if (null != zhunTotalSample) {
                chunks.add(String.format("2024年准业主%s户", zhunTotalSample));
            }

            Map<String, Map<String, Integer>> indexScoreOwnerType = projectDetail.getIndexScoreOwnerType();
            if (null != indexScoreOwnerType && !indexScoreOwnerType.isEmpty()) {
                Map<String, Integer> zhunIndexScore = indexScoreOwnerType.get("准业主");
                if (null != zhunIndexScore && !zhunIndexScore.isEmpty()) {
                    Integer ra1a5 = zhunIndexScore.get("ra1a5");
                    if (null != ra1a5) {
                        chunks.add(String.format("满意度为%d分", ra1a5));
                    }
                }
            }

            // TODO: 同城总体准业主满意度
            // chunks.add(String.format("同城总体[A1得分]分", projectDetail.getTotalSample()));

            if (!chunks.isEmpty()) {
                lines.add("准业主满意度评价：" + String.join("，", chunks));
            }
        }

        // 年龄段
        chunks.clear();
        Map<String, Integer> ageGroup = projectDetail.getAgeGroup();
        if (null != ageGroup && !ageGroup.isEmpty()) {
            // 1-60前；2-60后；3-70后；4-85前；5-85后；6-95前；7-95后；8-00后
            Map<String, String> ageDescMap = new HashMap<>();
            ageDescMap.put("c1", "60前");
            ageDescMap.put("c2", "60后");
            ageDescMap.put("c3", "70后");
            ageDescMap.put("c4", "85前");
            ageDescMap.put("c5", "85后");
            ageDescMap.put("c6", "95前");
            ageDescMap.put("c7", "95后");
            ageDescMap.put("c8", "00后");

            // 找出数值最大的两个c值
            List<Map.Entry<String, Integer>> sortedEntries = ageGroup.entrySet().stream()
                    .filter(entry -> entry.getValue() != null)
                    .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                    .limit(2)
                    .toList();

            if (!sortedEntries.isEmpty()) {
                List<String> topAgeGroups = new ArrayList<>();
                for (Map.Entry<String, Integer> entry : sortedEntries) {
                    String ageDesc = ageDescMap.get(entry.getKey());
                    if (ageDesc != null) {
                        topAgeGroups.add(ageDesc);
                    }
                }

                if (!topAgeGroups.isEmpty()) {
                    chunks.add(String.format("客户年龄段主要分布在%s", String.join("，", topAgeGroups)));
                }
                lines.add(String.join("，", chunks));
            }
        }

        // TODO: 开放题
        chunks.clear();
        // chunks.add(String.format("较关注[开放题归纳]问题", projectDetail.getTotalSample()));
        // lines.add(String.join("，", chunks));

        contents.add(MessageContent.text(String.join("。\n", lines)));

        return contents;
    }

    private String str(String text) {
        if (StringUtils.isBlank(text))
            return "";
        return text;
    }

}