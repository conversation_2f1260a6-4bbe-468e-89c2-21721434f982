package com.dipspro.modules.profile.dao.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import com.dipspro.modules.profile.dao.ProjectAnalyticsDao;
import com.dipspro.util.MapUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 楼盘分析数据访问实现类
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@Repository
public class ProjectAnalyticsDaoImpl implements ProjectAnalyticsDao {

    @Autowired
    @Qualifier("mysqlJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Override
    public Map<String, Integer> queryIndexScore(String projectName, String[] indexCodes) {
        Map<String, Integer> result = new HashMap<>();

        // 参数验证
        if (indexCodes == null || indexCodes.length == 0) {
            log.warn("指标代码数组为空，无法查询指标得分");
            return result;
        }

        if (StringUtils.isBlank(projectName)) {
            log.warn("项目名称为空，无法查询指标得分");
            return result;
        }

        try {
            // 将indexCodes数组转换为SQL IN子句格式
            String indexCodesStr = Arrays.stream(indexCodes)
                    .map(code -> "'" + code + "'")
                    .collect(Collectors.joining(", "));

            // 构建SQL查询语句
            String sqlTemplate = "select source_table_field, weighted_4_or_5_percentage from dataset_1_ana where data_period = '%s' and split_group_id = %d and source_table_field in (%s) and main_unit_name like '%%%s%%'";
            String sql = String.format(sqlTemplate, "2024-0112", 17, indexCodesStr, projectName);
            log.info("执行行业指标得分SQL: {}", sql);

            // 执行查询
            List<Map<String, Object>> queryResults = jdbcTemplate.queryForList(sql);

            // 处理查询结果
            for (Map<String, Object> row : queryResults) {
                String sourceTableField = MapUtils.safeGetString(row, "source_table_field");
                Double weighted4Or5Percentage = MapUtils.safeGetDouble(row, "weighted_4_or_5_percentage");

                if (StringUtils.isNotBlank(sourceTableField) && weighted4Or5Percentage != null) {
                    // 将得分乘以100
                    Integer score = (int) Math.round(weighted4Or5Percentage * 100);
                    result.put(sourceTableField, score);
                    log.debug("指标 {} 得分: {}", sourceTableField, score);
                }
            }

            log.info("查询到 {} 个指标得分", result.size());

        } catch (Exception e) {
            log.error("查询指标得分时发生错误，项目名称: {}, 指标代码: {}", projectName, Arrays.toString(indexCodes), e);
        }

        return result;
    }

    @Override
    public Map<String, Integer> queryIndexScoreWithOwnerType(String projectName, String ownerType,
            String[] indexCodes) {
        Map<String, Integer> result = new HashMap<>();

        // 参数验证
        if (indexCodes == null || indexCodes.length == 0) {
            log.warn("指标代码数组为空，无法查询指标得分");
            return result;
        }

        if (StringUtils.isBlank(projectName)) {
            log.warn("项目名称为空，无法查询指标得分");
            return result;
        }

        try {
            // 将indexCodes数组转换为SQL IN子句格式
            String indexCodesStr = Arrays.stream(indexCodes)
                    .map(code -> "'" + code + "'")
                    .collect(Collectors.joining(", "));

            // 构建SQL查询语句
            String sqlTemplate = "select extension_refer_values, source_table_field, weighted_4_or_5_percentage from dataset_1_ana where data_period = '%s' and split_group_id = %d and source_table_field in (%s) and main_unit_name like '%%%s%%'";
            String sql = String.format(sqlTemplate, "2024-0112", 23, indexCodesStr, projectName);
            log.info("执行行业指标得分SQL: {}", sql);

            // 执行查询
            List<Map<String, Object>> queryResults = jdbcTemplate.queryForList(sql);

            // 处理查询结果
            for (Map<String, Object> row : queryResults) {
                String extensionReferValues = MapUtils.safeGetString(row, "extension_refer_values");
                if (!StringUtils.equalsIgnoreCase(ownerType, extensionReferValues))
                    continue;

                String sourceTableField = MapUtils.safeGetString(row, "source_table_field");
                Double weighted4Or5Percentage = MapUtils.safeGetDouble(row, "weighted_4_or_5_percentage");

                if (StringUtils.isNotBlank(sourceTableField) && weighted4Or5Percentage != null) {
                    // 将得分乘以100
                    Integer score = (int) Math.round(weighted4Or5Percentage * 100);
                    result.put(sourceTableField, score);
                    log.debug("业主类型 {} 指标 {} 得分: {}", ownerType, sourceTableField, score);
                }
            }

            log.info("查询到 {} 个指标得分", result.size());

        } catch (Exception e) {
            log.error("查询指标得分时发生错误，项目名称: {}, 指标代码: {}", projectName, Arrays.toString(indexCodes), e);
        }

        return result;
    }

    @Override
    public Map<String, Map<String, Integer>> batchQueryIndexScore(List<String> projectNames, String[] indexCodes) {
        Map<String, Map<String, Integer>> result = new HashMap<>();

        // 参数验证
        if (projectNames == null || projectNames.isEmpty()) {
            log.warn("项目名称列表为空，无法批量查询指标得分");
            return result;
        }

        if (indexCodes == null || indexCodes.length == 0) {
            log.warn("指标代码数组为空，无法批量查询指标得分");
            return result;
        }

        // 过滤空白项目名称
        List<String> validProjectNames = projectNames.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        if (validProjectNames.isEmpty()) {
            log.warn("有效项目名称列表为空，无法批量查询指标得分");
            return result;
        }

        try {
            // 构建项目名称的 LIKE 条件
            String likeConditions = buildLikeConditions(validProjectNames);

            // 构建指标代码的 IN 子句
            String indexCodesInClause = Arrays.stream(indexCodes)
                    .map(code -> "'" + code + "'")
                    .collect(Collectors.joining(", "));

            // 构建SQL查询语句
            String sqlTemplate = "select main_unit_name, source_table_field, weighted_4_or_5_percentage from dataset_1_ana where data_period = '%s' and split_group_id = %d and source_table_field in (%s) and (%s)";
            String sql = String.format(sqlTemplate, "2024-0112", 17, indexCodesInClause, likeConditions);
            log.info("执行批量指标得分查询SQL，项目数量: {}, 指标数量: {}", validProjectNames.size(), indexCodes.length);

            // 执行查询
            List<Map<String, Object>> queryResults = jdbcTemplate.queryForList(sql);

            // 按项目名称分组处理结果
            Map<String, List<Map<String, Object>>> groupedResults = queryResults.stream()
                    .collect(Collectors.groupingBy(row -> MapUtils.safeGetString(row, "main_unit_name")));

            // 处理每个项目的数据
            for (String projectName : validProjectNames) {
                Map<String, Integer> projectIndexScores = new HashMap<>();

                // 匹配 key
                groupedResults.keySet().stream()
                        .filter(key -> key.contains(projectName))
                        .findFirst()
                        .ifPresent(mainUnitName -> {
                            List<Map<String, Object>> projectRows = groupedResults.get(mainUnitName);
                            if (projectRows != null) {
                                for (Map<String, Object> row : projectRows) {
                                    String sourceTableField = MapUtils.safeGetString(row, "source_table_field");
                                    Double weighted4Or5Percentage = MapUtils.safeGetDouble(row,
                                            "weighted_4_or_5_percentage");

                                    if (StringUtils.isNotBlank(sourceTableField) && weighted4Or5Percentage != null) {
                                        // 将得分乘以100
                                        Integer score = (int) Math.round(weighted4Or5Percentage * 100);
                                        projectIndexScores.put(sourceTableField, score);
                                    }
                                }
                            }

                            result.put(projectName, projectIndexScores);
                        });
            }

            log.info("批量指标得分查询完成，查询项目数量: {}, 返回结果数量: {}", validProjectNames.size(), result.size());

        } catch (Exception e) {
            log.error("批量查询指标得分时发生错误，项目数量: {}, 指标代码: {}", validProjectNames.size(), Arrays.toString(indexCodes), e);
        }

        return result;
    }

    @Override
    public Map<String, Object> queryTotalSample(String projectName) {
        // 参数验证
        if (StringUtils.isBlank(projectName)) {
            log.warn("项目名称为空，无法查询样框数量");
            return null;
        }

        Map<String, Object> result = new HashMap<>();
        List<String> ownerStages = new ArrayList<>();

        try {
            // 构建SQL查询语句
            String sqlTemplate = "select split_group_id, total_sample, extension_refer_values from dataset_1_ana where data_period = '%s' and split_group_id in (%s) and source_table_field = '-1999' and main_unit_name like '%%%s%%'";
            String sql = String.format(sqlTemplate, "2024-0112", "17, 23", projectName);
            log.info("执行样框查询SQL: {}", sql);

            // 执行查询
            List<Map<String, Object>> queryResults = jdbcTemplate.queryForList(sql);

            for (Map<String, Object> row : queryResults) {
                Integer splitGroupId = MapUtils.safeGetInteger(row, "split_group_id");
                if (splitGroupId == null)
                    continue;

                // split_group_id = 17 总体
                if (splitGroupId == 17) {
                    Integer totalSample = MapUtils.safeGetInteger(row, "total_sample");
                    if (totalSample != null) {
                        result.put("totalSample", row.get("total_sample"));
                    } else {
                        log.debug("查询结果中 total_sample 字段为空");
                    }
                }

                // split_group_id = 23 四类业主
                if (splitGroupId == 23) {
                    String ownerType = MapUtils.safeGetString(row, "extension_refer_values");
                    if (StringUtils.isNotBlank(ownerType)) {
                        // 记录原始数据
                        result.put(ownerType, MapUtils.safeGetString(row, "total_sample"));

                        // 记录业务阶段
                        if (!ownerStages.contains("销售阶段") && ownerType.contains("准业主")) {
                            ownerStages.add("销售阶段");
                        }
                        if (!ownerStages.contains("交付两年内")
                                && (ownerType.contains("磨合期") || ownerType.contains("稳定期"))) {
                            ownerStages.add("交付两年内");
                        }
                        if (!ownerStages.contains("已出保阶段") && ownerType.contains("老业主")) {
                            ownerStages.add("已出保阶段");
                        }
                    } else {
                        log.debug("查询结果中 extension_refer_values 字段为空");
                    }
                }
            }

            if (!ownerStages.isEmpty()) {
                String ownerStageStr = StringUtils.join(ownerStages, "、");
                result.put("ownerStage", ownerStageStr);
            }
            return result;
        } catch (Exception e) {
            log.error("查询样框数量时发生错误，项目名称: {}", projectName, e);
            return null;
        }
    }

    @Override
    public Map<String, Map<String, Object>> batchQueryTotalSample(List<String> projectNames) {
        Map<String, Map<String, Object>> result = new HashMap<>();

        // 参数验证
        if (projectNames == null || projectNames.isEmpty()) {
            log.warn("项目名称列表为空，无法批量查询样框数据");
            return result;
        }

        // 过滤空白项目名称
        List<String> validProjectNames = projectNames.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        if (validProjectNames.isEmpty()) {
            log.warn("有效项目名称列表为空，无法批量查询样框数据");
            return result;
        }

        try {
            // 构建项目名称的 LIKE 条件
            String likeConditions = buildLikeConditions(validProjectNames);

            // 构建SQL查询语句
            String sqlTemplate = "select main_unit_name, split_group_id, total_sample, extension_refer_values from dataset_1_ana where data_period = '%s' and split_group_id in (%s) and source_table_field = '-1999' and (%s)";
            String sql = String.format(sqlTemplate, "2024-0112", "17, 23", likeConditions);
            log.info("执行批量样框查询SQL，项目数量: {}", validProjectNames.size());

            // 执行查询
            List<Map<String, Object>> queryResults = jdbcTemplate.queryForList(sql);

            // 按项目名称分组处理结果
            Map<String, List<Map<String, Object>>> groupedResults = queryResults.stream()
                    .collect(Collectors.groupingBy(row -> MapUtils.safeGetString(row, "main_unit_name")));

            // 处理每个项目的数据
            for (String projectName : validProjectNames) {
                Map<String, Object> projectResult = new HashMap<>();
                List<String> ownerStages = new ArrayList<>();

                // 匹配 key
                groupedResults.keySet().stream()
                        .filter(key -> key.contains(projectName))
                        .findFirst()
                        .ifPresent(mainUnitName -> {
                            List<Map<String, Object>> projectRows = groupedResults.get(mainUnitName);
                            if (projectRows != null) {
                                for (Map<String, Object> row : projectRows) {
                                    Integer splitGroupId = MapUtils.safeGetInteger(row, "split_group_id");
                                    if (splitGroupId == null)
                                        continue;

                                    // split_group_id = 17 总体
                                    if (splitGroupId == 17) {
                                        Object totalSample = row.get("total_sample");
                                        if (totalSample != null) {
                                            projectResult.put("totalSample", totalSample);
                                        }
                                    }

                                    // split_group_id = 23 四类业主
                                    if (splitGroupId == 23) {
                                        String ownerType = MapUtils.safeGetString(row, "extension_refer_values");
                                        if (StringUtils.isNotBlank(ownerType)) {
                                            if (!ownerStages.contains("销售阶段") && ownerType.contains("准业主")) {
                                                ownerStages.add("销售阶段");
                                            }
                                            if (!ownerStages.contains("交付两年内")
                                                    && (ownerType.contains("磨合期") || ownerType.contains("稳定期"))) {
                                                ownerStages.add("交付两年内");
                                            }
                                            if (!ownerStages.contains("已出保阶段") && ownerType.contains("老业主")) {
                                                ownerStages.add("已出保阶段");
                                            }
                                        }
                                    }
                                }
                            }

                            if (!ownerStages.isEmpty()) {
                                String ownerStageStr = StringUtils.join(ownerStages, "、");
                                projectResult.put("ownerStage", ownerStageStr);
                            }

                            result.put(projectName, projectResult);
                        });
            }

            log.info("批量样框查询完成，查询项目数量: {}, 返回结果数量: {}", validProjectNames.size(), result.size());

        } catch (Exception e) {
            log.error("批量查询样框数据时发生错误，项目数量: {}", validProjectNames.size(), e);
        }

        return result;
    }

    @Override
    public Map<String, Integer> queryAgeGroup(String projectName, String ownerType) {
        Map<String, Integer> result = new HashMap<>();

        if (StringUtils.isBlank(projectName)) {
            log.warn("项目名称为空，无法查询指标得分");
            return result;
        }

        try {
            // 构建SQL查询语句
            String sqlTemplate = "select * from dataset_1_ana where data_period = '%s' and split_group_id = %d and source_table_field = 'ry10c' and extension_refer_values = '%s'  and main_unit_name like '%%%s%%'";
            String sql = String.format(sqlTemplate, "2024-0112", 23, ownerType, projectName);
            log.info("执行行业指标得分SQL: {}", sql);

            // 执行查询
            List<Map<String, Object>> queryResults = jdbcTemplate.queryForList(sql);

            // 处理查询结果，应该只有一条数据
            if (!queryResults.isEmpty()) {
                Map<String, Object> queryResultsFirst = queryResults.get(0);
                result.put("c1", MapUtils.safeGetInteger(queryResultsFirst, "c1"));
                result.put("c2", MapUtils.safeGetInteger(queryResultsFirst, "c2"));
                result.put("c3", MapUtils.safeGetInteger(queryResultsFirst, "c3"));
                result.put("c4", MapUtils.safeGetInteger(queryResultsFirst, "c4"));
                result.put("c5", MapUtils.safeGetInteger(queryResultsFirst, "c5"));
                result.put("c6", MapUtils.safeGetInteger(queryResultsFirst, "c6"));
                result.put("c7", MapUtils.safeGetInteger(queryResultsFirst, "c7"));
                result.put("c8", MapUtils.safeGetInteger(queryResultsFirst, "c8"));
                log.info("查询到年龄段数据：{}", result.size());
            }
        } catch (Exception e) {
            log.error("查询年龄段数据时发生错误，项目名称: {}", projectName, e);
        }

        return result;
    }

    @Override
    public Map<String, Integer> queryAna(String projectName, String dataPeriod, String attribute, String indexName) {
        Map<String, Integer> result = new HashMap<>();

        try {
            // 构建SQL查询语句
            String attributeCondition = "extension_refer_values='%s'";
            if (StringUtils.equals(attribute, "总体")) {
                attributeCondition = "extension_refer_values is null";
            } else {
                attributeCondition = String.format(attributeCondition, attribute);
            }
            String sqlTemplate = "select source_table_field, total_sample,weighted_4_or_5_percentage from dataset_1_ana where data_period = '%s' and source_table_field='%s' and %s and main_unit_name like '%%%s%%'";
            String sql = String.format(sqlTemplate, dataPeriod, indexName, attributeCondition, projectName);
            log.info("执行指标得分SQL: {}", sql);

            // 执行查询
            List<Map<String, Object>> queryResults = jdbcTemplate.queryForList(sql);

            // 处理查询结果，只有一条结果
            if (!queryResults.isEmpty()) {
                Map<String, Object> row = queryResults.get(0);
                String sourceTableField = MapUtils.safeGetString(row, "source_table_field");
                Double weighted4Or5Percentage = MapUtils.safeGetDouble(row, "weighted_4_or_5_percentage");
                Integer totalSample = MapUtils.safeGetInteger(row, "total_sample");
                // 将得分乘以100
                Integer score = (int) Math.round(weighted4Or5Percentage * 100);
                result.put(sourceTableField, score);
                log.debug("属性 {} 指标 {} 得分: {}", attribute, sourceTableField, score);
                result.put("total_sample", totalSample);
                log.debug("属性 {} 指标 {} 样本量: {}", attribute, sourceTableField, totalSample);
            }
        } catch (Exception e) {
            log.error("查询指标得分时发生错误，项目名称: {}, 指标代码: {}", projectName, indexName, e);
        }

        return result;
    }

    /**
     * 构建多个LIKE条件的SQL片段
     *
     * @param projectNames 项目名称列表
     * @return LIKE条件的SQL片段，用OR连接
     */
    private String buildLikeConditions(List<String> projectNames) {
        return projectNames.stream()
                .map(name -> "main_unit_name like '%" + name.replace("'", "''") + "%'")
                .collect(Collectors.joining(" OR "));
    }
}