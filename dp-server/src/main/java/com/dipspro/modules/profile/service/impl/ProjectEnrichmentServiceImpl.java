package com.dipspro.modules.profile.service.impl;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.dipspro.modules.normalize.dto.NormalizeData;
import com.dipspro.modules.normalize.service.DataMigrationService;
import com.dipspro.modules.profile.dto.MobileCombinedStatsDTO;
import com.dipspro.modules.profile.dto.ProjectDetail;
import com.dipspro.modules.profile.processor.IndexDataProcessor;
import com.dipspro.modules.profile.processor.PoiDataProcessor;
import com.dipspro.modules.profile.service.ProjectEnrichmentService;
import com.dipspro.util.MapUtils;
import com.dipspro.util.MaskUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 项目数据丰富化服务实现类
 * 
 * <AUTHOR>
 * @since 2025-06-03 14:51:45
 */
@Slf4j
@Service
public class ProjectEnrichmentServiceImpl implements ProjectEnrichmentService {

    private static final String SQL_QUERY_LIANJIA_MATCHING = "SELECT * FROM ds.lj_matching WHERE match_status IN ('manual_match', 'auto_match') "
            +
            "AND (customer_project = ? OR projectname LIKE ?)";

    private static final double DEFAULT_SEARCH_RADIUS_KM = 2.0; // 默认搜索半径，单位：公里

    @Autowired
    private DataMigrationService dataMigrationService;

    @Autowired
    @Qualifier("mysqlJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private IndexDataProcessor indexDataProcessor;

    @Autowired
    private PoiDataProcessor poiDataProcessor;

    @Override
    public void enrichProjectsWithAllData(List<ProjectDetail> projects, String mobile,
            MobileCombinedStatsDTO combinedStats) {
        // 为项目设置归一名单数据和归一原始数据
        enrichProjectsWithNormalizedData(projects, mobile);

        // 查询并填充链家匹配项目信息
        enrichProjectsWithLianjiaData(projects);

        // 通过项目坐标匹配周边配套信息
        poiDataProcessor.enrichProjectsWithNearbyPois(projects, DEFAULT_SEARCH_RADIUS_KM);

        // 查询原始数据，找开放题
        enrichProjectsWithOpenTextData(projects, combinedStats, mobile);

        // 查询指标数据
        indexDataProcessor.processProjectsIndexData(projects, combinedStats, mobile);
    }

    @Override
    public void enrichProjectsWithLianjiaData(List<ProjectDetail> projects) {
        log.info("开始查询链家匹配项目信息，项目数量: {}", projects.size());

        for (ProjectDetail project : projects) {
            enrichProjectWithLianjiaData(project);
        }
    }

    @Override
    public void enrichProjectsWithNormalizedData(List<ProjectDetail> projects, String mobile) {
        // 查询归一名单原始数据
        List<NormalizeData> nameList = dataMigrationService.queryNameList(mobile);
        // 查询归一数据原始数据
        List<NormalizeData> dataList = dataMigrationService.queryDataset(mobile);

        // 将名单和原始数据按customer_project进行分组
        Map<String, List<NormalizeData>> nameListMap = nameList.stream()
                .filter(item -> item.getData() != null && item.getData().containsKey("customer_project"))
                .collect(Collectors.groupingBy(
                        item -> String.valueOf(item.getData().get("customer_project")),
                        Collectors.toList()));

        Map<String, List<NormalizeData>> dataListMap = dataList.stream()
                .filter(item -> item.getData() != null && item.getData().containsKey("customer_project"))
                .collect(Collectors.groupingBy(
                        item -> String.valueOf(item.getData().get("customer_project")),
                        Collectors.toList()));

        // 为每个项目设置对应的名单数据和原始数据
        for (ProjectDetail project : projects) {
            // 将对应的名单数据加入项目
            String customerProject = project.getCustomerProject();
            if (nameListMap.containsKey(customerProject)) {
                project.setNameListData(nameListMap.get(customerProject));
            } else {
                project.setNameListData(new ArrayList<>());
            }

            // 将对应的原始数据加入项目
            if (dataListMap.containsKey(customerProject)) {
                project.setRawData(dataListMap.get(customerProject));
            } else {
                project.setRawData(new ArrayList<>());
            }
        }
    }

    /**
     * 为单个项目查询并填充链家匹配数据
     * 
     * @param project 项目详情
     */
    private void enrichProjectWithLianjiaData(ProjectDetail project) {
        try {
            // 使用占位符参数构造SQL
            String projectNamePattern = "%" + project.getProjectName() + "%";
            log.info("执行SQL查询链家匹配项目，参数: [{}, {}]", project.getCustomerProject(), projectNamePattern);

            List<Map<String, Object>> results = jdbcTemplate.queryForList(
                    SQL_QUERY_LIANJIA_MATCHING,
                    project.getCustomerProject(),
                    projectNamePattern);

            if (results.isEmpty()) {
                project.setMatchedLianjia(false);
                return;
            }

            project.setMatchedLianjia(true);

            // 处理查询结果
            Map<String, Object> matchedRecord = selectBestMatchingRecord(results);
            if (matchedRecord != null) {
                fillProjectWithMatchedRecord(project, matchedRecord);
            }
        } catch (Exception e) {
            log.error("执行SQL查询链家匹配项目时发生错误: {}", e.getMessage(), e);
            project.setMatchedLianjia(false);
        }
    }

    /**
     * 查询原始数据，找开放题
     * 
     * @param validProjects 有效项目列表
     * @param combinedStats 综合统计数据
     * @param mobile        手机号
     */
    private void enrichProjectsWithOpenTextData(List<ProjectDetail> validProjects,
            MobileCombinedStatsDTO combinedStats,
            String mobile) {
        MobileCombinedStatsDTO.NamelistStats namelistStats = combinedStats.getNamelistStats();
        MobileCombinedStatsDTO.DatasetStats datasetStats = combinedStats.getDatasetStats();
        if (null != datasetStats && datasetStats.getTotal() > 0) {
            List<NormalizeData> dataDtos = dataMigrationService.queryDataset(mobile);

            // 1. 将数据按customer_project分组
            Map<String, List<NormalizeData>> projectDataMap = dataDtos.stream()
                    .filter(dto -> dto.getData() != null && dto.getData().containsKey("customer_project"))
                    .collect(Collectors.groupingBy(
                            dto -> String.valueOf(dto.getData().get("customer_project")),
                            Collectors.toList()));

            log.debug("按customer_project分组后的数据集: {}", projectDataMap.keySet());

            // 处理每个项目的详细数据
            for (ProjectDetail project : validProjects) {
                String customerProject = project.getCustomerProject();
                if (projectDataMap.containsKey(customerProject)) {
                    List<NormalizeData> projectRecords = projectDataMap.get(customerProject);

                    // 2. 在分组结果中有多条记录取时间（data_period）最近的有值记录
                    Optional<NormalizeData> latestRecord = getLatestRecordWithDataPeriod(projectRecords);

                    if (latestRecord.isPresent()) {
                        Map<String, Object> data = latestRecord.get().getData();

                        // 3. 找ra1a5分值
                        if (data.containsKey("ra1a5")) {
                            try {
                                String ra1a5Value = String.valueOf(data.get("ra1a5"));
                                if (!ra1a5Value.equalsIgnoreCase("null") && !ra1a5Value.isEmpty()) {
                                    project.setRa1a5Score(ra1a5Value);
                                    log.debug("为项目[{}]设置ra1a5分值: {}", customerProject, ra1a5Value);
                                }
                            } catch (Exception e) {
                                log.warn("处理项目[{}]的ra1a5分值时出错: {}", customerProject, e.getMessage());
                            }
                        }

                        // 4. 找text_good, text_bad数据
                        extractAndSetTextData(project, data, "open_text_good_1");
                        extractAndSetTextData(project, data, "open_text_bad_1");
                    }
                } else {
                    log.debug("项目[{}]在归一数据中未找到记录", customerProject);
                }
            }
        } else {
            log.info("手机号: {}, 没有找到归一数据", MaskUtil.maskMobile(mobile));
        }
    }

    /**
     * 从多个匹配记录中选择最佳匹配记录
     * 
     * @param results 查询结果列表
     * @return 最佳匹配记录
     */
    private Map<String, Object> selectBestMatchingRecord(List<Map<String, Object>> results) {
        if (results.isEmpty()) {
            return null;
        }

        // 如果只有一条记录，直接返回
        if (results.size() == 1) {
            return results.get(0);
        }

        // 如果有多条记录，选择第一条（可以根据业务需求调整选择策略）
        log.debug("找到{}条匹配记录，选择第一条", results.size());
        return results.get(0);
    }

    /**
     * 将匹配的记录数据填充到项目详情中
     * 
     * @param project       项目详情
     * @param matchedRecord 匹配的记录
     */
    private void fillProjectWithMatchedRecord(ProjectDetail project, Map<String, Object> matchedRecord) {
        project.setDistrict(MapUtils.safeGetString(matchedRecord, "district"));
        project.setLjLoupanPriceAverage(MapUtils.safeGetString(matchedRecord, "lj_loupan_price_average"));
        project.setLjXiaoquBuildType(MapUtils.safeGetString(matchedRecord, "lj_xiaoqu_build_type"));
        project.setPropertyCostLevel(MapUtils.safeGetString(matchedRecord, "property_cost_level"));
        project.setLjXiaoquPropertyFeeDesc(MapUtils.safeGetString(matchedRecord, "lj_xiaoqu_property_fee_desc"));
        project.setMallAmount(MapUtils.safeGetInteger(matchedRecord, "mall_amount"));
        project.setSupermarketAmount(MapUtils.safeGetInteger(matchedRecord, "supermarket_amount"));
        project.setHospitalAmount(MapUtils.safeGetInteger(matchedRecord, "hospital_amount"));
        project.setParkAmount(MapUtils.safeGetInteger(matchedRecord, "park_amount"));
        project.setSchoolAmount(MapUtils.safeGetInteger(matchedRecord, "school_amount"));
        project.setSubwayStationAmount(MapUtils.safeGetInteger(matchedRecord, "subway_station_amount"));
        project.setBusStopAmount(MapUtils.safeGetInteger(matchedRecord, "bus_stop_amount"));

        // 设置地理位置坐标(经纬度)
        project.setLng(MapUtils.safeGetDouble(matchedRecord, "lng"));
        project.setLat(MapUtils.safeGetDouble(matchedRecord, "lat"));
    }

    /**
     * 获取最新的带有data_period的记录
     * 
     * @param records 记录列表
     * @return 最新记录
     */
    private Optional<NormalizeData> getLatestRecordWithDataPeriod(List<NormalizeData> records) {
        return records.stream()
                .filter(record -> record.getData() != null && record.getData().containsKey("data_period"))
                .max(Comparator.comparing(record -> {
                    String dataPeriod = String.valueOf(record.getData().get("data_period"));
                    // 尝试解析日期，格式可能是yyyy-MM-dd或yyyy-MM
                    try {
                        // 如果是yyyy-MM格式，转换为yyyy-MM-01
                        if (dataPeriod.matches("\\d{4}-\\d{2}")) {
                            dataPeriod += "-01";
                        }
                        return LocalDate.parse(dataPeriod);
                    } catch (Exception e) {
                        // 日期解析失败，返回最小日期作为默认值
                        log.debug("日期[{}]解析失败: {}", dataPeriod, e.getMessage());
                        return LocalDate.MIN;
                    }
                }));
    }

    /**
     * 提取并设置文本数据
     * 
     * @param project   项目详情
     * @param data      数据
     * @param textField 文本字段名
     */
    private void extractAndSetTextData(ProjectDetail project, Map<String, Object> data, String textField) {
        try {
            Object textObj = data.get(textField);
            String textContent = textObj != null ? String.valueOf(textObj) : null;

            if (textContent != null && !textContent.equalsIgnoreCase("null") && !textContent.isEmpty()) {
                if (textField.startsWith("open_text_good_")) {
                    project.setTextGood(textContent);
                    log.debug("为项目[{}]设置text_good: {}", project.getCustomerProject(), textContent);
                } else if (textField.startsWith("open_text_bad_")) {
                    project.setTextBad(textContent);
                    log.debug("为项目[{}]设置text_bad: {}", project.getCustomerProject(), textContent);
                }
            }
        } catch (Exception e) {
            log.warn("处理项目[{}]的{}数据时出错: {}", project.getCustomerProject(), textField, e.getMessage());
        }
    }
}