package com.dipspro.modules.profile.processor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import com.dipspro.modules.profile.dto.PointOfInterestDto;
import com.dipspro.modules.profile.dto.ProjectDetail;
import com.dipspro.util.MapUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * POI数据处理器
 * 
 * <AUTHOR>
 * @since 2025-06-03 14:51:45
 */
@Slf4j
@Component
public class PoiDataProcessor {

    private static final String SQL_QUERY_NEARBY_POIS = "SELECT *, " +
            "ROUND(6371000 * ACOS(COS(RADIANS(?)) * COS(RADIANS(lat)) * COS(RADIANS(lng) - RADIANS(?)) + SIN(RADIANS(?)) * SIN(RADIANS(lat))), 2) as distance "
            +
            "FROM inf_poi " +
            "HAVING distance <= ? " +
            "ORDER BY distance";

    @Autowired
    @Qualifier("mysqlJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    /**
     * 为项目列表查询并填充周边POI数据
     * 
     * @param projects 项目列表
     * @param radiusKm 搜索半径，单位：公里
     */
    public void enrichProjectsWithNearbyPois(List<ProjectDetail> projects, double radiusKm) {
        log.info("开始查询项目周边POI数据，项目数量: {}，搜索半径: {}km", projects.size(), radiusKm);

        for (ProjectDetail project : projects) {
            enrichProjectWithNearbyPois(project, radiusKm);
        }
    }

    /**
     * 为单个项目查询并填充周边POI数据
     * 
     * @param project  项目详情
     * @param radiusKm 搜索半径，单位：公里
     */
    public void enrichProjectWithNearbyPois(ProjectDetail project, double radiusKm) {
        Double lat = project.getLat();
        Double lng = project.getLng();

        // 只有当项目有经纬度信息时才进行查询
        if (lat == null || lng == null) {
            log.debug("项目[{}]没有经纬度信息，无法查询周边POI", project.getCustomerProject());
            project.setNearbyPois(new ArrayList<>());
            return;
        }

        try {
            log.info("查询项目[{}]周边{}km范围内的POI，经纬度: [{}, {}]",
                    project.getCustomerProject(), radiusKm, lat, lng);

            // 使用Haversine公式通过SQL查询计算距离并筛选
            List<Map<String, Object>> results = jdbcTemplate.queryForList(
                    SQL_QUERY_NEARBY_POIS,
                    lat, lng, lat, // Haversine公式参数
                    radiusKm * 1000 // 转换为米
            );

            if (results.isEmpty()) {
                log.info("项目[{}]周边{}km范围内未找到POI数据", project.getCustomerProject(), radiusKm);
                project.setNearbyPois(new ArrayList<>());
                return;
            }

            List<PointOfInterestDto> pois = new ArrayList<>();

            for (Map<String, Object> row : results) {
                PointOfInterestDto poi = new PointOfInterestDto();
                poi.setId(((Number) row.get("id")).longValue());
                poi.setAmapId(MapUtils.safeGetString(row, "amap_id"));
                poi.setName(MapUtils.safeGetString(row, "name"));
                poi.setType(MapUtils.safeGetString(row, "type"));
                poi.setTypeCode(MapUtils.safeGetString(row, "typecode"));
                poi.setBizType(MapUtils.safeGetString(row, "biz_type"));
                poi.setAddress(MapUtils.safeGetString(row, "address"));
                poi.setLocation(MapUtils.safeGetString(row, "location"));
                poi.setLng(MapUtils.safeGetDouble(row, "lng"));
                poi.setLat(MapUtils.safeGetDouble(row, "lat"));
                poi.setDistance(MapUtils.safeGetDouble(row, "distance"));
                poi.setTel(MapUtils.safeGetString(row, "tel"));
                poi.setCityName(MapUtils.safeGetString(row, "cityname"));
                poi.setAdName(MapUtils.safeGetString(row, "adname"));
                poi.setTag(MapUtils.safeGetString(row, "tag"));

                pois.add(poi);
            }

            project.setNearbyPois(pois);
            log.info("项目[{}]周边{}km范围内找到{}个POI",
                    project.getCustomerProject(), radiusKm, pois.size());

        } catch (Exception e) {
            log.error("查询项目[{}]周边POI数据时发生错误: {}", project.getCustomerProject(), e.getMessage(), e);
            project.setNearbyPois(new ArrayList<>());
        }
    }
}