package com.dipspro.modules.profile.service.impl;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.dipspro.modules.normalize.service.DataMigrationService;
import com.dipspro.modules.profile.dto.MobileCombinedStatsDTO;
import com.dipspro.modules.profile.dto.ProjectDetail;
import com.dipspro.modules.profile.dto.ProjectsQueryResult;
import com.dipspro.modules.profile.service.ProjectDataQueryService;
import com.dipspro.util.MapUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 项目数据查询服务实现类
 * 
 * <AUTHOR>
 * @since 2025-06-03 14:51:45
 */
@Slf4j
@Service
public class ProjectDataQueryServiceImpl implements ProjectDataQueryService {

    // 楼盘查询相关SQL
    private static final String SQL_QUERY_PROJECT_BY_EXACT_NAME = "SELECT * FROM ds.lj_matching WHERE match_status IN ('manual_match', 'auto_match') "
            +
            "AND projectname = ?";

    private static final String SQL_QUERY_PROJECT_BY_FUZZY_NAME = "SELECT * FROM ds.lj_matching WHERE match_status IN ('manual_match', 'auto_match') "
            +
            "AND projectname LIKE ?";

    private static final String SQL_QUERY_NEARBY_PROJECTS = "SELECT *, " +
            "ROUND(6371000 * ACOS(COS(RADIANS(?)) * COS(RADIANS(lat)) * COS(RADIANS(lng) - RADIANS(?)) + SIN(RADIANS(?)) * SIN(RADIANS(lat))), 2) as distance "
            +
            "FROM ds.lj_matching " +
            "WHERE match_status IN ('manual_match', 'auto_match') " +
            "HAVING distance <= ? " +
            "ORDER BY distance";

    @Autowired
    private DataMigrationService dataMigrationService;

    @Autowired
    @Qualifier("mysqlJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Override
    public List<Map<String, Object>> queryProjectByExactName(String name) {
        try {
            log.info("执行楼盘精确查询，楼盘名称: {}", name);

            List<Map<String, Object>> results = jdbcTemplate.queryForList(
                    SQL_QUERY_PROJECT_BY_EXACT_NAME, name);

            log.info("楼盘精确查询完成，楼盘名称: {}, 原始结果数量: {}", name, results.size());

            // 对结果进行去重
            List<Map<String, Object>> deduplicatedResults = deduplicateProjectResults(results);
            log.info("楼盘精确查询去重完成，楼盘名称: {}, 最终结果数量: {}", name, deduplicatedResults.size());

            return deduplicatedResults;

        } catch (Exception e) {
            log.error("执行楼盘精确查询时发生错误，楼盘名称: {}", name, e);
            throw new RuntimeException("楼盘精确查询失败", e);
        }
    }

    @Override
    public List<Map<String, Object>> queryProjectByFuzzyName(String name) {
        try {
            String fuzzyName = "%" + name + "%";
            log.info("执行楼盘模糊查询，楼盘名称: {}, 模糊匹配: {}", name, fuzzyName);

            List<Map<String, Object>> results = jdbcTemplate.queryForList(
                    SQL_QUERY_PROJECT_BY_FUZZY_NAME, fuzzyName);
            log.info("楼盘模糊查询完成，楼盘名称: {}, 原始结果数量: {}", name, results.size());

            // 对结果进行去重
            List<Map<String, Object>> deduplicatedResults = deduplicateProjectResults(results);
            log.info("楼盘模糊查询去重完成，楼盘名称: {}, 最终结果数量: {}", name, deduplicatedResults.size());

            return deduplicatedResults;

        } catch (Exception e) {
            log.error("执行楼盘模糊查询时发生错误，楼盘名称: {}", name, e);
            throw new RuntimeException("楼盘模糊查询失败", e);
        }
    }

    @Override
    public List<Map<String, Object>> queryNearbyProjects(Double lat, Double lng, double radiusKm) {
        try {
            log.info("执行周边楼盘查询，经纬度: [{}, {}], 搜索半径: {}km", lat, lng, radiusKm);

            List<Map<String, Object>> results = jdbcTemplate.queryForList(
                    SQL_QUERY_NEARBY_PROJECTS,
                    lat, lng, lat, // Haversine公式参数
                    radiusKm * 1000 // 转换为米
            );

            log.info("周边楼盘查询完成，经纬度: [{}, {}], 结果数量: {}", lat, lng, results.size());
            return results;

        } catch (Exception e) {
            log.error("执行周边楼盘查询时发生错误，经纬度: [{}, {}]", lat, lng, e);
            throw new RuntimeException("周边楼盘查询失败", e);
        }
    }

    @Override
    public ProjectsQueryResult getValidProjectsFromMobile(String mobile) {
        // 查询归一名单、数据统计信息
        MobileCombinedStatsDTO combinedStatsByMobile = dataMigrationService.getCombinedStatsByMobile(mobile);
        List<MobileCombinedStatsDTO.ProjectDetail> projects = combinedStatsByMobile.getSummary()
                .getCustomerProjectStats().getProjects();

        List<ProjectDetail> validProjects = projects.stream()
                .filter(this::isValidProject)
                .map(this::convertToProjectDetail)
                .collect(Collectors.toList());

        return new ProjectsQueryResult(validProjects, combinedStatsByMobile);
    }

    /**
     * 对项目查询结果进行去重
     * 使用 customer_project 和 district 的组合作为唯一键进行去重
     * 
     * @param results 原始查询结果
     * @return 去重后的结果列表
     */
    private List<Map<String, Object>> deduplicateProjectResults(List<Map<String, Object>> results) {
        if (results == null || results.isEmpty()) {
            return results;
        }

        log.debug("开始对项目结果进行去重，原始结果数量: {}", results.size());

        // 使用 customer_project 和 district 的组合进行去重
        Map<String, Map<String, Object>> uniqueResults = new LinkedHashMap<>();

        for (Map<String, Object> result : results) {
            String customerProject = MapUtils.safeGetString(result, "customer_project");
            String district = MapUtils.safeGetString(result, "district");

            // 创建唯一键：customer_project + district
            String uniqueKey = customerProject + "|" + district;

            // 如果该组合还未存在，则添加到去重结果中
            if (!uniqueResults.containsKey(uniqueKey)) {
                uniqueResults.put(uniqueKey, result);
            }
        }

        List<Map<String, Object>> deduplicatedResults = new ArrayList<>(uniqueResults.values());
        log.debug("项目结果去重完成，去重后结果数量: {}", deduplicatedResults.size());

        return deduplicatedResults;
    }

    /**
     * 判断项目是否有效
     * 
     * @param project 项目详情
     * @return 是否有效
     */
    private boolean isValidProject(MobileCombinedStatsDTO.ProjectDetail project) {
        return project.getCustomerProject() != null
                && !project.getCustomerProject().isEmpty()
                && !project.getCustomerProject().equalsIgnoreCase("null");
    }

    /**
     * 将MobileCombinedStatsDTO.ProjectDetail转换为ProjectDetail
     * 
     * @param project 原始项目详情
     * @return 转换后的项目详情
     */
    private ProjectDetail convertToProjectDetail(MobileCombinedStatsDTO.ProjectDetail project) {
        String customerProject = project.getCustomerProject();

        String customerName = "", projectName = "";
        if (customerProject.contains("_")) {
            String[] parts = customerProject.split("_", 2);
            customerName = parts[0];
            projectName = parts[1];
        } else {
            projectName = customerProject;
        }

        ProjectDetail detail = new ProjectDetail();
        detail.setCustomerProject(customerProject);
        detail.setCustomerName(customerName);
        detail.setProjectName(projectName);
        detail.setProvince(project.getProvince());
        detail.setCity(project.getCity());
        detail.setSignDatetime(project.getSignDatetime());
        detail.setBuildingGrade(project.getBuildingGrade());
        detail.setHouseType(project.getHouseType());
        detail.setDecorationType(project.getDecorationType());
        detail.setHouseNo(project.getHouseNo());
        return detail;
    }
}