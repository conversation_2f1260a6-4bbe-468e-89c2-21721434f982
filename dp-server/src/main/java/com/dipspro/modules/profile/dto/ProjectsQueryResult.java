package com.dipspro.modules.profile.dto;

import java.util.List;

/**
 * 项目查询结果封装类，同时包含处理后的项目列表和原始统计数据
 * 
 * <AUTHOR>
 * @since 2025-06-03 14:51:45
 */
public class ProjectsQueryResult {

    private final List<ProjectDetail> projects;
    private final MobileCombinedStatsDTO combinedStats;

    public ProjectsQueryResult(List<ProjectDetail> projects, MobileCombinedStatsDTO combinedStats) {
        this.projects = projects;
        this.combinedStats = combinedStats;
    }

    public List<ProjectDetail> getProjects() {
        return projects;
    }

    public MobileCombinedStatsDTO getCombinedStats() {
        return combinedStats;
    }
}