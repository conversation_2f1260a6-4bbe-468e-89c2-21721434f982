package com.dipspro.modules.profile.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dipspro.modules.chat.dto.MessageContent;
import com.dipspro.modules.chat.dto.RelatedTemplateDto;
import com.dipspro.modules.chat.dto.SlotDefinition;
import com.dipspro.modules.chat.dto.SuggestionItem;
import com.dipspro.modules.chat.dto.SuggestionLinkParams;
import com.dipspro.modules.chat.service.PromptTemplateService;
import com.dipspro.modules.normalize.dto.ProjectProfileRequest;
import com.dipspro.modules.profile.dao.ProjectAnalyticsDao;
import com.dipspro.modules.profile.dto.ProjectDetail;
import com.dipspro.modules.profile.dto.ProjectProfileDto;
import com.dipspro.modules.profile.dto.PromptTemplateDto;
import com.dipspro.modules.profile.processor.ProjectProfileProcessor;
import com.dipspro.modules.profile.service.ProjectDataQueryService;
import com.dipspro.modules.profile.service.ProjectProfileService;
import com.dipspro.util.MapUtils;
import com.dipspro.util.ProjectNameParseUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 重构后的楼盘画像服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@Service
public class ProjectProfileServiceImpl implements ProjectProfileService {

    private static final double PROJECT_SEARCH_RADIUS_KM = 1.0; // 楼盘搜索半径，单位：公里

    @Autowired
    private ProjectProfileProcessor projectProfileProcessor;

    @Autowired
    private ProjectDataQueryService projectDataQueryService;

    @Autowired
    private ProjectAnalyticsDao projectAnalyticsDao;

    @Autowired
    private PromptTemplateService promptTemplateService;

    @Override
    public ProjectProfileDto queryProjectProfile(ProjectProfileRequest request) {
        ProjectProfileDto projectProfile = createProjectProfileFromRequest(request);

        String projectName = projectProfile.getProjectName();
        try {
            log.info("开始查询楼盘画像，楼盘名称: {}", projectName);

            // 处理楼盘查询逻辑
            boolean foundUniqueProject = projectProfileProcessor.processProjectQuery(projectProfile, request);

            if (foundUniqueProject) {
                // 找到唯一匹配的楼盘，丰富数据
                projectProfileProcessor.enrichProjectProfileWithData(projectProfile);
            } else {
                // 多个结果或未找到，构建选择选项
                if ("multiple".equals(projectProfile.getQueryStatus())) {
                    List<Map<String, Object>> results = getQueryResults(projectProfile, request);
                    buildSelectionOptions(projectProfile, results, request);
                }
            }

            // 生成楼盘画像内容
            projectProfile.generateProjectProfile();

            // 添加关联模板的建议选项
            addRelatedTemplateSuggestions(projectProfile, request);

            log.info("楼盘画像查询完成，楼盘名称: {}, 查询状态: {}", projectName, projectProfile.getQueryStatus());
            return projectProfile;

        } catch (Exception e) {
            log.error("查询楼盘画像时发生错误，楼盘名称: {}", projectName, e);
            projectProfile.setQueryStatus("error");
            projectProfile.generateProjectProfile();
            return projectProfile;
        }
    }

    @Override
    public ProjectProfileDto projectProfileSalesInfo(ProjectProfileRequest request) {
        ProjectProfileDto projectProfile = createProjectProfileFromRequest(request);

        String projectName = projectProfile.getProjectName();
        try {
            log.info("开始查询楼盘销售信息，楼盘名称: {}", projectName);

            // 处理楼盘查询逻辑
            boolean foundUniqueProject = projectProfileProcessor.processProjectQuery(projectProfile, request);

            if (foundUniqueProject) {
                // 找到唯一匹配的楼盘，丰富销售信息数据
                projectProfileProcessor.enrichSalesInfo(projectProfile);
            } else {
                // 多个结果或未找到，构建选择选项
                if ("multiple".equals(projectProfile.getQueryStatus())) {
                    List<Map<String, Object>> results = getQueryResults(projectProfile, request);
                    buildSelectionOptions(projectProfile, results, request);
                }
            }

            // 生成楼盘画像内容
            projectProfile.generateProjectProfileSalesInfo();

            // 添加关联模板的建议选项
            addRelatedTemplateSuggestions(projectProfile, request);

            log.info("楼盘销售信息查询完成，楼盘名称: {}, 查询状态: {}", projectName, projectProfile.getQueryStatus());
            return projectProfile;

        } catch (Exception e) {
            log.error("查询楼盘销售信息时发生错误，楼盘名称: {}", projectName, e);
            projectProfile.setQueryStatus("error");
            projectProfile.generateProjectProfile();
            return projectProfile;
        }
    }

    @Override
    public ProjectProfileDto projectProfileSurrounding(ProjectProfileRequest request) {
        ProjectProfileDto projectProfile = createProjectProfileFromRequest(request);

        String projectName = projectProfile.getProjectName();
        try {
            log.info("开始查询楼盘周边信息，楼盘名称: {}", projectName);

            // 处理楼盘查询逻辑
            boolean foundUniqueProject = projectProfileProcessor.processProjectQuery(projectProfile, request);

            if (foundUniqueProject) {
                // 查询周边楼盘数据
                enrichProjectProfileWithNearbyProjects(projectProfile);
            } else {
                // 多个结果或未找到，构建选择选项
                if ("multiple".equals(projectProfile.getQueryStatus())) {
                    List<Map<String, Object>> results = getQueryResults(projectProfile, request);
                    buildSelectionOptions(projectProfile, results, request);
                }
            }

            // 生成楼盘画像内容
            projectProfile.generateProjectProfileSurrounding();

            // 添加关联模板的建议选项
            addRelatedTemplateSuggestions(projectProfile, request);

            log.info("楼盘周边信息查询完成，楼盘名称: {}, 查询状态: {}", projectName, projectProfile.getQueryStatus());
            return projectProfile;

        } catch (Exception e) {
            log.error("查询楼盘周边信息时发生错误，楼盘名称: {}", projectName, e);
            projectProfile.setQueryStatus("error");
            projectProfile.generateProjectProfile();
            return projectProfile;
        }
    }

    @Override
    public ProjectProfileDto projectProfileAna(ProjectProfileRequest request) {
        ProjectProfileDto projectProfile = createProjectProfileFromRequest(request);

        String projectName = projectProfile.getProjectName();
        try {
            log.info("开始查询楼盘分析数据，楼盘名称: {}", projectName);

            // 处理楼盘查询逻辑
            boolean foundUniqueProject = projectProfileProcessor.processProjectQuery(projectProfile, request);

            if (foundUniqueProject) {
                // 查询分析数据
                projectProfileProcessor.enrichAnalysisData(projectProfile);

                // 查询原始 slot 定义，用来做转换
                if (request.getTemplateId() != null) {
                    promptTemplateService.getTemplateById(UUID.fromString(request.getTemplateId()))
                            .ifPresent(promptTemplateDto -> {
                                List<SlotDefinition> slotDefinitions = promptTemplateDto.getSlotDefinitions();
                                projectProfile.setSlotDefinitions(slotDefinitions);
                            });
                }
            } else {
                // 多个结果或未找到，构建选择选项
                if ("multiple".equals(projectProfile.getQueryStatus())) {
                    List<Map<String, Object>> results = getQueryResults(projectProfile, request);
                    buildSelectionOptions(projectProfile, results, request);
                }
            }

            // 生成楼盘画像内容
            projectProfile.generateProjectProfileAna();

            // 添加关联模板的建议选项
            addRelatedTemplateSuggestions(projectProfile, request);

            log.info("楼盘分析数据查询完成，楼盘名称: {}, 查询状态: {}", projectName, projectProfile.getQueryStatus());
            return projectProfile;

        } catch (Exception e) {
            log.error("查询楼盘分析数据时发生错误，楼盘名称: {}", projectName, e);
            projectProfile.setQueryStatus("error");
            projectProfile.generateProjectProfile();
            return projectProfile;
        }
    }

    /**
     * 从请求创建楼盘画像DTO
     */
    private ProjectProfileDto createProjectProfileFromRequest(ProjectProfileRequest request) {
        String name = request.getName();

        // 解析楼盘名称，提取项目名称和开发商名称
        ProjectNameParseUtil.ProjectNameParseResult parseResult = ProjectNameParseUtil.parseProjectName(name);

        ProjectProfileDto projectProfile = new ProjectProfileDto();
        projectProfile.setProjectName(parseResult.getProjectName());
        projectProfile.setCustomerName(parseResult.getCustomerName());
        projectProfile.setSlots(request.getSlots());
        projectProfile.setPromptTemplateId(request.getTemplateId());
        return projectProfile;
    }

    /**
     * 获取查询结果
     */
    private List<Map<String, Object>> getQueryResults(ProjectProfileDto projectProfile, ProjectProfileRequest request) {
        String projectName = projectProfile.getProjectName();

        // 先尝试精确查询
        List<Map<String, Object>> exactResults = projectDataQueryService.queryProjectByExactName(projectName);
        if (!exactResults.isEmpty()) {
            return exactResults;
        }

        // 再尝试模糊查询
        return projectDataQueryService.queryProjectByFuzzyName(projectName);
    }

    /**
     * 构建选择选项列表
     */
    private void buildSelectionOptions(ProjectProfileDto projectProfile, List<Map<String, Object>> results,
            ProjectProfileRequest request) {
        log.info("构建楼盘选择选项，结果数量: {}", results.size());

        // 查询原始的 PromptTemplate
        if (request.getTemplateId() != null) {
            promptTemplateService.getTemplateById(UUID.fromString(request.getTemplateId()))
                    .ifPresent(request::setPromptTemplateDto);
        }

        List<SuggestionItem> suggestionItems = new ArrayList<>();
        Map<String, Object> originalSlots = request.getSlots();

        for (Map<String, Object> result : results) {
            String projectName = MapUtils.safeGetString(result, "projectname");
            String city = MapUtils.safeGetString(result, "city");
            String district = MapUtils.safeGetString(result, "district");
            String developer = MapUtils.safeGetString(result, "customer_name");

            // 构建显示名称
            StringBuilder displayName = new StringBuilder();

            if (developer != null && !developer.isEmpty()) {
                displayName.append("[").append(developer).append("]");
            }
            displayName.append(projectName);
            if (city != null && !city.isEmpty()) {
                displayName.append(" (").append(city);
                if (district != null && !district.isEmpty()) {
                    displayName.append(" ").append(district);
                }
                displayName.append(")");
            }

            // 构建新的slots，将楼盘名称填充到name字段
            Map<String, Object> newSlots = new HashMap<>();
            if (originalSlots != null) {
                newSlots.putAll(originalSlots);
            }
            newSlots.keySet().forEach(key -> {
                if (key.equals("name") || key.equalsIgnoreCase("project_name")) {
                    newSlots.put(key, projectName);
                }
            });

            // 创建链接参数
            SuggestionLinkParams linkParams = SuggestionLinkParams.create(
                    "楼盘画像：" + projectName,
                    request.getTemplateId(),
                    newSlots,
                    request.getAgentType(),
                    request.getSessionId(),
                    request.getUserId(),
                    request.getUsername());

            // 创建建议选项
            SuggestionItem suggestionItem = SuggestionItem.withLink(
                    displayName.toString(),
                    projectName,
                    linkParams);
            suggestionItems.add(suggestionItem);
        }

        projectProfile.setSuggestionItems(suggestionItems);
    }

    /**
     * 丰富楼盘画像的周边楼盘信息
     */
    private void enrichProjectProfileWithNearbyProjects(ProjectProfileDto projectProfile) {
        ProjectDetail detail = projectProfile.getProjectDetail();
        if (detail == null || detail.getLat() == null || detail.getLng() == null) {
            log.warn("项目经纬度信息为空，无法查询周边楼盘");
            return;
        }

        // 找距离参数
        double distance = PROJECT_SEARCH_RADIUS_KM;
        Map<String, Object> slots = projectProfile.getSlots();
        if (null != slots && !slots.isEmpty()) {
            for (String key : slots.keySet()) {
                if (StringUtils.equalsIgnoreCase(key, "distance")) {
                    Object distanceObj = slots.get("distance");
                    if (null != distanceObj) {
                        distance = Double.parseDouble(distanceObj.toString());
                    }
                }
            }
        }
        projectProfile.setDistance(distance);

        List<Map<String, Object>> nearbyResults = projectDataQueryService.queryNearbyProjects(
                detail.getLat(), detail.getLng(), distance);

        List<ProjectDetail> nearbyProjects = new ArrayList<>();

        // 收集所有周边项目名称，用于批量查询
        List<String> nearbyProjectNames = new ArrayList<>();
        List<ProjectProfileDto> nearbyProfiles = new ArrayList<>();

        for (Map<String, Object> result : nearbyResults) {
            ProjectDetail nearbyProject = new ProjectDetail();
            projectProfileProcessor.fillProjectWithMatchedRecord(nearbyProject, result);

            ProjectProfileDto nearbyProfile = new ProjectProfileDto();
            nearbyProfile.setProjectName(nearbyProject.getProjectName());
            nearbyProfile.setProjectDetail(nearbyProject);

            nearbyProfiles.add(nearbyProfile);
            nearbyProjectNames.add(nearbyProject.getProjectName());
        }

        // 批量查询样框数据
        Map<String, Map<String, Object>> batchTotalSampleResults = projectAnalyticsDao
                .batchQueryTotalSample(nearbyProjectNames);

        // 批量查询指标得分数据
        Map<String, Map<String, Integer>> batchIndexScoreResults = projectAnalyticsDao.batchQueryIndexScore(
                nearbyProjectNames,
                new String[] { "ra1a5", "rt0a5" });

        // 将批量查询结果应用到每个项目
        for (ProjectProfileDto nearbyProfile : nearbyProfiles) {
            String projectName = nearbyProfile.getProjectName();

            // 设置样框数据
            Map<String, Object> totalSampleResult = batchTotalSampleResults.get(projectName);
            enrichTotalSampleFromBatchResult(nearbyProfile, totalSampleResult);

            // 设置指标得分数据
            Map<String, Integer> indexScoreResult = batchIndexScoreResults.get(projectName);
            enrichIndexScoreFromBatchResult(nearbyProfile, indexScoreResult);

            nearbyProjects.add(nearbyProfile.getProjectDetail());
        }

        projectProfile.setNearbyProjects(nearbyProjects);
        log.info("找到周边楼盘数量: {}", nearbyProjects.size());
    }

    /**
     * 从批量查询结果中提取样框数据并设置到项目画像中
     */
    private void enrichTotalSampleFromBatchResult(ProjectProfileDto projectProfile, Map<String, Object> batchResult) {
        if (batchResult == null) {
            return;
        }

        Object totalSampleObj = batchResult.get("totalSample");
        Object ownerStage = batchResult.get("ownerStage");

        if (totalSampleObj != null) {
            Long totalSample = (Long) totalSampleObj;
            projectProfile.getProjectDetail().setTotalSample(totalSample.intValue());
        }
        if (ownerStage != null) {
            projectProfile.getProjectDetail().setOwnerStage(ownerStage.toString());
        }
    }

    /**
     * 从批量查询结果中提取指标得分并设置到项目画像中
     */
    private void enrichIndexScoreFromBatchResult(ProjectProfileDto projectProfile, Map<String, Integer> indexScores) {
        if (indexScores != null) {
            projectProfile.getProjectDetail().setIndexScore(indexScores);
        }
    }

    /**
     * 添加关联模板的建议选项
     */
    private void addRelatedTemplateSuggestions(ProjectProfileDto projectProfile, ProjectProfileRequest request) {
        // 卫函数：检查是否已有建议选项存在
        if (projectProfile.getSuggestionItems() != null && !projectProfile.getSuggestionItems().isEmpty()) {
            log.debug("已存在建议选项，跳过关联模板处理");
            return;
        }

        // 卫函数：检查模板ID是否存在
        if (request.getTemplateId() == null) {
            log.debug("请求中没有模板ID，跳过关联模板处理");
            return;
        }

        // 卫函数：检查 profiles 中是否已有 suggestion 类型的 MessageContent
        if (projectProfile.getProfiles() != null) {
            boolean hasSuggestion = projectProfile.getProfiles().stream()
                    .anyMatch(content -> "suggestion".equals(content.getType()));
            if (hasSuggestion) {
                log.debug("消息列表中已存在建议选项，跳过关联模板处理");
                return;
            }
        }

        try {
            // 查询关联的模板关系
            UUID templateId = UUID.fromString(request.getTemplateId());
            List<RelatedTemplateDto> relatedTemplateRelations = promptTemplateService.getRelatedTemplates(templateId);

            if (relatedTemplateRelations == null || relatedTemplateRelations.isEmpty()) {
                log.debug("模板 {} 没有关联的模板", templateId);
                return;
            }

            log.info("找到 {} 个关联模板关系", relatedTemplateRelations.size());

            // 提取所有关联模板的ID
            List<UUID> relatedTemplateIds = relatedTemplateRelations.stream()
                    .map(RelatedTemplateDto::getId)
                    .collect(Collectors.toList());

            // 批量查询所有关联模板的完整信息
            List<PromptTemplateDto> relatedTemplates = promptTemplateService.getTemplatesByIds(relatedTemplateIds);

            // 创建ID到模板的映射，便于后续查找
            Map<UUID, PromptTemplateDto> templateMap = relatedTemplates.stream()
                    .collect(Collectors.toMap(PromptTemplateDto::getId, template -> template));

            // 根据关联模板生成 suggestion
            List<SuggestionItem> suggestionItems = new ArrayList<>();
            Map<String, Object> originalSlots = request.getSlots();

            for (RelatedTemplateDto relatedTemplateRelation : relatedTemplateRelations) {
                UUID relatedTemplateId = relatedTemplateRelation.getId();
                PromptTemplateDto relatedTemplate = templateMap.get(relatedTemplateId);

                if (relatedTemplate == null) {
                    log.warn("关联模板 {} 不存在，跳过", relatedTemplateId);
                    continue;
                }

                // 构建新的slots，保持原有的slots内容
                Map<String, Object> newSlots = new HashMap<>();
                if (originalSlots != null) {
                    newSlots.putAll(originalSlots);
                }

                // 创建链接参数
                SuggestionLinkParams linkParams = SuggestionLinkParams.create(
                        relatedTemplate.getName(),
                        relatedTemplate.getId().toString(),
                        newSlots,
                        relatedTemplate.getAgentType(),
                        request.getSessionId(),
                        request.getUserId(),
                        request.getUsername());

                // 创建建议选项
                String displayName = relatedTemplate.getName();
                if (StringUtils.isNotBlank(relatedTemplate.getDescription())) {
                    displayName = relatedTemplate.getDescription();
                }

                SuggestionItem suggestionItem = SuggestionItem.withLink(
                        displayName,
                        relatedTemplate.getName(),
                        linkParams);
                suggestionItems.add(suggestionItem);
            }

            // 将 suggestion 添加到返回的消息列表中
            if (!suggestionItems.isEmpty()) {
                // 确保 profiles 列表已初始化
                if (projectProfile.getProfiles() == null) {
                    projectProfile.setProfiles(new ArrayList<>());
                }

                // 添加说明文本
                projectProfile.getProfiles().add(MessageContent.text("#### 相关功能"));

                // 添加建议选项
                MessageContent suggestionContent = MessageContent.suggestion(
                        suggestionItems.toArray(new SuggestionItem[0]));
                projectProfile.getProfiles().add(suggestionContent);

                log.info("成功添加 {} 个关联模板建议选项到楼盘画像", suggestionItems.size());
            }

        } catch (Exception e) {
            log.error("处理关联模板建议选项时发生错误，模板ID: {}", request.getTemplateId(), e);
        }
    }
}