package com.dipspro.modules.profile.dto;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.dipspro.modules.chat.dto.MessageContent;
import com.dipspro.modules.chat.dto.SlotDefinition;
import com.dipspro.modules.chat.dto.SuggestionItem;
import com.dipspro.modules.profile.formatter.ProjectProfileAnaFormatter;
import com.dipspro.modules.profile.formatter.ProjectProfileFormatter;

import com.dipspro.modules.profile.formatter.ProjectProfileSalesInfoFormatter;
import com.dipspro.modules.profile.formatter.ProjectProfileSurroundingFormatter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 楼盘画像DTO
 * 
 * <AUTHOR>
 *         2025/5/11 08:00
 * @apiNote 楼盘画像信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectProfileDto {

    /**
     * 楼盘名称
     */
    private String projectName;

    /**
     * 开发商名称
     */
    private String customerName;

    /**
     * 项目详情信息
     */
    private ProjectDetail projectDetail;

    /**
     * 周边楼盘列表（1公里范围内）
     */
    private List<ProjectDetail> nearbyProjects = new ArrayList<>();
    // 周边搜索距离
    private double distance;

    /**
     * 城市政策信息（预留）
     */
    private String cityPolicy;

    /**
     * 地产中介网站链接（预留）
     */
    private String agentWebsiteUrl;

    /**
     * 客户评价信息
     */
    private String customerReviews;

    /**
     * 楼盘画像的描述内容
     */
    private List<MessageContent> profiles;

    /**
     * 查询状态：exact（精确匹配）、fuzzy（模糊匹配）、multiple（多个结果）、not_found（未找到）
     */
    private String queryStatus;

    /**
     * 多个匹配结果的建议选项（当queryStatus为multiple时使用）
     */
    private List<SuggestionItem> suggestionItems = new ArrayList<>();

    /**
     * 插槽参数
     */
    private Map<String, Object> slots;

    // 使用提示词模板 ID
    private String promptTemplateId;
    // 提示词模板中的插槽定义
    private List<SlotDefinition> slotDefinitions;

    /**
     * 生成楼盘画像的详细信息
     */
    public void generateProjectProfile() {
        ProjectProfileFormatter formatter = new ProjectProfileFormatter(this);
        setProfiles(formatter.formatProjectProfile());
    }

    public void generateProjectProfileSurrounding() {
        ProjectProfileSurroundingFormatter formatter = new ProjectProfileSurroundingFormatter(this);
        setProfiles(formatter.formatProjectProfile());
    }

    public void generateProjectProfileSalesInfo() {
        ProjectProfileSalesInfoFormatter formatter = new ProjectProfileSalesInfoFormatter(this);
        setProfiles(formatter.formatProjectProfile());
    }

    public void generateProjectProfileAna() {
        ProjectProfileAnaFormatter formatter = new ProjectProfileAnaFormatter(this);
        setProfiles(formatter.formatProjectProfile());
    }
}