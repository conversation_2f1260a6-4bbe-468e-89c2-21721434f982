package com.dipspro.modules.profile.dto;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

import com.dipspro.modules.chat.dto.RelatedTemplateDto;
import com.dipspro.modules.chat.dto.SlotDefinition;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Data Transfer Object for PromptTemplate entity.
 * Used for API requests and responses.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PromptTemplateDto {

    private UUID id;

    @NotBlank(message = "模板名称不能为空")
    private String name;

    private String description;

    private String agentType;

    private String templateContent;

    @NotNull(message = "槽位定义列表不能为空")
    @NotEmpty(message = "槽位定义列表不能为空")
    @Valid // Enable validation for nested SlotDefinition objects
    private List<SlotDefinition> slotDefinitions;

    private Instant createdAt;

    private Instant updatedAt;

    // 关联的模板列表
    private List<RelatedTemplateDto> relatedTemplates;

    // Static factory method for conversion from Entity (optional but convenient)
    // Removed: This logic is handled correctly in
    // PromptTemplateServiceImpl.convertToDto
    // public static PromptTemplateDto fromEntity(com.dipspro.entity.PromptTemplate
    // entity) { ... }

    // Method to convert DTO to Entity (optional but convenient)
    // Removed: This logic is handled correctly in
    // PromptTemplateServiceImpl.convertToEntity
    // public com.dipspro.entity.PromptTemplate toEntity() { ... }
}