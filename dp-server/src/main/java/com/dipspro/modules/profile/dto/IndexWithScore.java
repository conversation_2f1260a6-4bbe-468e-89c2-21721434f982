package com.dipspro.modules.profile.dto;

import static com.dipspro.constant.ProjectStatsConstants.SCORE_HIGH_MAX;
import static com.dipspro.constant.ProjectStatsConstants.SCORE_HIGH_MIN;
import static com.dipspro.constant.ProjectStatsConstants.SCORE_LOW_MAX;
import static com.dipspro.constant.ProjectStatsConstants.SCORE_LOW_MIN;
import static com.dipspro.constant.ProjectStatsConstants.SCORE_NOT_RATED;
import static com.dipspro.constant.ProjectStatsConstants.SCORE_VERY_LOW;

import com.dipspro.modules.chat.entity.IndexDefinition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 带有分值的指标数据
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IndexWithScore {
    private String indexCode;          // 指标代码
    private String indexGridName;      // 指标名称
    private String parentIndexCode;    // 父指标代码
    private boolean isMainIndex;       // 是否主指标
    private int score;                 // 得分
    private String legacyIndexCode;    // 遗留指标代码（如ra1a5）
    
    /**
     * 从指标定义和分数创建带分数的指标
     * 
     * @param definition 指标定义
     * @param score 分数
     * @return 带分数的指标
     */
    public static IndexWithScore fromDefinition(IndexDefinition definition, int score) {
        return new IndexWithScore(
            definition.getPlanarIndexCode(), 
            definition.getIndexGridName(),
            definition.getParentIndexCode(),
            definition.isMainIndex(),
            score,
            definition.getLegacyIndexCode()
        );
    }
    
    /**
     * 检查是否是高分(4-5分)
     * 
     * @return 是否高分
     */
    public boolean isHighScore() {
        return score >= SCORE_HIGH_MIN && score <= SCORE_HIGH_MAX;
    }
    
    /**
     * 检查是否是低分(2-3分)
     * 
     * @return 是否低分
     */
    public boolean isLowScore() {
        return score >= SCORE_LOW_MIN && score <= SCORE_LOW_MAX;
    }
    
    /**
     * 检查是否是极低分(1分)
     * 
     * @return 是否极低分
     */
    public boolean isVeryLowScore() {
        return score == SCORE_VERY_LOW;
    }
    
    /**
     * 检查是否未评分(-1分)
     * 
     * @return 是否未评分
     */
    public boolean isNotRated() {
        return score == SCORE_NOT_RATED;
    }
} 