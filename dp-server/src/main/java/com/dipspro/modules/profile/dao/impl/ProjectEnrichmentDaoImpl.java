package com.dipspro.modules.profile.dao.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import com.dipspro.modules.profile.dao.ProjectEnrichmentDao;

import lombok.extern.slf4j.Slf4j;

/**
 * 楼盘数据丰富化访问实现类
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@Repository
public class ProjectEnrichmentDaoImpl implements ProjectEnrichmentDao {

    @Autowired
    @Qualifier("mysqlJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Override
    public Map<String, Object> queryLjLouPan(String ljCode) {
        Map<String, Object> result = new HashMap<>();

        if (StringUtils.isBlank(ljCode)) {
            log.warn("链家唯一码为空，无法查询LjLoupan");
            return result;
        }

        try {
            // 构建SQL查询语句
            // 因为项目信息来自 lj_matching 表，已经有明确的链家 code
            // 因为 lj_loupan 会在不同时间抓取，同一个楼盘会有多条记录，取时间最近的一条
            String sqlTemplate = "select * from ds.lj_loupan where code='%s' order by created_time DESC limit 1";
            String sql = String.format(sqlTemplate, ljCode);
            log.info("执行链家楼盘查询SQL: {}", sql);

            // 执行查询
            List<Map<String, Object>> queryResults = jdbcTemplate.queryForList(sql);

            // 处理查询结果
            if (!queryResults.isEmpty()) {
                result.putAll(queryResults.get(0));
            }
        } catch (Exception e) {
            log.error("查询链家楼盘时发生错误，链家唯一码: {}", ljCode, e);
        }

        return result;
    }

    @Override
    public List<Map<String, Object>> queryLjLouPanLayout(String ljCode) {
        List<Map<String, Object>> result = new ArrayList<>();

        if (StringUtils.isBlank(ljCode)) {
            log.warn("链家唯一码为空，无法查询LjLoupan户型");
            return result;
        }

        try {
            // 构建SQL查询语句
            // 因为 lj_house_layout 会在不同时间抓取，同一个楼盘会有多条记录，取batch_no最近的一批
            String sqlTemplate = "select * from ds.lj_house_layout where loupan_code='%s' and batch_no = (select max(batch_no) from ds.lj_house_layout where loupan_code='%s')";
            String sql = String.format(sqlTemplate, ljCode, ljCode);
            log.info("执行链家户型查询SQL: {}", sql);

            // 执行查询
            result = jdbcTemplate.queryForList(sql);

            // 处理查询结果 - 返回最新批次的所有户型数据
            if (!result.isEmpty()) {
                // 将所有户型数据存储到result中，可以根据需要调整数据结构
                log.info("查询到最新批次 {} 的户型数据，共 {} 条", result.get(0).get("batch_no"), result.size());
            } else {
                log.warn("未查询到楼盘户型数据，链家唯一码: {}", ljCode);
            }
        } catch (Exception e) {
            log.error("查询链家户型时发生错误，链家唯一码: {}", ljCode, e);
        }

        return result;
    }
}