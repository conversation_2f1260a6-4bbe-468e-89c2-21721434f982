package com.dipspro.modules.profile.analyzer;

import com.dipspro.modules.profile.dto.ProjectDetail;
import com.dipspro.modules.profile.dto.UserProfileTag;
import com.dipspro.util.ProjectStatsUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.dipspro.constant.ProjectStatsConstants.TAG_SINGLE_BRAND_HOLDING;
import static com.dipspro.constant.ProjectStatsConstants.TAG_TYPE_BRAND_PREFERENCE;

/**
 * 品牌标签分析器 - 处理品牌忠实、单一品牌、品牌首购等标签
 * 
 * <AUTHOR>
 */
public class BrandTagAnalyzer extends UserProfileAnalyzer {
    
    /**
     * 构造函数
     * 
     * @param projects 项目列表
     * @param userProfileTags 用户画像标签列表
     */
    public BrandTagAnalyzer(List<ProjectDetail> projects, List<UserProfileTag> userProfileTags) {
        super(projects, userProfileTags);
    }
    
    @Override
    protected void doAnalyze(StringRedisTemplate redisTemplate) {
        // 实现所有分析方法
        analyzeBrandLoyaltyTag();
        analyzeSingleBrandTag();
        analyzeFirstBrandPurchaseTag();
        analyzeTopBrandPreferenceTag();
    }
    
    /**
     * 分析品牌忠实标签
     * 标签规则：namelist能找到同品牌的房产持有记录
     */
    public void analyzeBrandLoyaltyTag() {
        // 按开发商分组
        Map<String, List<ProjectDetail>> projectsByDeveloper = new HashMap<>();
        
        for (ProjectDetail project : projects) {
            if (StringUtils.isBlank(project.getCustomerName())) {
                continue;
            }
            
            String developer = project.getCustomerName();
            if (!projectsByDeveloper.containsKey(developer)) {
                projectsByDeveloper.put(developer, new ArrayList<>());
            }
            
            projectsByDeveloper.get(developer).add(project);
        }
        
        // 检查是否有同一开发商的多套房产
        for (Map.Entry<String, List<ProjectDetail>> entry : projectsByDeveloper.entrySet()) {
            if (entry.getValue().size() >= 2) {
                String developer = entry.getKey();
                UserProfileTag tag = UserProfileTag.create(
                    "brand_loyalty_" + developer.hashCode(),
                    "品牌忠实(" + developer + ")", 
                    "客户多次购买" + developer + "开发的房产", 
                    TAG_TYPE_BRAND_PREFERENCE
                );
                
                // 添加该开发商的项目作为匹配项
                for (ProjectDetail project : entry.getValue()) {
                    if (StringUtils.isNotBlank(project.getCustomerProject()) && 
                        StringUtils.isNotBlank(project.getProjectName())) {
                        tag.addMatchedProject(project.getCustomerProject(), project.getProjectName());
                    }
                }
                
                addTag(tag);
            }
        }
    }
    
    /**
     * 分析单一品牌标签
     * 标签规则：namelist中能找到>=2套房产（含2套），且均为同一个品牌的房产
     */
    public void analyzeSingleBrandTag() {
        if (projects.size() < 2) {
            return;
        }
        
        // 统计各品牌房产数量
        Map<String, Integer> brandCounts = new HashMap<>();
        Map<String, List<ProjectDetail>> brandProjects = new HashMap<>();
        
        for (ProjectDetail project : projects) {
            if (StringUtils.isNotBlank(project.getCustomerName())) {
                String brand = project.getCustomerName();
                
                brandCounts.put(brand, brandCounts.getOrDefault(brand, 0) + 1);
                
                if (!brandProjects.containsKey(brand)) {
                    brandProjects.put(brand, new ArrayList<>());
                }
                brandProjects.get(brand).add(project);
            }
        }
        
        // 找出占比最高的品牌
        String dominantBrand = null;
        int maxCount = 0;
        
        for (Map.Entry<String, Integer> entry : brandCounts.entrySet()) {
            if (entry.getValue() > maxCount) {
                maxCount = entry.getValue();
                dominantBrand = entry.getKey();
            }
        }
        
        // 如果最高品牌的房产数量等于总房产数量，且不少于2套，则添加标签
        if (dominantBrand != null && maxCount >= 2 && maxCount == projects.size()) {
            UserProfileTag tag = UserProfileTag.create(
                TAG_SINGLE_BRAND_HOLDING,
                dominantBrand + "品牌专属", 
                "客户所持房产均为" + dominantBrand + "品牌开发", 
                TAG_TYPE_BRAND_PREFERENCE
            );
            
            // 添加匹配项目
            List<ProjectDetail> matchedProjects = brandProjects.get(dominantBrand);
            for (ProjectDetail project : matchedProjects) {
                if (StringUtils.isNotBlank(project.getCustomerProject()) && 
                    StringUtils.isNotBlank(project.getProjectName())) {
                    tag.addMatchedProject(project.getCustomerProject(), project.getProjectName());
                }
            }
            
            addTag(tag);
        }
    }
    
    /**
     * 分析首次购买品牌标签
     * 标签规则：namelist中第一套房产的品牌
     */
    public void analyzeFirstBrandPurchaseTag() {
        if (projects.isEmpty()) {
            return;
        }
        
        // 查找签约时间最早的项目
        ProjectDetail earliestProject = ProjectStatsUtil.findEarliestProject(projects);
        if (earliestProject == null) {
            return; // 没有有效签约时间的项目
        }
        
        // 提取最早购买的项目品牌
        String firstBrand = earliestProject.getCustomerName();
        if (StringUtils.isBlank(firstBrand)) {
            return;
        }
        
        // 直接使用最早购买的项目信息创建标签
        UserProfileTag tag = UserProfileTag.create(
            "first_brand_purchase_" + firstBrand.hashCode(),
            "品牌首购(" + firstBrand + ")", 
            "客户首次购买" + firstBrand + "开发的房产", 
            TAG_TYPE_BRAND_PREFERENCE
        );
        
        // 添加最早购买的项目作为匹配项
        if (StringUtils.isNotBlank(earliestProject.getCustomerProject()) && 
            StringUtils.isNotBlank(earliestProject.getProjectName())) {
            tag.addMatchedProject(
                earliestProject.getCustomerProject(), 
                earliestProject.getProjectName()
            );
        }
        
        addTag(tag);
    }
    
    /**
     * 分析顶级品牌偏好标签
     * 标签规则：namelist中有50%及以上的房产为顶级房企品牌
     */
    public void analyzeTopBrandPreferenceTag() {
        if (projects.size() < 2) {
            return;
        }
        
        // 获取头部TOP20房企列表
        java.util.Set<String> top20Developers = ProjectStatsUtil.getTop20Developers();
        
        boolean allFromTopDevelopers = true;
        
        for (ProjectDetail project : projects) {
            if (StringUtils.isBlank(project.getCustomerName())) {
                allFromTopDevelopers = false;
                break;
            }
            
            boolean isTopDeveloper = false;
            for (String developer : top20Developers) {
                if (project.getCustomerName().contains(developer)) {
                    isTopDeveloper = true;
                    break;
                }
            }
            
            if (!isTopDeveloper) {
                allFromTopDevelopers = false;
                break;
            }
        }
        
        if (allFromTopDevelopers) {
            UserProfileTag tag = UserProfileTag.create(
                "top_brand_preference",
                "头部品牌偏好", 
                "客户偏好购买头部房企开发的房产", 
                TAG_TYPE_BRAND_PREFERENCE
            );
            
            // 添加所有项目作为匹配项
            for (ProjectDetail project : projects) {
                if (StringUtils.isNotBlank(project.getCustomerProject()) && 
                    StringUtils.isNotBlank(project.getProjectName())) {
                    tag.addMatchedProject(project.getCustomerProject(), project.getProjectName());
                }
            }
            
            addTag(tag);
        }
    }
} 