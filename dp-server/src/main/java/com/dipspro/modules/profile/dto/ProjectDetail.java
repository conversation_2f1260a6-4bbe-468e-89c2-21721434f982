package com.dipspro.modules.profile.dto;

import static com.dipspro.constant.ProjectStatsConstants.*;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

import com.dipspro.modules.normalize.dto.NormalizeData;
import com.dipspro.util.ProjectStatsUtil;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 项目详情
 *
 * <AUTHOR>
 * 2025/5/11 08:45
 * @apiNote 楼盘基本信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectDetail {
    private String customerProject; // 客户项目编码
    private String province; // 省份
    private String city; // 城市
    private String signDatetime; // 签约时间
    private String buildingGrade; // 楼盘档次
    private String houseType; // 房屋类型
    private String decorationType; // 装修类型
    private String houseNo; // 房号
    private String ownerStage; // 业主阶段

    // 是否匹配到链家楼盘信息 lj_matching
    private boolean matchedLianjia;
    // 开发商
    private String branName; // 来自 lj_loupan
    private String customerName; // 来自 lj_matching
    // 楼盘名称
    private String projectName;
    // 行政区
    private String district;
    // 地址
    private String address;
    // 链家唯一码
    private String ljCode;
    // 均价
    private String ljLoupanPriceAverage;
    // 楼盘类型
    private String ljXiaoquBuildType;
    // 物业费水平
    private String propertyCostLevel;
    // 物业费
    private String ljXiaoquPropertyFeeDesc;
    // 周边购物中心数量
    private Integer mallAmount;
    // 周边超市数量
    private Integer supermarketAmount;
    // 周边医院数量
    private Integer hospitalAmount;
    // 周边公园数量
    private Integer parkAmount;
    // 周边学校数量
    private Integer schoolAmount;
    // 周边地铁站数量
    private Integer subwayStationAmount;
    // 周边公交站数量
    private Integer busStopAmount;

    // 项目指标得分，key:indexCode, value:indexScore
    private Map<String, Integer> indexScore;
    // 项目业主类型指标得分，key:ownerType, value:<key:indexCode, value:indexScore>
    private Map<String, Map<String, Integer>> indexScoreOwnerType;
    // 样框
    private Integer totalSample;
    // 样框原始数据
    private Map<String, Object> totalSampleRawData;
    // 年龄段
    private Map<String, Integer> ageGroup;

    // ra1a5评分
    private String ra1a5Score;
    // 好评文本
    private String textGood;
    // 差评文本
    private String textBad;

    // 地理位置坐标
    private Double lng; // 经度
    private Double lat; // 纬度

    // 链家楼盘数据表原始数据
    private Map<String, Object> ljLouPan;
    // 链家楼盘户型数据
    private List<Map<String, Object>> ljLouPanLayout;

    // 周边兴趣点列表
    private List<PointOfInterestDto> nearbyPois;

    // 原始名单数据
    private List<NormalizeData> nameListData;

    // 原始数据
    private List<NormalizeData> rawData;

    // 指标相关字段
    private List<IndexWithScore> indexScores = new ArrayList<>();
    private List<IndexWithScore> highScoreIndices = new ArrayList<>(); // 4-5分
    private List<IndexWithScore> lowScoreIndices = new ArrayList<>(); // 2-3分
    private List<IndexWithScore> veryLowScoreIndices = new ArrayList<>(); // 1分
    private List<IndexWithScore> mainLowScoreIndices = new ArrayList<>(); // 低分/极低分的主指标
    private List<IndexWithScore> subLowScoreIndices = new ArrayList<>(); // 低分/极低分主指标的子指标

    // 用于标签分析的缓存数据
    private transient boolean isUsedForTagAnalysis = false;

    /**
     * 获取格式化的价格
     *
     * @return 格式化后的价格字符串
     */
    public String getFormattedPrice() {
        return ProjectStatsUtil.formatPrice(ljLoupanPriceAverage);
    }

    /**
     * 判断是否有有效的签约日期
     *
     * @return 是否有有效的签约日期
     */
    public boolean hasValidSignDate() {
        return StringUtils.isNotBlank(signDatetime)
                && !StringUtils.isNumeric(signDatetime)
                && !StringUtils.equalsIgnoreCase(signDatetime, "null");
    }

    /**
     * 判断城市是否需要显示（如果城市等于省份，则不需要显示）
     *
     * @return 是否需要显示城市
     */
    public boolean shouldShowCity() {
        return StringUtils.isNotBlank(city) && !StringUtils.equals(province, city);
    }

    /**
     * 获取周边配套描述
     *
     * @return 周边配套描述字符串
     */
    public String getFacilitiesDescription() {
        StringBuilder facilitiesBuilder = new StringBuilder();
        boolean hasAnyFacility = false;

        hasAnyFacility = ProjectStatsUtil.appendFacilityIfNotNull(facilitiesBuilder, mallAmount, "购物中心", true);
        boolean added = ProjectStatsUtil.appendFacilityIfNotNull(facilitiesBuilder, supermarketAmount, "超市",
                !hasAnyFacility);
        hasAnyFacility = hasAnyFacility || added;

        added = ProjectStatsUtil.appendFacilityIfNotNull(facilitiesBuilder, hospitalAmount, "医院", !hasAnyFacility);
        hasAnyFacility = hasAnyFacility || added;

        added = ProjectStatsUtil.appendFacilityIfNotNull(facilitiesBuilder, parkAmount, "公园", !hasAnyFacility);
        hasAnyFacility = hasAnyFacility || added;

        added = ProjectStatsUtil.appendFacilityIfNotNull(facilitiesBuilder, schoolAmount, "学校", !hasAnyFacility);
        hasAnyFacility = hasAnyFacility || added;

        added = ProjectStatsUtil.appendFacilityIfNotNull(facilitiesBuilder, subwayStationAmount, "地铁站",
                !hasAnyFacility);
        hasAnyFacility = hasAnyFacility || added;

        added = ProjectStatsUtil.appendFacilityIfNotNull(facilitiesBuilder, busStopAmount, "公交站", !hasAnyFacility);

        return facilitiesBuilder.toString();
    }

    /**
     * 判断是否有任何周边配套信息（数值不为null且不为0）
     *
     * @return 是否有周边配套信息
     */
    public boolean hasAnyFacilities() {
        return (mallAmount != null && mallAmount > 0) ||
                (supermarketAmount != null && supermarketAmount > 0) ||
                (hospitalAmount != null && hospitalAmount > 0) ||
                (parkAmount != null && parkAmount > 0) ||
                (schoolAmount != null && schoolAmount > 0) ||
                (subwayStationAmount != null && subwayStationAmount > 0) ||
                (busStopAmount != null && busStopAmount > 0);
    }

    /**
     * 判断是否有周边POI数据
     *
     * @return 是否有周边POI数据
     */
    public boolean hasNearbyPois() {
        return nearbyPois != null && !nearbyPois.isEmpty();
    }

    /**
     * 计算POI到项目的距离
     *
     * @param poi POI对象
     * @return 计算后的距离（米），如果无法计算则返回null
     */
    public Double calculatePoiDistance(PointOfInterestDto poi) {
        // 如果POI已有距离信息，直接返回
        if (poi.getDistance() != null) {
            return poi.getDistance();
        }

        // 如果POI或项目缺少经纬度信息，无法计算距离
        if (poi.getLng() == null || poi.getLat() == null || this.lng == null || this.lat == null) {
            return null;
        }

        // 计算POI与项目之间的距离
        return ProjectStatsUtil.calculateDistance(this.lat, this.lng, poi.getLat(), poi.getLng());
    }

    /**
     * 获取周边POI描述
     *
     * @param maxItemsPerType 每种类型最多显示的POI数量
     * @return 周边POI描述字符串
     */
    public String getNearbyPoisDescription(int maxItemsPerType) {
        if (!hasNearbyPois()) {
            return "";
        }

        // 为每个POI计算距离（如果未提供）
        for (PointOfInterestDto poi : nearbyPois) {
            if (poi.getDistance() == null) {
                Double distance = calculatePoiDistance(poi);
                if (distance != null) {
                    poi.setDistance(distance);
                }
            }
        }

        // 按类型分组
        java.util.Map<String, List<PointOfInterestDto>> poiByType = nearbyPois.stream()
                .collect(Collectors.groupingBy(PointOfInterestDto::getSimpleType));

        StringBuilder builder = new StringBuilder();

        // 获取类型数量Top3
        poiByType.entrySet().stream()
                .sorted((e1, e2) -> Integer.compare(e2.getValue().size(), e1.getValue().size()))
                .limit(3)
                .forEach(entry -> {
                    if (builder.length() > 0) {
                        builder.append(FACILITY_SEPARATOR);
                    }
                    builder.append(entry.getKey()).append("(").append(entry.getValue().size()).append(")");
                });

        // 根据类型分组输出POI详情
        if (!poiByType.isEmpty()) {
            builder.append("\n");

            // 对每个类型分别处理
            poiByType.entrySet().stream()
                    .sorted((e1, e2) -> Integer.compare(e2.getValue().size(), e1.getValue().size()))
                    .forEach(entry -> {
                        String poiType = entry.getKey();
                        List<PointOfInterestDto> poisOfType = entry.getValue();

                        // 安全处理：对每种类型的POI按距离排序并限制数量，处理null值情况
                        List<PointOfInterestDto> nearestPois = poisOfType.stream()
                                .filter(poi -> poi.getDistance() != null) // 过滤掉距离为null的POI
                                .sorted(Comparator.comparing(PointOfInterestDto::getDistance))
                                .limit(maxItemsPerType)
                                .collect(Collectors.toList());

                        // 如果过滤后没有剩余POI，就按原始顺序取前N个
                        if (nearestPois.isEmpty() && !poisOfType.isEmpty()) {
                            nearestPois = poisOfType.stream()
                                    .limit(maxItemsPerType)
                                    .collect(Collectors.toList());
                        }

                        if (!nearestPois.isEmpty()) {
                            builder.append("      - ").append(poiType).append("：");

                            for (int i = 0; i < nearestPois.size(); i++) {
                                PointOfInterestDto poi = nearestPois.get(i);
                                if (i > 0) {
                                    builder.append(FACILITY_SEPARATOR);
                                }

                                builder.append(poi.getName());
                                if (poi.getDistance() != null) {
                                    builder.append("（");
                                    // 根据距离大小决定单位
                                    if (poi.getDistance() < NEARBY_POI_DISTANCE_THRESHOLD) {
                                        // 距离小于500米，使用米为单位，取整数
                                        builder.append(String.format("%.0f", poi.getDistance()))
                                                .append("米");
                                    } else {
                                        // 距离大于等于500米，使用公里为单位，保留一位小数
                                        builder.append(String.format("%.1f", poi.getDistance() / 1000))
                                                .append("公里");
                                    }
                                    builder.append("）");
                                }
                            }

                            builder.append("\n");
                        }
                    });
        }

        return builder.toString();
    }
}