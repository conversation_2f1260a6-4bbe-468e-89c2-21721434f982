package com.dipspro.modules.profile.analyzer;

import com.dipspro.modules.profile.dto.ProjectDetail;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.List;

/**
 * 主用户画像标签分析器
 * 集成所有子分析器的分析逻辑
 * 
 * <AUTHOR>
 */
public class MainUserProfileAnalyzer extends UserProfileAnalyzer {
    
    private BasicTagAnalyzer basicAnalyzer;
    private PropertyTagAnalyzer propertyAnalyzer;
    private BrandTagAnalyzer brandAnalyzer;
    private PreferenceTagAnalyzer preferenceAnalyzer;
    
    /**
     * 构造函数
     * 
     * @param projects 项目列表
     */
    public MainUserProfileAnalyzer(List<ProjectDetail> projects) {
        super(projects);
    }
    
    @Override
    protected void createAnalyzers() {
        // 初始化专门的分析器类
        basicAnalyzer = new BasicTagAnalyzer(projects, userProfileTags);
        propertyAnalyzer = new PropertyTagAnalyzer(projects, userProfileTags);
        brandAnalyzer = new BrandTagAnalyzer(projects, userProfileTags);
        preferenceAnalyzer = new PreferenceTagAnalyzer(projects, userProfileTags);
    }
    
    @Override
    protected void doAnalyze(StringRedisTemplate redisTemplate) {
        // 调用各子分析器的doAnalyze方法，利用多态
        basicAnalyzer.doAnalyze(redisTemplate);
        propertyAnalyzer.doAnalyze(redisTemplate);
        brandAnalyzer.doAnalyze(redisTemplate);
        preferenceAnalyzer.doAnalyze(redisTemplate);
    }
} 