package com.dipspro.modules.profile.formatter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import com.dipspro.modules.chat.dto.MessageContent;
import com.dipspro.modules.chat.dto.SuggestionItem;
import com.dipspro.modules.profile.dto.ProjectDetail;
import com.dipspro.modules.profile.dto.ProjectProfileDto;

import lombok.extern.slf4j.Slf4j;

/**
 * 楼盘画像格式化器
 *
 * <AUTHOR>
 * 2025/5/11 08:30
 * @apiNote 用于格式化楼盘画像信息为对话内容
 */
@Slf4j
public class ProjectProfileSurroundingFormatter {

    private final ProjectProfileDto projectProfile;

    public ProjectProfileSurroundingFormatter(ProjectProfileDto projectProfile) {
        this.projectProfile = projectProfile;
    }

    /**
     * 格式化楼盘画像为MessageContent列表
     *
     * @return 格式化后的消息内容列表
     */
    public List<MessageContent> formatProjectProfile() {
        List<MessageContent> contents = new ArrayList<>();

        // 根据查询状态生成不同的内容
        switch (projectProfile.getQueryStatus()) {
            case "exact":
                contents.addAll(formatExactMatchProfile());
                break;
            case "fuzzy":
                contents.addAll(formatFuzzyMatchProfile());
                break;
            case "multiple":
                contents.addAll(formatMultipleResultsProfile());
                break;
            case "not_found":
                contents.addAll(formatNotFoundProfile());
                break;
            default:
                contents.add(MessageContent.text("查询状态未知"));
        }

        return contents;
    }

    /**
     * 格式化精确匹配的楼盘画像
     *
     * @return 消息内容列表
     */
    private List<MessageContent> formatExactMatchProfile() {
        List<MessageContent> contents = new ArrayList<>();

        // 项目基本信息
        if (projectProfile.getProjectDetail() == null
                || null == projectProfile.getProjectDetail().getLng()
                || null == projectProfile.getProjectDetail().getLat()) {
            log.warn("没有楼盘的位置信息，无法查找周边项目");
            contents.add(MessageContent.text("没有楼盘的位置信息，无法查找周边项目"));
            return contents;
        }

        // 周边楼盘信息
        contents.addAll(formatNearbyProjects());

        return contents;
    }

    /**
     * 格式化模糊匹配的楼盘画像
     *
     * @return 消息内容列表
     */
    private List<MessageContent> formatFuzzyMatchProfile() {
        List<MessageContent> contents = new ArrayList<>();

        // TODO: 实现模糊匹配楼盘画像的格式化逻辑
        contents.add(MessageContent.text("#### 楼盘画像（模糊匹配）：" + projectProfile.getProjectName()));
        contents.add(MessageContent.text("*注：通过模糊匹配找到的楼盘信息*"));

        // 复用精确匹配的格式化逻辑
        contents.addAll(formatExactMatchProfile().subList(1, formatExactMatchProfile().size()));

        return contents;
    }

    /**
     * 格式化多个匹配结果
     *
     * @return 消息内容列表
     */
    private List<MessageContent> formatMultipleResultsProfile() {
        List<MessageContent> contents = new ArrayList<>();

        contents.add(MessageContent.text("#### 找到多个匹配的楼盘"));
        contents.add(MessageContent.text("请从以下选项中选择您要查询的楼盘："));

        // 直接使用已经构建好的建议选项
        List<SuggestionItem> suggestionItems = projectProfile.getSuggestionItems();
        if (suggestionItems != null && !suggestionItems.isEmpty()) {
            contents.add(MessageContent.suggestion(suggestionItems.toArray(new SuggestionItem[0])));
        }

        return contents;
    }

    /**
     * 格式化未找到结果
     *
     * @return 消息内容列表
     */
    private List<MessageContent> formatNotFoundProfile() {
        List<MessageContent> contents = new ArrayList<>();

        // TODO: 实现未找到结果的格式化逻辑
        contents.add(MessageContent.text("#### 未找到楼盘信息"));
        contents.add(MessageContent.text("抱歉，没有找到名为「" + projectProfile.getProjectName() + "」的楼盘信息。"));
        contents.add(MessageContent.text("请检查楼盘名称是否正确，或尝试使用其他关键词搜索。"));

        return contents;
    }

    /**
     * 格式化周边楼盘信息
     *
     * @return 消息内容列表
     */
    private List<MessageContent> formatNearbyProjects() {
        List<MessageContent> contents = new ArrayList<>();

        List<ProjectDetail> nearbyProjects = projectProfile.getNearbyProjects();
        if (nearbyProjects.isEmpty())
            return contents;

        /*
         * 如果只取出来一个项目，显示如下：
         * [北京保利中央公馆]周边直线距离[5公里]内仅1个项目，户数[多于/少于][北京保利中央公馆]，
         * 处于[业主阶段]，客户满意度[好于/不及][北京保利中央公馆]，物业服务[好于/不及][北京保利中央公馆]。
         */
        String projectName = projectProfile.getProjectName();
        if (nearbyProjects.size() == 1) {
            ProjectDetail nearby = nearbyProjects.getFirst();

            List<String> lineList = new ArrayList<>();
            String line1 = String.format("%s 周边直线距离 %f 公里内仅1个项目"
                    , projectName, projectProfile.getDistance()
            );
            lineList.add(line1);

            Integer totalSample = projectProfile.getProjectDetail().getTotalSample();
            Integer nearbyTotalSample = nearby.getTotalSample();
            if (null != totalSample && null != nearbyTotalSample) {
                String compare = nearbyTotalSample > totalSample ? "多于" : "少于";
                String line2 = String.format("户数%s%s", compare, projectName);
                lineList.add(line2);
            }

            if (StringUtils.isNotBlank(nearby.getOwnerStage())) {
                String line3 = String.format("处于%s", nearby.getOwnerStage());
                lineList.add(line3);
            }

            Map<String, Integer> indexScore = projectProfile.getProjectDetail().getIndexScore();
            Map<String, Integer> nearbyIndexScore = nearby.getIndexScore();
            if (null != indexScore && null != nearbyIndexScore) {
                Integer ra1a5 = indexScore.get("ra1a5");
                Integer nearbyRa1a5 = nearbyIndexScore.get("ra1a5");
                if (null != ra1a5 && null != nearbyRa1a5) {
                    String compare = nearbyRa1a5 > ra1a5 ? "多于" : "少于";
                    String line4 = String.format("客户满意度%s%s", compare, projectName);
                    lineList.add(line4);
                }

                Integer rt0a5 = indexScore.get("rt0a5");
                Integer nearbyRt0a5 = nearbyIndexScore.get("rt0a5");
                if (null != rt0a5 && null != nearbyRt0a5) {
                    String compare = nearbyRt0a5 > rt0a5 ? "多于" : "少于";
                    String line5 = String.format("物业服务%s%s", compare, projectName);
                    lineList.add(line5);
                }
            }

            String txt = String.join("，", lineList);
            if (StringUtils.isNotBlank(txt)) {
                txt += "。";
                contents.add(MessageContent.text(txt));
            }
            return contents;
        }

        // 计算样框平均值
        int totalSampleSum = 0, totalSampleCount = 0;
        for (ProjectDetail projectDetail : nearbyProjects) {
            Integer totalSample = projectDetail.getTotalSample();
            if (null == totalSample)
                continue;
            totalSampleSum += totalSample;
            totalSampleCount++;
        }
        double totalSampleAvg = (double) totalSampleSum / totalSampleCount;

        // 统计业主阶段
        int zhunCount = 0, laoCount = 0;
        for (ProjectDetail nearbyProject : nearbyProjects) {
            String ownerStage = nearbyProject.getOwnerStage();
            if (StringUtils.isBlank(ownerStage))
                continue;

            if (ownerStage.contains("销售阶段"))
                zhunCount++;
            if (ownerStage.contains("已出保阶段"))
                laoCount++;
        }

        // 计算总体满意度平均值
        int ra1a5Sum = 0, ra1a5Count = 0, ra1a5Max = 0;
        for (ProjectDetail projectDetail : nearbyProjects) {
            Map<String, Integer> indexScore = projectDetail.getIndexScore();
            if (null == indexScore)
                continue;
            Integer ra1a5 = indexScore.get("ra1a5");
            if (null == ra1a5)
                continue;
            ra1a5Sum += ra1a5;
            ra1a5Count++;
            ra1a5Max = Math.max(ra1a5Max, ra1a5);
        }
        double ra1a5Avg = (double) ra1a5Sum / ra1a5Count;

        // 计算物业满意度平均值
        int rt0a5Sum = 0, rt0a5Count = 0, rt0a5Max = 0;
        for (ProjectDetail projectDetail : nearbyProjects) {
            Map<String, Integer> indexScore = projectDetail.getIndexScore();
            if (null == indexScore)
                continue;
            Integer rt0a5 = indexScore.get("rt0a5");
            if (null == rt0a5)
                continue;
            rt0a5Sum += rt0a5;
            rt0a5Count++;
            rt0a5Max = Math.max(rt0a5Max, rt0a5);
        }
        double rt0a5Avg = (double) rt0a5Sum / rt0a5Count;

        /*
         * 2个项目及以上:
         * [北京保利中央公馆]周边直线距离[5公里]内共有[项目数量]个项目，
         * 平均项目户数[样框数量均值]户，其中[项目数量]个项目处于销售阶段，[项目数量]个项目已出保。
         * 相较周边项目，总体满意度[比较结果]均值（[A1得分均值]，最佳[A1得分]），物业服务[比较结果]均值（[J1得分均值]，最佳[J1得分]）。
         */
        List<String> lineList = new ArrayList<>();
        String line1 = String.format("**%s** 周边直线距离 %d 公里内共有 %d 个项目", projectName,
                Math.round(projectProfile.getDistance()), nearbyProjects.size());
        lineList.add(line1);

        String line2 = String.format("平均项目户数 %d 户，其中 %d 个项目处于销售阶段， %d 个项目已出保。", Math.round(totalSampleAvg), zhunCount,
                laoCount);
        lineList.add(line2);

        String line3 = String.format("相较周边项目，总体满意度均值 %d 分，最佳 %d 分，物业服务均值 %d 分，最佳 %d 分。",
                Math.round(ra1a5Avg), ra1a5Max, Math.round(rt0a5Avg), rt0a5Max);
        lineList.add(line3);

        contents.add(MessageContent.text(String.join("\n", lineList)));

        return contents;
    }

}