package com.dipspro.modules.profile.dto;

import java.util.ArrayList;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户画像标签
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserProfileTag {
    /**
     * 标签代码
     */
    private String code;
    
    /**
     * 标签名称
     */
    private String name;
    
    /**
     * 标签描述
     */
    private String description;
    
    /**
     * 标签类型（购买行为、消费偏好等）
     */
    private String type;
    
    /**
     * 标签置信度
     */
    private double confidence;
    
    /**
     * 标签匹配的项目代码列表
     */
    private List<String> matchedProjectCodes = new ArrayList<>();
    
    /**
     * 标签匹配的项目名称列表（用于展示）
     */
    private List<String> matchedProjectNames = new ArrayList<>();
    
    /**
     * 创建一个新的标签实例
     */
    public static UserProfileTag create(String code, String name, String description, String type) {
        UserProfileTag tag = new UserProfileTag();
        tag.setCode(code);
        tag.setName(name);
        tag.setDescription(description);
        tag.setType(type);
        tag.setConfidence(1.0); // 默认置信度
        return tag;
    }
    
    /**
     * 添加匹配的项目
     */
    public void addMatchedProject(String projectCode, String projectName) {
        if (!this.matchedProjectCodes.contains(projectCode)) {
            this.matchedProjectCodes.add(projectCode);
            this.matchedProjectNames.add(projectName);
        }
    }
} 