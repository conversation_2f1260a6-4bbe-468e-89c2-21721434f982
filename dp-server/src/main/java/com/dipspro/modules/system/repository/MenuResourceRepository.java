package com.dipspro.modules.system.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.dipspro.modules.system.entity.MenuResourceEntity;

/**
 * 菜单资源Repository接口
 */
@Repository
public interface MenuResourceRepository extends JpaRepository<MenuResourceEntity, Long>, JpaSpecificationExecutor<MenuResourceEntity> {

    /**
     * 根据菜单编码查找菜单资源（未删除）
     */
    Optional<MenuResourceEntity> findByMenuCodeAndDeleted(String menuCode, Integer deleted);

    /**
     * 检查菜单编码是否存在（排除指定ID）
     */
    @Query("SELECT COUNT(m) > 0 FROM MenuResourceEntity m WHERE m.menuCode = :menuCode AND m.deleted = 0 AND (:excludeId IS NULL OR m.id != :excludeId)")
    boolean existsByMenuCodeAndNotDeleted(@Param("menuCode") String menuCode, @Param("excludeId") Long excludeId);

    /**
     * 根据父菜单ID查找子菜单列表
     */
    List<MenuResourceEntity> findByParentIdAndDeletedOrderBySortOrderAsc(Long parentId, Integer deleted);

    /**
     * 根据菜单类型查找菜单列表
     */
    List<MenuResourceEntity> findByMenuTypeAndDeletedOrderBySortOrderAsc(String menuType, Integer deleted);

    /**
     * 根据状态查找菜单列表
     */
    List<MenuResourceEntity> findByStatusAndDeletedOrderBySortOrderAsc(Integer status, Integer deleted);

    /**
     * 查找所有启用的菜单
     */
    @Query("SELECT m FROM MenuResourceEntity m WHERE m.status = 1 AND m.deleted = 0 ORDER BY m.sortOrder ASC")
    List<MenuResourceEntity> findAllActiveMenus();

    /**
     * 查找根菜单列表（父菜单ID为空）
     */
    @Query("SELECT m FROM MenuResourceEntity m WHERE m.parentId IS NULL AND m.deleted = 0 ORDER BY m.sortOrder ASC")
    List<MenuResourceEntity> findRootMenus();

    /**
     * 根据层级查找菜单列表
     */
    List<MenuResourceEntity> findByLevelAndDeletedOrderBySortOrderAsc(Integer level, Integer deleted);

    /**
     * 查找指定菜单的所有子菜单（递归）
     */
    @Query(value = "WITH RECURSIVE menu_tree AS (" +
            "SELECT * FROM sys_menu_resource WHERE parent_id = :parentId AND deleted = 0 " +
            "UNION ALL " +
            "SELECT m.* FROM sys_menu_resource m " +
            "INNER JOIN menu_tree mt ON m.parent_id = mt.id " +
            "WHERE m.deleted = 0" +
            ") SELECT * FROM menu_tree ORDER BY sort_order", nativeQuery = true)
    List<MenuResourceEntity> findAllChildrenByParentId(@Param("parentId") Long parentId);
} 