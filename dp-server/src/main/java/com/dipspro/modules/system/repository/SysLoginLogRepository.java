package com.dipspro.modules.system.repository;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.dipspro.modules.system.entity.SysLoginLog;

/**
 * 系统登录日志Repository接口
 */
@Repository
public interface SysLoginLogRepository extends JpaRepository<SysLoginLog, Long>, JpaSpecificationExecutor<SysLoginLog> {

    /**
     * 根据租户ID查询登录日志
     */
    Page<SysLoginLog> findByTenantIdOrderByLoginTimeDesc(Long tenantId, Pageable pageable);

    /**
     * 根据用户ID查询登录日志
     */
    Page<SysLoginLog> findByUserIdOrderByLoginTimeDesc(Long userId, Pageable pageable);

    /**
     * 根据登录类型查询登录日志
     */
    Page<SysLoginLog> findByLoginTypeOrderByLoginTimeDesc(String loginType, Pageable pageable);

    /**
     * 根据登录状态查询登录日志
     */
    Page<SysLoginLog> findByStatusOrderByLoginTimeDesc(Integer status, Pageable pageable);

    /**
     * 根据用户名模糊查询登录日志
     */
    Page<SysLoginLog> findByUsernameContainingOrderByLoginTimeDesc(String username, Pageable pageable);

    /**
     * 根据IP地址查询登录日志
     */
    Page<SysLoginLog> findByIpAddressOrderByLoginTimeDesc(String ipAddress, Pageable pageable);

    /**
     * 根据设备类型查询登录日志
     */
    Page<SysLoginLog> findByDeviceTypeOrderByLoginTimeDesc(String deviceType, Pageable pageable);

    /**
     * 根据时间范围查询登录日志
     */
    Page<SysLoginLog> findByLoginTimeBetweenOrderByLoginTimeDesc(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * 查询用户最近的成功登录记录
     */
    List<SysLoginLog> findByUserIdAndStatusOrderByLoginTimeDesc(Long userId, Integer status, Pageable pageable);

    /**
     * 更新登出时间
     */
    @Modifying
    @Query("UPDATE SysLoginLog s SET s.logoutTime = :logoutTime WHERE s.id = :id")
    int updateLogoutTime(@Param("id") Long id, @Param("logoutTime") LocalDateTime logoutTime);

    /**
     * 清理过期日志
     */
    @Modifying
    @Query("DELETE FROM SysLoginLog s WHERE s.loginTime < :expireTime")
    int deleteByLoginTimeBefore(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 统计指定时间范围内的登录次数
     */
    @Query("SELECT COUNT(s) FROM SysLoginLog s WHERE s.loginTime BETWEEN :startTime AND :endTime AND s.status = 1")
    long countSuccessLoginsByTimeBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内的失败登录次数
     */
    @Query("SELECT COUNT(s) FROM SysLoginLog s WHERE s.loginTime BETWEEN :startTime AND :endTime AND s.status = 0")
    long countFailedLoginsByTimeBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 根据租户ID和时间范围查询登录日志
     */
    Page<SysLoginLog> findByTenantIdAndLoginTimeBetweenOrderByLoginTimeDesc(
            Long tenantId, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * 查询最近的登录日志
     */
    List<SysLoginLog> findTop10ByOrderByLoginTimeDesc();

    /**
     * 查询用户最近的登录记录（限制数量）
     */
    @Query("SELECT s FROM SysLoginLog s WHERE s.userId = :userId AND s.status = 1 ORDER BY s.loginTime DESC")
    List<SysLoginLog> findRecentLoginsByUserId(@Param("userId") Long userId, Pageable pageable);
} 