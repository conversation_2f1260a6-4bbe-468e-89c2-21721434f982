package com.dipspro.modules.system.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.system.dto.MenuResourceCreateDto;
import com.dipspro.modules.system.dto.MenuResourceQueryDto;
import com.dipspro.modules.system.dto.MenuResourceResponseDto;
import com.dipspro.modules.system.dto.MenuResourceUpdateDto;
import com.dipspro.modules.system.service.MenuResourceService;

import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;

/**
 * 菜单资源管理控制器
 */
@RestController
@RequestMapping("/api/sys/menu-resource")
@Slf4j
public class MenuResourceController {

    @Autowired
    private MenuResourceService menuResourceService;

    /**
     * 分页查询菜单资源列表
     */
    @GetMapping("/list")
    public ApiResponse<Page<MenuResourceResponseDto>> getMenuResourceList(MenuResourceQueryDto queryDto) {
        try {
            Page<MenuResourceResponseDto> result = menuResourceService.getMenuResourceList(queryDto);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("查询菜单资源列表失败", e);
            return ApiResponse.error("查询菜单资源列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取菜单资源详情
     */
    @GetMapping("/detail")
    public ApiResponse<MenuResourceResponseDto> getMenuResourceDetail(@RequestParam Long id) {
        try {
            MenuResourceResponseDto result = menuResourceService.getMenuResourceById(id);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取菜单资源详情失败", e);
            return ApiResponse.error("获取菜单资源详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建菜单资源
     */
    @PostMapping("/create")
    public ApiResponse<MenuResourceResponseDto> createMenuResource(@Valid @RequestBody MenuResourceCreateDto createDto) {
        try {
            MenuResourceResponseDto result = menuResourceService.createMenuResource(createDto);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("创建菜单资源失败", e);
            return ApiResponse.error("创建菜单资源失败: " + e.getMessage());
        }
    }

    /**
     * 更新菜单资源
     */
    @PostMapping("/update")
    public ApiResponse<MenuResourceResponseDto> updateMenuResource(@RequestParam Long id, @RequestBody MenuResourceUpdateDto updateDto) {
        try {
            MenuResourceResponseDto result = menuResourceService.updateMenuResource(id, updateDto);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("更新菜单资源失败", e);
            return ApiResponse.error("更新菜单资源失败: " + e.getMessage());
        }
    }

    /**
     * 删除菜单资源
     */
    @PostMapping("/delete")
    public ApiResponse<Void> deleteMenuResource(@RequestParam Long id) {
        try {
            menuResourceService.deleteMenuResource(id);
            return ApiResponse.success(null);
        } catch (Exception e) {
            log.error("删除菜单资源失败", e);
            return ApiResponse.error("删除菜单资源失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除菜单资源
     */
    @PostMapping("/batch-delete")
    public ApiResponse<Void> deleteMenuResources(@RequestBody List<Long> ids) {
        try {
            menuResourceService.deleteMenuResources(ids);
            return ApiResponse.success(null);
        } catch (Exception e) {
            log.error("批量删除菜单资源失败", e);
            return ApiResponse.error("批量删除菜单资源失败: " + e.getMessage());
        }
    }

    /**
     * 更新菜单资源状态
     */
    @PostMapping("/update-status")
    public ApiResponse<Void> updateMenuResourceStatus(@RequestParam Long id, @RequestParam Integer status) {
        try {
            menuResourceService.updateMenuResourceStatus(id, status);
            return ApiResponse.success(null);
        } catch (Exception e) {
            log.error("更新菜单资源状态失败", e);
            return ApiResponse.error("更新菜单资源状态失败: " + e.getMessage());
        }
    }

    /**
     * 检查菜单编码是否存在
     */
    @GetMapping("/check-menu-code")
    public ApiResponse<Boolean> checkMenuCodeExists(@RequestParam String menuCode, @RequestParam(required = false) Long excludeId) {
        try {
            boolean exists = menuResourceService.checkMenuCodeExists(menuCode, excludeId);
            return ApiResponse.success(exists);
        } catch (Exception e) {
            log.error("检查菜单编码失败", e);
            return ApiResponse.error("检查菜单编码失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有启用的菜单资源
     */
    @GetMapping("/active")
    public ApiResponse<List<MenuResourceResponseDto>> getAllActiveMenuResources() {
        try {
            List<MenuResourceResponseDto> result = menuResourceService.getAllActiveMenuResources();
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取启用菜单资源失败", e);
            return ApiResponse.error("获取启用菜单资源失败: " + e.getMessage());
        }
    }

    /**
     * 获取菜单树形结构
     */
    @GetMapping("/tree")
    public ApiResponse<List<MenuResourceResponseDto>> getMenuResourceTree() {
        try {
            List<MenuResourceResponseDto> result = menuResourceService.getMenuResourceTree();
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取菜单树形结构失败", e);
            return ApiResponse.error("获取菜单树形结构失败: " + e.getMessage());
        }
    }

    /**
     * 根据菜单类型获取菜单列表
     */
    @GetMapping("/by-type")
    public ApiResponse<List<MenuResourceResponseDto>> getMenuResourcesByType(@RequestParam String menuType) {
        try {
            List<MenuResourceResponseDto> result = menuResourceService.getMenuResourcesByType(menuType);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("根据类型获取菜单资源失败", e);
            return ApiResponse.error("根据类型获取菜单资源失败: " + e.getMessage());
        }
    }
} 