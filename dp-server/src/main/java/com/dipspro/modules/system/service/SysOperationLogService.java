package com.dipspro.modules.system.service;

import java.util.List;

import com.dipspro.modules.system.entity.SysOperationLog;

/**
 * 系统操作日志服务接口
 */
public interface SysOperationLogService {

    /**
     * 记录操作日志
     */
    void recordOperationLog(SysOperationLog operationLog);

    /**
     * 异步记录操作日志
     */
    void recordOperationLogAsync(SysOperationLog operationLog);

    /**
     * 根据ID查询操作日志
     */
    SysOperationLog getById(Long id);

    /**
     * 分页查询操作日志
     */
    List<SysOperationLog> getPage(SysOperationLog operationLog, int pageNum, int pageSize);

    /**
     * 统计操作日志数量
     */
    long count(SysOperationLog operationLog);

    /**
     * 清理过期日志
     */
    int cleanExpiredLogs(int days);

    /**
     * 导出操作日志
     */
    void exportOperationLogs(SysOperationLog operationLog, String filePath);
} 