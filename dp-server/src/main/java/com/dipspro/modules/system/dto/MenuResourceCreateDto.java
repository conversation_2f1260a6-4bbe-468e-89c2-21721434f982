package com.dipspro.modules.system.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 菜单资源创建DTO
 */
@Data
public class MenuResourceCreateDto {

    @NotBlank(message = "菜单编码不能为空")
    private String menuCode;

    @NotBlank(message = "菜单名称不能为空")
    private String menuName;

    @NotBlank(message = "菜单类型不能为空")
    private String menuType;

    private Long parentId;

    private Integer level = 1;

    private String path;

    private String component;

    private String icon;

    private Integer sortOrder = 0;

    @NotNull(message = "是否可见不能为空")
    private Integer visible = 1;

    @NotNull(message = "状态不能为空")
    private Integer status = 1;

    private Integer externalLink = 0;

    private Integer cache = 0;

    private String description;
} 