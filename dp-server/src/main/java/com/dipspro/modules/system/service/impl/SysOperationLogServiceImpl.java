package com.dipspro.modules.system.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.dipspro.modules.system.entity.SysOperationLog;
import com.dipspro.modules.system.repository.SysOperationLogRepository;
import com.dipspro.modules.system.service.SysOperationLogService;

import jakarta.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;

/**
 * 系统操作日志服务实现类
 */
@Slf4j
@Service
public class SysOperationLogServiceImpl implements SysOperationLogService {

    @Autowired
    private SysOperationLogRepository operationLogRepository;

    @Override
    public void recordOperationLog(SysOperationLog operationLog) {
        try {
            if (operationLog.getCreatedTime() == null) {
                operationLog.setCreatedTime(LocalDateTime.now());
            }
            operationLogRepository.save(operationLog);
        } catch (Exception e) {
            log.error("记录操作日志失败", e);
        }
    }

    @Override
    @Async("taskExecutor")
    public void recordOperationLogAsync(SysOperationLog operationLog) {
        recordOperationLog(operationLog);
    }

    @Override
    public SysOperationLog getById(Long id) {
        return operationLogRepository.findById(id).orElse(null);
    }

    @Override
    public List<SysOperationLog> getPage(SysOperationLog operationLog, int pageNum, int pageSize) {
        Pageable pageable = PageRequest.of(pageNum - 1, pageSize, Sort.by(Sort.Direction.DESC, "createdTime"));
        
        Specification<SysOperationLog> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            if (operationLog.getTenantId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("tenantId"), operationLog.getTenantId()));
            }
            if (operationLog.getUserId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("userId"), operationLog.getUserId()));
            }
            if (StringUtils.hasText(operationLog.getUsername())) {
                predicates.add(criteriaBuilder.like(root.get("username"), "%" + operationLog.getUsername() + "%"));
            }
            if (StringUtils.hasText(operationLog.getOperationType())) {
                predicates.add(criteriaBuilder.equal(root.get("operationType"), operationLog.getOperationType()));
            }
            if (StringUtils.hasText(operationLog.getModule())) {
                predicates.add(criteriaBuilder.equal(root.get("module"), operationLog.getModule()));
            }
            if (StringUtils.hasText(operationLog.getBusinessType())) {
                predicates.add(criteriaBuilder.equal(root.get("businessType"), operationLog.getBusinessType()));
            }
            if (operationLog.getStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), operationLog.getStatus()));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        
        Page<SysOperationLog> page = operationLogRepository.findAll(spec, pageable);
        return page.getContent();
    }

    @Override
    public long count(SysOperationLog operationLog) {
        Specification<SysOperationLog> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            if (operationLog.getTenantId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("tenantId"), operationLog.getTenantId()));
            }
            if (operationLog.getUserId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("userId"), operationLog.getUserId()));
            }
            if (StringUtils.hasText(operationLog.getUsername())) {
                predicates.add(criteriaBuilder.like(root.get("username"), "%" + operationLog.getUsername() + "%"));
            }
            if (StringUtils.hasText(operationLog.getOperationType())) {
                predicates.add(criteriaBuilder.equal(root.get("operationType"), operationLog.getOperationType()));
            }
            if (StringUtils.hasText(operationLog.getModule())) {
                predicates.add(criteriaBuilder.equal(root.get("module"), operationLog.getModule()));
            }
            if (StringUtils.hasText(operationLog.getBusinessType())) {
                predicates.add(criteriaBuilder.equal(root.get("businessType"), operationLog.getBusinessType()));
            }
            if (operationLog.getStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), operationLog.getStatus()));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        
        return operationLogRepository.count(spec);
    }

    @Override
    @Transactional
    public int cleanExpiredLogs(int days) {
        try {
            LocalDateTime expireTime = LocalDateTime.now().minusDays(days);
            return operationLogRepository.deleteByCreatedTimeBefore(expireTime);
        } catch (Exception e) {
            log.error("清理过期操作日志失败", e);
            return 0;
        }
    }

    @Override
    public void exportOperationLogs(SysOperationLog operationLog, String filePath) {
        // TODO: 实现日志导出功能
        log.info("导出操作日志到文件: {}", filePath);
    }
} 