package com.dipspro.modules.system.dto;

import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;

/**
 * 菜单资源响应DTO
 */
@Data
public class MenuResourceResponseDto {

    private Long id;

    private Long tenantId;

    private String menuCode;

    private String menuName;

    private String menuType;

    private String menuTypeText;

    private Long parentId;

    private String parentName;

    private Integer level;

    private String path;

    private String component;

    private String icon;

    private Integer sortOrder;

    private Integer visible;

    private String visibleText;

    private Integer status;

    private String statusText;

    private Integer externalLink;

    private String externalLinkText;

    private Integer cache;

    private String cacheText;

    private String description;

    private LocalDateTime createdTime;

    private LocalDateTime updatedTime;

    private Long createdBy;

    private Long updatedBy;

    // 树形结构相关
    private List<MenuResourceResponseDto> children;

    private Boolean hasChildren;
} 