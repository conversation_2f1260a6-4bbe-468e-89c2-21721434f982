package com.dipspro.modules.system.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.system.entity.SysOperationLog;
import com.dipspro.modules.system.service.SysOperationLogService;

import lombok.extern.slf4j.Slf4j;

/**
 * 系统操作日志控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/system/operation-log")
public class SysOperationLogController {

    @Autowired
    private SysOperationLogService operationLogService;

    /**
     * 分页查询操作日志
     */
    @GetMapping("/page")
    public ApiResponse<Map<String, Object>> getPage(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            SysOperationLog operationLog) {
        
        try {
            List<SysOperationLog> list = operationLogService.getPage(operationLog, pageNum, pageSize);
            long total = operationLogService.count(operationLog);
            
            Map<String, Object> data = new HashMap<>();
            data.put("list", list);
            data.put("total", total);
            data.put("pageNum", pageNum);
            data.put("pageSize", pageSize);
            
            return ApiResponse.success(data, "查询操作日志成功");
        } catch (Exception e) {
            log.error("查询操作日志失败", e);
            return ApiResponse.error("查询操作日志失败");
        }
    }

    /**
     * 根据ID查询操作日志详情
     */
    @GetMapping("/{id}")
    public ApiResponse<SysOperationLog> getById(@PathVariable Long id) {
        try {
            SysOperationLog operationLog = operationLogService.getById(id);
            if (operationLog == null) {
                return ApiResponse.error("操作日志不存在");
            }
            return ApiResponse.success(operationLog, "查询操作日志详情成功");
        } catch (Exception e) {
            log.error("查询操作日志详情失败", e);
            return ApiResponse.error("查询操作日志详情失败");
        }
    }

    /**
     * 清理过期日志
     */
    @DeleteMapping("/clean")
    public ApiResponse<String> cleanExpiredLogs(@RequestParam(defaultValue = "30") int days) {
        try {
            int count = operationLogService.cleanExpiredLogs(days);
            return ApiResponse.success("清理完成，共清理 " + count + " 条记录");
        } catch (Exception e) {
            log.error("清理过期日志失败", e);
            return ApiResponse.error("清理过期日志失败");
        }
    }

    /**
     * 导出操作日志
     */
    @PostMapping("/export")
    public ApiResponse<String> exportLogs(
            @RequestBody SysOperationLog operationLog,
            @RequestParam String filePath) {
        try {
            operationLogService.exportOperationLogs(operationLog, filePath);
            return ApiResponse.success("导出成功");
        } catch (Exception e) {
            log.error("导出操作日志失败", e);
            return ApiResponse.error("导出操作日志失败");
        }
    }
} 