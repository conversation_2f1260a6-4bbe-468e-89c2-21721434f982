package com.dipspro.modules.system.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.system.entity.SysLoginLog;
import com.dipspro.modules.system.service.SysLoginLogService;

import lombok.extern.slf4j.Slf4j;

/**
 * 系统登录日志控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/system/login-log")
public class SysLoginLogController {

    @Autowired
    private SysLoginLogService loginLogService;

    /**
     * 分页查询登录日志
     */
    @GetMapping("/page")
    public ApiResponse<Map<String, Object>> getPage(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            SysLoginLog loginLog) {
        
        try {
            List<SysLoginLog> list = loginLogService.getPage(loginLog, pageNum, pageSize);
            long total = loginLogService.count(loginLog);
            
            Map<String, Object> data = new HashMap<>();
            data.put("list", list);
            data.put("total", total);
            data.put("pageNum", pageNum);
            data.put("pageSize", pageSize);
            
            return ApiResponse.success(data, "查询登录日志成功");
        } catch (Exception e) {
            log.error("查询登录日志失败", e);
            return ApiResponse.error("查询登录日志失败");
        }
    }

    /**
     * 根据ID查询登录日志详情
     */
    @GetMapping("/{id}")
    public ApiResponse<SysLoginLog> getById(@PathVariable Long id) {
        try {
            SysLoginLog loginLog = loginLogService.getById(id);
            if (loginLog == null) {
                return ApiResponse.error("登录日志不存在");
            }
            return ApiResponse.success(loginLog, "查询登录日志详情成功");
        } catch (Exception e) {
            log.error("查询登录日志详情失败", e);
            return ApiResponse.error("查询登录日志详情失败");
        }
    }

    /**
     * 查询用户最近登录记录
     */
    @GetMapping("/recent/{userId}")
    public ApiResponse<List<SysLoginLog>> getRecentLogins(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "10") int limit) {
        try {
            List<SysLoginLog> list = loginLogService.getRecentLoginsByUserId(userId, limit);
            return ApiResponse.success(list, "查询用户最近登录记录成功");
        } catch (Exception e) {
            log.error("查询用户最近登录记录失败", e);
            return ApiResponse.error("查询用户最近登录记录失败");
        }
    }

    /**
     * 获取登录统计信息
     */
    @GetMapping("/statistics")
    public ApiResponse<Object> getLoginStatistics(
            @RequestParam(required = false) Long tenantId,
            @RequestParam(defaultValue = "7") String dateRange) {
        try {
            Object statistics = loginLogService.getLoginStatistics(tenantId, dateRange);
            if (statistics == null) {
                return ApiResponse.error("获取登录统计信息失败");
            }
            return ApiResponse.success(statistics, "获取登录统计信息成功");
        } catch (Exception e) {
            log.error("获取登录统计信息失败", e);
            return ApiResponse.error("获取登录统计信息失败");
        }
    }

    /**
     * 清理过期日志
     */
    @DeleteMapping("/clean")
    public ApiResponse<String> cleanExpiredLogs(@RequestParam(defaultValue = "30") int days) {
        try {
            int count = loginLogService.cleanExpiredLogs(days);
            return ApiResponse.success("清理完成，共清理 " + count + " 条记录");
        } catch (Exception e) {
            log.error("清理过期登录日志失败", e);
            return ApiResponse.error("清理过期登录日志失败");
        }
    }
} 