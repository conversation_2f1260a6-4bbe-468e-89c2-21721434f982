package com.dipspro.modules.system.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.dipspro.modules.system.entity.SysLoginLog;
import com.dipspro.modules.system.repository.SysLoginLogRepository;
import com.dipspro.modules.system.service.SysLoginLogService;

import jakarta.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;

/**
 * 系统登录日志服务实现类
 */
@Slf4j
@Service
public class SysLoginLogServiceImpl implements SysLoginLogService {

    @Autowired
    private SysLoginLogRepository loginLogRepository;

    @Override
    public void recordLoginLog(SysLoginLog loginLog) {
        try {
            if (loginLog.getLoginTime() == null) {
                loginLog.setLoginTime(LocalDateTime.now());
            }
            loginLogRepository.save(loginLog);
        } catch (Exception e) {
            log.error("记录登录日志失败", e);
        }
    }

    @Override
    @Async("taskExecutor")
    public void recordLoginLogAsync(SysLoginLog loginLog) {
        recordLoginLog(loginLog);
    }

    @Override
    @Transactional
    public void updateLogoutTime(Long id) {
        try {
            loginLogRepository.updateLogoutTime(id, LocalDateTime.now());
        } catch (Exception e) {
            log.error("更新登出时间失败", e);
        }
    }

    @Override
    public SysLoginLog getById(Long id) {
        return loginLogRepository.findById(id).orElse(null);
    }

    @Override
    public List<SysLoginLog> getPage(SysLoginLog loginLog, int pageNum, int pageSize) {
        Pageable pageable = PageRequest.of(pageNum - 1, pageSize, Sort.by(Sort.Direction.DESC, "loginTime"));
        
        Specification<SysLoginLog> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            if (loginLog.getTenantId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("tenantId"), loginLog.getTenantId()));
            }
            if (loginLog.getUserId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("userId"), loginLog.getUserId()));
            }
            if (StringUtils.hasText(loginLog.getUsername())) {
                predicates.add(criteriaBuilder.like(root.get("username"), "%" + loginLog.getUsername() + "%"));
            }
            if (StringUtils.hasText(loginLog.getLoginType())) {
                predicates.add(criteriaBuilder.equal(root.get("loginType"), loginLog.getLoginType()));
            }
            if (StringUtils.hasText(loginLog.getIpAddress())) {
                predicates.add(criteriaBuilder.equal(root.get("ipAddress"), loginLog.getIpAddress()));
            }
            if (StringUtils.hasText(loginLog.getDeviceType())) {
                predicates.add(criteriaBuilder.equal(root.get("deviceType"), loginLog.getDeviceType()));
            }
            if (loginLog.getStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), loginLog.getStatus()));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        
        Page<SysLoginLog> page = loginLogRepository.findAll(spec, pageable);
        return page.getContent();
    }

    @Override
    public long count(SysLoginLog loginLog) {
        Specification<SysLoginLog> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            if (loginLog.getTenantId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("tenantId"), loginLog.getTenantId()));
            }
            if (loginLog.getUserId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("userId"), loginLog.getUserId()));
            }
            if (StringUtils.hasText(loginLog.getUsername())) {
                predicates.add(criteriaBuilder.like(root.get("username"), "%" + loginLog.getUsername() + "%"));
            }
            if (StringUtils.hasText(loginLog.getLoginType())) {
                predicates.add(criteriaBuilder.equal(root.get("loginType"), loginLog.getLoginType()));
            }
            if (StringUtils.hasText(loginLog.getIpAddress())) {
                predicates.add(criteriaBuilder.equal(root.get("ipAddress"), loginLog.getIpAddress()));
            }
            if (StringUtils.hasText(loginLog.getDeviceType())) {
                predicates.add(criteriaBuilder.equal(root.get("deviceType"), loginLog.getDeviceType()));
            }
            if (loginLog.getStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), loginLog.getStatus()));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        
        return loginLogRepository.count(spec);
    }

    @Override
    public List<SysLoginLog> getRecentLoginsByUserId(Long userId, int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return loginLogRepository.findRecentLoginsByUserId(userId, pageable);
    }

    @Override
    @Transactional
    public int cleanExpiredLogs(int days) {
        try {
            LocalDateTime expireTime = LocalDateTime.now().minusDays(days);
            return loginLogRepository.deleteByLoginTimeBefore(expireTime);
        } catch (Exception e) {
            log.error("清理过期登录日志失败", e);
            return 0;
        }
    }

    @Override
    public Object getLoginStatistics(Long tenantId, String dateRange) {
        try {
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusDays(Integer.parseInt(dateRange));
            
            Map<String, Object> statistics = new HashMap<>();
            
            // 统计成功登录次数
            long successCount = loginLogRepository.countSuccessLoginsByTimeBetween(startTime, endTime);
            statistics.put("successCount", successCount);
            
            // 统计失败登录次数
            long failedCount = loginLogRepository.countFailedLoginsByTimeBetween(startTime, endTime);
            statistics.put("failedCount", failedCount);
            
            // 计算成功率
            long totalCount = successCount + failedCount;
            double successRate = totalCount > 0 ? (double) successCount / totalCount * 100 : 0;
            statistics.put("successRate", Math.round(successRate * 100.0) / 100.0);
            
            statistics.put("totalCount", totalCount);
            statistics.put("dateRange", dateRange + "天");
            
            log.info("获取登录统计信息成功，租户ID: {}, 日期范围: {}天", tenantId, dateRange);
            return statistics;
        } catch (Exception e) {
            log.error("获取登录统计信息失败", e);
            return null;
        }
    }
} 