package com.dipspro.modules.system.service;

import java.util.List;

import com.dipspro.modules.system.entity.SysLoginLog;

/**
 * 系统登录日志服务接口
 */
public interface SysLoginLogService {

    /**
     * 记录登录日志
     */
    void recordLoginLog(SysLoginLog loginLog);

    /**
     * 异步记录登录日志
     */
    void recordLoginLogAsync(SysLoginLog loginLog);

    /**
     * 更新登出时间
     */
    void updateLogoutTime(Long id);

    /**
     * 根据ID查询登录日志
     */
    SysLoginLog getById(Long id);

    /**
     * 分页查询登录日志
     */
    List<SysLoginLog> getPage(SysLoginLog loginLog, int pageNum, int pageSize);

    /**
     * 统计登录日志数量
     */
    long count(SysLoginLog loginLog);

    /**
     * 查询用户最近登录记录
     */
    List<SysLoginLog> getRecentLoginsByUserId(Long userId, int limit);

    /**
     * 清理过期日志
     */
    int cleanExpiredLogs(int days);

    /**
     * 获取登录统计信息
     */
    Object getLoginStatistics(Long tenantId, String dateRange);
} 