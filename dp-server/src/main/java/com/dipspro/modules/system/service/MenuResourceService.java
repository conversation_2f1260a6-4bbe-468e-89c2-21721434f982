package com.dipspro.modules.system.service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.dipspro.modules.system.dto.MenuResourceCreateDto;
import com.dipspro.modules.system.dto.MenuResourceQueryDto;
import com.dipspro.modules.system.dto.MenuResourceResponseDto;
import com.dipspro.modules.system.dto.MenuResourceUpdateDto;
import com.dipspro.modules.system.entity.MenuResourceEntity;
import com.dipspro.modules.system.repository.MenuResourceRepository;

import jakarta.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;

/**
 * 菜单资源Service
 */
@Service
@Transactional
@Slf4j
public class MenuResourceService {

    @Autowired
    private MenuResourceRepository menuResourceRepository;

    /**
     * 分页查询菜单资源列表
     */
    public Page<MenuResourceResponseDto> getMenuResourceList(MenuResourceQueryDto queryDto) {
        // 构建查询条件
        Specification<MenuResourceEntity> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 未删除
            predicates.add(criteriaBuilder.equal(root.get("deleted"), 0));

            // 菜单编码
            if (StringUtils.hasText(queryDto.getMenuCode())) {
                predicates.add(criteriaBuilder.like(root.get("menuCode"), "%" + queryDto.getMenuCode() + "%"));
            }

            // 菜单名称
            if (StringUtils.hasText(queryDto.getMenuName())) {
                predicates.add(criteriaBuilder.like(root.get("menuName"), "%" + queryDto.getMenuName() + "%"));
            }

            // 菜单类型
            if (StringUtils.hasText(queryDto.getMenuType())) {
                predicates.add(criteriaBuilder.equal(root.get("menuType"), queryDto.getMenuType()));
            }

            // 父菜单ID
            if (queryDto.getParentId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("parentId"), queryDto.getParentId()));
            }

            // 层级
            if (queryDto.getLevel() != null) {
                predicates.add(criteriaBuilder.equal(root.get("level"), queryDto.getLevel()));
            }

            // 是否可见
            if (queryDto.getVisible() != null) {
                predicates.add(criteriaBuilder.equal(root.get("visible"), queryDto.getVisible()));
            }

            // 状态
            if (queryDto.getStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), queryDto.getStatus()));
            }

            // 是否外链
            if (queryDto.getExternalLink() != null) {
                predicates.add(criteriaBuilder.equal(root.get("externalLink"), queryDto.getExternalLink()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        // 构建排序
        Sort sort = Sort.by(Sort.Direction.ASC, "sortOrder");
        if (StringUtils.hasText(queryDto.getSortField())) {
            Sort.Direction direction = "desc".equalsIgnoreCase(queryDto.getSortOrder()) 
                ? Sort.Direction.DESC : Sort.Direction.ASC;
            sort = Sort.by(direction, queryDto.getSortField());
        }

        // 分页查询 (前端页码从1开始，Spring Data JPA从0开始，需要减1)
        int pageIndex = Math.max(0, queryDto.getPage() - 1);
        Pageable pageable = PageRequest.of(pageIndex, queryDto.getSize(), sort);
        Page<MenuResourceEntity> page = menuResourceRepository.findAll(spec, pageable);

        // 转换为响应DTO
        return page.map(this::convertToResponseDto);
    }

    /**
     * 根据ID获取菜单资源详情
     */
    public MenuResourceResponseDto getMenuResourceById(Long id) {
        Optional<MenuResourceEntity> optional = menuResourceRepository.findById(id);
        if (optional.isPresent() && optional.get().getDeleted() == 0) {
            return convertToResponseDto(optional.get());
        }
        throw new RuntimeException("菜单资源不存在");
    }

    /**
     * 创建菜单资源
     */
    public MenuResourceResponseDto createMenuResource(MenuResourceCreateDto createDto) {
        // 检查菜单编码是否已存在
        if (checkMenuCodeExists(createDto.getMenuCode(), null)) {
            throw new RuntimeException("菜单编码已存在");
        }

        MenuResourceEntity entity = new MenuResourceEntity();
        BeanUtils.copyProperties(createDto, entity);
        
        // 设置创建时间
        LocalDateTime now = LocalDateTime.now();
        entity.setCreatedTime(now);
        entity.setUpdatedTime(now);
        
        // 如果有父菜单，计算层级
        if (createDto.getParentId() != null) {
            Optional<MenuResourceEntity> parent = menuResourceRepository.findById(createDto.getParentId());
            if (parent.isPresent()) {
                entity.setLevel(parent.get().getLevel() + 1);
            }
        }

        entity = menuResourceRepository.save(entity);
        return convertToResponseDto(entity);
    }

    /**
     * 更新菜单资源
     */
    public MenuResourceResponseDto updateMenuResource(Long id, MenuResourceUpdateDto updateDto) {
        Optional<MenuResourceEntity> optional = menuResourceRepository.findById(id);
        if (!optional.isPresent() || optional.get().getDeleted() == 1) {
            throw new RuntimeException("菜单资源不存在");
        }

        MenuResourceEntity entity = optional.get();

        // 检查菜单编码是否已存在（排除当前记录）
        if (StringUtils.hasText(updateDto.getMenuCode()) && 
            checkMenuCodeExists(updateDto.getMenuCode(), id)) {
            throw new RuntimeException("菜单编码已存在");
        }

        // 更新字段
        if (StringUtils.hasText(updateDto.getMenuCode())) {
            entity.setMenuCode(updateDto.getMenuCode());
        }
        if (StringUtils.hasText(updateDto.getMenuName())) {
            entity.setMenuName(updateDto.getMenuName());
        }
        if (StringUtils.hasText(updateDto.getMenuType())) {
            entity.setMenuType(updateDto.getMenuType());
        }
        if (updateDto.getParentId() != null) {
            entity.setParentId(updateDto.getParentId());
            // 重新计算层级
            if (updateDto.getParentId() != 0) {
                Optional<MenuResourceEntity> parent = menuResourceRepository.findById(updateDto.getParentId());
                if (parent.isPresent()) {
                    entity.setLevel(parent.get().getLevel() + 1);
                }
            } else {
                entity.setLevel(1);
            }
        }
        if (updateDto.getLevel() != null) {
            entity.setLevel(updateDto.getLevel());
        }
        if (StringUtils.hasText(updateDto.getPath())) {
            entity.setPath(updateDto.getPath());
        }
        if (StringUtils.hasText(updateDto.getComponent())) {
            entity.setComponent(updateDto.getComponent());
        }
        if (StringUtils.hasText(updateDto.getIcon())) {
            entity.setIcon(updateDto.getIcon());
        }
        if (updateDto.getSortOrder() != null) {
            entity.setSortOrder(updateDto.getSortOrder());
        }
        if (updateDto.getVisible() != null) {
            entity.setVisible(updateDto.getVisible());
        }
        if (updateDto.getStatus() != null) {
            entity.setStatus(updateDto.getStatus());
        }
        if (updateDto.getExternalLink() != null) {
            entity.setExternalLink(updateDto.getExternalLink());
        }
        if (updateDto.getCache() != null) {
            entity.setCache(updateDto.getCache());
        }
        if (StringUtils.hasText(updateDto.getDescription())) {
            entity.setDescription(updateDto.getDescription());
        }

        entity.setUpdatedTime(LocalDateTime.now());
        entity = menuResourceRepository.save(entity);
        return convertToResponseDto(entity);
    }

    /**
     * 删除菜单资源
     */
    public void deleteMenuResource(Long id) {
        Optional<MenuResourceEntity> optional = menuResourceRepository.findById(id);
        if (!optional.isPresent() || optional.get().getDeleted() == 1) {
            throw new RuntimeException("菜单资源不存在");
        }

        // 检查是否有子菜单
        List<MenuResourceEntity> children = menuResourceRepository.findByParentIdAndDeletedOrderBySortOrderAsc(id, 0);
        if (!children.isEmpty()) {
            throw new RuntimeException("存在子菜单，无法删除");
        }

        MenuResourceEntity entity = optional.get();
        entity.setDeleted(1);
        entity.setUpdatedTime(LocalDateTime.now());
        menuResourceRepository.save(entity);
    }

    /**
     * 批量删除菜单资源
     */
    public void deleteMenuResources(List<Long> ids) {
        for (Long id : ids) {
            deleteMenuResource(id);
        }
    }

    /**
     * 更新菜单资源状态
     */
    public void updateMenuResourceStatus(Long id, Integer status) {
        Optional<MenuResourceEntity> optional = menuResourceRepository.findById(id);
        if (!optional.isPresent() || optional.get().getDeleted() == 1) {
            throw new RuntimeException("菜单资源不存在");
        }

        MenuResourceEntity entity = optional.get();
        entity.setStatus(status);
        entity.setUpdatedTime(LocalDateTime.now());
        menuResourceRepository.save(entity);
    }

    /**
     * 检查菜单编码是否存在
     */
    public boolean checkMenuCodeExists(String menuCode, Long excludeId) {
        return menuResourceRepository.existsByMenuCodeAndNotDeleted(menuCode, excludeId);
    }

    /**
     * 获取所有启用的菜单
     */
    public List<MenuResourceResponseDto> getAllActiveMenuResources() {
        List<MenuResourceEntity> entities = menuResourceRepository.findAllActiveMenus();
        return entities.stream()
                .map(this::convertToResponseDto)
                .collect(Collectors.toList());
    }

    /**
     * 获取菜单树形结构
     */
    public List<MenuResourceResponseDto> getMenuResourceTree() {
        List<MenuResourceEntity> allMenus = menuResourceRepository.findAllActiveMenus();
        return buildMenuTree(allMenus, null);
    }

    /**
     * 根据菜单类型获取菜单列表
     */
    public List<MenuResourceResponseDto> getMenuResourcesByType(String menuType) {
        List<MenuResourceEntity> entities = menuResourceRepository.findByMenuTypeAndDeletedOrderBySortOrderAsc(menuType, 0);
        return entities.stream()
                .map(this::convertToResponseDto)
                .collect(Collectors.toList());
    }

    /**
     * 构建菜单树形结构
     */
    private List<MenuResourceResponseDto> buildMenuTree(List<MenuResourceEntity> allMenus, Long parentId) {
        List<MenuResourceResponseDto> result = new ArrayList<>();
        
        for (MenuResourceEntity menu : allMenus) {
            if ((parentId == null && menu.getParentId() == null) || 
                (parentId != null && parentId.equals(menu.getParentId()))) {
                
                MenuResourceResponseDto dto = convertToResponseDto(menu);
                List<MenuResourceResponseDto> children = buildMenuTree(allMenus, menu.getId());
                dto.setChildren(children);
                dto.setHasChildren(!children.isEmpty());
                result.add(dto);
            }
        }
        
        return result;
    }

    /**
     * 转换为响应DTO
     */
    private MenuResourceResponseDto convertToResponseDto(MenuResourceEntity entity) {
        MenuResourceResponseDto dto = new MenuResourceResponseDto();
        BeanUtils.copyProperties(entity, dto);

        // 设置文本字段
        dto.setMenuTypeText(getMenuTypeText(entity.getMenuType()));
        dto.setVisibleText(entity.getVisible() == 1 ? "显示" : "隐藏");
        dto.setStatusText(getStatusText(entity.getStatus()));
        dto.setExternalLinkText(entity.getExternalLink() == 1 ? "是" : "否");
        dto.setCacheText(entity.getCache() == 1 ? "是" : "否");

        // 设置父菜单名称
        if (entity.getParentId() != null) {
            Optional<MenuResourceEntity> parent = menuResourceRepository.findById(entity.getParentId());
            if (parent.isPresent()) {
                dto.setParentName(parent.get().getMenuName());
            }
        }

        return dto;
    }

    /**
     * 获取菜单类型文本
     */
    private String getMenuTypeText(String menuType) {
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("catalog", "目录");
        typeMap.put("menu", "菜单");
        typeMap.put("button", "按钮");
        return typeMap.getOrDefault(menuType, "未知");
    }

    /**
     * 获取状态文本
     */
    private String getStatusText(Integer status) {
        Map<Integer, String> statusMap = new HashMap<>();
        statusMap.put(0, "禁用");
        statusMap.put(1, "启用");
        return statusMap.getOrDefault(status, "未知");
    }
} 