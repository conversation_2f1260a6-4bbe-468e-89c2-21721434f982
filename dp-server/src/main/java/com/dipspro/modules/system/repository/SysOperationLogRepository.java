package com.dipspro.modules.system.repository;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.dipspro.modules.system.entity.SysOperationLog;

/**
 * 系统操作日志Repository接口
 */
@Repository
public interface SysOperationLogRepository extends JpaRepository<SysOperationLog, Long>, JpaSpecificationExecutor<SysOperationLog> {

    /**
     * 根据租户ID查询操作日志
     */
    Page<SysOperationLog> findByTenantIdOrderByCreatedTimeDesc(Long tenantId, Pageable pageable);

    /**
     * 根据用户ID查询操作日志
     */
    Page<SysOperationLog> findByUserIdOrderByCreatedTimeDesc(Long userId, Pageable pageable);

    /**
     * 根据操作类型查询操作日志
     */
    Page<SysOperationLog> findByOperationTypeOrderByCreatedTimeDesc(String operationType, Pageable pageable);

    /**
     * 根据模块查询操作日志
     */
    Page<SysOperationLog> findByModuleOrderByCreatedTimeDesc(String module, Pageable pageable);

    /**
     * 根据状态查询操作日志
     */
    Page<SysOperationLog> findByStatusOrderByCreatedTimeDesc(Integer status, Pageable pageable);

    /**
     * 根据用户名模糊查询操作日志
     */
    Page<SysOperationLog> findByUsernameContainingOrderByCreatedTimeDesc(String username, Pageable pageable);

    /**
     * 根据时间范围查询操作日志
     */
    Page<SysOperationLog> findByCreatedTimeBetweenOrderByCreatedTimeDesc(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * 清理过期日志
     */
    @Modifying
    @Query("DELETE FROM SysOperationLog s WHERE s.createdTime < :expireTime")
    int deleteByCreatedTimeBefore(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 统计指定时间范围内的操作日志数量
     */
    @Query("SELECT COUNT(s) FROM SysOperationLog s WHERE s.createdTime BETWEEN :startTime AND :endTime")
    long countByCreatedTimeBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 根据租户ID和时间范围查询操作日志
     */
    Page<SysOperationLog> findByTenantIdAndCreatedTimeBetweenOrderByCreatedTimeDesc(
            Long tenantId, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * 查询最近的操作日志
     */
    List<SysOperationLog> findTop10ByOrderByCreatedTimeDesc();
} 