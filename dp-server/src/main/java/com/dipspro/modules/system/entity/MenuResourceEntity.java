package com.dipspro.modules.system.entity;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 菜单资源实体类
 */
@Entity
@Table(name = "sys_menu_resource")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MenuResourceEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "menu_code", nullable = false, length = 64)
    private String menuCode;

    @Column(name = "menu_name", nullable = false, length = 128)
    private String menuName;

    @Column(name = "menu_type", nullable = false, length = 32)
    private String menuType;

    @Column(name = "parent_id")
    private Long parentId;

    @Column(name = "level")
    private Integer level = 1;

    @Column(name = "path", length = 256)
    private String path;

    @Column(name = "component", length = 256)
    private String component;

    @Column(name = "icon", length = 128)
    private String icon;

    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    @Column(name = "visible", nullable = false)
    private Integer visible = 1;

    @Column(name = "status", nullable = false)
    private Integer status = 1;

    @Column(name = "external_link")
    private Integer externalLink = 0;

    @Column(name = "cache")
    private Integer cache = 0;

    @Column(name = "description", length = 512)
    private String description;

    @Column(name = "created_time", nullable = false)
    private LocalDateTime createdTime;

    @Column(name = "updated_time", nullable = false)
    private LocalDateTime updatedTime;

    @Column(name = "created_by")
    private Long createdBy;

    @Column(name = "updated_by")
    private Long updatedBy;

    @Column(name = "deleted", nullable = false)
    private Integer deleted = 0;
} 