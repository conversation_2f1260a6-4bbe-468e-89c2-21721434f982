package com.dipspro.modules.route.controller;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.route.service.RouteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import jakarta.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * 路由转发控制器
 * 提供统一的API入口，将请求转发到对应的服务
 */
@Slf4j
@RestController
@RequestMapping("/api/route")
@RequiredArgsConstructor
public class RouteController {
    
    private final RouteService routeService;
    
    /**
     * 处理所有HTTP方法的路由转发请求
     * 
     * @param serviceName 服务名称
     * @param request HTTP请求
     * @param requestParams 请求参数
     * @return 转发后的响应
     */
    @RequestMapping(value = "/{serviceName}/**", method = {
            RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, 
            RequestMethod.DELETE, RequestMethod.PATCH, RequestMethod.HEAD, 
            RequestMethod.OPTIONS
    })
    public Mono<ResponseEntity<String>> forwardRequest(
            @PathVariable String serviceName,
            HttpServletRequest request,
            @RequestParam(required = false) MultiValueMap<String, String> requestParams) {
        
        try {
            // 提取剩余路径
            String fullPath = request.getRequestURI();
            String basePath = "/api/route/" + serviceName;
            String remainingPath = fullPath.substring(basePath.length());
            
            // 获取HTTP方法
            HttpMethod method = HttpMethod.valueOf(request.getMethod());
            
            // 获取请求头
            Map<String, String> headers = new HashMap<>();
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                headers.put(headerName, request.getHeader(headerName));
            }
            
            // 读取请求体
            String body = readRequestBody(request);
            
            // 构建查询参数字符串
            String queryString = URLDecoder.decode(request.getQueryString(), "UTF-8");

            log.info("转发请求: {} {} -> 服务: {}, 路径: {}", 
                    method, fullPath, serviceName, remainingPath);
            
            // 调用路由服务进行转发
            return routeService.forwardRequest(
                    serviceName, remainingPath, method, headers, body, queryString
            );
            
        } catch (Exception e) {
            log.error("路由转发失败: {}", e.getMessage(), e);
            return Mono.just(ResponseEntity.internalServerError()
                    .body("{\"error\":\"路由转发失败: " + e.getMessage() + "\"}"));
        }
    }
    
    /**
     * 健康检查接口
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public ApiResponse<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "route-controller");
        health.put("timestamp", System.currentTimeMillis());
        return ApiResponse.success(health, "路由服务运行正常");
    }
    
    /**
     * 读取请求体内容
     * 
     * @param request HTTP请求
     * @return 请求体字符串
     * @throws IOException IO异常
     */
    private String readRequestBody(HttpServletRequest request) throws IOException {
        StringBuilder body = new StringBuilder();
        try (BufferedReader reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
        }
        return body.toString();
    }
}