package com.dipspro.modules.route.service;

import com.dipspro.config.RouteConfig;
import com.dipspro.config.RouteConfig.ServiceConfig;
import com.dipspro.config.RouteConfig.AuthType;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 路由服务
 * 负责API请求转发和认证处理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RouteService {
    
    private final RouteConfig routeConfig;
    private final WebClient.Builder webClientBuilder;
    private final StringRedisTemplate redisTemplate;
    private final ObjectMapper objectMapper;
    
    // Token缓存前缀
    private static final String TOKEN_CACHE_PREFIX = "route:token:";
    // Token缓存过期时间(秒)
    private static final long TOKEN_CACHE_EXPIRE = 3600;
    
    /**
     * 转发请求到目标服务
     * 
     * @param serviceName 服务名称
     * @param path 请求路径
     * @param method HTTP方法
     * @param headers 请求头
     * @param body 请求体
     * @param queryString 查询参数字符串
     * @return 响应结果
     */
    public Mono<ResponseEntity<String>> forwardRequest(
            String serviceName, String path, HttpMethod method, 
            Map<String, String> headers, String body, String queryString) {
        
        ServiceConfig serviceConfig = routeConfig.getServices().get(serviceName);
        if (serviceConfig == null || !serviceConfig.isEnabled()) {
            log.warn("服务 {} 不存在或未启用", serviceName);
            return Mono.just(ResponseEntity.notFound().build());
        }
        
        // 路径重写
        String targetPath = rewritePath(path, serviceConfig.getPathRewrite());
        
        // 构建目标URL
        String targetUrl = serviceConfig.getBaseUrl() + targetPath;
        if (StringUtils.hasText(queryString)) {
            targetUrl += "?" + queryString;
        }
        
        final String finalTargetUrl = targetUrl;
        final HttpMethod finalMethod = method;
        final String finalBody = body;
        
        log.info("转发请求到: {} {}", finalMethod, finalTargetUrl);
        
        // 转换headers为HttpHeaders
        HttpHeaders httpHeaders = new HttpHeaders();
        if (headers != null) {
            headers.forEach(httpHeaders::set);
        }
        
        // 处理认证并转发请求
        return handleAuthentication(serviceConfig, httpHeaders)
                .flatMap(authHeaders -> {
                    WebClient webClient = webClientBuilder.build();
                    
                    WebClient.RequestBodySpec requestSpec = webClient
                            .method(finalMethod)
                            .uri(finalTargetUrl)
                            .headers(requestHeaders -> {
                                // 添加原始请求头（排除认证相关）
                                httpHeaders.forEach((key, values) -> {
                                    if (!isAuthHeader(key)) {
                                        requestHeaders.addAll(key, values);
                                    }
                                });
                                // 添加认证头
                                authHeaders.forEach(requestHeaders::set);
                            });
                    
                    // 添加请求体
                    if (StringUtils.hasText(finalBody) && 
                        (finalMethod == HttpMethod.POST || finalMethod == HttpMethod.PUT || finalMethod == HttpMethod.PATCH)) {
                        requestSpec.bodyValue(finalBody);
                    }
                    
                    // 发送请求并处理响应
                    return requestSpec
                            .retrieve()
                            .toEntity(String.class)
                            .timeout(Duration.ofMillis(serviceConfig.getReadTimeout()))
                            .onErrorResume(WebClientResponseException.class, ex -> {
                                log.error("请求转发失败: {} {}, 状态码: {}, 响应: {}", 
                                        finalMethod, finalTargetUrl, ex.getStatusCode(), ex.getResponseBodyAsString());
                                return Mono.just(ResponseEntity.status(ex.getStatusCode())
                                        .headers(ex.getHeaders())
                                        .body(ex.getResponseBodyAsString()));
                            })
                            .onErrorResume(Exception.class, ex -> {
                                log.error("请求转发异常: {} {}", finalMethod, finalTargetUrl, ex);
                                return Mono.just(ResponseEntity.internalServerError()
                                        .body("{\"error\":\"Internal server error: " + ex.getMessage() + "\"}"));
                            });
                })
                .doOnSuccess(response -> log.info("请求转发成功: {} {}, 状态码: {}", 
                        finalMethod, finalTargetUrl, response.getStatusCode()))
                .doOnError(ex -> log.error("请求转发失败: {} {}", finalMethod, finalTargetUrl, ex));
    }
    
    /**
     * 处理认证
     * 
     * @param serviceConfig 服务配置
     * @param originalHeaders 原始请求头
     * @return 认证后的请求头
     */
    private Mono<Map<String, String>> handleAuthentication(ServiceConfig serviceConfig, HttpHeaders originalHeaders) {
        Map<String, String> authHeaders = new HashMap<>();
        
        switch (serviceConfig.getAuthType()) {
            case NONE:
                return Mono.just(authHeaders);
                
            case BASIC:
                if (StringUtils.hasText(serviceConfig.getUsername()) && 
                    StringUtils.hasText(serviceConfig.getPassword())) {
                    String credentials = serviceConfig.getUsername() + ":" + serviceConfig.getPassword();
                    String encodedCredentials = Base64.getEncoder().encodeToString(credentials.getBytes());
                    authHeaders.put(HttpHeaders.AUTHORIZATION, "Basic " + encodedCredentials);
                }
                return Mono.just(authHeaders);
                
            case JWT:
                // 使用当前用户的JWT token
                String jwtToken = originalHeaders.getFirst(HttpHeaders.AUTHORIZATION);
                if (StringUtils.hasText(jwtToken)) {
                    authHeaders.put(HttpHeaders.AUTHORIZATION, jwtToken);
                }
                return Mono.just(authHeaders);
                
            case TOKEN:
                // 获取或刷新token
                return getOrRefreshToken(serviceConfig)
                        .map(token -> {
                            authHeaders.put(HttpHeaders.AUTHORIZATION, "Bearer " + token);
                            return authHeaders;
                        })
                        .onErrorReturn(authHeaders);
                        
            default:
                return Mono.just(authHeaders);
        }
    }
    
    /**
     * 获取或刷新token
     * 
     * @param serviceConfig 服务配置
     * @return token
     */
    private Mono<String> getOrRefreshToken(ServiceConfig serviceConfig) {
        String cacheKey = TOKEN_CACHE_PREFIX + serviceConfig.getBaseUrl();
        
        // 先从缓存获取
        String cachedToken = redisTemplate.opsForValue().get(cacheKey);
        if (StringUtils.hasText(cachedToken)) {
            log.debug("使用缓存的token: {}", cacheKey);
            return Mono.just(cachedToken);
        }
        
        // 缓存中没有，重新获取
        log.info("获取新的token: {}", serviceConfig.getTokenUrl());
        
        WebClient tokenClient = webClientBuilder.build();
        
        Map<String, String> tokenRequest = new HashMap<>();
        tokenRequest.put("client_id", serviceConfig.getClientId());
        tokenRequest.put("client_secret", serviceConfig.getClientSecret());
        tokenRequest.put("grant_type", "client_credentials");
        
        return tokenClient.post()
                .uri(serviceConfig.getTokenUrl())
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(tokenRequest)
                .retrieve()
                .bodyToMono(Map.class)
                .map(response -> {
                    String token = (String) response.get("access_token");
                    if (StringUtils.hasText(token)) {
                        // 缓存token
                        redisTemplate.opsForValue().set(cacheKey, token, TOKEN_CACHE_EXPIRE, TimeUnit.SECONDS);
                        log.info("获取token成功并缓存: {}", cacheKey);
                        return token;
                    } else {
                        throw new RuntimeException("获取token失败: 响应中没有access_token");
                    }
                })
                .onErrorMap(ex -> {
                    log.error("获取token失败: {}", serviceConfig.getTokenUrl(), ex);
                    return new RuntimeException("获取token失败: " + ex.getMessage(), ex);
                });
    }
    
    /**
     * 路径重写
     * 
     * @param originalPath 原始路径
     * @param rewriteRules 重写规则
     * @return 重写后的路径
     */
    private String rewritePath(String originalPath, Map<String, String> rewriteRules) {
        if (rewriteRules == null || rewriteRules.isEmpty()) {
            return originalPath;
        }
        
        for (Map.Entry<String, String> rule : rewriteRules.entrySet()) {
            String pattern = rule.getKey();
            String replacement = rule.getValue();
            if (originalPath.matches(pattern)) {
                String rewrittenPath = originalPath.replaceAll(pattern, replacement);
                log.debug("路径重写: {} -> {}", originalPath, rewrittenPath);
                return rewrittenPath;
            }
        }
        
        return originalPath;
    }
    
    /**
     * 判断是否为认证相关的请求头
     * 
     * @param headerName 请求头名称
     * @return 是否为认证头
     */
    private boolean isAuthHeader(String headerName) {
        return HttpHeaders.AUTHORIZATION.equalsIgnoreCase(headerName) ||
               "X-Auth-Token".equalsIgnoreCase(headerName) ||
               "X-API-Key".equalsIgnoreCase(headerName);
    }
}