package com.dipspro.modules.route.service;

import com.dipspro.config.RouteConfig;
import com.dipspro.config.RouteConfig.ServiceConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 路由管理服务
 * 提供路由配置管理和健康检查功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RouteManagementService {
    
    private final RouteConfig routeConfig;
    private final WebClient.Builder webClientBuilder;
    private final StringRedisTemplate redisTemplate;
    
    // 健康检查缓存前缀
    private static final String HEALTH_CACHE_PREFIX = "route:health:";
    // 健康检查缓存过期时间(秒)
    private static final long HEALTH_CACHE_EXPIRE = 60;
    
    /**
     * 获取所有路由服务配置
     * 
     * @return 路由服务配置映射
     */
    public Map<String, ServiceConfig> getAllServices() {
        return routeConfig.getServices();
    }
    
    /**
     * 获取指定服务配置
     * 
     * @param serviceName 服务名称
     * @return 服务配置
     */
    public ServiceConfig getServiceConfig(String serviceName) {
        return routeConfig.getServices().get(serviceName);
    }
    
    /**
     * 检查服务是否可用
     * 
     * @param serviceName 服务名称
     * @return 是否可用
     */
    public boolean isServiceEnabled(String serviceName) {
        ServiceConfig config = getServiceConfig(serviceName);
        return config != null && config.isEnabled();
    }
    
    /**
     * 检查所有服务的健康状态
     * 
     * @return 健康状态映射
     */
    public Mono<Map<String, Object>> checkAllServicesHealth() {
        Map<String, Object> healthStatus = new HashMap<>();
        
        return Mono.fromCallable(() -> {
            routeConfig.getServices().forEach((serviceName, config) -> {
                if (config.isEnabled()) {
                    // 异步检查每个服务的健康状态
                    checkServiceHealth(serviceName)
                            .subscribe(
                                    status -> healthStatus.put(serviceName, status),
                                    error -> {
                                        log.error("检查服务 {} 健康状态失败", serviceName, error);
                                        Map<String, Object> errorStatus = new HashMap<>();
                                        errorStatus.put("status", "DOWN");
                                        errorStatus.put("error", error.getMessage());
                                        healthStatus.put(serviceName, errorStatus);
                                    }
                            );
                } else {
                    Map<String, Object> disabledStatus = new HashMap<>();
                    disabledStatus.put("status", "DISABLED");
                    healthStatus.put(serviceName, disabledStatus);
                }
            });
            return healthStatus;
        });
    }
    
    /**
     * 检查单个服务的健康状态
     * 
     * @param serviceName 服务名称
     * @return 健康状态
     */
    public Mono<Map<String, Object>> checkServiceHealth(String serviceName) {
        ServiceConfig config = getServiceConfig(serviceName);
        if (config == null || !config.isEnabled()) {
            Map<String, Object> result = new HashMap<>();
            result.put("status", config == null ? "NOT_FOUND" : "DISABLED");
            return Mono.just(result);
        }
        
        String cacheKey = HEALTH_CACHE_PREFIX + serviceName;
        
        // 先检查缓存
        String cachedStatus = redisTemplate.opsForValue().get(cacheKey);
        if (cachedStatus != null) {
            Map<String, Object> result = new HashMap<>();
            result.put("status", cachedStatus);
            result.put("cached", true);
            return Mono.just(result);
        }
        
        // 缓存中没有，进行健康检查
        WebClient webClient = webClientBuilder.build();
        
        return webClient.get()
                .uri(config.getBaseUrl() + "/health")
                .retrieve()
                .toEntity(String.class)
                .timeout(Duration.ofMillis(config.getConnectTimeout()))
                .map(response -> {
                    Map<String, Object> result = new HashMap<>();
                    if (response.getStatusCode().is2xxSuccessful()) {
                        result.put("status", "UP");
                        result.put("responseTime", System.currentTimeMillis());
                        // 缓存健康状态
                        redisTemplate.opsForValue().set(cacheKey, "UP", HEALTH_CACHE_EXPIRE, TimeUnit.SECONDS);
                    } else {
                        result.put("status", "DOWN");
                        result.put("statusCode", response.getStatusCode().value());
                    }
                    return result;
                })
                .onErrorResume(error -> {
                    log.warn("服务 {} 健康检查失败: {}", serviceName, error.getMessage());
                    Map<String, Object> result = new HashMap<>();
                    result.put("status", "DOWN");
                    result.put("error", error.getMessage());
                    // 缓存DOWN状态，但时间较短
                    redisTemplate.opsForValue().set(cacheKey, "DOWN", 30, TimeUnit.SECONDS);
                    return Mono.just(result);
                });
    }
    
    /**
     * 清除服务健康检查缓存
     * 
     * @param serviceName 服务名称，为null时清除所有缓存
     */
    public void clearHealthCache(String serviceName) {
        if (serviceName != null) {
            String cacheKey = HEALTH_CACHE_PREFIX + serviceName;
            redisTemplate.delete(cacheKey);
            log.info("清除服务 {} 的健康检查缓存", serviceName);
        } else {
            // 清除所有健康检查缓存
            redisTemplate.delete(redisTemplate.keys(HEALTH_CACHE_PREFIX + "*"));
            log.info("清除所有服务的健康检查缓存");
        }
    }
    
    /**
     * 获取路由统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> getRouteStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        long totalServices = routeConfig.getServices().size();
        long enabledServices = routeConfig.getServices().values().stream()
                .mapToLong(config -> config.isEnabled() ? 1 : 0)
                .sum();
        
        stats.put("totalServices", totalServices);
        stats.put("enabledServices", enabledServices);
        stats.put("disabledServices", totalServices - enabledServices);
        
        // 按认证类型分组统计
        Map<String, Long> authTypeStats = new HashMap<>();
        routeConfig.getServices().values().forEach(config -> {
            String authType = config.getAuthType().name();
            authTypeStats.put(authType, authTypeStats.getOrDefault(authType, 0L) + 1);
        });
        stats.put("authTypeDistribution", authTypeStats);
        
        return stats;
    }
}