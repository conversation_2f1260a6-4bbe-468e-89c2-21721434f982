package com.dipspro.modules.route.controller;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.config.RouteConfig.ServiceConfig;
import com.dipspro.modules.route.service.RouteManagementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * 路由管理控制器
 * 提供路由配置查看和管理功能
 */
@Slf4j
@RestController
@RequestMapping("/api/route/management")
@RequiredArgsConstructor
public class RouteManagementController {
    
    private final RouteManagementService routeManagementService;
    
    /**
     * 获取所有服务配置
     * 
     * @return 服务配置列表
     */
    @GetMapping("/services")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Map<String, ServiceConfig>> getAllServices() {
        Map<String, ServiceConfig> services = routeManagementService.getAllServices();
        return ApiResponse.success(services, "获取服务配置成功");
    }
    
    /**
     * 获取指定服务配置
     * 
     * @param serviceName 服务名称
     * @return 服务配置
     */
    @GetMapping("/services/{serviceName}")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<ServiceConfig> getServiceConfig(@PathVariable String serviceName) {
        ServiceConfig config = routeManagementService.getServiceConfig(serviceName);
        if (config != null) {
            return ApiResponse.success(config, "获取服务配置成功");
        } else {
            return ApiResponse.error("服务配置不存在: " + serviceName);
        }
    }
    
    /**
     * 检查所有服务健康状态
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public Mono<ApiResponse<Map<String, Object>>> checkAllServicesHealth() {
        return routeManagementService.checkAllServicesHealth()
                .map(healthStatus -> ApiResponse.success(healthStatus, "健康检查完成"))
                .onErrorReturn(ApiResponse.error("健康检查失败"));
    }
    
    /**
     * 检查指定服务健康状态
     * 
     * @param serviceName 服务名称
     * @return 健康状态
     */
    @GetMapping("/health/{serviceName}")
    public Mono<ApiResponse<Map<String, Object>>> checkServiceHealth(@PathVariable String serviceName) {
        return routeManagementService.checkServiceHealth(serviceName)
                .map(healthStatus -> ApiResponse.success(healthStatus, "服务健康检查完成"))
                .onErrorReturn(ApiResponse.error("服务健康检查失败: " + serviceName));
    }
    
    /**
     * 清除健康检查缓存
     * 
     * @param serviceName 服务名称，为空时清除所有缓存
     * @return 操作结果
     */
    @DeleteMapping("/health/cache")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<String> clearHealthCache(
            @RequestParam(required = false) String serviceName) {
        try {
            routeManagementService.clearHealthCache(serviceName);
            String message = serviceName != null ? 
                    "清除服务 " + serviceName + " 的健康检查缓存成功" : 
                    "清除所有服务的健康检查缓存成功";
            return ApiResponse.success(message);
        } catch (Exception e) {
            log.error("清除健康检查缓存失败", e);
            return ApiResponse.error("清除健康检查缓存失败: " + e.getMessage());
        }
    }
    
    /**
     * 清除指定服务的健康检查缓存
     * 
     * @param serviceName 服务名称
     * @return 操作结果
     */
    @DeleteMapping("/health/cache/{serviceName}")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<String> clearServiceHealthCache(@PathVariable String serviceName) {
        try {
            routeManagementService.clearHealthCache(serviceName);
            return ApiResponse.success("清除服务 " + serviceName + " 的健康检查缓存成功");
        } catch (Exception e) {
            log.error("清除服务 {} 健康检查缓存失败", serviceName, e);
            return ApiResponse.error("清除健康检查缓存失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取路由统计信息
     * 
     * @return 统计信息
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Map<String, Object>> getRouteStatistics() {
        try {
            Map<String, Object> statistics = routeManagementService.getRouteStatistics();
            return ApiResponse.success(statistics, "获取路由统计信息成功");
        } catch (Exception e) {
            log.error("获取路由统计信息失败", e);
            return ApiResponse.error("获取路由统计信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查服务是否启用
     * 
     * @param serviceName 服务名称
     * @return 是否启用
     */
    @GetMapping("/services/{serviceName}/enabled")
    public ApiResponse<Boolean> isServiceEnabled(@PathVariable String serviceName) {
        try {
            boolean enabled = routeManagementService.isServiceEnabled(serviceName);
            return ApiResponse.success(enabled, "检查服务启用状态成功");
        } catch (Exception e) {
            log.error("检查服务 {} 启用状态失败", serviceName, e);
            return ApiResponse.error("检查服务启用状态失败: " + e.getMessage());
        }
    }
}