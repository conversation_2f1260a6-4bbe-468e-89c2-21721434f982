package com.dipspro.modules.chat.dto;

import java.util.ArrayList;
import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 可折叠内容
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CollapsibleContent {
    
    /**
     * 摘要信息
     */
    private String summary;
    
    /**
     * 详情列表
     */
    private List<CollapsibleDetail> details;
    
    /**
     * 构造方法
     * 
     * @param summary 摘要信息
     */
    public CollapsibleContent(String summary) {
        this.summary = summary;
        this.details = new ArrayList<>();
    }
    
    /**
     * 添加详情项
     * 
     * @param detail 详情项
     * @return 当前对象，支持链式调用
     */
    public CollapsibleContent addDetail(CollapsibleDetail detail) {
        if (this.details == null) {
            this.details = new ArrayList<>();
        }
        this.details.add(detail);
        return this;
    }
    
    /**
     * 添加可展开的详情项
     * 
     * @param title 标题
     * @param content 内容
     * @return 当前对象，支持链式调用
     */
    public CollapsibleContent addExpandableDetail(String title, String content) {
        return addDetail(CollapsibleDetail.expandable(title, content));
    }
    
    /**
     * 添加简单的详情项
     * 
     * @param title 标题
     * @return 当前对象，支持链式调用
     */
    public CollapsibleContent addSimpleDetail(String title) {
        return addDetail(CollapsibleDetail.simple(title));
    }
    
    /**
     * 创建带摘要的可折叠内容
     * 
     * @param summary 摘要信息
     * @return CollapsibleContent实例
     */
    public static CollapsibleContent withSummary(String summary) {
        return new CollapsibleContent(summary);
    }
    
    /**
     * 创建无摘要的可折叠内容
     * 
     * @return CollapsibleContent实例
     */
    public static CollapsibleContent create() {
        return new CollapsibleContent();
    }
} 