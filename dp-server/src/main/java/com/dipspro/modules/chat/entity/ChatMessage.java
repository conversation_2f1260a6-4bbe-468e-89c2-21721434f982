package com.dipspro.modules.chat.entity;

import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import org.hibernate.annotations.Comment;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.type.SqlTypes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "chat_messages")
@Data
@NoArgsConstructor
public class ChatMessage {

    private static final Logger log = LoggerFactory.getLogger(ChatMessage.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    public enum MessageRole {
        USER, ASSISTANT
    }

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "conversation_id", nullable = false)
    @JsonIgnore
    private Conversation conversation;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private MessageRole role;

    /**
     * 消息内容字段。
     * 可以存储简单文本或JSON格式的多类型消息内容。
     * JSON格式示例：[{"type":"text","content":"文本内容"},{"type":"suggestion","content":["建议1","建议2"]}]
     */
    @Column(columnDefinition = "TEXT", nullable = false)
    private String content;

    /**
     * 消息所属用户ID，用于多用户消息隔离
     * 对应数据库中的user_id字段，类型为VARCHAR(32)
     */
    @Column(name = "user_id", length = 32, nullable = true)
    private String userId;

    /**
     * 中间步骤的 JSON 字符串存储。
     * 用于存储 AI 思维链数据。
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "intermediate_steps_json", columnDefinition = "jsonb")
    private String intermediateStepsJson;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private Instant createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 轮次序号
     * 一个用户问题及其对应的所有助手回复构成一个轮次，从1开始递增
     */
    @Column(name = "round_sequence")
    @Comment("轮次序号，从1开始递增")
    private Long roundSequence;

    /**
     * 轮次内消息顺序
     * 在同一轮次内，用户消息为1，助手消息按出现顺序递增
     */
    @Column(name = "message_order")
    @Comment("轮次内消息顺序，从1开始递增")
    private Integer messageOrder;

    @Transient
    public List<Object> getIntermediateSteps() {
        if (this.intermediateStepsJson == null || this.intermediateStepsJson.isEmpty()
                || this.intermediateStepsJson.equals("[]")) {
            return Collections.emptyList();
        }
        try {
            return objectMapper.readValue(this.intermediateStepsJson, new TypeReference<List<Object>>() {
            });
        } catch (JsonProcessingException e) {
            log.error("Failed to deserialize intermediate_steps_json for message id {}: {}", this.id,
                    this.intermediateStepsJson, e);
            return Collections.singletonList("Error deserializing steps: " + e.getMessage());
        }
    }
}