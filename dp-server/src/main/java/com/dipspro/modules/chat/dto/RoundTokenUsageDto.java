package com.dipspro.modules.chat.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 轮次 Token 用量数据传输对象
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class RoundTokenUsageDto {

    /**
     * 对话ID
     */
    private UUID conversationId;

    /**
     * 轮次序号
     */
    private Long roundSequence;

    /**
     * 输入Token数量（用户消息）
     */
    private Long inputTokens;

    /**
     * 输出Token数量（助手消息）
     */
    private Long outputTokens;

    /**
     * 思维链Token数量
     */
    private Long thoughtChainTokens;

    /**
     * 总Token数量
     */
    private Long totalTokens;

    /**
     * 输入费用
     */
    private BigDecimal inputCost;

    /**
     * 输出费用
     */
    private BigDecimal outputCost;

    /**
     * 总费用
     */
    private BigDecimal totalCost;

    /**
     * 计算状态：PENDING, SUCCESS, FAILED
     */
    private String status;

    /**
     * 使用的模型名称
     */
    private String modelName;

    /**
     * 计算完成时间
     */
    private LocalDateTime calculatedAt;

    /**
     * 错误信息（如果计算失败）
     */
    private String errorMessage;
}