package com.dipspro.modules.chat.dto;

import java.util.Map;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 建议选项链接参数数据传输对象
 * 包含调用chat API所需的所有参数
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SuggestionLinkParams {

    /**
     * 消息内容
     */
    private String message;

    /**
     * 模板ID
     */
    private String templateId;

    /**
     * 插槽参数
     */
    private Map<String, Object> slots;

    /**
     * 代理类型
     */
    private String agentType;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 创建链接参数
     * 
     * @param message    消息内容
     * @param templateId 模板ID
     * @param slots      插槽参数
     * @param agentType  代理类型
     * @param sessionId  会话ID
     * @param userId     用户ID
     * @param username   用户名
     * @return SuggestionLinkParams实例
     */
    public static SuggestionLinkParams create(String message, String templateId, Map<String, Object> slots,
            String agentType, String sessionId, Long userId, String username) {
        SuggestionLinkParams params = new SuggestionLinkParams();
        params.setMessage(message);
        params.setTemplateId(templateId);
        params.setSlots(slots);
        params.setAgentType(agentType);
        params.setSessionId(sessionId);
        params.setUserId(userId);
        params.setUsername(username);
        return params;
    }
}