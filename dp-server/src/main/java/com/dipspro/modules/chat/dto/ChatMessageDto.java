package com.dipspro.modules.chat.dto;

import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.dipspro.modules.chat.entity.ChatMessage;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatMessageDto {

    private static final Logger log = LoggerFactory.getLogger(ChatMessageDto.class);
    private static final ObjectMapper objectMapper = new ObjectMapper(); // Reuse ObjectMapper

    private UUID id;
    private String role;
    private Instant createdAt;
    private List<Object> intermediateSteps;
    private boolean loading;

    /**
     * 多类型消息内容列表。
     * 这是唯一的消息内容字段，所有消息都使用此格式。
     */
    private List<MessageContent> list;

    /**
     * 从 ChatMessage 实体转换为 DTO
     * 解析 content 字段中的 JSON 数据为 list 格式
     */
    public static ChatMessageDto fromEntity(ChatMessage entity) {
        ChatMessageDto dto = new ChatMessageDto();
        dto.setId(entity.getId());
        dto.setRole(entity.getRole().name().toLowerCase());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setLoading(false); // DTO中的loading默认为false

        // 解析思维链数据
        if (entity.getIntermediateStepsJson() != null && !entity.getIntermediateStepsJson().trim().isEmpty()) {
            try {
                List<Object> steps = objectMapper.readValue(entity.getIntermediateStepsJson(), new TypeReference<List<Object>>() {});
                dto.setIntermediateSteps(steps);
            } catch (JsonProcessingException e) {
                log.warn("解析思维链JSON失败，消息ID: {}, 错误: {}", entity.getId(), e.getMessage());
                dto.setIntermediateSteps(Collections.emptyList());
            }
        } else {
            dto.setIntermediateSteps(Collections.emptyList());
        }

        // 解析消息内容：尝试解析为list格式，失败则包装为简单文本
        if (entity.getContent() != null && !entity.getContent().trim().isEmpty()) {
            try {
                // 尝试解析为MessageContent列表
                List<MessageContent> messageList = objectMapper.readValue(entity.getContent(), new TypeReference<List<MessageContent>>() {});
                dto.setList(messageList);
                log.debug("成功解析list格式消息，消息ID: {}, list大小: {}", entity.getId(), messageList.size());
            } catch (JsonProcessingException e) {
                // 解析失败，说明是简单文本格式，包装为text类型的MessageContent
                log.debug("content不是JSON格式，包装为text类型，消息ID: {}", entity.getId());
                MessageContent textContent = new MessageContent("text", entity.getContent());
                dto.setList(List.of(textContent));
            }
        } else {
            // 内容为空，设置空列表
            dto.setList(Collections.emptyList());
        }

        return dto;
    }
} 