package com.dipspro.modules.chat.controller;

import java.util.List;
import java.util.UUID;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.chat.dto.PromptTemplateRelationDto;
import com.dipspro.modules.chat.dto.RelatedTemplateDto;
import com.dipspro.modules.chat.service.PromptTemplateService;
import com.dipspro.modules.profile.dto.PromptTemplateDto;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/prompt/templates") // Base path for template endpoints
@RequiredArgsConstructor
public class PromptTemplateController {

    private final PromptTemplateService promptTemplateService;

    /**
     * Get all templates, or filter by agentType and/or tags.
     *
     * @param agentType Optional filter by agent type.
     * @return ApiResponse containing list of prompt templates.
     */
    @GetMapping
    public ApiResponse<List<PromptTemplateDto>> findTemplates(
            @RequestParam(required = false) String agentType) {
        log.info("Received request to find templates with agentType: {}", agentType);
        List<PromptTemplateDto> templates = promptTemplateService.findTemplates(agentType);
        log.info("Found {} templates for agentType: {}", templates.size(), agentType);
        return ApiResponse.success(templates);
    }

    /**
     * Get a specific template by its ID.
     *
     * @param id The UUID of the template.
     * @return ApiResponse containing the prompt template DTO or error message if
     *         not found.
     */
    @GetMapping("/{id}")
    public ApiResponse<PromptTemplateDto> getTemplateById(@PathVariable UUID id) {
        log.info("Received request to get template by id: {}", id);
        return promptTemplateService.getTemplateById(id)
                .map(ApiResponse::success)
                .orElse(ApiResponse.error("Template not found with id: " + id));
    }

    /**
     * Create a new prompt template.
     *
     * @param templateDto The DTO containing template data.
     * @return ApiResponse containing the created prompt template DTO with assigned
     *         ID and timestamps.
     */
    @PostMapping
    public ApiResponse<PromptTemplateDto> createTemplate(@Valid @RequestBody PromptTemplateDto templateDto) {
        log.info("Received request to create template: {}", templateDto.getName());
        try {
            PromptTemplateDto createdTemplate = promptTemplateService.createTemplate(templateDto);
            return ApiResponse.success(createdTemplate, "模板创建成功");
        } catch (Exception e) {
            log.error("Error creating template: {}", e.getMessage(), e);
            return ApiResponse.error("创建模板失败: " + e.getMessage());
        }
    }

    /**
     * Update an existing prompt template.
     *
     * @param id          The UUID of the template to update.
     * @param templateDto The DTO containing updated template data.
     * @return ApiResponse containing the updated prompt template DTO or error
     *         message if not found.
     */
    @PutMapping("/{id}")
    public ApiResponse<PromptTemplateDto> updateTemplate(@PathVariable UUID id,
            @Valid @RequestBody PromptTemplateDto templateDto) {
        log.info("Received request to update template with id: {}", id);
        return promptTemplateService.updateTemplate(id, templateDto)
                .map(ApiResponse::success)
                .orElse(ApiResponse.error("Template not found for update with id: " + id));
    }

    /**
     * Delete a prompt template by its ID.
     *
     * @param id The UUID of the template to delete.
     * @return ApiResponse with success status or error message if not found.
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteTemplate(@PathVariable UUID id) {
        log.warn("Received request to delete template with id: {}", id);
        boolean deleted = promptTemplateService.deleteTemplate(id);
        if (deleted) {
            return ApiResponse.success(null);
        } else {
            return ApiResponse.error("Template not found for deletion with id: " + id);
        }
    }

    // 关联关系管理 API

    /**
     * 获取模板的关联关系列表
     *
     * @param id 模板ID
     * @return ApiResponse containing list of related templates
     */
    @GetMapping("/{id}/relations")
    public ApiResponse<List<RelatedTemplateDto>> getTemplateRelations(@PathVariable UUID id) {
        log.info("Received request to get relations for template id: {}", id);
        List<RelatedTemplateDto> relatedTemplates = promptTemplateService.getRelatedTemplates(id);
        return ApiResponse.success(relatedTemplates);
    }

    /**
     * 添加模板关联关系
     *
     * @param id          源模板ID
     * @param relationDto 关联关系数据
     * @return ApiResponse containing the created relation
     */
    @PostMapping("/{id}/relations")
    public ApiResponse<PromptTemplateRelationDto> addTemplateRelation(@PathVariable UUID id,
            @Valid @RequestBody PromptTemplateRelationDto relationDto) {
        log.info("Received request to add relation for template id: {}", id);
        try {
            PromptTemplateRelationDto createdRelation = promptTemplateService.addTemplateRelation(
                    id, relationDto.getTargetTemplateId(), relationDto.getPriority());
            return ApiResponse.success(createdRelation, "关联关系创建成功");
        } catch (Exception e) {
            log.error("Error creating template relation: {}", e.getMessage(), e);
            return ApiResponse.error("创建关联关系失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新模板的关联关系
     *
     * @param id        模板ID
     * @param relations 关联关系列表
     * @return ApiResponse containing updated relations
     */
    @PutMapping("/{id}/relations")
    public ApiResponse<List<PromptTemplateRelationDto>> updateTemplateRelations(@PathVariable UUID id,
            @Valid @RequestBody List<PromptTemplateRelationDto> relations) {
        log.info("Received request to update relations for template id: {}", id);
        try {
            List<PromptTemplateRelationDto> updatedRelations = promptTemplateService.updateTemplateRelations(id,
                    relations);
            return ApiResponse.success(updatedRelations, "关联关系更新成功");
        } catch (Exception e) {
            log.error("Error updating template relations: {}", e.getMessage(), e);
            return ApiResponse.error("更新关联关系失败: " + e.getMessage());
        }
    }

    /**
     * 删除模板关联关系
     *
     * @param id         模板ID
     * @param relationId 关联关系ID
     * @return ApiResponse with success status
     */
    @DeleteMapping("/{id}/relations/{relationId}")
    public ApiResponse<Void> deleteTemplateRelation(@PathVariable UUID id, @PathVariable UUID relationId) {
        log.warn("Received request to delete relation {} for template id: {}", relationId, id);
        boolean deleted = promptTemplateService.deleteTemplateRelation(relationId);
        if (deleted) {
            return ApiResponse.success(null, "关联关系删除成功");
        } else {
            return ApiResponse.error("关联关系不存在或删除失败");
        }
    }

    /**
     * 获取可用于关联的模板列表
     *
     * @param id 要排除的模板ID
     * @return ApiResponse containing available templates
     */
    @GetMapping("/{id}/available-relations")
    public ApiResponse<List<PromptTemplateDto>> getAvailableTemplatesForRelation(@PathVariable UUID id) {
        log.info("Received request to get available templates for relation, excluding template id: {}", id);
        List<PromptTemplateDto> availableTemplates = promptTemplateService.getAvailableTemplatesForRelation(id);
        return ApiResponse.success(availableTemplates);
    }
}