package com.dipspro.modules.chat.dto;

import java.util.List;

import com.dipspro.constant.SlotType;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Represents the definition of a single slot within a PromptTemplate.
 * This is typically stored as part of a JSON array within the PromptTemplate entity.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SlotDefinition {

    @NotBlank(message = "槽位内部名称不能为空")
    private String name; // Internal identifier (e.g., customer_name)

    @NotBlank(message = "槽位显示标签不能为空")
    private String label; // User-facing label (e.g., "客户姓名")

    @NotNull(message = "槽位类型不能为空")
    private SlotType type; // Type of the slot (TEXT, NUMBER, DATE, SELECT, TEXTAREA)

    private String placeholder; // Placeholder text for input fields

    private Boolean required = false; // Whether the slot is mandatory

    private String defaultValue; // Default value for the slot

    // Options for SELECT type slots
    private List<Option> options;

    private String restUrl; // URL for REST API calls

    private String restMethod; // HTTP method for REST API calls (e.g., GET, POST)

    /**
     * Represents a single option for a SELECT type slot.
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Option {
        @NotBlank(message = "选项显示标签不能为空")
        private String label; // Display label for the option (e.g., "选项一")
        @NotBlank(message = "选项值不能为空")
        private String value; // Actual value of the option (e.g., "opt1")
    }
} 