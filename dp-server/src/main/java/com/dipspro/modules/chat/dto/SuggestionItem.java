package com.dipspro.modules.chat.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 建议选项数据传输对象
 * 支持带链接参数的建议选项，用于生成可点击的建议
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SuggestionItem {

    /**
     * 建议选项的显示文本
     */
    private String label;

    /**
     * 建议选项的值
     */
    private String value;

    /**
     * 建议选项的图标（可选）
     */
    private String icon;

    /**
     * 建议选项的额外信息（可选）
     */
    private String extra;

    /**
     * 建议选项的链接参数
     * 包含调用API所需的所有参数
     */
    private SuggestionLinkParams linkParams;

    /**
     * 创建简单的建议选项（仅文本）
     * 
     * @param label 显示文本
     * @param value 选项值
     * @return SuggestionItem实例
     */
    public static SuggestionItem simple(String label, String value) {
        return new SuggestionItem(label, value, null, null, null);
    }

    /**
     * 创建带链接参数的建议选项
     * 
     * @param label      显示文本
     * @param value      选项值
     * @param linkParams 链接参数
     * @return SuggestionItem实例
     */
    public static SuggestionItem withLink(String label, String value, SuggestionLinkParams linkParams) {
        return new SuggestionItem(label, value, null, null, linkParams);
    }

    /**
     * 创建完整的建议选项
     * 
     * @param label      显示文本
     * @param value      选项值
     * @param icon       图标
     * @param extra      额外信息
     * @param linkParams 链接参数
     * @return SuggestionItem实例
     */
    public static SuggestionItem full(String label, String value, String icon, String extra,
            SuggestionLinkParams linkParams) {
        return new SuggestionItem(label, value, icon, extra, linkParams);
    }
}