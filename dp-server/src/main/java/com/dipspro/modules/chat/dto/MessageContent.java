package com.dipspro.modules.chat.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 消息内容的数据传输对象，用于表示不同类型的消息内容。
 * 支持多种内容类型：text、suggestion、file、collapsible等。
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class MessageContent {

    /**
     * 消息内容类型，支持以下类型：
     * - text: 文本内容，支持Markdown格式
     * - suggestion: 建议选项数组，用于快速操作
     * - file: 文件信息对象，支持文件展示和下载
     * - collapsible: 可折叠内容，支持展开/折叠交互
     * - image: 图片信息对象，支持图片展示
     * - chart: 图表数据对象，支持数据可视化
     * - table: 表格数据对象，支持结构化数据展示
     */
    private String type;

    /**
     * 消息内容，根据type的不同，可能是字符串、数组或对象：
     * - text类型：String，文本内容
     * - suggestion类型：SuggestionItem[]，建议选项数组
     * - file类型：Object，文件信息对象
     * - collapsible类型：Object，包含summary和details的对象
     * - 其他类型：Object，对应的数据结构
     */
    private Object content;

    /**
     * 构造函数
     * 
     * @param type    消息类型
     * @param content 消息内容
     */
    public MessageContent(String type, Object content) {
        this.type = type;
        this.content = content;
    }

    /**
     * 创建文本类型的消息内容
     * 
     * @param content 文本内容
     * @return MessageContent实例
     */
    public static MessageContent text(String content) {
        return new MessageContent("text", content);
    }

    /**
     * 创建建议类型的消息内容
     * 
     * @param suggestions 建议选项数组
     * @return MessageContent实例
     */
    public static MessageContent suggestion(SuggestionItem[] suggestions) {
        return new MessageContent("suggestion", suggestions);
    }

    /**
     * 创建可折叠类型的消息内容
     * 
     * @param content 可折叠内容对象
     * @return MessageContent实例
     */
    public static MessageContent collapsible(CollapsibleContent content) {
        return new MessageContent("collapsible", content);
    }

    /**
     * 创建文件类型的消息内容
     * 
     * @param content 文件信息对象
     * @return MessageContent实例
     */
    public static MessageContent file(Object content) {
        return new MessageContent("file", content);
    }
}