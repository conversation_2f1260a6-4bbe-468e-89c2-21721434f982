package com.dipspro.modules.chat.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 指标定义实体类
 * 用于存储指标数据，不需要数据库持久化，通过配置文件加载
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class IndexDefinition {
    
    /**
     * 指标名称
     */
    private String indexGridName;
    
    /**
     * 遗留指标代码（原始系统指标代码）
     */
    private String legacyIndexCode;
    
    /**
     * planar指标代码（新系统指标代码）
     */
    private String planarIndexCode;
    
    /**
     * 父指标代码（planar指标代码），如果是主指标则为null
     */
    private String parentIndexCode;
    
    /**
     * 是否是主指标
     */
    private boolean isMainIndex;
} 