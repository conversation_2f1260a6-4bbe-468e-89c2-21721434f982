package com.dipspro.modules.chat.dto;

import java.util.List;
import java.util.UUID;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 批量 Token 用量查询请求数据传输对象
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class BatchTokenUsageRequestDto {

    /**
     * 轮次查询请求列表
     */
    @NotEmpty(message = "查询请求列表不能为空")
    @Valid
    private List<RoundTokenUsageRequest> requests;

    /**
     * 单个轮次查询请求
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class RoundTokenUsageRequest {

        /**
         * 对话ID
         */
        @NotNull(message = "对话ID不能为空")
        private UUID conversationId;

        /**
         * 轮次序号
         */
        @NotNull(message = "轮次序号不能为空")
        private Long roundSequence;
    }
}