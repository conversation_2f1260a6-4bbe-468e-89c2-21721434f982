package com.dipspro.modules.chat.dto;

import java.time.Instant;
import java.util.UUID;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 提示词模板关联关系 DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PromptTemplateRelationDto {

    private UUID id;

    @NotNull(message = "源模板ID不能为空")
    private UUID sourceTemplateId;

    @NotNull(message = "目标模板ID不能为空")
    private UUID targetTemplateId;

    @NotNull(message = "优先级不能为空")
    private Integer priority = 0;

    private Instant createdAt;

    private Instant updatedAt;

    // 关联的模板信息（用于显示）
    private String sourceTemplateName;
    private String targetTemplateName;
}