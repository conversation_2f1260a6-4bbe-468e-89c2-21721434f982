package com.dipspro.modules.chat.repository;

import java.util.List;
import java.util.UUID;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.dipspro.modules.chat.entity.PromptTemplateRelation;

@Repository
public interface PromptTemplateRelationRepository extends JpaRepository<PromptTemplateRelation, UUID> {

    /**
     * 查找指定模板的所有关联关系（作为源模板）
     * 按优先级排序
     */
    @Query("SELECT r FROM PromptTemplateRelation r " +
            "WHERE r.sourceTemplate.id = :sourceTemplateId " +
            "ORDER BY r.priority ASC")
    List<PromptTemplateRelation> findBySourceTemplateIdOrderByPriority(
            @Param("sourceTemplateId") UUID sourceTemplateId);

    /**
     * 查找指定模板的所有关联关系（作为目标模板）
     * 按优先级排序
     */
    @Query("SELECT r FROM PromptTemplateRelation r " +
            "WHERE r.targetTemplate.id = :targetTemplateId " +
            "ORDER BY r.priority ASC")
    List<PromptTemplateRelation> findByTargetTemplateIdOrderByPriority(
            @Param("targetTemplateId") UUID targetTemplateId);

    /**
     * 查找指定模板的所有相关模板（双向查询）
     * 包括作为源模板和目标模板的关联关系
     */
    @Query("SELECT r FROM PromptTemplateRelation r " +
            "WHERE r.sourceTemplate.id = :templateId OR r.targetTemplate.id = :templateId " +
            "ORDER BY r.priority ASC")
    List<PromptTemplateRelation> findAllRelatedByTemplateId(@Param("templateId") UUID templateId);

    /**
     * 检查两个模板之间是否已存在关联关系
     */
    @Query("SELECT COUNT(r) > 0 FROM PromptTemplateRelation r " +
            "WHERE r.sourceTemplate.id = :sourceTemplateId AND r.targetTemplate.id = :targetTemplateId")
    boolean existsBySourceTemplateIdAndTargetTemplateId(@Param("sourceTemplateId") UUID sourceTemplateId,
            @Param("targetTemplateId") UUID targetTemplateId);

    /**
     * 删除指定源模板的所有关联关系
     */
    @Modifying
    @Query("DELETE FROM PromptTemplateRelation r WHERE r.sourceTemplate.id = :sourceTemplateId")
    void deleteBySourceTemplateId(@Param("sourceTemplateId") UUID sourceTemplateId);

    /**
     * 删除指定模板的所有关联关系（双向删除）
     */
    @Modifying
    @Query("DELETE FROM PromptTemplateRelation r " +
            "WHERE r.sourceTemplate.id = :templateId OR r.targetTemplate.id = :templateId")
    void deleteAllByTemplateId(@Param("templateId") UUID templateId);
}