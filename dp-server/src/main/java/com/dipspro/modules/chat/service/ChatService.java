package com.dipspro.modules.chat.service;

import java.util.List;

import com.dipspro.modules.chat.dto.BatchTokenUsageRequestDto;
// 导入 DTO 类，使用新的包名
import com.dipspro.modules.chat.dto.ChatMessageDto; // Assuming a DTO for chat messages
import com.dipspro.modules.chat.dto.ChatRequest;
import com.dipspro.modules.chat.dto.ChatResponse;
import com.dipspro.modules.chat.dto.ConversationSummaryDTO; // Import new DTO
import com.dipspro.modules.chat.dto.RoundTokenUsageDto;

import reactor.core.publisher.Mono; // Import Mono

/**
 * 聊天服务接口。
 * 定义了处理聊天相关业务逻辑的方法。
 */
public interface ChatService {

    /**
     * 处理用户发送的聊天消息，并将其与助手回复一起保存。
     * 如果 request 中包含 conversationId，则将消息添加到现有对话中；
     * 否则，创建一个新对话。
     *
     * @param request 包含用户消息和可选 conversationId 的请求对象。
     * @return 包含后端回复和 conversationId 的响应对象。
     */
    Mono<ChatResponse> processAndSaveMessage(ChatRequest request);

    /**
     * 获取指定会话 ID 的聊天历史记录。
     *
     * @param conversationId 会话 ID。
     * @return 包含该会话所有消息的 DTO 列表，按时间升序排列。
     */
    List<ChatMessageDto> getChatHistory(String conversationId);

    /**
     * 获取所有会话的摘要列表。
     *
     * @return 包含会话 ID 和标签的摘要 DTO 列表，按最新会话优先排序。
     */
    List<ConversationSummaryDTO> getConversationSummaries();

    /**
     * 获取指定轮次的 Token 用量信息
     * 
     * @param conversationId 对话ID
     * @param roundSequence  轮次序号
     * @return 轮次 Token 用量信息
     */
    RoundTokenUsageDto getRoundTokenUsage(String conversationId, Long roundSequence);

    /**
     * 批量获取多个轮次的 Token 用量信息
     * 
     * @param requests 批量查询请求
     * @return 轮次 Token 用量信息列表
     */
    List<RoundTokenUsageDto> getBatchRoundTokenUsage(List<BatchTokenUsageRequestDto.RoundTokenUsageRequest> requests);

}