package com.dipspro.modules.chat.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.dipspro.modules.chat.dto.PromptTemplateRelationDto;
import com.dipspro.modules.chat.dto.RelatedTemplateDto;
import com.dipspro.modules.chat.dto.SlotDefinition;
import com.dipspro.modules.chat.entity.PromptTemplate;
import com.dipspro.modules.chat.entity.PromptTemplateRelation;
import com.dipspro.modules.chat.repository.PromptTemplateRelationRepository;
import com.dipspro.modules.chat.repository.PromptTemplateRepository;
import com.dipspro.modules.chat.service.PromptTemplateService;
import com.dipspro.modules.profile.dto.PromptTemplateDto;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor // Automatically injects dependencies via constructor
public class PromptTemplateServiceImpl implements PromptTemplateService {

    private static final Logger log = LoggerFactory.getLogger(PromptTemplateServiceImpl.class);

    private final PromptTemplateRepository promptTemplateRepository;
    private final PromptTemplateRelationRepository promptTemplateRelationRepository;
    private final ObjectMapper objectMapper;

    private String serializeSlotDefinitions(List<SlotDefinition> slots) {
        if (CollectionUtils.isEmpty(slots)) {
            return "[]";
        }
        try {
            return objectMapper.writeValueAsString(slots);
        } catch (JsonProcessingException e) {
            log.error("Error serializing slot definitions to JSON", e);
            throw new RuntimeException("Error serializing slot definitions", e);
        }
    }

    private List<SlotDefinition> deserializeSlotDefinitions(String json) {
        if (!StringUtils.hasText(json)) {
            return Collections.emptyList();
        }
        try {
            return objectMapper.readValue(json, new TypeReference<List<SlotDefinition>>() {
            });
        } catch (IOException e) {
            log.error("Error deserializing slot definitions from JSON: {}", json, e);
            throw new RuntimeException("Error deserializing slot definitions", e);
        }
    }

    private PromptTemplateDto convertToDto(PromptTemplate entity) {
        List<SlotDefinition> slots = deserializeSlotDefinitions(entity.getSlotDefinitions());
        PromptTemplateDto dto = new PromptTemplateDto();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setDescription(entity.getDescription());
        dto.setAgentType(entity.getAgentType());
        dto.setTemplateContent(entity.getTemplateContent());
        dto.setSlotDefinitions(slots);
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        // relatedTemplates 将在需要时单独设置
        return dto;
    }

    private PromptTemplate convertToEntity(PromptTemplateDto dto) {
        PromptTemplate entity = new PromptTemplate();
        entity.setId(dto.getId());
        entity.setName(dto.getName());
        entity.setDescription(dto.getDescription());
        entity.setAgentType(dto.getAgentType());
        entity.setTemplateContent(dto.getTemplateContent());
        entity.setSlotDefinitions(serializeSlotDefinitions(dto.getSlotDefinitions()));
        return entity;
    }

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateDto> getAllTemplates() {
        log.debug("Fetching all prompt templates");
        return promptTemplateRepository.findAllByOrderByCreatedAtDesc().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<PromptTemplateDto> getTemplateById(UUID id) {
        log.debug("Fetching prompt template by id: {}", id);
        return promptTemplateRepository.findById(id)
                .map(this::convertToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateDto> getTemplatesByIds(List<UUID> ids) {
        log.debug("Fetching prompt templates by ids: {}", ids);
        if (ids == null || ids.isEmpty()) {
            return Collections.emptyList();
        }
        return promptTemplateRepository.findByIdIn(ids).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateDto> findTemplates(String agentType) {
        log.debug("Finding templates with agentType: {}", agentType);

        List<PromptTemplate> templates;
        if (StringUtils.hasText(agentType)) {
            templates = promptTemplateRepository.findByAgentTypeOrderByCreatedAtDesc(agentType);
        } else {
            templates = promptTemplateRepository.findAllByOrderByCreatedAtDesc();
        }

        return templates.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public PromptTemplateDto createTemplate(PromptTemplateDto templateDto) {
        log.info("Creating new prompt template: {}", templateDto.getName());
        PromptTemplate template = convertToEntity(templateDto);
        template.setId(null);
        PromptTemplate savedTemplate = promptTemplateRepository.save(template);
        log.info("Created prompt template with id: {}", savedTemplate.getId());
        return convertToDto(savedTemplate);
    }

    @Override
    @Transactional
    public Optional<PromptTemplateDto> updateTemplate(UUID id, PromptTemplateDto templateDto) {
        log.info("Updating prompt template with id: {}", id);
        return promptTemplateRepository.findById(id)
                .map(existingTemplate -> {
                    existingTemplate.setName(templateDto.getName());
                    existingTemplate.setDescription(templateDto.getDescription());
                    existingTemplate.setAgentType(templateDto.getAgentType());
                    existingTemplate.setTemplateContent(templateDto.getTemplateContent());
                    existingTemplate.setSlotDefinitions(serializeSlotDefinitions(templateDto.getSlotDefinitions()));

                    PromptTemplate updatedTemplate = promptTemplateRepository.save(existingTemplate);
                    log.info("Successfully updated template with id: {}", updatedTemplate.getId());
                    return convertToDto(updatedTemplate);
                });
    }

    @Override
    @Transactional
    public boolean deleteTemplate(UUID id) {
        log.warn("Attempting to delete prompt template with id: {}", id);
        if (promptTemplateRepository.existsById(id)) {
            promptTemplateRepository.deleteById(id);
            log.warn("Successfully deleted prompt template with id: {}", id);
            return true;
        } else {
            log.warn("Prompt template with id: {} not found for deletion", id);
            return false;
        }
    }

    // 关联关系管理方法实现

    @Override
    @Transactional(readOnly = true)
    public List<RelatedTemplateDto> getRelatedTemplates(UUID templateId) {
        log.debug("Fetching related templates for template id: {}", templateId);
        List<PromptTemplateRelation> relations = promptTemplateRelationRepository
                .findBySourceTemplateIdOrderByPriority(templateId);

        return relations.stream()
                .map(relation -> new RelatedTemplateDto(
                        relation.getTargetTemplate().getId(),
                        relation.getTargetTemplate().getName(),
                        relation.getTargetTemplate().getDescription(),
                        relation.getTargetTemplate().getAgentType(),
                        relation.getPriority(),
                        relation.getId()))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public PromptTemplateRelationDto addTemplateRelation(UUID sourceTemplateId, UUID targetTemplateId,
            Integer priority) {
        log.info("Adding template relation from {} to {} with priority {}", sourceTemplateId, targetTemplateId,
                priority);

        // 检查是否已存在关联关系
        if (promptTemplateRelationRepository.existsBySourceTemplateIdAndTargetTemplateId(sourceTemplateId,
                targetTemplateId)) {
            throw new RuntimeException("关联关系已存在");
        }

        // 获取源模板和目标模板
        PromptTemplate sourceTemplate = promptTemplateRepository.findById(sourceTemplateId)
                .orElseThrow(() -> new RuntimeException("源模板不存在"));
        PromptTemplate targetTemplate = promptTemplateRepository.findById(targetTemplateId)
                .orElseThrow(() -> new RuntimeException("目标模板不存在"));

        // 创建关联关系
        PromptTemplateRelation relation = new PromptTemplateRelation();
        relation.setSourceTemplate(sourceTemplate);
        relation.setTargetTemplate(targetTemplate);
        relation.setPriority(priority != null ? priority : 0);

        PromptTemplateRelation savedRelation = promptTemplateRelationRepository.save(relation);
        log.info("Successfully created template relation with id: {}", savedRelation.getId());

        return convertRelationToDto(savedRelation);
    }

    @Override
    @Transactional
    public List<PromptTemplateRelationDto> updateTemplateRelations(UUID templateId,
            List<PromptTemplateRelationDto> relations) {
        log.info("Updating template relations for template id: {}", templateId);

        // 删除现有的关联关系
        promptTemplateRelationRepository.deleteBySourceTemplateId(templateId);

        // 创建新的关联关系
        List<PromptTemplateRelationDto> result = new ArrayList<>();
        for (PromptTemplateRelationDto relationDto : relations) {
            PromptTemplateRelationDto created = addTemplateRelation(templateId, relationDto.getTargetTemplateId(),
                    relationDto.getPriority());
            result.add(created);
        }

        return result;
    }

    @Override
    @Transactional
    public boolean deleteTemplateRelation(UUID relationId) {
        log.warn("Attempting to delete template relation with id: {}", relationId);
        if (promptTemplateRelationRepository.existsById(relationId)) {
            promptTemplateRelationRepository.deleteById(relationId);
            log.warn("Successfully deleted template relation with id: {}", relationId);
            return true;
        } else {
            log.warn("Template relation with id: {} not found for deletion", relationId);
            return false;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateDto> getAvailableTemplatesForRelation(UUID excludeTemplateId) {
        log.debug("Fetching available templates for relation, excluding template id: {}", excludeTemplateId);
        return promptTemplateRepository.findAllByOrderByCreatedAtDesc().stream()
                .filter(template -> !template.getId().equals(excludeTemplateId))
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    private PromptTemplateRelationDto convertRelationToDto(PromptTemplateRelation relation) {
        return new PromptTemplateRelationDto(
                relation.getId(),
                relation.getSourceTemplate().getId(),
                relation.getTargetTemplate().getId(),
                relation.getPriority(),
                relation.getCreatedAt(),
                relation.getUpdatedAt(),
                relation.getSourceTemplate().getName(),
                relation.getTargetTemplate().getName());
    }
}