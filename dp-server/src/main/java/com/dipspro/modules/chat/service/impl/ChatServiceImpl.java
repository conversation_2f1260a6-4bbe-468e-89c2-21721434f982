package com.dipspro.modules.chat.service.impl;

import java.time.Duration; // 导入Duration
import java.util.ArrayList;
import java.util.Base64; // 导入 Base64 用于 n8n 外部服务认证
import java.util.Collections; // 导入 Collections
import java.util.HashMap; // 导入 HashMap
import java.util.List; // 导入 List 类
// 导入 Map 用于构建请求体
import java.util.Map;
import java.util.Optional; // 导入 Optional
import java.util.UUID; // 导入 UUID 类
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value; // 重新导入 @Value
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional; // 导入 Transactional
import org.springframework.util.StringUtils; // 导入 StringUtils
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.context.request.async.AsyncRequestTimeoutException; // 导入超时异常
import org.springframework.web.reactive.function.client.WebClient;

import com.dipspro.modules.billing.entity.BillingUsageRecord;
import com.dipspro.modules.billing.service.BillingService;
import com.dipspro.modules.chat.dto.BatchTokenUsageRequestDto;
import com.dipspro.modules.chat.dto.ChatMessageDto;
import com.dipspro.modules.chat.dto.ChatRequest;
import com.dipspro.modules.chat.dto.ChatResponse;
import com.dipspro.modules.chat.dto.ConversationSummaryDTO;
import com.dipspro.modules.chat.dto.MessageContent;
import com.dipspro.modules.chat.dto.RoundTokenUsageDto;
// 导入 DTO 和 Service 接口，使用新的包名
import com.dipspro.modules.chat.entity.ChatMessage;
import com.dipspro.modules.chat.entity.ChatMessage.MessageRole; // 导入内部枚举
import com.dipspro.modules.chat.entity.Conversation;
import com.dipspro.modules.chat.repository.ChatMessageRepository;
import com.dipspro.modules.chat.repository.ConversationRepository;
import com.dipspro.modules.chat.service.ChatService;
import com.dipspro.modules.chat.service.PromptTemplateService;
import com.dipspro.modules.profile.dto.PromptTemplateDto;
import com.fasterxml.jackson.core.JsonProcessingException; // 导入异常类
import com.fasterxml.jackson.core.type.TypeReference; // 导入 TypeReference
import com.fasterxml.jackson.databind.ObjectMapper; // 导入 Jackson ObjectMapper

import io.netty.channel.ConnectTimeoutException; // 导入连接超时异常
import io.netty.handler.timeout.ReadTimeoutException; // 导入读取超时异常
import io.netty.handler.timeout.WriteTimeoutException; // 导入写入超时异常
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 聊天服务的实现类
 */
@Slf4j
@Service // 标记这是一个 Spring Service 组件，可以被依赖注入
public class ChatServiceImpl implements ChatService {

    // WebClient 用于发送 HTTP 请求
    private final WebClient webClient;

    // Jackson ObjectMapper 用于将 Map 转换为 JSON 字符串
    private final ObjectMapper objectMapper;

    private final ChatMessageRepository chatMessageRepository; // 注入 Repository
    private final ConversationRepository conversationRepository; // 注入 ConversationRepository
    private final PromptTemplateService promptTemplateService; // 注入 PromptTemplateService
    private final BillingService billingService; // 注入 BillingService

    // 从配置文件注入 n8n webhook 地址
    @Value("${n8n.webhook.url:default_webhook_url}") // 提供默认值或确保已设置
    private String n8nWebhookUrl;

    private static final String PLACEHOLDER_USER_ID = "anonymous"; // 占位符用户 ID

    // 从配置文件注入 n8n 外部服务认证凭据
    @Value("${n8n.auth.username}")
    private String n8nUsername;

    @Value("${n8n.auth.password}")
    private String n8nPassword;

    /**
     * 构造函数，注入 WebClient.Builder 和 ObjectMapper
     * 
     * @param webClientBuilder       Spring Boot 自动配置的 WebClient 构建器
     * @param objectMapper           Spring Boot 自动配置的 ObjectMapper
     * @param chatMessageRepository  聊天消息仓库
     * @param conversationRepository 会话仓库
     * @param promptTemplateService  提示词模板服务
     * @param billingService         计费服务
     */
    @Autowired
    public ChatServiceImpl(WebClient.Builder webClientBuilder, ObjectMapper objectMapper,
            ChatMessageRepository chatMessageRepository,
            ConversationRepository conversationRepository,
            PromptTemplateService promptTemplateService,
            BillingService billingService) {
        // 使用构建器创建一个 WebClient 实例，设置5分钟超时
        this.webClient = webClientBuilder
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(16 * 1024 * 1024)) // 设置最大内存缓冲区为16MB
                .build();
        this.objectMapper = objectMapper; // 注入 ObjectMapper
        this.chatMessageRepository = chatMessageRepository; // 分配 Repository
        this.conversationRepository = conversationRepository; // 分配 ConversationRepository
        this.promptTemplateService = promptTemplateService; // 分配 PromptTemplateService
        this.billingService = billingService; // 分配 BillingService
    }

    /**
     * 处理用户发送的聊天消息。
     * 调用 n8n webhook 来获取回复。
     *
     * @param request 包含用户消息的请求对象。
     * @return 包含后端回复的响应对象。
     */
    @Override
    @Transactional
    public Mono<ChatResponse> processAndSaveMessage(ChatRequest request) {
        log.info("处理会话 {} 的聊天请求", request.getConversationId());

        // 获取当前用户的认证信息
        String currentUserToken = getCurrentUserToken();
        Long currentUserId = getCurrentUserId();
        String currentUsername = getCurrentUsername();

        log.debug("当前用户信息 - ID: {}, 用户名: {}, Token: {}",
                currentUserId, currentUsername,
                currentUserToken != null
                        ? currentUserToken.substring(0, Math.min(20, currentUserToken.length())) + "..."
                        : "null");

        // 确定或创建会话
        UUID conversationId = request.getConversationId() != null
                ? request.getConversationId()
                : UUID.randomUUID();

        // 使用 final 变量以便 lambda 访问
        final UUID finalConversationId = conversationId;
        Conversation conversation = conversationRepository.findById(finalConversationId)
                .orElseGet(() -> {
                    log.info("创建新会话，ID: {}，用户ID: {}", finalConversationId, currentUserId);
                    Conversation newConversation = new Conversation();
                    newConversation.setId(finalConversationId);
                    // 设置会话所属用户ID
                    newConversation.setUserId(currentUserId);
                    // 初始标签
                    newConversation.setLabel("新会话 - " + finalConversationId.toString().substring(0, 8));
                    return conversationRepository.save(newConversation);
                });

        // 确定要保存的用户输入内容
        String userMessageContentToSave;
        String userMessageForAI; // 添加一个变量，用于发送给AI的实际消息内容

        // 声明模板变量
        Optional<PromptTemplateDto> templateOpt = Optional.empty();

        if (request.getTemplateId() != null && request.getSlots() != null) {
            log.info("处理模板请求 - 模板 ID: {}", request.getTemplateId());

            // 1. 查询模板对象
            templateOpt = promptTemplateService.getTemplateById(request.getTemplateId());

            if (templateOpt.isPresent()) {
                PromptTemplateDto template = templateOpt.get();
                log.debug("找到模板: {}, 内容: {}", template.getName(), template.getTemplateContent());

                // 2. 使用模板的template_content作为提示词基础
                String templateContent = template.getTemplateContent();

                // 3. 替换模板中的占位符
                String processedTemplate = templateContent;
                // 遍历所有槽位的键值对
                for (Map.Entry<String, Object> entry : request.getSlots().entrySet()) {
                    String placeholder = "{{" + entry.getKey() + "}}";
                    String value = entry.getValue() != null ? entry.getValue().toString() : "";
                    processedTemplate = processedTemplate.replace(placeholder, value);
                    log.debug("替换占位符 {} 为 {}", placeholder, value);
                }

                // 设置用于发送给AI的消息
                userMessageForAI = processedTemplate;
                // 记录使用的模板信息和填充后的内容
                userMessageContentToSave = processedTemplate;
            } else {
                log.warn("未找到模板: {}", request.getTemplateId());
                // 未找到模板时的回退行为，仍记录基本信息
                userMessageContentToSave = String.format("使用了模板: %s (槽位: %s)",
                        request.getTemplateId(), request.getSlots().keySet());
                userMessageForAI = userMessageContentToSave;
            }
        } else if (StringUtils.hasText(request.getMessage())) {
            log.info("处理文本消息请求。");
            userMessageContentToSave = request.getMessage();
            userMessageForAI = request.getMessage();
        } else {
            log.error("无效的聊天请求：未提供消息或模板信息。");
            return Mono.error(new IllegalArgumentException("请求无效：必须提供消息文本或模板信息。"));
        }

        // 保存用户消息
        ChatMessage userMessage = new ChatMessage();
        userMessage.setConversation(conversation);
        userMessage.setRole(MessageRole.USER); // 使用枚举值
        userMessage.setContent(userMessageContentToSave);
        userMessage.setUserId(currentUserId != null ? currentUserId.toString() : PLACEHOLDER_USER_ID); // 设置用户ID
        chatMessageRepository.save(userMessage);
        log.info("已保存会话 {} 的用户消息（用户ID: {}）：{}", conversation.getId(), currentUserId, userMessageContentToSave);

        // --- 调用 n8n Webhook --- //
        // 验证webhook URL是否配置正确
        if ("default_webhook_url".equals(n8nWebhookUrl) || !StringUtils.hasText(n8nWebhookUrl)) {
            log.error("n8n webhook URL 未正确配置。当前值: {}", n8nWebhookUrl);
            String errorContent = "系统配置错误：n8n webhook URL未正确配置，请联系系统管理员。";
            // 保存错误响应消息
            ChatMessage errorResponse = new ChatMessage();
            errorResponse.setConversation(conversation);
            errorResponse.setRole(MessageRole.ASSISTANT);
            errorResponse.setContent(errorContent);
            chatMessageRepository.save(errorResponse);
            // 返回错误响应，使用list格式
            List<MessageContent> errorList = List.of(new MessageContent("text", errorContent));
            return Mono.just(new ChatResponse(errorList, finalConversationId, Collections.emptyList(), "error"));
        }

        // 构建 n8n 请求体
        Map<String, Object> n8nPayload = new HashMap<>();
        String sessionId = conversation.getId().toString(); // 使用会话ID作为sessionId
        n8nPayload.put("sessionId", sessionId);

        // 添加用户认证信息，供 n8n 调用 dp-server 接口时使用
        if (currentUserToken != null) {
            n8nPayload.put("userToken", currentUserToken);
            log.debug("已将用户 JWT token 添加到 n8n 请求中");
        } else {
            log.warn("当前请求缺少用户 JWT token，n8n 可能无法调用需要认证的接口");
        }

        // 添加用户信息
        if (currentUserId != null) {
            n8nPayload.put("userId", currentUserId);
        }
        if (currentUsername != null) {
            n8nPayload.put("username", currentUsername);
        }

        if (request.getTemplateId() != null && templateOpt.isPresent()) {
            // 可选：同时添加模板信息，以便n8n工作流可以识别这是模板请求
            PromptTemplateDto templateDto = templateOpt.get();
            // 通过此参数来决定执行的工作流
            n8nPayload.put("agentType", templateDto.getAgentType());
            n8nPayload.put("templateId", request.getTemplateId().toString());
            n8nPayload.put("slots", request.getSlots());

            // 添加处理后的消息内容
            n8nPayload.put("chatInput", userMessageForAI);

            log.debug("n8n 请求体 (模板填充): {}", n8nPayload);
        } else {
            // 处理文本请求
            n8nPayload.put("chatInput", userMessage.getContent());
            log.debug("n8n 请求体 (文本): {}", n8nPayload);
        }

        log.info("发送请求到 n8n webhook: {}", n8nWebhookUrl);
        // 构建等效的 curl 命令用于调试
        String curlCommand = buildCurlCommand(n8nWebhookUrl, n8nPayload);
        log.info("等效的 curl 命令: \n {}", curlCommand);

        // 执行 WebClient 调用
        return webClient.post()
                .uri(n8nWebhookUrl)
                // 添加 n8n 外部服务认证请求头
                .header("Authorization",
                        "Basic " + Base64.getEncoder().encodeToString((n8nUsername + ":" + n8nPassword).getBytes()))
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(n8nPayload)
                .retrieve()
                // 定义响应体类型
                .bodyToMono(new ParameterizedTypeReference<List<Map<String, Object>>>() {
                })
                .defaultIfEmpty(Collections.emptyList())
                // 设置5分钟超时
                .timeout(Duration.ofMinutes(5))
                // 添加成功响应的详细日志记录
                .doOnNext(responseList -> {
                    log.debug("=== WebClient POST 调用成功 ===");
                    log.debug("请求 URL: {}", n8nWebhookUrl);
                    log.debug("请求体: {}", n8nPayload);
                    log.debug("响应状态: 成功");
                    log.debug("响应内容类型: List<Map<String, Object>>");
                    log.debug("响应数据大小: {}", responseList != null ? responseList.size() : 0);

                    // 打印完整的响应内容
                    try {
                        String responseJson = objectMapper.writeValueAsString(responseList);
                        log.debug("完整响应内容: {}", responseJson);
                    } catch (JsonProcessingException e) {
                        log.warn("无法序列化响应内容为JSON: {}", e.getMessage());
                        log.debug("原始响应内容: {}", responseList);
                    }
                    log.debug("=== WebClient POST 调用成功结束 ===");
                })
                .flatMap(responseList -> {
                    log.debug("收到来自 n8n webhook 的响应。");
                    log.debug("n8n 原始响应列表: {}", responseList);
                    String agentType = n8nPayload.get("agentType") + "";

                    // 检查响应是否为空
                    if (responseList == null || responseList.isEmpty()) {
                        if (org.apache.commons.lang3.StringUtils.equals(agentType, "visual")) {
                            return handleEmptyVisual(conversation);
                        }
                        log.error("n8n 响应列表为空或 null。");
                        return Mono.error(new RuntimeException("从 n8n 收到了空的响应"));
                    }

                    // 提取响应数据
                    final Map<String, Object> responseData;
                    if (org.apache.commons.lang3.StringUtils.equals(agentType, "visual")) {
                        Map<String, Object> visualData = new HashMap<>();
                        try {
                            String json = objectMapper.writeValueAsString(responseList);
                            visualData.put("output", json);
                        } catch (JsonProcessingException e) {
                            log.error("序列化会话 {} 的可视化时出错", conversation.getId(), e);
                        }
                        responseData = visualData;
                    } else {
                        responseData = responseList.get(0);
                    }
                    log.info("提取的 n8n 响应数据: {}", responseData);
                    String responseDataString = responseData.toString();
                    log.debug("提取的 n8n 响应数据 (前50字符): {}",
                            responseDataString.substring(0, Math.min(responseDataString.length(), 50)));

                    // 提取助手回复内容，支持多种字段名，提供默认值
                    String assistantReply = Optional.ofNullable(responseData.get("output"))
                            .map(Object::toString)
                            .or(() -> Optional.ofNullable(responseData.get("text"))
                                    .map(Object::toString))
                            .or(() -> Optional.ofNullable(responseData.get("response"))
                                    .map(Object::toString))
                            .or(() -> Optional.ofNullable(responseData.get("message"))
                                    .map(Object::toString))
                            .orElse("抱歉，未能获取到有效回复。");

                    log.debug("成功提取助手回复内容: {}",
                            assistantReply.length() > 50 ? assistantReply.substring(0, 50) + "..." : assistantReply);

                    // 处理中间步骤 (思维链)
                    // 修改这里以同时支持intermediate_steps和intermediateSteps两种键值
                    List<Object> intermediateSteps = null;
                    // 先尝试获取 intermediate_steps 键
                    Object stepsObj = responseData.get("intermediate_steps");
                    if (stepsObj instanceof List) {
                        intermediateSteps = (List<Object>) stepsObj;
                    } else {
                        // 如果 intermediate_steps 不存在或不是列表，尝试获取 intermediateSteps 键
                        stepsObj = responseData.get("intermediateSteps");
                        if (stepsObj instanceof List) {
                            intermediateSteps = (List<Object>) stepsObj;
                        }
                    }

                    // 如果两者都不存在或不是列表，使用空列表
                    if (intermediateSteps == null) {
                        intermediateSteps = Collections.emptyList();
                    }

                    // 统一处理消息内容为list格式
                    List<MessageContent> messageList = null;

                    // 首先尝试从n8n响应中获取output字段（新格式）
                    Object outputObj = responseData.get("output");

                    if (outputObj instanceof List) {
                        try {
                            @SuppressWarnings("unchecked")
                            List<Object> rawOutputList = (List<Object>) outputObj;

                            // 直接将output数组转换为MessageContent列表
                            List<MessageContent> outputMessageList = new ArrayList<>();
                            for (Object item : rawOutputList) {
                                if (item instanceof Map) {
                                    @SuppressWarnings("unchecked")
                                    Map<String, Object> itemMap = (Map<String, Object>) item;
                                    String type = (String) itemMap.get("type");
                                    Object content = itemMap.get("content");
                                    outputMessageList.add(new MessageContent(type, content));
                                }
                            }

                            messageList = outputMessageList;
                        } catch (Exception e) {
                            log.warn("解析n8n返回的output字段(List类型)时出错: {}，回退到assistantReply", e.getMessage());
                            // 解析失败，回退到assistantReply
                            messageList = List.of(new MessageContent("text", assistantReply));
                        }
                    }
                    // 检查output字段是否为JSON字符串（新格式的另一种情况）
                    else if (outputObj instanceof String) {
                        if (org.apache.commons.lang3.StringUtils.equals(agentType, "visual")) {
                            messageList = List.of(new MessageContent("text", assistantReply));
                        } else {
                            try {
                                String outputStr = (String) outputObj;

                                // 尝试将JSON字符串解析为MessageContent列表
                                List<MessageContent> outputMessageList = objectMapper.readValue(outputStr,
                                        new TypeReference<List<MessageContent>>() {
                                        });

                                messageList = outputMessageList;
                            } catch (JsonProcessingException e) {
                                log.warn("解析n8n返回的output字段(String类型)时出错: {}，回退到assistantReply", e.getMessage());
                                // 解析失败，回退到assistantReply
                                messageList = List.of(new MessageContent("text", assistantReply));
                            }
                        }
                    }

                    // 如果没有处理成功，使用assistantReply作为兜底
                    if (messageList == null) {
                        messageList = List.of(new MessageContent("text", assistantReply));
                    }

                    // 创建并保存助手消息实体
                    ChatMessage assistantMessage = new ChatMessage();
                    assistantMessage.setConversation(conversation);
                    assistantMessage.setRole(MessageRole.ASSISTANT);
                    assistantMessage.setUserId(currentUserId != null ? currentUserId.toString() : PLACEHOLDER_USER_ID); // 设置用户ID

                    // 将messageList序列化为JSON存储在content字段中
                    try {
                        String contentJson = objectMapper.writeValueAsString(messageList);
                        assistantMessage.setContent(contentJson);
                        log.debug("已将list序列化为JSON存储在content字段中");
                    } catch (JsonProcessingException e) {
                        log.error("序列化messageList为JSON时出错: {}", e.getMessage());
                        // 序列化失败时，使用简单文本作为备选
                        assistantMessage.setContent(assistantReply);
                    }

                    // 序列化并保存中间步骤
                    if (!intermediateSteps.isEmpty()) {
                        try {
                            String stepsJson = objectMapper.writeValueAsString(intermediateSteps);
                            assistantMessage.setIntermediateStepsJson(stepsJson);
                            log.debug("已序列化会话 {} 的中间步骤", conversation.getId());
                        } catch (JsonProcessingException e) {
                            log.error("序列化会话 {} 的中间步骤时出错", conversation.getId(), e);
                            // 保存空数组以避免 null
                            assistantMessage.setIntermediateStepsJson("[]");
                        }
                    } else {
                        // 保存空数组
                        assistantMessage.setIntermediateStepsJson("[]");
                    }

                    chatMessageRepository.save(assistantMessage);
                    log.debug("已保存会话 {} 的助手消息", conversation.getId());

                    // 返回统一的list格式响应
                    return Mono.just(new ChatResponse(messageList, finalConversationId, intermediateSteps, agentType));
                })
                // 记录 WebClient 调用错误
                .doOnError(error -> {
                    log.error("=== WebClient POST 调用失败 ===");
                    log.error("请求 URL: {}", n8nWebhookUrl);
                    log.error("请求体: {}", n8nPayload);
                    log.error("会话 ID: {}", finalConversationId);
                    log.error("错误类型: {}", error.getClass().getSimpleName());
                    log.error("错误消息: {}", error.getMessage());

                    // 根据错误类型打印更详细的信息
                    if (error instanceof org.springframework.web.reactive.function.client.WebClientResponseException) {
                        org.springframework.web.reactive.function.client.WebClientResponseException responseException = (org.springframework.web.reactive.function.client.WebClientResponseException) error;
                        log.error("HTTP 状态码: {}", responseException.getStatusCode().value());
                        log.error("响应头: {}", responseException.getHeaders());
                        log.error("响应体: {}", responseException.getResponseBodyAsString());
                    } else if (error instanceof org.springframework.web.reactive.function.client.WebClientRequestException) {
                        org.springframework.web.reactive.function.client.WebClientRequestException requestException = (org.springframework.web.reactive.function.client.WebClientRequestException) error;
                        log.error("请求异常详情: {}", requestException.getMessage());
                        if (requestException.getCause() != null) {
                            log.error("根本原因: {}", requestException.getCause().getMessage());
                        }
                    }

                    log.error("完整异常堆栈:", error);
                    log.error("=== WebClient POST 调用失败结束 ===");
                })
                // 处理错误并返回错误响应
                .onErrorResume(error -> {
                    log.warn("为会话 {} 返回错误响应", finalConversationId);
                    // 为不同类型的错误提供更具体的错误消息
                    String errorMessage;
                    if (error instanceof AsyncRequestTimeoutException ||
                            error instanceof ReadTimeoutException ||
                            error instanceof WriteTimeoutException ||
                            error instanceof ConnectTimeoutException ||
                            error instanceof java.util.concurrent.TimeoutException) {
                        // 超时错误
                        errorMessage = "AI服务响应超时。为确保回答质量，系统允许AI思考最多5分钟。请尝试简化您的问题，或稍后再试。";
                        log.error("请求 n8n webhook 超时 (5分钟)，会话 ID: {}", finalConversationId);
                    } else if (error instanceof org.springframework.web.reactive.function.client.WebClientRequestException) {
                        // 网络请求错误
                        if (error.getMessage().contains("Connection refused")) {
                            errorMessage = "无法连接到AI服务，服务可能暂时不可用。请稍后再试。";
                        } else if (error.getMessage().contains("Connection timed out")) {
                            errorMessage = "连接AI服务超时，请稍后再试。";
                        } else if (error.getMessage().contains("Connection prematurely closed")) {
                            errorMessage = "与AI服务的连接意外关闭，请稍后再试。";
                        } else {
                            errorMessage = "与AI服务通信时出现网络错误: " + error.getMessage();
                        }
                    } else if (error instanceof org.springframework.web.reactive.function.client.WebClientResponseException) {
                        // HTTP 响应错误
                        int statusCode = ((org.springframework.web.reactive.function.client.WebClientResponseException) error)
                                .getStatusCode().value();
                        errorMessage = String.format("AI服务返回错误状态码 %d，请稍后再试或联系系统管理员。", statusCode);
                    } else {
                        // 其他未知错误
                        errorMessage = "处理您的请求时发生错误: " + error.getMessage();
                    }

                    // 保存错误消息到数据库
                    ChatMessage errorResponse = new ChatMessage();
                    errorResponse.setConversation(conversation);
                    errorResponse.setRole(MessageRole.ASSISTANT);
                    errorResponse.setContent(errorMessage);
                    chatMessageRepository.save(errorResponse);

                    // 确保错误响应也使用 finalConversationId
                    List<MessageContent> errorList = List.of(new MessageContent("text", errorMessage));
                    return Mono
                            .just(new ChatResponse(errorList, finalConversationId, Collections.emptyList(), "error"));
                });
    }

    private Mono<ChatResponse> handleEmptyVisual(Conversation conversation) {
        String content = "没有查询到相关可视化页面。";
        List<MessageContent> messageList = List.of(new MessageContent("text", content));

        ChatMessage assistantMessage = new ChatMessage();
        assistantMessage.setConversation(conversation);
        assistantMessage.setRole(MessageRole.ASSISTANT);
        assistantMessage.setContent(content);
        chatMessageRepository.save(assistantMessage);

        return Mono.just(new ChatResponse(messageList, conversation.getId(), Collections.emptyList(), "empty"));
    }

    /**
     * 获取指定会话的聊天历史记录。
     *
     * @param conversationIdString 会话ID（字符串形式）。
     * @return 聊天消息 DTO 列表。
     */
    @Override
    @Transactional(readOnly = true) // 标记这是一个只读事务
    public List<ChatMessageDto> getChatHistory(String conversationIdString) {
        // 获取当前用户ID
        Long currentUserId = getCurrentUserId();

        log.info("获取用户 {} 的会话历史: {}", currentUserId, conversationIdString);

        // 如果没有获取到用户ID，返回空列表
        if (currentUserId == null) {
            log.warn("无法获取当前用户ID，返回空消息列表");
            return Collections.emptyList();
        }

        UUID conversationId;
        try {
            // 尝试将字符串ID转换为UUID
            conversationId = UUID.fromString(conversationIdString);
        } catch (IllegalArgumentException e) {
            log.error("无效的会话ID格式: {}", conversationIdString, e);
            return Collections.emptyList(); // 返回空列表或抛出特定异常
        }

        // 验证会话归属：检查会话是否属于当前用户
        Optional<Conversation> conversationOpt = conversationRepository.findByIdAndUserId(conversationId,
                currentUserId);
        if (conversationOpt.isEmpty()) {
            log.warn("会话 {} 不属于用户 {} 或不存在，拒绝访问", conversationId, currentUserId);
            return Collections.emptyList();
        }

        // 从仓库获取消息
        List<ChatMessage> messages = chatMessageRepository.findByConversationIdOrderByCreatedAtAsc(conversationId);
        log.info("找到 {} 条消息 for conversationId {} (用户: {})", messages.size(), conversationId, currentUserId);
        // 转换实体到 DTO，包括反序列化中间步骤
        // 转换逻辑（包括反序列化）现在由 ChatMessageDto.fromEntity 处理
        return messages.stream()
                .map(ChatMessageDto::fromEntity) // fromEntity 现在处理反序列化
                .collect(Collectors.toList());
    }

    /**
     * 构建等效的 curl 命令字符串，用于调试。
     *
     * @param url     请求的 URL。
     * @param payload 请求体 Map 对象。
     * @return 格式化的 curl 命令字符串。
     */
    private String buildCurlCommand(String url, Map<String, Object> payload) {
        // 创建本地 ObjectMapper 实例
        ObjectMapper localMapper = new ObjectMapper();
        String payloadJson;
        try {
            // 将 Map 序列化为 JSON 字符串
            payloadJson = localMapper.writeValueAsString(payload);
        } catch (JsonProcessingException e) {
            // 序列化失败时的回退
            payloadJson = "{\"error\": \"无法序列化请求体\"}";
        }

        // 构建 n8n 外部服务认证请求头
        String authHeader = "Authorization: Basic "
                + Base64.getEncoder().encodeToString((n8nUsername + ":" + n8nPassword).getBytes());

        // 格式化 curl 命令，注意转义 payload 中的单引号
        return String.format(
                "curl -X POST '%s' -H 'Content-Type: application/json' -H '%s' -d '%s'",
                url,
                authHeader,
                payloadJson.replace("'", "'\\''") // 为 curl 命令转义 payload 中的单引号
        );
    }

    /**
     * 获取所有会话的摘要列表。
     *
     * @return 会话摘要 DTO 列表。
     */
    @Override
    @Transactional(readOnly = true)
    public List<ConversationSummaryDTO> getConversationSummaries() {
        // 获取当前用户ID
        Long currentUserId = getCurrentUserId();

        log.info("获取用户 {} 的会话摘要列表。", currentUserId);

        // 如果没有获取到用户ID，返回空列表
        if (currentUserId == null) {
            log.warn("无法获取当前用户ID，返回空会话摘要列表");
            return Collections.emptyList();
        }

        // 步骤 1: 获取基础摘要 (ID 和时间戳)，按用户过滤
        List<ConversationSummaryDTO> basicSummaries = chatMessageRepository
                .findConversationSummariesByUserId(currentUserId);
        log.debug("获取到用户 {} 的 {} 个基础摘要。", currentUserId, basicSummaries.size());

        // 步骤 2: 处理每个基础摘要以获取正确的标签 (第一条用户消息)。
        List<ConversationSummaryDTO> processedSummaries = basicSummaries.stream().map(basicSummary -> {
            // 直接使用来自 DTO 的 UUID
            UUID conversationId = basicSummary.getId();

            // 步骤 2a: 获取按时间顺序的第一条用户消息的内容。
            Optional<String> firstUserMessageContentOpt = chatMessageRepository
                    .findFirstMessageContentByConversationIdAndRole(conversationId, MessageRole.USER);

            // 步骤 2b: 确定最终标签。
            String finalLabel;
            if (firstUserMessageContentOpt.isPresent() && !firstUserMessageContentOpt.get().trim().isEmpty()) {
                // 使用获取到的第一条用户消息内容。
                String rawLabel = firstUserMessageContentOpt.get();
                // 如有必要，截断标签 (例如，截断至30个字符)
                finalLabel = rawLabel.length() > 30 ? rawLabel.substring(0, 30) + "..." : rawLabel;
                log.debug("找到会话 {} 的第一条用户消息: {}", conversationId, finalLabel);
            } else {
                // 如果未找到用户消息或内容为空，则回退。
                // 使用初始查询中可能不准确的标签作为回退
                finalLabel = basicSummary.getLabel();
                if (finalLabel == null || finalLabel.trim().isEmpty()) {
                    // 默认的回退标签
                    finalLabel = "会话 " + conversationId.toString().substring(0, 8) + "...";
                }
                log.debug("未找到会话 {} 的第一条用户消息或内容为空，使用回退标签: {}", conversationId, finalLabel);
            }

            // 步骤 2c: 创建最终的 DTO，包含正确的 ID (String), 正确的标签, 和原始时间戳。
            // 注意：这里 basicSummary.getId() 返回的是 UUID，如果 DTO 构造函数需要 String，需要 toString()
            // 假设 ConversationSummaryDTO 构造函数接受 (UUID id, String label, Instant timestamp)
            return new ConversationSummaryDTO(basicSummary.getId(), finalLabel, basicSummary.getTimestamp());

        }).collect(Collectors.toList());

        log.info("返回 {} 个处理后的会话摘要。", processedSummaries.size());
        return processedSummaries;
    }

    /**
     * 获取当前用户的JWT Token
     * 
     * @return JWT Token 或 null
     */
    private String getCurrentUserToken() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder
                    .getRequestAttributes();
            if (attributes != null) {
                String authHeader = attributes.getRequest().getHeader("Authorization");
                log.debug("Authorization头信息: {}",
                        authHeader != null ? authHeader.substring(0, Math.min(20, authHeader.length())) + "..."
                                : "null");

                if (authHeader != null && authHeader.startsWith("Bearer ")) {
                    String token = authHeader.substring(7);
                    log.debug("成功提取JWT token，长度: {}", token.length());
                    return token;
                } else {
                    log.debug("Authorization头格式不正确或不存在Bearer token");
                }
            } else {
                log.debug("无法获取ServletRequestAttributes，可能不在HTTP请求上下文中");
            }
        } catch (Exception e) {
            log.warn("获取当前用户Token失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取当前用户ID
     * 
     * @return 用户ID 或 null
     */
    private Long getCurrentUserId() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder
                    .getRequestAttributes();
            if (attributes != null) {
                Object userId = attributes.getRequest().getAttribute("userId");
                if (userId instanceof Long) {
                    return (Long) userId;
                }
            }
        } catch (Exception e) {
            log.warn("获取当前用户ID失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取当前用户名
     * 
     * @return 用户名 或 null
     */
    private String getCurrentUsername() {
        try {
            // 首先尝试从Spring Security上下文获取
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                String username = authentication.getName();
                if (username != null && !"anonymousUser".equals(username)) {
                    return username;
                }
            }

            // 如果Spring Security上下文没有，尝试从请求属性获取
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder
                    .getRequestAttributes();
            if (attributes != null) {
                Object username = attributes.getRequest().getAttribute("username");
                if (username instanceof String) {
                    return (String) username;
                }
            }
        } catch (Exception e) {
            log.warn("获取当前用户名失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取指定轮次的 Token 用量信息
     * 
     * @param conversationId 对话ID
     * @param roundSequence  轮次序号
     * @return 轮次 Token 用量信息
     */
    @Override
    @Transactional(readOnly = true)
    public RoundTokenUsageDto getRoundTokenUsage(String conversationId, Long roundSequence) {
        log.info("获取轮次Token用量: conversationId={}, roundSequence={}", conversationId, roundSequence);

        // 参数验证
        if (conversationId == null || conversationId.trim().isEmpty()) {
            throw new IllegalArgumentException("对话ID不能为空");
        }
        if (roundSequence == null || roundSequence <= 0) {
            throw new IllegalArgumentException("轮次序号必须大于0");
        }

        try {
            UUID conversationUuid = UUID.fromString(conversationId);

            // 获取当前用户ID
            Long currentUserId = getCurrentUserId();
            if (currentUserId == null) {
                throw new IllegalArgumentException("无法获取当前用户信息");
            }

            // 验证会话归属
            Optional<Conversation> conversationOpt = conversationRepository.findByIdAndUserId(conversationUuid,
                    currentUserId);
            if (conversationOpt.isEmpty()) {
                throw new IllegalArgumentException("会话不存在或无权限访问");
            }

            // 查询该轮次的所有消息
            List<ChatMessage> roundMessages = chatMessageRepository
                    .findByConversationIdAndRoundSequenceOrderByMessageOrder(conversationUuid, roundSequence);

            if (roundMessages.isEmpty()) {
                log.warn("未找到轮次消息: conversationId={}, roundSequence={}", conversationId, roundSequence);
                return createEmptyTokenUsage(conversationId, roundSequence);
            }

            // 提取消息ID列表
            List<UUID> messageIds = roundMessages.stream()
                    .map(ChatMessage::getId)
                    .collect(Collectors.toList());

            // 查询计费记录
            List<BillingUsageRecord> usageRecords = billingService.getUsageRecordsByMessageIds(messageIds);

            // 聚合Token用量数据
            return aggregateTokenUsage(conversationId, roundSequence, usageRecords);

        } catch (IllegalArgumentException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取轮次Token用量失败: conversationId={}, roundSequence={}", conversationId, roundSequence, e);
            throw new RuntimeException("获取Token用量失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量获取多个轮次的 Token 用量信息
     * 
     * @param requests 批量查询请求列表
     * @return 轮次 Token 用量信息列表
     */
    @Override
    @Transactional(readOnly = true)
    public List<RoundTokenUsageDto> getBatchRoundTokenUsage(
            List<BatchTokenUsageRequestDto.RoundTokenUsageRequest> requests) {
        log.info("批量获取轮次Token用量: requestCount={}", requests.size());

        if (requests == null || requests.isEmpty()) {
            return Collections.emptyList();
        }

        // 获取当前用户ID
        Long currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            throw new IllegalArgumentException("无法获取当前用户信息");
        }

        List<RoundTokenUsageDto> results = new ArrayList<>();

        for (BatchTokenUsageRequestDto.RoundTokenUsageRequest request : requests) {
            try {
                String conversationId = request.getConversationId().toString();
                RoundTokenUsageDto tokenUsage = getRoundTokenUsage(conversationId, request.getRoundSequence());
                results.add(tokenUsage);
            } catch (Exception e) {
                log.error("批量查询中单个请求失败: conversationId={}, roundSequence={}",
                        request.getConversationId(), request.getRoundSequence(), e);
                // 为失败的请求创建错误状态的结果
                RoundTokenUsageDto errorResult = createErrorTokenUsage(
                        request.getConversationId().toString(),
                        request.getRoundSequence(),
                        e.getMessage());
                results.add(errorResult);
            }
        }

        log.info("批量获取轮次Token用量完成: successCount={}", results.size());
        return results;
    }

    /**
     * 创建空的Token用量结果
     */
    private RoundTokenUsageDto createEmptyTokenUsage(String conversationId, Long roundSequence) {
        return new RoundTokenUsageDto()
                .setConversationId(UUID.fromString(conversationId))
                .setRoundSequence(roundSequence)
                .setStatus("NOT_FOUND")
                .setInputTokens(0L)
                .setOutputTokens(0L)
                .setTotalTokens(0L)
                .setTotalCost(java.math.BigDecimal.ZERO);
    }

    /**
     * 创建错误状态的Token用量结果
     */
    private RoundTokenUsageDto createErrorTokenUsage(String conversationId, Long roundSequence, String errorMessage) {
        return new RoundTokenUsageDto()
                .setConversationId(UUID.fromString(conversationId))
                .setRoundSequence(roundSequence)
                .setStatus("ERROR")
                .setErrorMessage(errorMessage)
                .setInputTokens(0L)
                .setOutputTokens(0L)
                .setTotalTokens(0L)
                .setTotalCost(java.math.BigDecimal.ZERO);
    }

    /**
     * 聚合Token用量数据
     */
    private RoundTokenUsageDto aggregateTokenUsage(String conversationId, Long roundSequence,
            List<BillingUsageRecord> usageRecords) {
        if (usageRecords.isEmpty()) {
            return createEmptyTokenUsage(conversationId, roundSequence);
        }

        // 聚合统计数据
        long totalInputTokens = 0L;
        long totalOutputTokens = 0L;
        long totalTokens = 0L;
        java.math.BigDecimal totalCost = java.math.BigDecimal.ZERO;

        // 检查是否有PENDING状态的记录
        boolean hasPending = false;
        boolean hasError = false;
        String errorMessage = null;

        for (BillingUsageRecord record : usageRecords) {
            if ("PENDING".equals(record.getStatus())) {
                hasPending = true;
            } else if ("FAILED".equals(record.getStatus())) {
                hasError = true;
                errorMessage = "部分Token计算失败";
            } else if ("SUCCESS".equals(record.getStatus())) {
                totalInputTokens += record.getInputTokens() != null ? record.getInputTokens() : 0L;
                totalOutputTokens += record.getOutputTokens() != null ? record.getOutputTokens() : 0L;
                totalTokens += record.getTotalTokens() != null ? record.getTotalTokens() : 0L;
                totalCost = totalCost
                        .add(record.getTotalCost() != null ? record.getTotalCost() : java.math.BigDecimal.ZERO);
            }
        }

        // 确定状态
        String status;
        if (hasPending) {
            status = "PENDING";
        } else if (hasError) {
            status = "PARTIAL_ERROR";
        } else {
            status = "SUCCESS";
        }

        return new RoundTokenUsageDto()
                .setConversationId(UUID.fromString(conversationId))
                .setRoundSequence(roundSequence)
                .setStatus(status)
                .setInputTokens(totalInputTokens)
                .setOutputTokens(totalOutputTokens)
                .setTotalTokens(totalTokens)
                .setTotalCost(totalCost)
                .setErrorMessage(errorMessage);
    }
}