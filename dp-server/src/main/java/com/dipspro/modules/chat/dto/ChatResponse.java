package com.dipspro.modules.chat.dto;

import java.util.List;
import java.util.UUID;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 聊天响应的数据传输对象 (DTO)。
 * 用于封装从后端返回给前端的聊天回复信息。
 * 统一使用list格式，支持多类型消息内容。
 */
@Data // 自动生成 getter, setter, toString, equals, hashCode 方法
@NoArgsConstructor // 自动生成无参构造函数
@AllArgsConstructor // 自动生成全参构造函数
public class ChatResponse {

    /**
     * 当前会话的 ID。
     * 对于新创建的会话，这将是新生成的 ID。
     */
    private UUID conversationId;

    /**
     * 从 n8n 返回的中间步骤 (思维链) 数据。
     */
    private List<Object> intermediateSteps;

    private String agentType;

    /**
     * 多类型消息内容列表。
     * 支持包含text、suggestion、file等不同类型的消息内容。
     * 这是唯一的消息内容字段，所有消息都使用此格式。
     */
    private List<MessageContent> list;

    /**
     * 构造函数：用于创建包含list消息内容的响应
     * 
     * @param list 多类型消息内容列表
     * @param conversationId 会话ID
     * @param intermediateSteps 思维链数据
     * @param agentType 智能体类型
     */
    public ChatResponse(List<MessageContent> list, UUID conversationId, List<Object> intermediateSteps, String agentType) {
        this.list = list;
        this.conversationId = conversationId;
        this.intermediateSteps = intermediateSteps;
        this.agentType = agentType;
    }

    /**
     * 便捷构造函数：用于创建简单文本响应
     * 
     * @param textContent 文本内容
     * @param conversationId 会话ID
     * @param intermediateSteps 思维链数据
     * @param agentType 智能体类型
     */
    public ChatResponse(String textContent, UUID conversationId, List<Object> intermediateSteps, String agentType) {
        this.list = List.of(MessageContent.text(textContent));
        this.conversationId = conversationId;
        this.intermediateSteps = intermediateSteps;
        this.agentType = agentType;
    }
} 