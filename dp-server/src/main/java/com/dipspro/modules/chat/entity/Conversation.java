package com.dipspro.modules.chat.entity;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Table(name = "conversations") // Define the table name
@Data
public class Conversation {

    @Id
    // Use GenerationType.UUID if your DB supports it and you want DB generation,
    // or assign manually like in ChatServiceImpl
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(nullable = true) // Label can be initially null
    private String label;

    /**
     * 会话所属用户ID，关联sys_user.id
     * 用于实现多用户会话隔离
     */
    @Column(name = "user_id", nullable = true)
    private Long userId;

    @OneToMany(mappedBy = "conversation", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private List<ChatMessage> messages = new ArrayList<>();

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private Instant createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    // Convenience methods (optional)
    public void addMessage(ChatMessage message) {
        messages.add(message);
        message.setConversation(this);
    }

    public void removeMessage(ChatMessage message) {
        messages.remove(message);
        message.setConversation(null);
    }
}