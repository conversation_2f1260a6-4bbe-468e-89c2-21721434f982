package com.dipspro.modules.chat.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 可折叠内容的详情项
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CollapsibleDetail {
    
    /**
     * 详情项标题
     */
    private String title;
    
    /**
     * 详情项内容
     */
    private String content;
    
    /**
     * 是否可展开
     */
    private boolean expandable;
    
    /**
     * 创建可展开的详情项
     * 
     * @param title 标题
     * @param content 内容
     * @return CollapsibleDetail实例
     */
    public static CollapsibleDetail expandable(String title, String content) {
        return new CollapsibleDetail(title, content, true);
    }
    
    /**
     * 创建不可展开的详情项
     * 
     * @param title 标题
     * @return CollapsibleDetail实例
     */
    public static CollapsibleDetail simple(String title) {
        return new CollapsibleDetail(title, null, false);
    }
} 