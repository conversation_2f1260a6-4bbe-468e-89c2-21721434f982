package com.dipspro.modules.chat.repository;

import java.util.List;
import java.util.UUID;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.dipspro.modules.chat.entity.PromptTemplate;

@Repository
public interface PromptTemplateRepository extends JpaRepository<PromptTemplate, UUID> {

    // Find templates by agent type
    List<PromptTemplate> findByAgentType(String agentType);

    // 获取所有模板，按创建时间倒序排列
    List<PromptTemplate> findAllByOrderByCreatedAtDesc();

    // 按代理类型过滤并按创建时间倒序排列
    List<PromptTemplate> findByAgentTypeOrderByCreatedAtDesc(String agentType);

    /**
     * 批量查询模板
     * 
     * @param ids 模板ID列表
     * @return 模板列表
     */
    @Query("SELECT pt FROM PromptTemplate pt WHERE pt.id IN :ids")
    List<PromptTemplate> findByIdIn(@Param("ids") List<UUID> ids);

    // Example custom queries (using JPQL or native SQL)
    // Note: Handling array types might require specific database functions or
    // libraries
    // like Hypersistence Utils for more complex operations.

    // Find templates containing AT LEAST ONE specified tag
    // JPQL doesn't have great native support for array containment checking across
    // all DBs.
    // A native query might be more robust for complex array operations.
    // @Query("SELECT pt FROM PromptTemplate pt WHERE :tag = ANY(pt.tags)")
    // List<PromptTemplate> findByTagsContaining(@Param("tag") String tag);

    // Find templates containing ALL specified tags
    // Using PostgreSQL specific array operator '@>'
    // Requires native query = true
    // @Query("SELECT pt FROM PromptTemplate pt WHERE pt.tags @>
    // ARRAY[:tags]::varchar[]")
    // List<PromptTemplate> findByTagsContainingAll(@Param("tags") List<String>
    // tags);
}