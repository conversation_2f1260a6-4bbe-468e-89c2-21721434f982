package com.dipspro.modules.chat.repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.dipspro.modules.chat.entity.Conversation;

@Repository
public interface ConversationRepository extends JpaRepository<Conversation, UUID> {

    /**
     * 根据用户ID查询会话列表，按创建时间降序排列
     * 
     * @param userId 用户ID
     * @return 用户的会话列表
     */
    List<Conversation> findByUserIdOrderByCreatedAtDesc(Long userId);

    /**
     * 根据会话ID和用户ID查询会话，用于验证会话归属
     * 
     * @param id     会话ID
     * @param userId 用户ID
     * @return 会话信息（如果会话属于该用户）
     */
    Optional<Conversation> findByIdAndUserId(UUID id, Long userId);

    /**
     * 根据用户ID统计会话数量
     * 
     * @param userId 用户ID
     * @return 会话数量
     */
    long countByUserId(Long userId);
}