package com.dipspro.modules.chat.service;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.dipspro.modules.chat.dto.PromptTemplateRelationDto;
import com.dipspro.modules.chat.dto.RelatedTemplateDto;
import com.dipspro.modules.profile.dto.PromptTemplateDto;

public interface PromptTemplateService {

    List<PromptTemplateDto> getAllTemplates();

    Optional<PromptTemplateDto> getTemplateById(UUID id);

    /**
     * 批量查询模板
     * 
     * @param ids 模板ID列表
     * @return 模板列表
     */
    List<PromptTemplateDto> getTemplatesByIds(List<UUID> ids);

    /**
     * Finds templates based on optional filtering criteria.
     *
     * @param agentType Optional agent type to filter by.
     * @param tags      Optional list of tags to filter by (currently checks if
     *                  agentType matches).
     * @return List of matching prompt template DTOs.
     */
    List<PromptTemplateDto> findTemplates(String agentType);

    PromptTemplateDto createTemplate(PromptTemplateDto templateDto);

    Optional<PromptTemplateDto> updateTemplate(UUID id, PromptTemplateDto templateDto);

    boolean deleteTemplate(UUID id);

    // 关联关系管理方法

    /**
     * 获取模板的关联关系列表
     * 
     * @param templateId 模板ID
     * @return 关联的模板列表
     */
    List<RelatedTemplateDto> getRelatedTemplates(UUID templateId);

    /**
     * 添加模板关联关系
     * 
     * @param sourceTemplateId 源模板ID
     * @param targetTemplateId 目标模板ID
     * @param priority         优先级
     * @return 创建的关联关系
     */
    PromptTemplateRelationDto addTemplateRelation(UUID sourceTemplateId, UUID targetTemplateId, Integer priority);

    /**
     * 批量更新模板的关联关系
     * 
     * @param templateId 模板ID
     * @param relations  关联关系列表
     * @return 更新后的关联关系列表
     */
    List<PromptTemplateRelationDto> updateTemplateRelations(UUID templateId, List<PromptTemplateRelationDto> relations);

    /**
     * 删除模板关联关系
     * 
     * @param relationId 关联关系ID
     * @return 是否删除成功
     */
    boolean deleteTemplateRelation(UUID relationId);

    /**
     * 获取所有可用于关联的模板（排除指定模板本身）
     * 
     * @param excludeTemplateId 要排除的模板ID
     * @return 可关联的模板列表
     */
    List<PromptTemplateDto> getAvailableTemplatesForRelation(UUID excludeTemplateId);
}