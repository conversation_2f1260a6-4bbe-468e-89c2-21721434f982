package com.dipspro.modules.chat.dto;

import java.time.Instant;
import java.util.UUID;

import lombok.Data;

@Data
public class ConversationSummaryDTO {
    private UUID id;
    private String label;
    private Instant timestamp;

    // Explicit constructor for JPQL query
    public ConversationSummaryDTO(UUID id, String label, Instant timestamp) {
        this.id = id;
        this.label = label;
        this.timestamp = timestamp;
    }

    // Explicit no-args constructor (optional, but good practice if other parts might need it)
    public ConversationSummaryDTO() {
    }

    // Lombok @Data will generate getters, setters, toString, equals, hashCode
}