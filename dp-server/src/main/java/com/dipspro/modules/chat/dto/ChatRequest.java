package com.dipspro.modules.chat.dto;

import java.util.Map;
import java.util.UUID;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 聊天请求的数据传输对象 (DTO)。
 * 用于封装从前端发送到后端的聊天信息。
 */
@Data // 自动生成 getter, setter, toString, equals, hashCode 方法
@NoArgsConstructor // 自动生成无参构造函数
@AllArgsConstructor // 自动生成全参构造函数
public class ChatRequest {

    /**
     * 用户发送的消息内容。
     */
    private String message;

    /**
     * 会话 ID。
     */
    private UUID conversationId;

    private UUID templateId; // ID of the selected prompt template (optional)

    private Map<String, Object> slots; // Key-value pairs for the template slots (optional)

    // 未来可以添加其他字段，例如：
    // private String agentId; // 使用的智能体 ID
    // private List<Message> history; // 历史消息 (如果需要在请求中传递)
} 