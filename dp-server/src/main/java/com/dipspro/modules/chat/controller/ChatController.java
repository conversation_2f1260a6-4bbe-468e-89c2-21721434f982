package com.dipspro.modules.chat.controller;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.context.request.async.DeferredResult;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.chat.dto.BatchTokenUsageRequestDto;
import com.dipspro.modules.chat.dto.ChatMessageDto;
import com.dipspro.modules.chat.dto.ChatRequest;
import com.dipspro.modules.chat.dto.ChatResponse;
import com.dipspro.modules.chat.dto.ConversationSummaryDTO;
import com.dipspro.modules.chat.dto.RoundTokenUsageDto;
import com.dipspro.modules.chat.service.ChatService;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 处理聊天相关 HTTP 请求的控制器。
 * 使用统一的 ApiResponse 格式返回响应。
 */
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class ChatController {

    private final ChatService chatService;

    // 超时时间设置为5分钟（300秒），与WebClient和application.yml中的设置一致
    private static final long CHAT_TIMEOUT_MS = 300_000;

    // 用于防重复提交的请求缓存
    private static final java.util.concurrent.ConcurrentHashMap<String, Long> REQUEST_CACHE = new java.util.concurrent.ConcurrentHashMap<>();
    private static final long REQUEST_CACHE_TIMEOUT = 5000; // 5秒超时

    /**
     * 处理 POST 请求到 /api/chat。
     * 接收前端发送的聊天消息（可能包含 conversationId），调用服务处理并保存，返回响应。
     * 使用DeferredResult显式控制请求超时，设置为5分钟。
     * 添加防重复提交机制。
     *
     * @param request The chat request containing the message or template info.
     * @return A DeferredResult containing the ResponseEntity with the ApiResponse
     *         wrapped ChatResponse.
     */
    @PostMapping("/chat")
    public DeferredResult<ResponseEntity<ApiResponse<ChatResponse>>> chat(@RequestBody ChatRequest request) {
        String requestId = UUID.randomUUID().toString().substring(0, 8);
        log.info("=== [{}] 接收到聊天请求: {} ===", requestId, request);

        // 获取HTTP请求信息
        HttpServletRequest httpRequest = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes())
                .getRequest();
        log.info("[{}] 请求来源详情 - 线程: {}, 远程地址: {}, User-Agent: {}, X-Forwarded-For: {}",
                requestId,
                Thread.currentThread().getName(),
                httpRequest.getRemoteAddr(),
                httpRequest.getHeader("User-Agent"),
                httpRequest.getHeader("X-Forwarded-For"));

        // 检查请求头中是否有特殊标识
        String requestSource = httpRequest.getHeader("X-Request-Source");
        if (requestSource != null) {
            log.info("[{}] 发现请求源标识: {}", requestId, requestSource);
        }

        // 生成请求指纹用于去重（基于消息内容和会话ID）
        String requestFingerprint = generateRequestFingerprint(request);
        long currentTime = System.currentTimeMillis();

        // 检查是否为重复请求
        Long lastRequestTime = REQUEST_CACHE.get(requestFingerprint);
        if (lastRequestTime != null && (currentTime - lastRequestTime) < REQUEST_CACHE_TIMEOUT) {
            log.warn("[{}] 检测到重复请求，拒绝处理。请求指纹: {}, 上次请求时间: {}",
                    requestId, requestFingerprint, new java.util.Date(lastRequestTime));

            DeferredResult<ResponseEntity<ApiResponse<ChatResponse>>> duplicateResult = new DeferredResult<>(
                    CHAT_TIMEOUT_MS);
            ChatResponse duplicateResponse = new ChatResponse("检测到重复请求，请稍后再试。",
                    request.getConversationId(), Collections.emptyList(), "error");
            ApiResponse<ChatResponse> apiResponse = ApiResponse.error(duplicateResponse, "重复请求");
            duplicateResult.setResult(ResponseEntity.status(429).body(apiResponse)); // 429 Too Many Requests
            return duplicateResult;
        }

        // 记录当前请求
        REQUEST_CACHE.put(requestFingerprint, currentTime);

        // 清理过期的缓存条目
        cleanExpiredRequests(currentTime);

        // 创建DeferredResult，设置超时时间为5分钟
        DeferredResult<ResponseEntity<ApiResponse<ChatResponse>>> result = new DeferredResult<>(CHAT_TIMEOUT_MS);

        // 设置超时处理回调
        result.onTimeout(() -> {
            log.warn("[{}] 聊天请求处理超时: {}", requestId, request);
            ChatResponse timeoutResponse = new ChatResponse("AI服务响应超时。为确保回答质量，系统允许AI思考最多5分钟。请尝试简化您的问题，或稍后再试。",
                    request.getConversationId(), Collections.emptyList(), "timeout");
            ApiResponse<ChatResponse> apiResponse = ApiResponse.error(timeoutResponse, "请求处理超时");
            result.setResult(ResponseEntity.status(503).body(apiResponse));
        });

        // 处理聊天请求
        chatService.processAndSaveMessage(request)
                .map(chatResponse -> {
                    log.info("[{}] 聊天请求处理成功", requestId);
                    return ResponseEntity.ok(ApiResponse.success(chatResponse, "聊天消息处理成功"));
                })
                .onErrorResume(IllegalArgumentException.class, e -> {
                    // Handle specific validation errors (like missing message/template)
                    log.warn("[{}] 无效的聊天请求: {}", requestId, e.getMessage());
                    ChatResponse errorResponse = new ChatResponse("请求错误: " + e.getMessage(),
                            request.getConversationId(), Collections.emptyList(), "validation_error");
                    ApiResponse<ChatResponse> apiResponse = ApiResponse.error(errorResponse,
                            "请求参数错误: " + e.getMessage());
                    return Mono.just(ResponseEntity.badRequest().body(apiResponse));
                })
                .onErrorResume(Exception.class, e -> {
                    // Handle other unexpected errors during processing
                    log.error("[{}] 处理聊天请求时发生内部错误", requestId, e);
                    ChatResponse errorResponse = new ChatResponse("处理请求时发生内部服务器错误",
                            request.getConversationId(), Collections.emptyList(), "internal_error");
                    ApiResponse<ChatResponse> apiResponse = ApiResponse.error(errorResponse,
                            "内部服务器错误: " + e.getMessage());
                    return Mono.just(ResponseEntity.internalServerError().body(apiResponse));
                })
                .subscribe(
                        // 成功时设置结果
                        result::setResult,
                        // 发生错误时，如果还没有被设置结果，则设置错误结果
                        error -> {
                            if (!result.isSetOrExpired()) {
                                log.error("[{}] 处理聊天请求时发生未捕获错误", requestId, error);
                                ChatResponse errorResponse = new ChatResponse("处理请求时发生意外错误: " + error.getMessage(),
                                        request.getConversationId(), Collections.emptyList(), "unexpected_error");
                                ApiResponse<ChatResponse> apiResponse = ApiResponse.error(errorResponse,
                                        "系统错误: " + error.getMessage());
                                result.setResult(ResponseEntity.internalServerError().body(apiResponse));
                            }
                        });

        return result;
    }

    /**
     * 生成请求指纹用于去重
     * 基于消息内容、模板ID、槽位值和会话ID生成唯一标识
     */
    private String generateRequestFingerprint(ChatRequest request) {
        StringBuilder fingerprint = new StringBuilder();

        // 添加消息内容
        if (request.getMessage() != null) {
            fingerprint.append("msg:").append(request.getMessage().hashCode());
        }

        // 添加模板ID
        if (request.getTemplateId() != null) {
            fingerprint.append("|tpl:").append(request.getTemplateId());
        }

        // 添加槽位值
        if (request.getSlots() != null && !request.getSlots().isEmpty()) {
            fingerprint.append("|slots:").append(request.getSlots().hashCode());
        }

        // 添加会话ID
        if (request.getConversationId() != null) {
            fingerprint.append("|conv:").append(request.getConversationId());
        }

        return fingerprint.toString();
    }

    /**
     * 清理过期的请求缓存条目
     */
    private void cleanExpiredRequests(long currentTime) {
        REQUEST_CACHE.entrySet().removeIf(entry -> (currentTime - entry.getValue()) > REQUEST_CACHE_TIMEOUT);
    }

    /**
     * 处理 GET 请求到 /api/history/{conversationId}。
     * 获取指定会话 ID 的聊天历史记录。
     *
     * @param conversationId 会话 ID（从路径中获取）。
     * @return 包含该会话所有消息 DTO 的列表响应实体，使用统一的 ApiResponse 格式。
     */
    @GetMapping("/history/{conversationId}")
    public ResponseEntity<ApiResponse<List<ChatMessageDto>>> getHistory(@PathVariable String conversationId) {
        log.info("获取聊天历史: conversationId={}", conversationId);
        try {
            List<ChatMessageDto> history = chatService.getChatHistory(conversationId);
            return ResponseEntity.ok(ApiResponse.success(history, "聊天历史获取成功"));
        } catch (Exception e) {
            log.error("获取聊天历史失败: conversationId={}", conversationId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取聊天历史失败: " + e.getMessage()));
        }
    }

    /**
     * 处理 GET 请求到 /api/conversations。
     * 获取所有会话的摘要列表。
     *
     * @return 包含所有会话摘要 DTO 的列表响应实体，使用统一的 ApiResponse 格式。
     */
    @GetMapping("/conversations")
    public ResponseEntity<ApiResponse<List<ConversationSummaryDTO>>> getConversations() {
        log.info("获取会话摘要列表");
        try {
            List<ConversationSummaryDTO> summaries = chatService.getConversationSummaries();
            return ResponseEntity.ok(ApiResponse.success(summaries, "会话列表获取成功"));
        } catch (Exception e) {
            log.error("获取会话摘要列表失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取会话列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取指定轮次的 Token 用量信息
     * 
     * @param conversationId 对话ID
     * @param roundSequence  轮次序号
     * @return 轮次 Token 用量信息
     */
    @GetMapping("/conversations/{conversationId}/rounds/{roundSequence}/tokens")
    public ResponseEntity<ApiResponse<RoundTokenUsageDto>> getRoundTokenUsage(
            @PathVariable String conversationId,
            @PathVariable Long roundSequence) {

        log.info("获取轮次Token用量: conversationId={}, roundSequence={}", conversationId, roundSequence);

        try {
            RoundTokenUsageDto tokenUsage = chatService.getRoundTokenUsage(conversationId, roundSequence);
            return ResponseEntity.ok(ApiResponse.success(tokenUsage, "轮次Token用量获取成功"));
        } catch (IllegalArgumentException e) {
            log.warn("无效的请求参数: conversationId={}, roundSequence={}", conversationId, roundSequence, e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("无效的请求参数: " + e.getMessage()));
        } catch (Exception e) {
            log.error("获取轮次Token用量失败: conversationId={}, roundSequence={}", conversationId, roundSequence, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取轮次Token用量失败: " + e.getMessage()));
        }
    }

    /**
     * 批量获取多个轮次的 Token 用量信息
     * 
     * @param request 批量查询请求
     * @return 轮次 Token 用量信息列表
     */
    @PostMapping("/tokens/batch")
    public ResponseEntity<ApiResponse<List<RoundTokenUsageDto>>> getBatchRoundTokenUsage(
            @Valid @RequestBody BatchTokenUsageRequestDto request) {

        log.info("批量获取轮次Token用量: requestCount={}", request.getRequests().size());

        try {
            List<RoundTokenUsageDto> tokenUsageList = chatService.getBatchRoundTokenUsage(request.getRequests());
            return ResponseEntity.ok(ApiResponse.success(tokenUsageList, "批量轮次Token用量获取成功"));
        } catch (Exception e) {
            log.error("批量获取轮次Token用量失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("批量获取轮次Token用量失败: " + e.getMessage()));
        }
    }

    // 未来可以添加其他端点，例如：
    // @GetMapping("/history/{conversationId}")
    // public ResponseEntity<List<ChatResponse>> getChatHistory(@PathVariable String
    // conversationId) {
    // // ... 获取历史记录的逻辑 ...
    // }
}
