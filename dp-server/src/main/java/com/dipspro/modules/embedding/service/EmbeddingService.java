package com.dipspro.modules.embedding.service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 中文句子转向量服务接口
 * 支持多线程调用
 */
public interface EmbeddingService {

    /**
     * 将单个中文句子转换为向量
     * 
     * @param text 中文句子
     * @return 向量数组
     */
    float[] encode(String text);

    /**
     * 批量将中文句子转换为向量
     * 
     * @param texts 中文句子列表
     * @return 向量数组列表
     */
    List<float[]> encodeBatch(List<String> texts);

    /**
     * 异步将单个中文句子转换为向量
     * 
     * @param text 中文句子
     * @return 向量数组的CompletableFuture
     */
    CompletableFuture<float[]> encodeAsync(String text);

    /**
     * 异步批量将中文句子转换为向量
     * 
     * @param texts 中文句子列表
     * @return 向量数组列表的CompletableFuture
     */
    CompletableFuture<List<float[]>> encodeBatchAsync(List<String> texts);

    /**
     * 并行批量将中文句子转换为向量（高性能版本）
     * 适用于大批量数据处理，自动分块并行处理
     * 
     * @param texts 中文句子列表
     * @return 向量数组列表的CompletableFuture
     */
    CompletableFuture<List<float[]>> encodeBatchParallel(List<String> texts);

    /**
     * 计算两个向量的余弦相似度
     * 
     * @param vector1 向量1
     * @param vector2 向量2
     * @return 余弦相似度值 [-1, 1]
     */
    double cosineSimilarity(float[] vector1, float[] vector2);

    /**
     * 获取向量维度
     * 
     * @return 向量维度
     */
    int getVectorDimension();

    /**
     * 检查服务是否已初始化
     * 
     * @return 是否已初始化
     */
    boolean isInitialized();
}