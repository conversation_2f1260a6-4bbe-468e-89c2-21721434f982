package com.dipspro.modules.embedding.entity;

import java.time.Instant;
import java.util.UUID;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Data;

import com.dipspro.modules.chat.entity.PromptTemplate;

/**
 * 提示词模板向量实体
 */
@Entity
@Table(name = "prompt_template_vectors")
@Data
public class PromptTemplateVector {

    @Column(name = "_id", nullable = false, unique = true, insertable = false, updatable = false)
    private Long _id;

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(name = "template_id")
    private UUID templateId;

    /**
     * 关联的提示词模板
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "template_id", referencedColumnName = "id", insertable = false, updatable = false)
    private PromptTemplate promptTemplate;

    @Column(name = "typical_description")
    private String typicalDescription;

    @Column(name = "embedding", columnDefinition = "VECTOR(768)")
    private float[] embedding;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private Instant createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    @Column(name = "created_by", length = 64)
    private String createdBy;

    @Column(name = "updated_by", length = 64)
    private String updatedBy;

    @Transient
    private double similarity; // 可能需要临时存储相似度
}