package com.dipspro.modules.embedding;

import com.dipspro.modules.embedding.service.impl.Text2VecEmbeddingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 批量嵌入向量处理器
 * 用于处理大量数据的向量化任务，支持分批并行处理
 */
@Slf4j
@Component
public class BatchEmbeddingProcessor {

    @Autowired
    private Text2VecEmbeddingService embeddingService;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;

    private static final int BATCH_SIZE = 1000;
    private static final int TOTAL_DATA_SIZE = 100000;

    /**
     * 处理10万条数据，每批1000条，使用encodeBatchParallel方法
     * 每个批次处理完后直接存储到数据库
     * @param dataList 待处理的数据列表
     * @return 处理统计信息
     */
    public ProcessResult processLargeDataset(List<String> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            log.warn("输入数据为空");
            return new ProcessResult(0, 0, 0, false, "输入数据为空");
        }

        log.info("开始处理大数据集，总数据量: {}, 批次大小: {}", dataList.size(), BATCH_SIZE);
        
        long startTime = System.currentTimeMillis();
        AtomicInteger processedBatches = new AtomicInteger(0);
        AtomicInteger totalProcessedItems = new AtomicInteger(0);
        
        // 分批处理数据
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        int totalBatches = (int) Math.ceil((double) dataList.size() / BATCH_SIZE);
        
        for (int i = 0; i < dataList.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, dataList.size());
            List<String> batch = dataList.subList(i, endIndex);
            int batchNumber = (i / BATCH_SIZE) + 1;
            int startIndex = i; // 保存起始索引用于数据库存储
            
            log.info("提交第 {}/{} 批次处理，数据量: {}", batchNumber, totalBatches, batch.size());
            
            // 使用encodeBatchParallel异步处理每个批次，并直接存储到数据库
            CompletableFuture<Void> future = embeddingService.encodeBatchParallel(batch)
                .thenAccept(vectors -> {
                    try {
                        // 将批次结果存储到数据库
                        saveBatchToDatabase(batch, vectors, batchNumber, startIndex);
                        int completed = processedBatches.incrementAndGet();
                        int itemsProcessed = totalProcessedItems.addAndGet(vectors.size());
                        
                        log.info("第 {}/{} 批次处理并存储完成，本批次数量: {}，累计处理: {}", 
                            completed, totalBatches, vectors.size(), itemsProcessed);
                        
                        // 每完成10个批次输出一次进度
                        if (completed % 10 == 0 || completed == totalBatches) {
                            double progress = (double) completed / totalBatches * 100;
                            log.info("批次完成进度: {}/{} ({:.1f}%), 累计处理数据: {}", 
                                completed, totalBatches, progress, itemsProcessed);
                        }
                    } catch (Exception e) {
                        log.error("第 {} 批次存储到数据库失败", batchNumber, e);
                        throw new RuntimeException("批次存储失败", e);
                    }
                })
                .exceptionally(throwable -> {
                    log.error("第 {} 批次处理失败", batchNumber, throwable);
                    return null;
                });
            
            futures.add(future);
        }
        
        // 等待所有批次完成
        return waitForAllBatchesToComplete(futures, dataList.size(), startTime);
    }

    /**
     * 生成测试数据（10万条）
     * @return 测试数据列表
     */
    public List<String> generateTestData() {
        log.info("开始生成测试数据，数量: {}", TOTAL_DATA_SIZE);
        
        List<String> testData = IntStream.range(0, TOTAL_DATA_SIZE)
            .mapToObj(i -> "测试文本数据第" + (i + 1) + "条：这是用于向量化处理的示例文本内容")
            .collect(Collectors.toList());
        
        log.info("测试数据生成完成，总数量: {}", testData.size());
        return testData;
    }

    /**
     * 将批次结果存储到数据库
     * @param texts 原始文本数据
     * @param vectors 向量结果
     * @param batchNumber 批次号
     * @param startIndex 起始索引
     */
    @Transactional
    private void saveBatchToDatabase(List<String> texts, List<float[]> vectors, int batchNumber, int startIndex) {
        if (texts.size() != vectors.size()) {
            throw new IllegalArgumentException("文本数量与向量数量不匹配");
        }
        
        String sql = "INSERT INTO embedding_results (batch_number, item_index, original_text, vector_data, created_time) VALUES (?, ?, ?, ?, NOW())";
        
        List<Object[]> batchArgs = new ArrayList<>();
        for (int i = 0; i < texts.size(); i++) {
            String vectorJson = convertVectorToJson(vectors.get(i));
            batchArgs.add(new Object[]{
                batchNumber,
                startIndex + i,
                texts.get(i),
                vectorJson
            });
        }
        
        jdbcTemplate.batchUpdate(sql, batchArgs);
        log.debug("批次 {} 的 {} 条记录已存储到数据库", batchNumber, texts.size());
    }
    
    /**
     * 将向量数组转换为JSON字符串
     * @param vector 向量数组
     * @return JSON字符串
     */
    private String convertVectorToJson(float[] vector) {
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < vector.length; i++) {
            if (i > 0) sb.append(",");
            sb.append(vector[i]);
        }
        sb.append("]");
        return sb.toString();
    }
    
    /**
     * 等待所有批次完成
     * @param futures 所有批次的Future对象
     * @param totalDataSize 总数据量
     * @param startTime 开始时间
     * @return 处理结果统计
     */
    private ProcessResult waitForAllBatchesToComplete(List<CompletableFuture<Void>> futures, int totalDataSize, long startTime) {
        log.info("等待所有批次完成，总批次数: {}", futures.size());
        
        int completedBatches = 0;
        int failedBatches = 0;
        
        for (int i = 0; i < futures.size(); i++) {
            try {
                CompletableFuture<Void> future = futures.get(i);
                future.get(); // 阻塞等待当前批次完成
                completedBatches++;
                
            } catch (InterruptedException e) {
                log.error("等待第 {} 批次时被中断", i + 1, e);
                Thread.currentThread().interrupt();
                failedBatches++;
            } catch (ExecutionException e) {
                log.error("第 {} 批次执行失败", i + 1, e);
                failedBatches++;
            }
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        boolean success = failedBatches == 0;
        String message = String.format("处理完成，成功批次: %d/%d, 失败批次: %d", 
            completedBatches, futures.size(), failedBatches);
        
        log.info("所有批次处理完成！{}", message);
        
        return new ProcessResult(totalDataSize, completedBatches * BATCH_SIZE, duration, success, message);
    }

    /**
     * 执行完整的批量处理流程
     * @return 处理结果统计信息
     */
    public ProcessResult executeFullProcess() {
        try {
            // 1. 初始化数据库表（如果不存在）
            initializeDatabaseTable();
            
            // 2. 生成测试数据
            List<String> testData = generateTestData();
            
            // 3. 执行批量处理（每批次处理完直接存储到数据库）
            ProcessResult result = processLargeDataset(testData);
            
            log.info("批量处理执行完成: {}", result);
            return result;
            
        } catch (Exception e) {
            ProcessResult result = new ProcessResult(
                0,
                0,
                0,
                false,
                "处理失败: " + e.getMessage()
            );
            
            log.error("批量处理执行失败: {}", result, e);
            return result;
        }
    }
    
    /**
     * 初始化数据库表
     */
    private void initializeDatabaseTable() {
        String createTableSql = """
            CREATE TABLE IF NOT EXISTS embedding_results (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                batch_number INT NOT NULL,
                item_index INT NOT NULL,
                original_text TEXT NOT NULL,
                vector_data JSON NOT NULL,
                created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_batch_number (batch_number),
                INDEX idx_item_index (item_index)
            )
            """;
        
        try {
            jdbcTemplate.execute(createTableSql);
            log.info("数据库表 embedding_results 初始化完成");
        } catch (Exception e) {
            log.error("初始化数据库表失败", e);
            throw new RuntimeException("初始化数据库表失败", e);
        }
    }

    /**
     * 处理结果统计信息
     */
    public static class ProcessResult {
        private final int inputDataCount;
        private final int outputResultCount;
        private final long durationMs;
        private final boolean success;
        private final String message;

        public ProcessResult(int inputDataCount, int outputResultCount, long durationMs, boolean success, String message) {
            this.inputDataCount = inputDataCount;
            this.outputResultCount = outputResultCount;
            this.durationMs = durationMs;
            this.success = success;
            this.message = message;
        }

        public int getInputDataCount() { return inputDataCount; }
        public int getOutputResultCount() { return outputResultCount; }
        public long getDurationMs() { return durationMs; }
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }

        @Override
        public String toString() {
            return String.format("ProcessResult{输入数据: %d, 输出结果: %d, 耗时: %dms (%.2fs), 成功: %s, 消息: '%s'}",
                inputDataCount, outputResultCount, durationMs, durationMs / 1000.0, success, message);
        }
    }
}