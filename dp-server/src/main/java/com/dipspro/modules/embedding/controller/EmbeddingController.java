package com.dipspro.modules.embedding.controller;

import com.dipspro.common.asyncTask.service.GlobalAsyncTaskService;
import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.embedding.dto.*;
import com.dipspro.modules.embedding.service.EmbeddingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 中文句子转向量API控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/embedding")
@RequiredArgsConstructor
@Validated
public class EmbeddingController {

    private final EmbeddingService embeddingService;
    private final GlobalAsyncTaskService globalAsyncTaskService;

    private static final String MODULE_NAME = "embedding";

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<String>> health() {
        if (embeddingService.isInitialized()) {
            return ResponseEntity.ok(ApiResponse.success("服务正常运行"));
        } else {
            return ResponseEntity.status(503)
                    .body(ApiResponse.<String>error("服务未初始化"));
        }
    }

    /**
     * 单文本转向量
     */
    @PostMapping("/encode")
    public ResponseEntity<ApiResponse<EmbeddingResponse>> encode(@Valid @RequestBody EmbeddingRequest request) {
        try {
            if (!request.isValid()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.<EmbeddingResponse>error("请求参数无效：必须提供text或texts"));
            }

            long startTime = System.currentTimeMillis();

            if (request.isAsync()) {
                // 异步处理
                String taskId;
                Map<String, Object> metadata = new HashMap<>();

                if (request.isBatchRequest()) {
                    CompletableFuture<List<float[]>> future = embeddingService.encodeBatchAsync(request.getTexts());
                    metadata.put("textCount", request.getTexts().size());
                    metadata.put("includeSimilarity", request.isIncludeSimilarity());
                    taskId = globalAsyncTaskService.submitTask(
                            MODULE_NAME,
                            GlobalAsyncTaskService.TaskType.BATCH_ENCODE,
                            String.format("批量文本编码（%d条文本）", request.getTexts().size()),
                            future,
                            metadata);
                } else {
                    CompletableFuture<float[]> future = embeddingService.encodeAsync(request.getText());
                    metadata.put("textLength", request.getText().length());
                    taskId = globalAsyncTaskService.submitTask(
                            MODULE_NAME,
                            GlobalAsyncTaskService.TaskType.TEXT_ENCODE,
                            String.format("单文本编码（长度：%d）", request.getText().length()),
                            future,
                            metadata);
                }

                EmbeddingResponse response = EmbeddingResponse.async(taskId);
                return ResponseEntity.ok(ApiResponse.success(response));

            } else {
                // 同步处理
                EmbeddingResponse response;

                if (request.isBatchRequest()) {
                    List<float[]> vectors = embeddingService.encodeBatch(request.getTexts());
                    long processingTime = System.currentTimeMillis() - startTime;

                    if (request.isIncludeSimilarity() && vectors.size() > 1) {
                        // 计算相似度矩阵
                        double[][] similarityMatrix = calculateSimilarityMatrix(vectors);
                        response = EmbeddingResponse.success(vectors, similarityMatrix, processingTime);
                    } else {
                        response = EmbeddingResponse.success(vectors, processingTime);
                    }
                } else {
                    float[] vector = embeddingService.encode(request.getText());
                    long processingTime = System.currentTimeMillis() - startTime;
                    response = EmbeddingResponse.success(vector, processingTime);
                }

                return ResponseEntity.ok(ApiResponse.success(response));
            }

        } catch (Exception e) {
            log.error("编码失败", e);
            EmbeddingResponse response = EmbeddingResponse.error("编码失败: " + e.getMessage());
            return ResponseEntity.status(500).body(ApiResponse.error(response, "编码失败: " + e.getMessage()));
        }
    }

    /**
     * 计算文本相似度
     */
    @PostMapping("/similarity")
    public ResponseEntity<ApiResponse<SimilarityResponse>> similarity(@Valid @RequestBody SimilarityRequest request) {
        try {
            if (!request.isValid()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.<SimilarityResponse>error("请求参数无效：必须提供两个文本或两个向量"));
            }

            long startTime = System.currentTimeMillis();

            if (request.isAsync()) {
                // 异步处理
                CompletableFuture<Object> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        return calculateSimilarity(request);
                    } catch (Exception e) {
                        log.error("异步相似度计算失败", e);
                        throw new RuntimeException(e);
                    }
                });

                Map<String, Object> metadata = new HashMap<>();
                metadata.put("usePrecomputedVectors", request.usePrecomputedVectors());
                if (!request.usePrecomputedVectors()) {
                    metadata.put("text1Length", request.getText1() != null ? request.getText1().length() : 0);
                    metadata.put("text2Length", request.getText2() != null ? request.getText2().length() : 0);
                }

                String taskId = globalAsyncTaskService.submitTask(
                        MODULE_NAME,
                        GlobalAsyncTaskService.TaskType.SIMILARITY_CALC,
                        "文本相似度计算",
                        future,
                        metadata);

                SimilarityResponse response = SimilarityResponse.async(taskId);
                return ResponseEntity.ok(ApiResponse.success(response));

            } else {
                // 同步处理
                SimilarityResponse response = calculateSimilarity(request);
                long processingTime = System.currentTimeMillis() - startTime;
                response.setProcessingTimeMs(processingTime);

                return ResponseEntity.ok(ApiResponse.success(response));
            }

        } catch (Exception e) {
            log.error("相似度计算失败", e);
            SimilarityResponse response = SimilarityResponse.error("相似度计算失败: " + e.getMessage());
            return ResponseEntity.status(500).body(ApiResponse.error(response, "相似度计算失败: " + e.getMessage()));
        }
    }

    /**
     * 批量文本转向量（简化接口）
     */
    @PostMapping("/batch")
    public ResponseEntity<EmbeddingResponse> encodeBatch(@RequestBody @Valid EmbeddingRequest request) {
        try {
            if (request.getTexts() == null || request.getTexts().isEmpty()) {
                return ResponseEntity.badRequest().body(EmbeddingResponse.error("文本列表不能为空"));
            }

            if (request.getTexts().size() > 100) {
                return ResponseEntity.badRequest().body(EmbeddingResponse.error("批量处理文本数量不能超过100个"));
            }

            if (request.isAsync()) {
                // 异步处理
                CompletableFuture<List<float[]>> future = embeddingService.encodeBatchAsync(request.getTexts());
                Map<String, Object> metadata = new HashMap<>();
                metadata.put("textCount", request.getTexts().size());
                metadata.put("includeSimilarity", request.isIncludeSimilarity());

                String taskId = globalAsyncTaskService.submitTask(
                        MODULE_NAME,
                        GlobalAsyncTaskService.TaskType.BATCH_ENCODE,
                        String.format("批量文本编码（%d条文本）", request.getTexts().size()),
                        future,
                        metadata);

                EmbeddingResponse response = EmbeddingResponse.async(taskId);
                return ResponseEntity.ok(response);
            } else {
                // 同步处理
                long startTime = System.currentTimeMillis();
                List<float[]> vectors = embeddingService.encodeBatch(request.getTexts());
                long processingTime = System.currentTimeMillis() - startTime;

                EmbeddingResponse response;
                if (request.isIncludeSimilarity() && vectors.size() > 1) {
                    // 计算相似度矩阵
                    double[][] similarityMatrix = calculateSimilarityMatrix(vectors);
                    response = EmbeddingResponse.success(vectors, similarityMatrix, processingTime);
                } else {
                    response = EmbeddingResponse.success(vectors, processingTime);
                }
                return ResponseEntity.ok(response);
            }
        } catch (Exception e) {
            log.error("批量编码失败", e);
            return ResponseEntity.status(500).body(EmbeddingResponse.error("批量编码失败: " + e.getMessage()));
        }
    }

    /**
     * 高性能并行批量文本转向量
     */
    @PostMapping("/batch-parallel")
    public ResponseEntity<ApiResponse<EmbeddingResponse>> encodeBatchParallel(
            @Valid @RequestBody EmbeddingRequest request) {
        // 实现并行批量处理逻辑
        try {
            if (!request.isValid()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.<EmbeddingResponse>error("请求参数无效：必须提供texts"));
            }
            if (request.getTexts() == null || request.getTexts().isEmpty()) {
                return ResponseEntity.badRequest().body(ApiResponse.<EmbeddingResponse>error("文本列表不能为空"));
            }
            if (request.getTexts().size() > 100) {
                return ResponseEntity.badRequest().body(ApiResponse.<EmbeddingResponse>error("批量处理文本数量不能超过100个"));
            }
            CompletableFuture<List<float[]>> future = embeddingService.encodeBatchParallel(request.getTexts());
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("textCount", request.getTexts().size());
            metadata.put("includeSimilarity", request.isIncludeSimilarity());
            String taskId = globalAsyncTaskService.submitTask(
                    MODULE_NAME,
                    GlobalAsyncTaskService.TaskType.BATCH_ENCODE,
                    String.format("并行批量文本编码（%d条文本）", request.getTexts().size()),
                    future,
                    metadata);
            EmbeddingResponse response = EmbeddingResponse.async(taskId);
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("并行批量编码失败", e);
            return ResponseEntity.status(0).body(ApiResponse.error("并行批量编码失败: " + e.getMessage()));
        }
    }

    /**
     * 计算相似度矩阵
     */
    private double[][] calculateSimilarityMatrix(List<float[]> vectors) {
        int size = vectors.size();
        double[][] matrix = new double[size][size];

        for (int i = 0; i < size; i++) {
            for (int j = 0; j < size; j++) {
                if (i == j) {
                    matrix[i][j] = 1.0;
                } else if (i < j) {
                    double similarity = embeddingService.cosineSimilarity(vectors.get(i), vectors.get(j));
                    matrix[i][j] = similarity;
                    matrix[j][i] = similarity; // 对称矩阵
                }
            }
        }

        return matrix;
    }

    /**
     * 计算两个文本或向量的相似度
     */
    private SimilarityResponse calculateSimilarity(SimilarityRequest request) {
        if (request.usePrecomputedVectors()) {
            // 使用预计算的向量
            double similarity = embeddingService.cosineSimilarity(request.getVector1(), request.getVector2());
            return SimilarityResponse.success(similarity, 0);
        } else {
            // 从文本计算向量
            float[] vector1 = embeddingService.encode(request.getText1());
            float[] vector2 = embeddingService.encode(request.getText2());
            double similarity = embeddingService.cosineSimilarity(vector1, vector2);
            return SimilarityResponse.success(similarity, vector1, vector2, 0);
        }
    }
}