package com.dipspro.modules.embedding.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 文本相似度计算请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SimilarityRequest {
    
    /**
     * 第一个文本
     */
    @NotBlank(message = "第一个文本不能为空")
    @Size(max = 512, message = "文本长度不能超过512个字符")
    private String text1;
    
    /**
     * 第二个文本
     */
    @NotBlank(message = "第二个文本不能为空")
    @Size(max = 512, message = "文本长度不能超过512个字符")
    private String text2;
    
    /**
     * 第一个向量（可选，如果提供则直接使用，不进行文本编码）
     */
    private float[] vector1;
    
    /**
     * 第二个向量（可选，如果提供则直接使用，不进行文本编码）
     */
    private float[] vector2;
    
    /**
     * 是否异步处理
     */
    private boolean async = false;
    
    /**
     * 验证请求是否有效
     */
    public boolean isValid() {
        // 要么提供文本，要么提供向量
        boolean hasTexts = text1 != null && !text1.trim().isEmpty() && 
                          text2 != null && !text2.trim().isEmpty();
        boolean hasVectors = vector1 != null && vector1.length > 0 && 
                            vector2 != null && vector2.length > 0 && 
                            vector1.length == vector2.length;
        
        return hasTexts || hasVectors;
    }
    
    /**
     * 是否使用预计算的向量
     */
    public boolean usePrecomputedVectors() {
        return vector1 != null && vector1.length > 0 && 
               vector2 != null && vector2.length > 0;
    }
}