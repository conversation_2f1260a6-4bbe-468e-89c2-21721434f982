package com.dipspro.modules.embedding.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.dipspro.modules.embedding.dto.PromptTemplateVectorDto;
import com.dipspro.modules.embedding.entity.PromptTemplateVector;
import com.dipspro.modules.embedding.mapper.PromptTemplateVectorMapper;
import com.dipspro.modules.embedding.repository.PromptTemplateVectorRepository;
import com.dipspro.modules.embedding.service.EmbeddingService;
import com.dipspro.modules.embedding.service.PromptTemplateVectorService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class PromptTemplateVectorServiceImpl implements PromptTemplateVectorService {

    private final PromptTemplateVectorRepository promptTemplateVectorRepository;
    private final EmbeddingService embeddingService;
    private final PromptTemplateVectorMapper mapper;

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateVectorDto> getAllVectors() {
        log.debug("Fetching all prompt template vectors");
        return promptTemplateVectorRepository.findAllByOrderByCreatedAtDesc().stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<PromptTemplateVectorDto> getVectorById(UUID id) {
        log.debug("Fetching prompt template vector by id: {}", id);
        return promptTemplateVectorRepository.findById(id)
                .map(mapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<PromptTemplateVectorDto> getVectorByTemplateId(UUID templateId) {
        log.debug("Fetching prompt template vector by template id: {}", templateId);
        return promptTemplateVectorRepository.findByTemplateId(templateId)
                .map(mapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateVectorDto> getVectorsByIds(List<UUID> ids) {
        log.debug("Fetching prompt template vectors by ids: {}", ids);
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return promptTemplateVectorRepository.findByIdIn(ids).stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateVectorDto> getVectorsByTemplateIds(List<UUID> templateIds) {
        log.debug("Fetching prompt template vectors by template ids: {}", templateIds);
        if (CollectionUtils.isEmpty(templateIds)) {
            return Collections.emptyList();
        }
        return promptTemplateVectorRepository.findByTemplateIdIn(templateIds).stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public PromptTemplateVectorDto createVector(PromptTemplateVectorDto vectorDto) {
        log.info("Creating new prompt template vector for template: {}", vectorDto.getTemplateId());

        // Auto-generate embedding from typicalDescription if not provided
        if (vectorDto.getEmbedding() == null && vectorDto.getTypicalDescription() != null) {
            float[] embedding = embeddingService.encode(vectorDto.getTypicalDescription());
            vectorDto.setEmbedding(embedding);
        }

        PromptTemplateVector vector = mapper.toEntity(vectorDto);
        vector.setId(null); // Ensure new entity
        PromptTemplateVector savedVector = promptTemplateVectorRepository.save(vector);
        log.info("Created prompt template vector with id: {}", savedVector.getId());
        return mapper.toDto(savedVector);
    }

    @Override
    @Transactional
    public Optional<PromptTemplateVectorDto> updateVector(UUID id, PromptTemplateVectorDto vectorDto) {
        log.info("Updating prompt template vector with id: {}", id);
        return promptTemplateVectorRepository.findById(id)
                .map(existingVector -> {
                    existingVector.setTemplateId(vectorDto.getTemplateId());
                    existingVector.setTypicalDescription(vectorDto.getTypicalDescription());
                    existingVector.setUpdatedBy(vectorDto.getUpdatedBy());

                    // Auto-regenerate embedding from typicalDescription
                    if (vectorDto.getTypicalDescription() != null) {
                        float[] newEmbedding = embeddingService.encode(vectorDto.getTypicalDescription());
                        existingVector.setEmbedding(newEmbedding);
                    } else if (vectorDto.getEmbedding() != null) {
                        existingVector.setEmbedding(vectorDto.getEmbedding());
                    }

                    PromptTemplateVector updatedVector = promptTemplateVectorRepository.save(existingVector);
                    log.info("Successfully updated vector with id: {}", updatedVector.getId());
                    return mapper.toDto(updatedVector);
                });
    }

    @Override
    @Transactional
    public boolean deleteVector(UUID id) {
        log.warn("Attempting to delete prompt template vector with id: {}", id);
        if (promptTemplateVectorRepository.existsById(id)) {
            promptTemplateVectorRepository.deleteById(id);
            log.warn("Successfully deleted prompt template vector with id: {}", id);
            return true;
        } else {
            log.warn("Prompt template vector with id: {} not found for deletion", id);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean deleteVectorByTemplateId(UUID templateId) {
        log.warn("Attempting to delete prompt template vector with template id: {}", templateId);
        if (promptTemplateVectorRepository.existsByTemplateId(templateId)) {
            promptTemplateVectorRepository.deleteByTemplateId(templateId);
            log.warn("Successfully deleted prompt template vector with template id: {}", templateId);
            return true;
        } else {
            log.warn("Prompt template vector with template id: {} not found for deletion", templateId);
            return false;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByTemplateId(UUID templateId) {
        return promptTemplateVectorRepository.existsByTemplateId(templateId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateVectorDto> getVectorsByCreatedBy(String createdBy) {
        log.debug("Fetching vectors by created by: {}", createdBy);
        return promptTemplateVectorRepository.findByCreatedByOrderByCreatedAtDesc(createdBy).stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateVectorDto> searchVectorsByDescription(String description) {
        log.debug("Searching vectors by description: {}", description);
        return promptTemplateVectorRepository.findByTypicalDescriptionContaining(description).stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public double calculateSimilarity(UUID vectorId1, UUID vectorId2) {
        log.debug("Calculating similarity between vectors: {} and {}", vectorId1, vectorId2);

        Optional<PromptTemplateVector> vector1 = promptTemplateVectorRepository.findById(vectorId1);
        Optional<PromptTemplateVector> vector2 = promptTemplateVectorRepository.findById(vectorId2);

        if (vector1.isEmpty() || vector2.isEmpty()) {
            throw new RuntimeException("向量不存在");
        }

        return embeddingService.cosineSimilarity(vector1.get().getEmbedding(), vector2.get().getEmbedding());
    }

    @Override
    @Transactional(readOnly = true)
    public double calculateSimilarityWithText(UUID vectorId, String text) {
        log.debug("Calculating similarity between vector {} and text", vectorId);

        Optional<PromptTemplateVector> vector = promptTemplateVectorRepository.findById(vectorId);
        if (vector.isEmpty()) {
            throw new RuntimeException("向量不存在");
        }

        float[] textEmbedding = embeddingService.encode(text);
        return embeddingService.cosineSimilarity(vector.get().getEmbedding(), textEmbedding);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateVectorDto> findSimilarVectors(String text, int limit) {
        log.debug("Finding similar vectors for text with limit: {}", limit);

        // Generate embedding for input text
        float[] textEmbedding = embeddingService.encode(text);

        // Get all vectors and calculate similarities
        List<PromptTemplateVector> allVectors = promptTemplateVectorRepository.findAll();

        return allVectors.stream()
                .map(vector -> {
                    double similarity = embeddingService.cosineSimilarity(textEmbedding, vector.getEmbedding());
                    PromptTemplateVectorDto dto = mapper.toDto(vector);
                    // Note: You might want to add a similarity field to the DTO
                    return dto;
                })
                .sorted((v1, v2) -> {
                    double sim1 = embeddingService.cosineSimilarity(textEmbedding, v1.getEmbedding());
                    double sim2 = embeddingService.cosineSimilarity(textEmbedding, v2.getEmbedding());
                    return Double.compare(sim2, sim1); // Descending order
                })
                .limit(limit)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateVectorDto> findSimilarVectors(UUID vectorId, int limit) {
        log.debug("Finding similar vectors for vector {} with limit: {}", vectorId, limit);

        Optional<PromptTemplateVector> targetVector = promptTemplateVectorRepository.findById(vectorId);
        if (targetVector.isEmpty()) {
            throw new RuntimeException("目标向量不存在");
        }

        float[] targetEmbedding = targetVector.get().getEmbedding();

        // Get all vectors except the target one
        List<PromptTemplateVector> allVectors = promptTemplateVectorRepository.findAll().stream()
                .filter(v -> !v.getId().equals(vectorId))
                .collect(Collectors.toList());

        return allVectors.stream()
                .map(mapper::toDto)
                .sorted((v1, v2) -> {
                    double sim1 = embeddingService.cosineSimilarity(targetEmbedding, v1.getEmbedding());
                    double sim2 = embeddingService.cosineSimilarity(targetEmbedding, v2.getEmbedding());
                    return Double.compare(sim2, sim1); // Descending order
                })
                .limit(limit)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public List<PromptTemplateVectorDto> createVectorsBatch(List<PromptTemplateVectorDto> vectorDtos) {
        log.info("Creating batch of {} vectors", vectorDtos.size());

        // Auto-generate embeddings for DTOs that don't have them
        List<String> textsToEncode = new ArrayList<>();
        List<Integer> indicesNeedingEmbedding = new ArrayList<>();

        for (int i = 0; i < vectorDtos.size(); i++) {
            PromptTemplateVectorDto dto = vectorDtos.get(i);
            if (dto.getEmbedding() == null && dto.getTypicalDescription() != null) {
                textsToEncode.add(dto.getTypicalDescription());
                indicesNeedingEmbedding.add(i);
            }
        }

        // Batch encode if needed
        if (!textsToEncode.isEmpty()) {
            List<float[]> embeddings = embeddingService.encodeBatchParallel(textsToEncode).join();
            for (int i = 0; i < indicesNeedingEmbedding.size(); i++) {
                int dtoIndex = indicesNeedingEmbedding.get(i);
                vectorDtos.get(dtoIndex).setEmbedding(embeddings.get(i));
            }
        }

        List<PromptTemplateVector> entities = vectorDtos.stream()
                .map(dto -> {
                    PromptTemplateVector entity = mapper.toEntity(dto);
                    entity.setId(null); // Ensure new entities
                    return entity;
                })
                .collect(Collectors.toList());

        List<PromptTemplateVector> savedEntities = promptTemplateVectorRepository.saveAll(entities);
        log.info("Successfully created {} vectors", savedEntities.size());

        return savedEntities.stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateVectorDto> getVectorsBySerialIdRange(Long startId, Long endId) {
        log.debug("Fetching vectors by serial id range: {} to {}", startId, endId);
        return promptTemplateVectorRepository.findBySerialIdBetween(startId, endId).stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<PromptTemplateVectorDto> getLatestVectors(int limit) {
        log.debug("Fetching latest {} vectors", limit);
        return promptTemplateVectorRepository.findTopByOrderByCreatedAtDesc(limit).stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }
}