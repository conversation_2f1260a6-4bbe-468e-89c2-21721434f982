package com.dipspro.modules.embedding.mapper;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import com.dipspro.modules.embedding.dto.PromptTemplateVectorDto;
import com.dipspro.modules.embedding.entity.PromptTemplateVector;

/**
 * PromptTemplateVector 实体和 DTO 之间的映射器
 * 使用 unmappedTargetPolicy = IGNORE 来忽略未映射的字段警告
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PromptTemplateVectorMapper {

    PromptTemplateVectorMapper INSTANCE = Mappers.getMapper(PromptTemplateVectorMapper.class);

    /**
     * 实体转换为 DTO
     *
     * @param entity 实体对象
     * @return DTO 对象
     */
    PromptTemplateVectorDto toDto(PromptTemplateVector entity);

    /**
     * DTO 转换为实体
     *
     * @param dto DTO 对象
     * @return 实体对象
     */
    PromptTemplateVector toEntity(PromptTemplateVectorDto dto);

    /**
     * 实体列表转换为 DTO 列表
     *
     * @param entities 实体列表
     * @return DTO 列表
     */
    List<PromptTemplateVectorDto> toDtoList(List<PromptTemplateVector> entities);

    /**
     * DTO 列表转换为实体列表
     *
     * @param dtos DTO 列表
     * @return 实体列表
     */
    List<PromptTemplateVector> toEntityList(List<PromptTemplateVectorDto> dtos);
}