package com.dipspro.modules.embedding.controller;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dipspro.common.asyncTask.service.GlobalAsyncTaskService;
import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.embedding.dto.PromptTemplateVectorDto;

import com.dipspro.modules.embedding.service.PromptTemplateVectorService;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/embedding/templateVectors")
@RequiredArgsConstructor
public class PromptTemplateVectorController {

    private final PromptTemplateVectorService promptTemplateVectorService;
    private final GlobalAsyncTaskService globalAsyncTaskService;

    /**
     * 获取所有向量
     */
    @GetMapping
    public ApiResponse<List<PromptTemplateVectorDto>> getAllVectors() {
        log.info("Received request to get all vectors");
        List<PromptTemplateVectorDto> vectors = promptTemplateVectorService.getAllVectors();
        log.info("Found {} vectors", vectors.size());
        return ApiResponse.success(vectors);
    }

    /**
     * 根据ID获取向量
     */
    @GetMapping("/{id}")
    public ApiResponse<PromptTemplateVectorDto> getVectorById(@PathVariable UUID id) {
        log.info("Received request to get vector by id: {}", id);
        return promptTemplateVectorService.getVectorById(id)
                .map(ApiResponse::success)
                .orElse(ApiResponse.error("Vector not found with id: " + id));
    }

    /**
     * 根据模板ID获取向量
     */
    @GetMapping("/template/{templateId}")
    public ApiResponse<PromptTemplateVectorDto> getVectorByTemplateId(@PathVariable UUID templateId) {
        log.info("Received request to get vector by template id: {}", templateId);
        return promptTemplateVectorService.getVectorByTemplateId(templateId)
                .map(ApiResponse::success)
                .orElse(ApiResponse.error("Vector not found with template id: " + templateId));
    }

    /**
     * 创建向量（支持同步和异步）
     * 传入的DTO不包含向量数据，Service层统一生成向量
     */
    @PostMapping
    public ResponseEntity<ApiResponse<?>> createVector(
            @Valid @RequestBody PromptTemplateVectorDto vectorDto,
            @RequestParam(defaultValue = "false") boolean async) {
        log.info("Received request to create vector for template: {}, async: {}", vectorDto.getTemplateId(), async);

        try {
            if (async) {
                // 异步创建
                CompletableFuture<PromptTemplateVectorDto> future = CompletableFuture.supplyAsync(() -> {
                    return promptTemplateVectorService.createVector(vectorDto);
                });

                String taskId = globalAsyncTaskService.submitTask(
                        "embedding",
                        GlobalAsyncTaskService.TaskType.TEXT_ENCODE,
                        "创建向量: " + vectorDto.getTemplateId(),
                        future);

                return ResponseEntity.ok(ApiResponse.success(taskId, "异步向量创建任务已提交"));
            } else {
                // 同步创建
                PromptTemplateVectorDto createdVector = promptTemplateVectorService.createVector(vectorDto);
                return ResponseEntity.ok(ApiResponse.success(createdVector, "向量创建成功"));
            }
        } catch (Exception e) {
            log.error("Error creating vector: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(ApiResponse.error("创建向量失败: " + e.getMessage()));
        }
    }

    /**
     * 更新向量（自动重新计算向量）
     */
    @PutMapping("/{id}")
    public ApiResponse<PromptTemplateVectorDto> updateVector(
            @PathVariable UUID id,
            @Valid @RequestBody PromptTemplateVectorDto vectorDto) {
        log.info("Received request to update vector with id: {}", id);
        try {
            return promptTemplateVectorService.updateVector(id, vectorDto)
                    .map(updatedVector -> ApiResponse.success(updatedVector, "向量更新成功"))
                    .orElse(ApiResponse.error("Vector not found for update with id: " + id));
        } catch (Exception e) {
            log.error("Error updating vector: {}", e.getMessage(), e);
            return ApiResponse.error("更新向量失败: " + e.getMessage());
        }
    }

    /**
     * 删除向量
     */
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteVector(@PathVariable UUID id) {
        log.info("Received request to delete vector with id: {}", id);
        try {
            boolean deleted = promptTemplateVectorService.deleteVector(id);
            if (deleted) {
                return ApiResponse.success("向量删除成功");
            } else {
                return ApiResponse.error("Vector not found with id: " + id);
            }
        } catch (Exception e) {
            log.error("Error deleting vector: {}", e.getMessage(), e);
            return ApiResponse.error("删除向量失败: " + e.getMessage());
        }
    }

    /**
     * 根据模板ID删除向量
     */
    @DeleteMapping("/template/{templateId}")
    public ApiResponse<String> deleteVectorByTemplateId(@PathVariable UUID templateId) {
        log.info("Received request to delete vector by template id: {}", templateId);
        try {
            boolean deleted = promptTemplateVectorService.deleteVectorByTemplateId(templateId);
            if (deleted) {
                return ApiResponse.success("向量删除成功");
            } else {
                return ApiResponse.error("Vector not found with template id: " + templateId);
            }
        } catch (Exception e) {
            log.error("Error deleting vector by template id: {}", e.getMessage(), e);
            return ApiResponse.error("删除向量失败: " + e.getMessage());
        }
    }

    /**
     * 检查模板ID是否已存在向量
     */
    @GetMapping("/exists/template/{templateId}")
    public ApiResponse<Boolean> existsByTemplateId(@PathVariable UUID templateId) {
        log.info("Received request to check if vector exists for template id: {}", templateId);
        boolean exists = promptTemplateVectorService.existsByTemplateId(templateId);
        return ApiResponse.success(exists);
    }

    /**
     * 根据创建者查找向量
     */
    @GetMapping("/created-by/{createdBy}")
    public ApiResponse<List<PromptTemplateVectorDto>> getVectorsByCreatedBy(@PathVariable String createdBy) {
        log.info("Received request to get vectors by created by: {}", createdBy);
        List<PromptTemplateVectorDto> vectors = promptTemplateVectorService.getVectorsByCreatedBy(createdBy);
        return ApiResponse.success(vectors);
    }

    /**
     * 根据典型描述模糊查询
     */
    @GetMapping("/search")
    public ApiResponse<List<PromptTemplateVectorDto>> searchVectorsByDescription(
            @RequestParam String description) {
        log.info("Received request to search vectors by description: {}", description);
        List<PromptTemplateVectorDto> vectors = promptTemplateVectorService.searchVectorsByDescription(description);
        return ApiResponse.success(vectors);
    }

    /**
     * 计算两个向量的相似度
     */
    @GetMapping("/similarity/{vectorId1}/{vectorId2}")
    public ApiResponse<Double> calculateSimilarity(
            @PathVariable UUID vectorId1,
            @PathVariable UUID vectorId2) {
        log.info("Received request to calculate similarity between vectors: {} and {}", vectorId1, vectorId2);
        try {
            double similarity = promptTemplateVectorService.calculateSimilarity(vectorId1, vectorId2);
            return ApiResponse.success(similarity);
        } catch (Exception e) {
            log.error("Error calculating similarity: {}", e.getMessage(), e);
            return ApiResponse.error("计算相似度失败: " + e.getMessage());
        }
    }

    /**
     * 计算向量与给定文本的相似度
     */
    @PostMapping("/similarity/{vectorId}/text")
    public ApiResponse<Double> calculateSimilarityWithText(
            @PathVariable UUID vectorId,
            @RequestBody String text) {
        log.info("Received request to calculate similarity between vector {} and text", vectorId);
        try {
            double similarity = promptTemplateVectorService.calculateSimilarityWithText(vectorId, text);
            return ApiResponse.success(similarity);
        } catch (Exception e) {
            log.error("Error calculating similarity with text: {}", e.getMessage(), e);
            return ApiResponse.error("计算相似度失败: " + e.getMessage());
        }
    }

    /**
     * 查找与给定文本最相似的向量
     */
    @PostMapping("/similar/text")
    public ApiResponse<List<PromptTemplateVectorDto>> findSimilarVectorsByText(
            @RequestBody String text,
            @RequestParam(defaultValue = "10") int limit) {
        log.info("Received request to find similar vectors for text with limit: {}", limit);
        try {
            List<PromptTemplateVectorDto> vectors = promptTemplateVectorService.findSimilarVectors(text, limit);
            return ApiResponse.success(vectors);
        } catch (Exception e) {
            log.error("Error finding similar vectors: {}", e.getMessage(), e);
            return ApiResponse.error("查找相似向量失败: " + e.getMessage());
        }
    }

    /**
     * 查找与给定向量最相似的向量
     */
    @GetMapping("/similar/{vectorId}")
    public ApiResponse<List<PromptTemplateVectorDto>> findSimilarVectors(
            @PathVariable UUID vectorId,
            @RequestParam(defaultValue = "10") int limit) {
        log.info("Received request to find similar vectors for vector {} with limit: {}", vectorId, limit);
        try {
            List<PromptTemplateVectorDto> vectors = promptTemplateVectorService.findSimilarVectors(vectorId, limit);
            return ApiResponse.success(vectors);
        } catch (Exception e) {
            log.error("Error finding similar vectors: {}", e.getMessage(), e);
            return ApiResponse.error("查找相似向量失败: " + e.getMessage());
        }
    }

    /**
     * 批量创建向量（支持同步和异步）
     * 传入的DTO列表不包含向量数据，Service层统一生成向量
     */
    @PostMapping("/batch")
    public ResponseEntity<ApiResponse<?>> createVectorsBatch(
            @Valid @RequestBody List<PromptTemplateVectorDto> vectorDtos,
            @RequestParam(defaultValue = "false") boolean async) {
        log.info("Received request to create batch of {} vectors, async: {}", vectorDtos.size(), async);

        try {
            if (async) {
                // 异步批量创建
                CompletableFuture<List<PromptTemplateVectorDto>> future = CompletableFuture.supplyAsync(() -> {
                    return promptTemplateVectorService.createVectorsBatch(vectorDtos);
                });

                String taskId = globalAsyncTaskService.submitTask(
                        "embedding",
                        GlobalAsyncTaskService.TaskType.BATCH_ENCODE,
                        "批量创建向量: " + vectorDtos.size() + "个",
                        future);

                return ResponseEntity.ok(ApiResponse.success(taskId, "异步批量向量创建任务已提交"));
            } else {
                // 同步批量创建
                List<PromptTemplateVectorDto> createdVectors = promptTemplateVectorService
                        .createVectorsBatch(vectorDtos);
                return ResponseEntity.ok(ApiResponse.success(createdVectors, "批量创建向量成功"));
            }
        } catch (Exception e) {
            log.error("Error creating vectors batch: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(ApiResponse.error("批量创建向量失败: " + e.getMessage()));
        }
    }
}