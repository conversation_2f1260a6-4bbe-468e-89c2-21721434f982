package com.dipspro.modules.embedding.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * 中文句子转向量请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmbeddingRequest {
    
    /**
     * 单个文本（用于单文本转换）
     */
    @Size(max = 512, message = "文本长度不能超过512个字符")
    private String text;
    
    /**
     * 文本列表（用于批量转换）
     */
    @Size(max = 100, message = "批量处理文本数量不能超过100个")
    private List<@NotBlank(message = "文本不能为空") @Size(max = 512, message = "文本长度不能超过512个字符") String> texts;
    
    /**
     * 是否异步处理
     */
    private boolean async = false;
    
    /**
     * 是否返回相似度计算结果（仅在提供多个文本时有效）
     */
    private boolean includeSimilarity = false;
    
    /**
     * 验证请求是否有效
     */
    public boolean isValid() {
        return (text != null && !text.trim().isEmpty()) || 
               (texts != null && !texts.isEmpty() && texts.stream().allMatch(t -> t != null && !t.trim().isEmpty()));
    }
    
    /**
     * 是否为批量请求
     */
    public boolean isBatchRequest() {
        return texts != null && !texts.isEmpty();
    }
}