package com.dipspro.modules.embedding.service.impl;

import ai.onnxruntime.*;
import com.dipspro.modules.embedding.service.EmbeddingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 高性能中文句子转向量服务实现
 * 基于shibing624/text2vec-base-chinese模型
 * 优化目标：1-2秒处理1000条文本
 * padTokenId *
 * 架构设计：
 * 1. 使用Session Pool避免重复创建ONNX Session
 * 2. 支持批量处理和并行处理提高吞吐量
 * 3. 缓存常用token ID减少查找开销
 * 4. 优化内存分配和张量创建
 */
@Slf4j
@Service
public class Text2VecEmbeddingService implements EmbeddingService {

    // ================================ 常量定义 ================================

    /** 最大序列长度，超过此长度的文本将被截断 */
    private static final int MAX_SEQUENCE_LENGTH = 512;

    /** 输出向量维度 */
    private static final int VECTOR_DIMENSION = 768;

    /** BERT特殊标记 */
    private static final String CLS_TOKEN = "[CLS]";
    private static final String SEP_TOKEN = "[SEP]";
    private static final String PAD_TOKEN = "[PAD]";
    private static final String UNK_TOKEN = "[UNK]";

    /** 性能优化常量 */
    private static final int OPTIMAL_BATCH_SIZE = 64; // 优化的批处理大小
    private static final int PARALLEL_THRESHOLD = 32; // 并行处理阈值

    // ================================ 配置属性 ================================

    /** ONNX模型文件路径 */
    @Value("${embedding.model.path:}")
    private String modelPath;

    /** 词汇表文件路径 */
    @Value("${embedding.model.vocab-path:}")
    private String vocabPath;

    /** 线程池大小，默认16个线程 */
    @Value("${embedding.thread-pool.size:16}")
    private int threadPoolSize;

    // ================================ 核心组件 ================================

    /** ONNX Runtime环境 */
    private OrtEnvironment environment;

    /** 词汇表映射：token -> token_id */
    private Map<String, Integer> vocabulary;

    /** 异步处理线程池 */
    private ExecutorService executorService;

    /** 服务初始化状态 */
    private volatile boolean initialized = false;

    /** 高性能Session Pool，避免重复创建Session */
    private BlockingQueue<OrtSession> sessionPool;
    private List<OrtSession> allSessions;

    /** 缓存常用token ID，避免重复查找 */
    private long clsTokenId, sepTokenId, padTokenId, unkTokenId;

    // ================================ 生命周期管理 ================================

    /**
     * 服务初始化方法
     * 按顺序初始化各个组件：环境 -> 词汇表 -> Session池 -> 线程池 -> 预热
     */
    @PostConstruct
    public void init() {
        try {
            log.info("初始化Text2Vec中文句子转向量服务...");

            // 初始化ONNX Runtime环境
            environment = OrtEnvironment.getEnvironment();

            // 检查模型文件是否存在
            Path modelFilePath = Paths.get(modelPath);
            if (!Files.exists(modelFilePath)) {
                log.error("模型文件不存在: {}", modelPath);
                return;
            }

            // 加载词汇表
            loadVocabulary();

            // 初始化Session Pool
            initializeSessionPool();

            // 初始化线程池
            initializeThreadPool();

            initialized = true;
            log.info("Text2Vec中文句子转向量服务初始化完成");

            // 预热模型以提高首次推理性能
            warmUp();

        } catch (Exception e) {
            log.error("初始化Text2Vec服务失败", e);
        }
    }

    /**
     * 服务销毁方法
     * 按顺序清理资源：线程池 -> Session池 -> ONNX环境
     */
    @PreDestroy
    public void destroy() {
        log.info("销毁Text2Vec服务...");

        // 关闭线程池
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 关闭所有Session
        if (allSessions != null) {
            for (OrtSession session : allSessions) {
                try {
                    session.close();
                } catch (Exception e) {
                    log.error("关闭ONNX会话失败", e);
                }
            }
        }

        // 关闭ONNX环境
        if (environment != null) {
            try {
                environment.close();
            } catch (Exception e) {
                log.error("关闭ONNX环境失败", e);
            }
        }

        initialized = false;
        log.info("Text2Vec服务销毁完成");
    }

    // ================================ 初始化辅助方法 ================================

    /**
     * 加载词汇表文件
     * 将词汇表文件中的每一行作为token，行号作为token_id
     * 同时缓存常用的特殊token ID以提高性能
     * 
     * @throws IOException 文件读取异常
     */
    private void loadVocabulary() throws IOException {
        log.info("加载词汇表: {}", vocabPath);
        vocabulary = new HashMap<>(); // 初始化后不再修改，使用HashMap即可

        Path vocabFilePath = Paths.get(vocabPath);
        if (!Files.exists(vocabFilePath)) {
            log.error("词汇表文件不存在: {}", vocabPath);
            throw new RuntimeException("词汇表文件不存在");
        }

        List<String> lines = Files.readAllLines(vocabFilePath);
        for (int i = 0; i < lines.size(); i++) {
            String token = lines.get(i).trim();
            if (!token.isEmpty()) {
                vocabulary.put(token, i);
            }
        }

        // 缓存常用token ID，提高性能
        clsTokenId = vocabulary.get(CLS_TOKEN);
        sepTokenId = vocabulary.get(SEP_TOKEN);
        padTokenId = vocabulary.get(PAD_TOKEN);
        unkTokenId = vocabulary.get(UNK_TOKEN);

        log.info("词汇表加载完成，共{}个词汇", vocabulary.size());
    }

    /**
     * 初始化高性能Session Pool
     * 预创建多个ONNX Session实例，避免运行时创建开销
     * 使用最高优化级别和合适的线程配置
     * 
     * @throws OrtException ONNX Runtime异常
     */
    private void initializeSessionPool() throws OrtException {
        log.info("初始化Session Pool，大小: {}", threadPoolSize);

        sessionPool = new LinkedBlockingQueue<>(threadPoolSize);
        allSessions = new ArrayList<>(threadPoolSize);

        // 创建优化的Session实例
        for (int i = 0; i < threadPoolSize; i++) {
            OrtSession.SessionOptions sessionOptions = new OrtSession.SessionOptions();
            sessionOptions.setOptimizationLevel(OrtSession.SessionOptions.OptLevel.ALL_OPT); // 使用最高优化级别
            sessionOptions.setIntraOpNumThreads(8); // 适当增加线程数
            sessionOptions.setExecutionMode(OrtSession.SessionOptions.ExecutionMode.SEQUENTIAL);

            OrtSession session = environment.createSession(modelPath, sessionOptions);
            sessionPool.offer(session);
            allSessions.add(session);
        }

        log.info("Session Pool初始化完成，创建了{}个Session实例", threadPoolSize);
    }

    /**
     * 初始化异步处理线程池
     * 使用固定大小的线程池，线程名称便于调试
     */
    private void initializeThreadPool() {
        ThreadFactory threadFactory = new ThreadFactory() {
            private final AtomicInteger threadNumber = new AtomicInteger(1);

            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r, "Text2Vec-Worker-" + threadNumber.getAndIncrement());
                t.setDaemon(true);
                return t;
            }
        };

        executorService = Executors.newFixedThreadPool(threadPoolSize, threadFactory);
        log.info("初始化线程池完成，线程数: {}", threadPoolSize);
    }

    /**
     * 预热模型，提高首次推理性能
     * 使用少量示例文本进行一次推理，让模型加载到内存
     */
    private void warmUp() {
        try {
            // 使用示例文本进行预热
            List<String> warmupTexts = Arrays.asList("测试", "预热", "模型");
            encodeBatch(warmupTexts);
            log.info("模型预热完成");
        } catch (Exception e) {
            log.warn("模型预热失败: {}", e.getMessage());
        }
    }

    // ================================ Session Pool管理
    // ================================

    /**
     * 从Session Pool获取Session
     * 线程安全的阻塞获取，确保每个线程都能获得可用的Session
     * 
     * @return ONNX Session实例
     * @throws InterruptedException 线程中断异常
     */
    private OrtSession borrowSession() throws InterruptedException {
        return sessionPool.take(); // 阻塞直到有可用的Session
    }

    /**
     * 归还Session到Pool
     * 使用完Session后必须归还，以供其他线程使用
     * 
     * @param session 要归还的Session实例
     */
    private void returnSession(OrtSession session) {
        if (session != null) {
            sessionPool.offer(session);
        }
    }

    // ================================ 核心编码接口实现 ================================

    /**
     * 单条文本编码为向量
     * 适用于实时性要求高的单条文本处理场景
     * 
     * @param text 待编码的文本
     * @return 768维向量数组
     */
    @Override
    public float[] encode(String text) {
        if (!initialized) {
            throw new IllegalStateException("服务未初始化");
        }

        if (text == null || text.trim().isEmpty()) {
            return new float[VECTOR_DIMENSION];
        }

        OrtSession session = null;
        try {
            // 从池中获取Session
            session = borrowSession();

            // 文本预处理和分词
            List<String> tokens = tokenize(text.trim());

            // 转换为输入张量
            Map<String, OnnxTensor> inputs = createInputTensors(tokens);

            // 执行推理
            try (OrtSession.Result result = session.run(inputs)) {
                // 获取输出张量
                OnnxTensor outputTensor = (OnnxTensor) result.get(0);
                float[][][] output = (float[][][]) outputTensor.getValue();

                // 提取[CLS]标记的向量（第一个位置）
                return output[0][0];
            } finally {
                // 释放输入张量
                for (OnnxTensor tensor : inputs.values()) {
                    tensor.close();
                }
            }

        } catch (Exception e) {
            log.error("编码文本失败: {}", text, e);
            throw new RuntimeException("编码文本失败", e);
        } finally {
            // 归还Session到池中
            returnSession(session);
        }
    }

    /**
     * 批量文本编码为向量
     * 根据文本数量自动选择最优处理策略：
     * - 小批量（≤128）：直接批量处理
     * - 大批量（>128）：分批处理
     * 
     * @param texts 待编码的文本列表
     * @return 向量列表，与输入文本一一对应
     */
    @Override
    public List<float[]> encodeBatch(List<String> texts) {
        if (!initialized) {
            throw new IllegalStateException("服务未初始化");
        }

        if (texts == null || texts.isEmpty()) {
            return new ArrayList<>();
        }

        // 优化策略：根据数据量选择处理方式
        if (texts.size() <= OPTIMAL_BATCH_SIZE) {
            return encodeBatchDirect(texts);
        } else {
            // 大批量：分批处理
            return encodeLargeBatch(texts);
        }
    }

    /**
     * 异步单条文本编码
     * 适用于不阻塞主线程的异步处理场景
     * 
     * @param text 待编码的文本
     * @return 异步结果Future
     */
    @Override
    public CompletableFuture<float[]> encodeAsync(String text) {
        if (!initialized) {
            return CompletableFuture.failedFuture(new IllegalStateException("服务未初始化"));
        }

        return CompletableFuture.supplyAsync(() -> encode(text), executorService);
    }

    /**
     * 异步批量文本编码
     * 适用于大批量数据的异步处理场景
     * 
     * @param texts 待编码的文本列表
     * @return 异步结果Future
     */
    @Override
    public CompletableFuture<List<float[]>> encodeBatchAsync(List<String> texts) {
        if (!initialized) {
            return CompletableFuture.failedFuture(new IllegalStateException("服务未初始化"));
        }

        return CompletableFuture.supplyAsync(() -> encodeBatch(texts), executorService);
    }

    /**
     * 高性能并行批量处理
     * 适用于超大批量数据的并行处理场景
     * 将大批量数据分块后并行处理，充分利用多核CPU
     * 
     * @param texts 待编码的文本列表
     * @return 异步结果Future
     */
    public CompletableFuture<List<float[]>> encodeBatchParallel(List<String> texts) {
        if (!initialized) {
            return CompletableFuture.failedFuture(new IllegalStateException("服务未初始化"));
        }

        if (texts == null || texts.isEmpty()) {
            return CompletableFuture.completedFuture(new ArrayList<>());
        }

        // 小批量直接处理
        if (texts.size() <= PARALLEL_THRESHOLD) {
            return CompletableFuture.supplyAsync(() -> encodeBatch(texts), executorService);
        }

        // 大批量并行处理
        return CompletableFuture.supplyAsync(() -> {
            int chunkSize = OPTIMAL_BATCH_SIZE;
            List<CompletableFuture<List<float[]>>> futures = new ArrayList<>();

            // 分块并行处理
            for (int i = 0; i < texts.size(); i += chunkSize) {
                int endIndex = Math.min(i + chunkSize, texts.size());
                List<String> chunk = texts.subList(i, endIndex);

                CompletableFuture<List<float[]>> future = CompletableFuture
                        .supplyAsync(() -> encodeBatchDirect(chunk), executorService);
                futures.add(future);
            }

            // 等待所有任务完成并合并结果
            try {
                List<float[]> allResults = new ArrayList<>(texts.size());
                for (CompletableFuture<List<float[]>> future : futures) {
                    allResults.addAll(future.get());
                }
                return allResults;
            } catch (Exception e) {
                log.error("并行批量编码失败", e);
                throw new RuntimeException("并行批量编码失败", e);
            }
        }, executorService);
    }

    // ================================ 批量处理核心实现 ================================

    /**
     * 直接批量推理实现
     * 将多个文本打包成一个批次进行推理，提高GPU利用率
     * 适用于中小批量数据（≤128条）
     * 
     * @param texts 待编码的文本列表
     * @return 向量列表
     */
    private List<float[]> encodeBatchDirect(List<String> texts) {
        OrtSession session = null;
        try {
            session = borrowSession();

            // 高效批量tokenize
            List<List<String>> allTokens = new ArrayList<>(texts.size());
            for (String text : texts) {
                allTokens.add(text == null || text.trim().isEmpty() ? Arrays.asList(CLS_TOKEN, SEP_TOKEN)
                        : tokenize(text.trim()));
            }

            // 创建批量输入张量
            Map<String, OnnxTensor> inputs = createBatchInputTensors(allTokens);

            // 执行批量推理
            try (OrtSession.Result result = session.run(inputs)) {
                OnnxTensor outputTensor = (OnnxTensor) result.get(0);
                float[][][] output = (float[][][]) outputTensor.getValue();

                // 提取每个样本的[CLS]向量
                List<float[]> results = new ArrayList<>(texts.size());
                for (int i = 0; i < texts.size(); i++) {
                    results.add(output[i][0]);
                }
                return results;
            } finally {
                // 释放输入张量
                inputs.values().forEach(OnnxTensor::close);
            }

        } catch (Exception e) {
            log.error("批量编码失败，文本数量: {}", texts.size(), e);
            throw new RuntimeException("批量编码失败", e);
        } finally {
            returnSession(session);
        }
    }

    /**
     * 大批量数据的分批处理
     * 将大批量数据按最优批次大小分割，逐批处理
     * 适用于超大批量数据（>128条）
     * 
     * @param texts 待编码的文本列表
     * @return 向量列表
     */
    private List<float[]> encodeLargeBatch(List<String> texts) {
        List<float[]> allResults = new ArrayList<>(texts.size());

        // 分批处理
        for (int i = 0; i < texts.size(); i += OPTIMAL_BATCH_SIZE) {
            int endIndex = Math.min(i + OPTIMAL_BATCH_SIZE, texts.size());
            List<String> batch = texts.subList(i, endIndex);

            List<float[]> batchResults = encodeBatchDirect(batch);
            allResults.addAll(batchResults);
        }

        return allResults;
    }

    // ================================ 张量创建和处理 ================================

    /**
     * 创建单条文本的输入张量
     * 将分词后的token列表转换为ONNX模型所需的输入格式
     * 
     * @param tokens 分词后的token列表
     * @return 输入张量映射
     * @throws OrtException ONNX Runtime异常
     */
    private Map<String, OnnxTensor> createInputTensors(List<String> tokens) throws OrtException {
        // 转换token为ID
        long[] inputIds = new long[MAX_SEQUENCE_LENGTH];
        long[] attentionMask = new long[MAX_SEQUENCE_LENGTH];
        long[] tokenTypeIds = new long[MAX_SEQUENCE_LENGTH];

        int tokenSize = Math.min(tokens.size(), MAX_SEQUENCE_LENGTH);

        // 处理有效token - 使用缓存的特殊token ID提高性能
        for (int i = 0; i < tokenSize; i++) {
            String token = tokens.get(i);
            long tokenId;
            if (CLS_TOKEN.equals(token)) {
                tokenId = clsTokenId;
            } else if (SEP_TOKEN.equals(token)) {
                tokenId = sepTokenId;
            } else if (PAD_TOKEN.equals(token)) {
                tokenId = padTokenId;
            } else {
                Integer vocabTokenId = vocabulary.get(token);
                tokenId = (vocabTokenId != null) ? vocabTokenId : unkTokenId;
            }
            inputIds[i] = tokenId;
            attentionMask[i] = 1;
        }

        // 填充部分
        for (int i = tokenSize; i < MAX_SEQUENCE_LENGTH; i++) {
            inputIds[i] = padTokenId;
        }

        // 创建张量
        Map<String, OnnxTensor> inputs = new HashMap<>(3);
        inputs.put("input_ids", OnnxTensor.createTensor(environment, new long[][] { inputIds }));
        inputs.put("attention_mask", OnnxTensor.createTensor(environment, new long[][] { attentionMask }));
        inputs.put("token_type_ids", OnnxTensor.createTensor(environment, new long[][] { tokenTypeIds }));

        return inputs;
    }

    /**
     * 创建批量输入张量 - 极致优化版本
     * 将多个文本的token列表批量转换为ONNX模型输入格式
     * 使用预分配数组和缓存token ID提高性能
     * 
     * @param allTokens 所有文本的token列表
     * @return 批量输入张量映射
     * @throws OrtException ONNX Runtime异常
     */
    private Map<String, OnnxTensor> createBatchInputTensors(List<List<String>> allTokens) throws OrtException {
        int batchSize = allTokens.size();

        // 创建批量输入数组
        long[][] inputIds = new long[batchSize][MAX_SEQUENCE_LENGTH];
        long[][] attentionMask = new long[batchSize][MAX_SEQUENCE_LENGTH];
        long[][] tokenTypeIds = new long[batchSize][MAX_SEQUENCE_LENGTH];

        // 高效处理批量数据
        for (int batchIdx = 0; batchIdx < batchSize; batchIdx++) {
            List<String> tokens = allTokens.get(batchIdx);
            int tokenSize = Math.min(tokens.size(), MAX_SEQUENCE_LENGTH);

            // 极致优化的token ID转换 - 使用缓存的特殊token ID
            for (int i = 0; i < tokenSize; i++) {
                String token = tokens.get(i);
                long tokenId;
                if (CLS_TOKEN.equals(token)) {
                    tokenId = clsTokenId;
                } else if (SEP_TOKEN.equals(token)) {
                    tokenId = sepTokenId;
                } else if (PAD_TOKEN.equals(token)) {
                    tokenId = padTokenId;
                } else {
                    Integer vocabTokenId = vocabulary.get(token);
                    tokenId = (vocabTokenId != null) ? vocabTokenId : unkTokenId;
                }
                inputIds[batchIdx][i] = tokenId;
                attentionMask[batchIdx][i] = 1;
            }

            // 填充部分
            for (int i = tokenSize; i < MAX_SEQUENCE_LENGTH; i++) {
                inputIds[batchIdx][i] = padTokenId;
            }
        }

        // 创建张量
        Map<String, OnnxTensor> inputs = new HashMap<>(3);
        inputs.put("input_ids", OnnxTensor.createTensor(environment, inputIds));
        inputs.put("attention_mask", OnnxTensor.createTensor(environment, attentionMask));
        inputs.put("token_type_ids", OnnxTensor.createTensor(environment, tokenTypeIds));

        return inputs;
    }

    // ================================ 文本预处理 ================================

    /**
     * 高度优化的分词器
     * 采用字符级分词，适用于中文文本处理
     * 优化点：
     * 1. 预先限制文本长度避免过长处理
     * 2. 预分配ArrayList容量
     * 3. 使用String.valueOf避免Character.toString()开销
     * 
     * @param text 待分词的文本
     * @return 分词结果，包含[CLS]和[SEP]标记
     */
    private List<String> tokenize(String text) {
        // 限制文本长度，避免过长文本影响性能
        if (text.length() > MAX_SEQUENCE_LENGTH - 2) {
            text = text.substring(0, MAX_SEQUENCE_LENGTH - 2);
        }

        List<String> tokens = new ArrayList<>(text.length() + 2);
        tokens.add(CLS_TOKEN);

        // 极度优化的字符级分词 - 使用字符缓存减少对象创建
        int len = text.length();
        for (int i = 0; i < len; i++) {
            char c = text.charAt(i);
            // 直接使用字符串，避免Character.toString()的开销
            tokens.add(String.valueOf(c));
        }

        tokens.add(SEP_TOKEN);
        return tokens;
    }

    // ================================ 工具方法 ================================

    /**
     * 计算两个向量的余弦相似度
     * 余弦相似度范围为[-1, 1]，值越接近1表示越相似
     * 
     * @param vector1 第一个向量
     * @param vector2 第二个向量
     * @return 余弦相似度值
     */
    @Override
    public double cosineSimilarity(float[] vector1, float[] vector2) {
        if (vector1.length != vector2.length) {
            throw new IllegalArgumentException("向量维度不匹配");
        }

        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;

        for (int i = 0; i < vector1.length; i++) {
            dotProduct += vector1[i] * vector2[i];
            norm1 += vector1[i] * vector1[i];
            norm2 += vector2[i] * vector2[i];
        }

        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0;
        }

        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }

    /**
     * 获取向量维度
     * 
     * @return 向量维度（768）
     */
    @Override
    public int getVectorDimension() {
        return VECTOR_DIMENSION;
    }

    /**
     * 检查服务是否已初始化
     * 
     * @return 初始化状态
     */
    @Override
    public boolean isInitialized() {
        return initialized;
    }
}