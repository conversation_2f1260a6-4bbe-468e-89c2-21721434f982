package com.dipspro.modules.billing.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.dipspro.modules.billing.entity.BillingPackage;

/**
 * 计费套餐配置服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface BillingPackageService {

    /**
     * 创建计费套餐
     * 
     * @param billingPackage 套餐信息
     * @return 创建的套餐
     */
    BillingPackage createPackage(BillingPackage billingPackage);

    /**
     * 更新计费套餐
     * 
     * @param id             套餐ID
     * @param billingPackage 套餐信息
     * @return 更新的套餐
     */
    BillingPackage updatePackage(Long id, BillingPackage billingPackage);

    /**
     * 根据ID查询套餐
     * 
     * @param id 套餐ID
     * @return 套餐信息
     */
    Optional<BillingPackage> getPackageById(Long id);

    /**
     * 根据名称查询套餐
     * 
     * @param name 套餐名称
     * @return 套餐信息
     */
    Optional<BillingPackage> getPackageByName(String name);

    /**
     * 获取所有激活的套餐
     * 
     * @return 激活的套餐列表
     */
    List<BillingPackage> getActivePackages();

    /**
     * 获取默认套餐
     * 
     * @return 默认套餐
     */
    Optional<BillingPackage> getDefaultPackage();

    /**
     * 分页查询套餐列表
     * 
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<BillingPackage> getPackages(Pageable pageable);

    /**
     * 根据激活状态分页查询套餐
     * 
     * @param isActive 是否激活
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<BillingPackage> getPackagesByStatus(Boolean isActive, Pageable pageable);

    /**
     * 根据套餐名称模糊查询
     * 
     * @param name     套餐名称关键词
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<BillingPackage> searchPackagesByName(String name, Pageable pageable);

    /**
     * 激活套餐
     * 
     * @param id 套餐ID
     * @return 是否成功
     */
    boolean activatePackage(Long id);

    /**
     * 停用套餐
     * 
     * @param id 套餐ID
     * @return 是否成功
     */
    boolean deactivatePackage(Long id);

    /**
     * 删除套餐
     * 
     * @param id 套餐ID
     * @return 是否成功
     */
    boolean deletePackage(Long id);

    /**
     * 检查套餐名称是否存在
     * 
     * @param name      套餐名称
     * @param excludeId 要排除的套餐ID（用于更新时检查）
     * @return 是否存在
     */
    boolean isPackageNameExists(String name, Long excludeId);

    /**
     * 查询指定价格范围内的套餐
     * 
     * @param minInputPrice 最小输入Token价格
     * @param maxInputPrice 最大输入Token价格
     * @param isActive      是否激活
     * @return 套餐列表
     */
    List<BillingPackage> getPackagesByPriceRange(BigDecimal minInputPrice, BigDecimal maxInputPrice, Boolean isActive);

    /**
     * 查询包含免费Token的套餐
     * 
     * @param isActive 是否激活
     * @return 套餐列表
     */
    List<BillingPackage> getPackagesWithFreeTokens(Boolean isActive);

    /**
     * 调整套餐排序权重
     * 
     * @param id        套餐ID
     * @param sortOrder 新的排序权重
     * @return 是否成功
     */
    boolean updatePackageSortOrder(Long id, Integer sortOrder);

    /**
     * 批量更新套餐状态
     * 
     * @param ids      套餐ID列表
     * @param isActive 激活状态
     * @return 更新的记录数
     */
    int batchUpdatePackageStatus(List<Long> ids, Boolean isActive);

    /**
     * 获取套餐使用统计
     * 
     * @param packageId 套餐ID
     * @return 使用该套餐的用户数量
     */
    long getPackageUsageCount(Long packageId);

    /**
     * 验证套餐配置的有效性
     * 
     * @param billingPackage 套餐信息
     * @return 验证结果消息，null表示验证通过
     */
    String validatePackageConfiguration(BillingPackage billingPackage);

    /**
     * 计算Token费用
     * 
     * @param packageId    套餐ID
     * @param inputTokens  输入Token数量
     * @param outputTokens 输出Token数量
     * @return 计算的费用
     */
    BigDecimal calculateTokenCost(Long packageId, Long inputTokens, Long outputTokens);

    /**
     * 获取推荐套餐（根据用户使用情况）
     * 
     * @param userId 用户ID
     * @return 推荐的套餐列表
     */
    List<BillingPackage> getRecommendedPackages(Long userId);

    // ==================== Controller需要的额外方法 ====================

    /**
     * 设置默认套餐
     * 
     * @param packageId 套餐ID
     * @return 是否成功
     */
    boolean setDefaultPackage(Long packageId);
}