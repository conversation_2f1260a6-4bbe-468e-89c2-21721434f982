package com.dipspro.modules.billing.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 用户余额DTO
 * 
 * 用于返回用户余额信息，包含当前余额、冻结金额等详细信息。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class UserBalanceDto {

    /**
     * 余额记录ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 现金余额
     */
    private BigDecimal cashBalance;

    /**
     * 赠送余额
     */
    private BigDecimal giftBalance;

    /**
     * 总余额（现金 + 赠送）
     */
    private BigDecimal totalBalance;

    /**
     * 冻结金额
     */
    private BigDecimal frozenAmount;

    /**
     * 可用余额（总余额 - 冻结金额）
     */
    private BigDecimal availableBalance;

    /**
     * 累计充值金额
     */
    private BigDecimal totalRechargeAmount;

    /**
     * 累计消费金额
     */
    private BigDecimal totalConsumeAmount;

    /**
     * 账户状态
     * NORMAL-正常, FROZEN-冻结, SUSPENDED-暂停
     */
    private String status;

    /**
     * 余额等级
     * 根据余额金额确定的等级
     */
    private String balanceLevel;

    /**
     * 最后交易时间
     */
    private LocalDateTime lastTransactionAt;

    /**
     * 余额更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 检查账户是否正常
     * 
     * @return 是否正常
     */
    public boolean isNormal() {
        return "NORMAL".equals(status);
    }

    /**
     * 检查账户是否冻结
     * 
     * @return 是否冻结
     */
    public boolean isFrozen() {
        return "FROZEN".equals(status);
    }

    /**
     * 检查账户是否暂停
     * 
     * @return 是否暂停
     */
    public boolean isSuspended() {
        return "SUSPENDED".equals(status);
    }

    /**
     * 获取账户状态显示文本
     * 
     * @return 状态显示文本
     */
    public String getStatusDisplay() {
        if (status == null) {
            return "未知状态";
        }

        return switch (status) {
            case "NORMAL" -> "正常";
            case "FROZEN" -> "冻结";
            case "SUSPENDED" -> "暂停";
            default -> "未知状态";
        };
    }

    /**
     * 获取格式化的现金余额
     * 
     * @return 格式化的金额字符串
     */
    public String getFormattedCashBalance() {
        return cashBalance != null ? "¥" + cashBalance.toString() : "¥0.00";
    }

    /**
     * 获取格式化的赠送余额
     * 
     * @return 格式化的金额字符串
     */
    public String getFormattedGiftBalance() {
        return giftBalance != null ? "¥" + giftBalance.toString() : "¥0.00";
    }

    /**
     * 获取格式化的总余额
     * 
     * @return 格式化的金额字符串
     */
    public String getFormattedTotalBalance() {
        return totalBalance != null ? "¥" + totalBalance.toString() : "¥0.00";
    }

    /**
     * 获取格式化的冻结金额
     * 
     * @return 格式化的金额字符串
     */
    public String getFormattedFrozenAmount() {
        return frozenAmount != null ? "¥" + frozenAmount.toString() : "¥0.00";
    }

    /**
     * 获取格式化的可用余额
     * 
     * @return 格式化的金额字符串
     */
    public String getFormattedAvailableBalance() {
        return availableBalance != null ? "¥" + availableBalance.toString() : "¥0.00";
    }

    /**
     * 获取格式化的累计充值金额
     * 
     * @return 格式化的金额字符串
     */
    public String getFormattedTotalRechargeAmount() {
        return totalRechargeAmount != null ? "¥" + totalRechargeAmount.toString() : "¥0.00";
    }

    /**
     * 获取格式化的累计消费金额
     * 
     * @return 格式化的金额字符串
     */
    public String getFormattedTotalConsumeAmount() {
        return totalConsumeAmount != null ? "¥" + totalConsumeAmount.toString() : "¥0.00";
    }

    /**
     * 计算总余额（现金 + 赠送）
     * 
     * @return 计算后的总余额
     */
    public BigDecimal calculateTotalBalance() {
        BigDecimal cash = cashBalance != null ? cashBalance : BigDecimal.ZERO;
        BigDecimal gift = giftBalance != null ? giftBalance : BigDecimal.ZERO;
        return cash.add(gift);
    }

    /**
     * 计算可用余额（总余额 - 冻结金额）
     * 
     * @return 计算后的可用余额
     */
    public BigDecimal calculateAvailableBalance() {
        BigDecimal total = totalBalance != null ? totalBalance : calculateTotalBalance();
        BigDecimal frozen = frozenAmount != null ? frozenAmount : BigDecimal.ZERO;
        return total.subtract(frozen);
    }

    /**
     * 检查是否有冻结金额
     * 
     * @return 是否有冻结金额
     */
    public boolean hasFrozenAmount() {
        return frozenAmount != null && frozenAmount.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 检查余额是否充足
     * 
     * @param amount 需要的金额
     * @return 是否充足
     */
    public boolean isBalanceSufficient(BigDecimal amount) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return true;
        }

        BigDecimal available = calculateAvailableBalance();
        return available.compareTo(amount) >= 0;
    }

    /**
     * 获取余额等级显示文本
     * 
     * @return 等级显示文本
     */
    public String getBalanceLevelDisplay() {
        if (balanceLevel == null) {
            // 根据余额自动计算等级
            return calculateBalanceLevel();
        }

        return switch (balanceLevel) {
            case "BRONZE" -> "青铜";
            case "SILVER" -> "白银";
            case "GOLD" -> "黄金";
            case "PLATINUM" -> "铂金";
            case "DIAMOND" -> "钻石";
            default -> balanceLevel;
        };
    }

    /**
     * 根据余额计算等级
     * 
     * @return 等级标识
     */
    public String calculateBalanceLevel() {
        BigDecimal total = totalBalance != null ? totalBalance : calculateTotalBalance();

        if (total.compareTo(BigDecimal.valueOf(100000)) >= 0) {
            return "钻石";
        } else if (total.compareTo(BigDecimal.valueOf(50000)) >= 0) {
            return "铂金";
        } else if (total.compareTo(BigDecimal.valueOf(10000)) >= 0) {
            return "黄金";
        } else if (total.compareTo(BigDecimal.valueOf(1000)) >= 0) {
            return "白银";
        } else {
            return "青铜";
        }
    }

    /**
     * 获取余额预警级别
     * 
     * @return 预警级别：SAFE, WARNING, DANGER
     */
    public String getBalanceWarningLevel() {
        BigDecimal available = calculateAvailableBalance();

        if (available.compareTo(BigDecimal.valueOf(100)) < 0) {
            return "DANGER";
        } else if (available.compareTo(BigDecimal.valueOf(500)) < 0) {
            return "WARNING";
        } else {
            return "SAFE";
        }
    }

    /**
     * 获取预警级别显示文本
     * 
     * @return 预警级别显示文本
     */
    public String getWarningLevelDisplay() {
        String level = getBalanceWarningLevel();
        return switch (level) {
            case "SAFE" -> "安全";
            case "WARNING" -> "预警";
            case "DANGER" -> "危险";
            default -> "未知";
        };
    }

    /**
     * 检查是否需要充值提醒
     * 
     * @return 是否需要充值
     */
    public boolean needsRechargeReminder() {
        return "WARNING".equals(getBalanceWarningLevel()) || "DANGER".equals(getBalanceWarningLevel());
    }

    /**
     * 获取余额摘要信息
     * 
     * @return 余额摘要
     */
    public String getBalanceSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("总余额: ").append(getFormattedTotalBalance());
        summary.append(" | 可用: ").append(getFormattedAvailableBalance());
        summary.append(" | 状态: ").append(getStatusDisplay());
        summary.append(" | 等级: ").append(getBalanceLevelDisplay());

        return summary.toString();
    }

    /**
     * 获取详细的余额信息
     * 
     * @return 详细余额信息
     */
    public String getDetailedBalanceInfo() {
        StringBuilder detail = new StringBuilder();
        detail.append("用户: ").append(username).append("(").append(userId).append(")").append("\n");
        detail.append("现金余额: ").append(getFormattedCashBalance()).append("\n");
        detail.append("赠送余额: ").append(getFormattedGiftBalance()).append("\n");
        detail.append("总余额: ").append(getFormattedTotalBalance()).append("\n");
        detail.append("可用余额: ").append(getFormattedAvailableBalance()).append("\n");

        if (hasFrozenAmount()) {
            detail.append("冻结金额: ").append(getFormattedFrozenAmount()).append("\n");
        }

        detail.append("账户状态: ").append(getStatusDisplay()).append("\n");
        detail.append("余额等级: ").append(getBalanceLevelDisplay()).append("\n");
        detail.append("累计充值: ").append(getFormattedTotalRechargeAmount()).append("\n");
        detail.append("累计消费: ").append(getFormattedTotalConsumeAmount());

        return detail.toString();
    }

    /**
     * 获取消费能力分析
     * 
     * @return 消费能力描述
     */
    public String getConsumptionCapacityAnalysis() {
        BigDecimal available = calculateAvailableBalance();
        BigDecimal totalRecharge = totalRechargeAmount != null ? totalRechargeAmount : BigDecimal.ZERO;
        BigDecimal totalConsume = totalConsumeAmount != null ? totalConsumeAmount : BigDecimal.ZERO;

        StringBuilder analysis = new StringBuilder();

        if (totalRecharge.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal consumeRate = totalConsume.divide(totalRecharge, 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            analysis.append("消费率: ").append(String.format("%.2f%%", consumeRate.doubleValue())).append(" | ");
        }

        if (available.compareTo(BigDecimal.valueOf(1000)) >= 0) {
            analysis.append("消费能力: 强");
        } else if (available.compareTo(BigDecimal.valueOf(100)) >= 0) {
            analysis.append("消费能力: 中等");
        } else {
            analysis.append("消费能力: 较弱");
        }

        return analysis.toString();
    }

    /**
     * 获取格式化的最后交易时间
     * 
     * @return 格式化的时间字符串
     */
    public String getFormattedLastTransactionAt() {
        if (lastTransactionAt == null) {
            return "无交易记录";
        }

        return lastTransactionAt.toString().replace("T", " ");
    }
}