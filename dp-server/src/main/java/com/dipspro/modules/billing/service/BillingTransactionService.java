package com.dipspro.modules.billing.service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.dipspro.modules.billing.entity.BillingTransaction;

/**
 * 计费交易记录服务接口
 * 
 * 根据DIPS Pro计费系统设计文档实现
 * 专注于核心交易记录功能，包括创建、查询和基本统计
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface BillingTransactionService {

        // ==================== 核心交易创建方法 ====================

        /**
         * 创建交易记录
         * 
         * 创建新的计费交易记录，记录用户余额的变动情况。
         * 该方法是所有交易记录创建的通用入口，支持多种交易类型。
         * 
         * 创建流程：
         * 1. 参数验证：检查用户ID、交易类型、金额等参数的有效性
         * 2. 用户验证：确认用户存在且余额状态正常
         * 3. 生成交易号：创建唯一的交易流水号
         * 4. 余额快照：记录交易前后的余额状态
         * 5. 设置交易信息：记录交易类型、金额、描述等
         * 6. 关联外部ID：如果提供外部交易ID，建立关联关系
         * 7. 保存记录：将交易记录保存到数据库
         * 
         * 交易类型说明：
         * - RECHARGE: 充值交易，增加用户充值余额
         * - DEDUCT: 扣费交易，因使用服务而扣除余额
         * - REFUND: 退款交易，将已扣费用退还给用户
         * - GIFT: 赠送交易，管理员赠送给用户的余额
         * 
         * 金额处理规则：
         * - 充值和赠送：金额为正数，增加用户余额
         * - 扣费：金额为负数，减少用户余额
         * - 退款：金额为正数，补回之前扣除的余额
         * - 金额精度保留到小数点后6位
         * 
         * 业务约束：
         * - 扣费交易要求用户余额充足
         * - 退款交易要求存在对应的原始扣费记录
         * - 外部交易ID用于防重复和关联查询
         * - 每笔交易都会记录完整的余额变动轨迹
         * 
         * @param userId                用户ID，必须是有效的用户标识
         * @param transactionType       交易类型（RECHARGE-充值, DEDUCT-扣费, REFUND-退款, GIFT-赠送）
         * @param amount                交易金额，正数表示增加余额，负数表示减少余额
         * @param description           交易描述，用于说明交易原因和用途
         * @param externalTransactionId 外部交易ID，用于关联外部系统的交易记录，可为空
         * @return 创建的交易记录，包含完整的交易信息和余额快照
         * @throws IllegalArgumentException 当参数验证失败时抛出
         * @throws IllegalStateException    当用户余额不足或状态异常时抛出
         */
        BillingTransaction createTransaction(Long userId, String transactionType, BigDecimal amount,
                        String description, String externalTransactionId);

        /**
         * 创建充值交易记录
         * 
         * @param userId                用户ID
         * @param amount                充值金额
         * @param description           充值描述
         * @param externalTransactionId 外部交易ID
         * @return 交易记录
         */
        BillingTransaction createRechargeTransaction(Long userId, BigDecimal amount, String description,
                        String externalTransactionId);

        /**
         * 创建扣费交易记录
         * 
         * @param userId          用户ID
         * @param amount          扣费金额
         * @param description     扣费描述
         * @param relatedRecordId 关联记录ID（如使用记录ID）
         * @return 交易记录
         */
        BillingTransaction createDeductionTransaction(Long userId, BigDecimal amount, String description,
                        Long relatedRecordId);

        /**
         * 创建退款交易记录
         * 
         * @param userId                用户ID
         * @param amount                退款金额
         * @param description           退款描述
         * @param originalTransactionId 原始交易ID
         * @return 交易记录
         */
        BillingTransaction createRefundTransaction(Long userId, BigDecimal amount, String description,
                        Long originalTransactionId);

        /**
         * 创建赠送交易记录
         * 
         * @param userId      用户ID
         * @param amount      赠送金额
         * @param description 赠送描述
         * @param giftReason  赠送原因
         * @return 交易记录
         */
        BillingTransaction createGiftTransaction(Long userId, BigDecimal amount, String description, String giftReason);

        // ==================== 基本查询方法 ====================

        /**
         * 根据ID查询交易记录
         * 
         * @param id 交易记录ID
         * @return 交易记录
         */
        Optional<BillingTransaction> getTransactionById(Long id);

        /**
         * 根据外部交易ID查询交易记录
         * 
         * @param externalTransactionId 外部交易ID
         * @return 交易记录
         */
        Optional<BillingTransaction> getTransactionByExternalId(String externalTransactionId);

        /**
         * 检查外部交易ID是否存在
         * 
         * @param externalTransactionId 外部交易ID
         * @return 是否存在
         */
        boolean isExternalTransactionIdExists(String externalTransactionId);

        /**
         * 查询用户交易记录（基础版本）
         * 
         * @param userId   用户ID
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<BillingTransaction> getUserTransactions(Long userId, Pageable pageable);

        /**
         * 查询用户交易记录（带筛选条件）
         * 
         * @param userId    用户ID
         * @param type      交易类型筛选
         * @param status    交易状态筛选
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @param pageable  分页参数
         * @return 分页结果
         */
        Page<BillingTransaction> getUserTransactions(Long userId, String type, String status,
                        java.time.LocalDate startDate, java.time.LocalDate endDate, Pageable pageable);

        /**
         * 根据用户ID和交易类型查询交易记录
         * 
         * @param userId          用户ID
         * @param transactionType 交易类型
         * @param pageable        分页参数
         * @return 分页结果
         */
        Page<BillingTransaction> getUserTransactionsByType(Long userId, String transactionType, Pageable pageable);

        /**
         * 根据用户ID和时间范围查询交易记录
         * 
         * @param userId    用户ID
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @param pageable  分页参数
         * @return 分页结果
         */
        Page<BillingTransaction> getUserTransactionsByDateRange(Long userId, LocalDateTime startTime,
                        LocalDateTime endTime, Pageable pageable);

        /**
         * 查询用户最近的交易记录
         * 
         * @param userId 用户ID
         * @param limit  限制数量
         * @return 最近的交易记录列表
         */
        List<BillingTransaction> getUserRecentTransactions(Long userId, int limit);

        // ==================== 基本统计方法 ====================

        /**
         * 查询用户总充值金额
         * 
         * @param userId 用户ID
         * @return 总充值金额
         */
        BigDecimal getUserTotalRechargeAmount(Long userId);

        /**
         * 查询用户总消费金额
         * 
         * @param userId 用户ID
         * @return 总消费金额
         */
        BigDecimal getUserTotalConsumptionAmount(Long userId);

        /**
         * 计算用户净充值金额（充值 - 退款）
         * 
         * @param userId 用户ID
         * @return 净充值金额
         */
        BigDecimal getUserNetRechargeAmount(Long userId);

        /**
         * 获取用户交易摘要（基础版本）
         * 
         * @param userId 用户ID
         * @return 交易摘要信息
         */
        Object getUserTransactionSummary(Long userId);

        /**
         * 获取用户交易摘要（带筛选条件）
         * 
         * @param userId 用户ID
         * @param period 统计周期
         * @param year   年份
         * @return 交易摘要信息
         */
        Object getUserTransactionSummary(Long userId, String period, Integer year);

        // ==================== 工具方法 ====================

        /**
         * 验证交易参数的有效性
         * 
         * @param userId          用户ID
         * @param transactionType 交易类型
         * @param amount          交易金额
         * @return 验证结果消息，null表示验证通过
         */
        String validateTransactionParameters(Long userId, String transactionType, BigDecimal amount);

        /**
         * 获取交易类型列表
         * 
         * @return 支持的交易类型列表
         */
        List<String> getSupportedTransactionTypes();

        /**
         * 批量删除历史交易记录
         * 
         * @param beforeTime 时间阈值
         * @return 删除的记录数
         */
        int deleteHistoryTransactions(LocalDateTime beforeTime);

        // ==================== Controller需要的额外方法 ====================

        /**
         * 管理端获取交易记录（带完整筛选条件）
         * 
         * @param userId    用户ID筛选
         * @param type      交易类型筛选
         * @param status    交易状态筛选
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @param minAmount 最小金额
         * @param maxAmount 最大金额
         * @param pageable  分页参数
         * @return 分页结果
         */
        Page<BillingTransaction> getTransactionsForAdmin(Long userId, String type, String status,
                        java.time.LocalDate startDate, java.time.LocalDate endDate,
                        BigDecimal minAmount, BigDecimal maxAmount, Pageable pageable);

        /**
         * 获取交易统计信息
         * 
         * @param period 统计周期
         * @param year   年份
         * @return 统计信息
         */
        Object getTransactionStatistics(String period, Integer year);

        /**
         * 生成财务报表
         * 
         * @param startDate  开始日期
         * @param endDate    结束日期
         * @param reportType 报表类型
         * @return 报表数据
         */
        Object generateFinancialReport(java.time.LocalDate startDate, java.time.LocalDate endDate, String reportType);

        /**
         * 获取异常交易记录
         * 
         * @param anomalyType 异常类型
         * @param pageable    分页参数
         * @return 分页结果
         */
        Page<BillingTransaction> getAnomalousTransactions(String anomalyType, Pageable pageable);

        /**
         * 导出交易记录
         * 
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @param format    导出格式
         * @param type      交易类型筛选
         * @return 导出文件信息
         */
        Object exportTransactions(java.time.LocalDate startDate, java.time.LocalDate endDate,
                        String format, String type);
}