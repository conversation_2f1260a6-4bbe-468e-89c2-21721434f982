package com.dipspro.modules.billing.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import org.hibernate.annotations.Comment;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 计费交易记录实体
 * 对应数据库表：b_billing_transactions
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "b_billing_transactions")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Comment("计费交易记录表")
public class BillingTransaction {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("主键ID")
    private Long id;

    /**
     * 用户ID，关联users.id
     */
    @Column(name = "user_id", nullable = false)
    @NotNull(message = "用户ID不能为空")
    @Comment("用户ID，关联users.id")
    private Long userId;

    /**
     * 交易流水号
     */
    @Column(name = "transaction_no", length = 100, nullable = false, unique = true)
    @NotBlank(message = "交易流水号不能为空")
    @Comment("交易流水号")
    private String transactionNo;

    /**
     * 外部交易ID
     */
    @Column(name = "external_transaction_id", length = 100)
    @Comment("外部交易ID")
    private String externalTransactionId;

    /**
     * 交易类型：RECHARGE, DEDUCT, REFUND, GIFT
     */
    @Column(name = "type", length = 20, nullable = false)
    @NotBlank(message = "交易类型不能为空")
    @Comment("交易类型：RECHARGE, DEDUCT, REFUND, GIFT")
    private String type;

    /**
     * 交易类型 - 别名字段，用于兼容Service层调用
     */
    public void setTransactionType(String transactionType) {
        this.type = transactionType;
    }

    /**
     * 获取交易类型 - 别名字段，用于兼容Service层调用
     */
    public String getTransactionType() {
        return this.type;
    }

    /**
     * 交易金额（正数为收入，负数为支出）
     */
    @Column(name = "amount", precision = 15, scale = 2, nullable = false)
    @NotNull(message = "交易金额不能为空")
    @Comment("交易金额（正数为收入，负数为支出）")
    private BigDecimal amount;

    /**
     * 余额类型：RECHARGED, GIFT, FREE_TOKENS
     */
    @Column(name = "balance_type", length = 20, nullable = false)
    @NotBlank(message = "余额类型不能为空")
    @Comment("余额类型：RECHARGED, GIFT, FREE_TOKENS")
    private String balanceType;

    /**
     * 交易状态：PENDING, SUCCESS, FAILED, CANCELLED
     */
    @Column(name = "status", length = 20)
    @Comment("交易状态：PENDING, SUCCESS, FAILED, CANCELLED")
    private String status = "SUCCESS";

    /**
     * 关联记录ID（通用）
     */
    @Column(name = "related_id")
    @Comment("关联记录ID（通用）")
    private Long relatedId;

    /**
     * 关联记录类型
     */
    @Column(name = "related_type", length = 50)
    @Comment("关联记录类型")
    private String relatedType;

    /**
     * 支付方式
     */
    @Column(name = "payment_method", length = 50)
    @Comment("支付方式")
    private String paymentMethod;

    // 关联记录
    /**
     * 关联的使用记录ID，关联b_billing_usage_records.id
     */
    @Column(name = "usage_record_id")
    @Comment("关联的使用记录ID，关联b_billing_usage_records.id")
    private Long usageRecordId;

    /**
     * 关联的支付记录ID，关联b_payment_records.id
     */
    @Column(name = "payment_record_id")
    @Comment("关联的支付记录ID，关联b_payment_records.id")
    private Long paymentRecordId;

    /**
     * 关联的申诉记录ID，关联b_billing_appeals.id
     */
    @Column(name = "appeal_id")
    @Comment("关联的申诉记录ID，关联b_billing_appeals.id")
    private Long appealId;

    // 余额快照
    /**
     * 交易前余额
     */
    @Column(name = "balance_before", precision = 15, scale = 2)
    @Comment("交易前余额")
    private BigDecimal balanceBefore;

    /**
     * 交易后余额
     */
    @Column(name = "balance_after", precision = 15, scale = 2)
    @Comment("交易后余额")
    private BigDecimal balanceAfter;

    /**
     * 交易描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    @Comment("交易描述")
    private String description;

    /**
     * 操作员ID（管理员操作时），关联users.id
     */
    @Column(name = "operator_id")
    @Comment("操作员ID（管理员操作时），关联users.id")
    private Long operatorId;

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false, updatable = false)
    @Comment("创建时间")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    @Comment("更新时间")
    private LocalDateTime updatedAt;

    /**
     * 创建时自动设置创建时间和更新时间
     */
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createdAt = now;
        this.updatedAt = now;
    }

    /**
     * 更新时自动设置更新时间
     */
    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 检查是否为充值交易
     * 
     * @return 是否为充值交易
     */
    public boolean isRecharge() {
        return "RECHARGE".equals(type);
    }

    /**
     * 检查是否为扣费交易
     * 
     * @return 是否为扣费交易
     */
    public boolean isDeduct() {
        return "DEDUCT".equals(type);
    }

    /**
     * 检查是否为退费交易
     * 
     * @return 是否为退费交易
     */
    public boolean isRefund() {
        return "REFUND".equals(type);
    }

    /**
     * 检查是否为赠送交易
     * 
     * @return 是否为赠送交易
     */
    public boolean isGift() {
        return "GIFT".equals(type);
    }

    /**
     * 获取交易影响的余额变化量
     * 
     * @return 余额变化量
     */
    public BigDecimal getBalanceChange() {
        if (balanceBefore != null && balanceAfter != null) {
            return balanceAfter.subtract(balanceBefore);
        }
        return BigDecimal.ZERO;
    }
}