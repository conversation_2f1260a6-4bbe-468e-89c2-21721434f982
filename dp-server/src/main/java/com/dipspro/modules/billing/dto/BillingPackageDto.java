package com.dipspro.modules.billing.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 计费套餐DTO
 * 
 * 用于返回套餐信息，包含价格配置、特性描述、
 * 使用限制等完整套餐信息。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class BillingPackageDto {

    /**
     * 套餐ID
     */
    private Long id;

    /**
     * 套餐名称
     */
    private String name;

    /**
     * 套餐描述
     */
    private String description;

    /**
     * 输入Token价格（每千Token）
     */
    private BigDecimal inputTokenPrice;

    /**
     * 输出Token价格（每千Token）
     */
    private BigDecimal outputTokenPrice;

    /**
     * 免费Token数量
     */
    private Long freeTokens;

    /**
     * 单次请求最大Token限制
     */
    private Long maxTokensPerRequest;

    /**
     * 每日Token使用限制
     */
    private Long dailyTokenLimit;

    /**
     * 是否激活
     */
    private Boolean isActive;

    /**
     * 是否为默认套餐
     */
    private Boolean isDefault;

    /**
     * 排序权重
     */
    private Integer sortOrder;

    /**
     * 套餐特性（JSON格式）
     */
    private String features;

    /**
     * 使用限制（JSON格式）
     */
    private String limitations;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 获取格式化的输入Token价格
     * 
     * @return 格式化的价格字符串
     */
    public String getFormattedInputTokenPrice() {
        return inputTokenPrice != null ? "¥" + inputTokenPrice.toString() + "/1K tokens" : "免费";
    }

    /**
     * 获取格式化的输出Token价格
     * 
     * @return 格式化的价格字符串
     */
    public String getFormattedOutputTokenPrice() {
        return outputTokenPrice != null ? "¥" + outputTokenPrice.toString() + "/1K tokens" : "免费";
    }

    /**
     * 获取格式化的免费Token数量
     * 
     * @return 格式化的Token数量字符串
     */
    public String getFormattedFreeTokens() {
        if (freeTokens == null || freeTokens == 0) {
            return "无免费Token";
        }

        if (freeTokens < 1000) {
            return freeTokens + " tokens";
        } else if (freeTokens < 1000000) {
            return String.format("%.1fK tokens", freeTokens / 1000.0);
        } else {
            return String.format("%.1fM tokens", freeTokens / 1000000.0);
        }
    }

    /**
     * 获取套餐状态显示文本
     * 
     * @return 状态显示文本
     */
    public String getStatusDisplay() {
        if (Boolean.TRUE.equals(isActive)) {
            return Boolean.TRUE.equals(isDefault) ? "激活（默认）" : "激活";
        } else {
            return "停用";
        }
    }

    /**
     * 检查套餐是否可用
     * 
     * @return 是否可用
     */
    public boolean isAvailable() {
        return Boolean.TRUE.equals(isActive);
    }

    /**
     * 检查是否为免费套餐
     * 
     * @return 是否为免费套餐
     */
    public boolean isFreePackage() {
        return (inputTokenPrice == null || inputTokenPrice.compareTo(BigDecimal.ZERO) == 0)
                && (outputTokenPrice == null || outputTokenPrice.compareTo(BigDecimal.ZERO) == 0);
    }

    /**
     * 计算指定Token数量的费用
     * 
     * @param inputTokens  输入Token数量
     * @param outputTokens 输出Token数量
     * @return 总费用
     */
    public BigDecimal calculateCost(Long inputTokens, Long outputTokens) {
        BigDecimal inputCost = inputTokenPrice != null && inputTokens != null
                ? inputTokenPrice.multiply(BigDecimal.valueOf(inputTokens)).divide(BigDecimal.valueOf(1000))
                : BigDecimal.ZERO;

        BigDecimal outputCost = outputTokenPrice != null && outputTokens != null
                ? outputTokenPrice.multiply(BigDecimal.valueOf(outputTokens)).divide(BigDecimal.valueOf(1000))
                : BigDecimal.ZERO;

        return inputCost.add(outputCost);
    }

    /**
     * 获取套餐价格等级
     * 基于价格范围返回等级标识
     * 
     * @return 价格等级：FREE, BASIC, PREMIUM, ENTERPRISE
     */
    public String getPriceLevel() {
        if (isFreePackage()) {
            return "FREE";
        }

        // 计算平均价格（输入+输出）
        BigDecimal avgPrice = BigDecimal.ZERO;
        int priceCount = 0;

        if (inputTokenPrice != null) {
            avgPrice = avgPrice.add(inputTokenPrice);
            priceCount++;
        }
        if (outputTokenPrice != null) {
            avgPrice = avgPrice.add(outputTokenPrice);
            priceCount++;
        }

        if (priceCount > 0) {
            avgPrice = avgPrice.divide(BigDecimal.valueOf(priceCount));
        }

        // 根据平均价格分级
        if (avgPrice.compareTo(BigDecimal.valueOf(0.01)) <= 0) {
            return "BASIC";
        } else if (avgPrice.compareTo(BigDecimal.valueOf(0.05)) <= 0) {
            return "PREMIUM";
        } else {
            return "ENTERPRISE";
        }
    }

    /**
     * 获取推荐指数
     * 基于价格、特性等因素计算推荐指数
     * 
     * @return 推荐指数（1-5星）
     */
    public int getRecommendationLevel() {
        int level = 1;

        // 默认套餐加分
        if (Boolean.TRUE.equals(isDefault)) {
            level += 2;
        }

        // 有免费Token加分
        if (freeTokens != null && freeTokens > 0) {
            level += 1;
        }

        // 价格合理性加分
        if (!isFreePackage() && getPriceLevel().equals("PREMIUM")) {
            level += 1;
        }

        return Math.min(5, level);
    }
}