package com.dipspro.modules.billing.controller;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.billing.dto.AppealCreateDto;
import com.dipspro.modules.billing.dto.AppealDto;
import com.dipspro.modules.billing.dto.AppealProcessDto;
import com.dipspro.modules.billing.entity.BillingAppeal;
import com.dipspro.modules.billing.service.BillingAppealService;
import com.dipspro.util.SecurityUtil;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 申诉管理控制器
 * 
 * 提供申诉相关的REST API接口，包括：
 * - 用户端：提交申诉、查看申诉状态、申诉历史
 * - 管理端：申诉审核、批量处理、申诉统计
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/billing/appeals")
@RequiredArgsConstructor
@Validated
public class BillingAppealController {

    private final BillingAppealService billingAppealService;

    /**
     * 提交申诉（用户端）
     * 
     * 用户针对特定的计费记录提交申诉，说明申诉原因和期望处理方式。
     * 每个计费记录只能提交一次申诉。
     * 
     * @param createDto 申诉创建参数
     * @return 创建的申诉信息
     */
    @PostMapping
    public ApiResponse<AppealDto> createAppeal(@Valid @RequestBody AppealCreateDto createDto) {
        log.info("用户提交申诉: UsageRecordId={}, Reason={}",
                createDto.getUsageRecordId(), createDto.getReason());

        Long userId = SecurityUtil.getCurrentUserId();
        createDto.setUserId(userId);

        // 检查是否已经存在申诉
        boolean exists = billingAppealService.hasExistingAppeal(createDto.getUsageRecordId());
        if (exists) {
            log.warn("该记录已存在申诉: UsageRecordId={}", createDto.getUsageRecordId());
            return ApiResponse.error("该计费记录已存在申诉，不能重复提交");
        }

        // 创建申诉
        BillingAppeal createdAppeal = billingAppealService.createAppeal(
                userId,
                createDto.getUsageRecordId(),
                createDto.getReason(),
                null, // 退款金额由系统计算
                createDto.getUserDescription());
        AppealDto appealDto = convertToAppealDto(createdAppeal);

        log.info("申诉提交成功: AppealId={}, UserId={}", createdAppeal.getId(), userId);
        return ApiResponse.success(appealDto, "申诉提交成功");
    }

    /**
     * 获取用户申诉列表（用户端）
     * 
     * 分页查询当前用户的所有申诉记录，支持按状态筛选。
     * 
     * @param page   页码，从0开始
     * @param size   每页大小，默认20，最大100
     * @param status 申诉状态筛选：PENDING, APPROVED, REJECTED
     * @return 分页的申诉列表
     */
    @GetMapping
    public ApiResponse<Page<AppealDto>> getUserAppeals(
            @RequestParam(defaultValue = "0") @Min(0) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size,
            @RequestParam(required = false) String status) {

        log.info("查询用户申诉列表: page={}, size={}, status={}", page, size, status);

        Long userId = SecurityUtil.getCurrentUserId();
        Pageable pageable = PageRequest.of(page, size);

        Page<BillingAppeal> appeals;
        if (status != null && !status.trim().isEmpty()) {
            appeals = billingAppealService.getUserAppealsByStatus(userId, status, pageable);
        } else {
            appeals = billingAppealService.getUserAppeals(userId, pageable);
        }

        Page<AppealDto> appealDtos = appeals.map(this::convertToAppealDto);

        log.info("用户申诉查询完成: UserId={}, TotalAppeals={}", userId, appeals.getTotalElements());
        return ApiResponse.success(appealDtos, "查询成功");
    }

    /**
     * 获取申诉详情
     * 
     * 根据申诉ID获取详细的申诉信息，包括申诉内容、处理结果等。
     * 用户只能查看自己的申诉，管理员可以查看所有申诉。
     * 
     * @param id 申诉ID
     * @return 申诉详情
     */
    @GetMapping("/{id}")
    public ApiResponse<AppealDto> getAppealDetail(@PathVariable @NotNull Long id) {
        log.info("获取申诉详情: AppealId={}", id);

        Long userId = SecurityUtil.getCurrentUserId();

        var appealOpt = billingAppealService.getAppealById(id);
        if (appealOpt.isEmpty()) {
            log.warn("申诉不存在: AppealId={}", id);
            return ApiResponse.error("申诉不存在");
        }

        BillingAppeal appeal = appealOpt.get();

        // 权限检查：用户只能查看自己的申诉
        if (!appeal.getUserId().equals(userId)) {
            log.warn("无权查看申诉: AppealId={}, UserId={}, AppealUserId={}",
                    id, userId, appeal.getUserId());
            return ApiResponse.error("无权查看该申诉");
        }

        AppealDto appealDto = convertToAppealDto(appeal);

        log.info("申诉详情获取成功: AppealId={}, UserId={}", id, userId);
        return ApiResponse.success(appealDto, "查询成功");
    }

    /**
     * 分页查询申诉（管理端）
     * 
     * 管理员可以分页查询所有申诉，支持按状态、用户、时间等条件筛选。
     * 
     * @param page   页码，从0开始
     * @param size   每页大小，默认20，最大100
     * @param status 申诉状态筛选
     * @param userId 用户ID筛选
     * @param reason 申诉原因关键词搜索
     * @return 分页的申诉列表
     */
    @GetMapping("/admin/list")
    public ApiResponse<Page<AppealDto>> getAppealsForAdmin(
            @RequestParam(defaultValue = "0") @Min(0) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) String reason) {

        log.info("管理员查询申诉: page={}, size={}, status={}, userId={}, reason={}",
                page, size, status, userId, reason);

        Pageable pageable = PageRequest.of(page, size);
        Page<BillingAppeal> appeals;

        if (reason != null && !reason.trim().isEmpty()) {
            // 按原因搜索
            appeals = billingAppealService.searchAppealsByReason(reason.trim(), pageable);
        } else if (status != null && !status.trim().isEmpty()) {
            // 按状态筛选
            appeals = billingAppealService.getAppealsByStatus(status, pageable);
        } else if (userId != null) {
            // 按用户筛选
            appeals = billingAppealService.getUserAppeals(userId, pageable);
        } else {
            // 查询全部
            appeals = billingAppealService.getAppeals(pageable);
        }

        Page<AppealDto> appealDtos = appeals.map(this::convertToAppealDto);

        log.info("管理员申诉查询完成: TotalAppeals={}", appeals.getTotalElements());
        return ApiResponse.success(appealDtos, "查询成功");
    }

    /**
     * 处理申诉（管理端）
     * 
     * 管理员审核申诉，可以批准或拒绝申诉，并添加处理说明。
     * 批准的申诉将自动退还相应费用。
     * 
     * @param id         申诉ID
     * @param processDto 处理参数
     * @return 处理结果
     */
    @PutMapping("/admin/{id}/process")
    public ApiResponse<AppealDto> processAppeal(
            @PathVariable @NotNull Long id,
            @Valid @RequestBody AppealProcessDto processDto) {

        log.info("处理申诉: AppealId={}, Action={}, AdminComment={}",
                id, processDto.getAction(), processDto.getAdminComment());

        // 获取申诉信息
        var appealOpt = billingAppealService.getAppealById(id);
        if (appealOpt.isEmpty()) {
            log.warn("申诉不存在: AppealId={}", id);
            return ApiResponse.error("申诉不存在");
        }

        BillingAppeal appeal = appealOpt.get();

        // 检查申诉状态
        if (!"PENDING".equals(appeal.getStatus())) {
            log.warn("申诉已处理，无法重复处理: AppealId={}, Status={}", id, appeal.getStatus());
            return ApiResponse.error("申诉已处理，无法重复操作");
        }

        // 处理申诉
        boolean success = billingAppealService.processAppeal(
                id, processDto.getAction(), processDto.getAdminComment());

        if (!success) {
            log.warn("申诉处理失败: AppealId={}", id);
            return ApiResponse.error("申诉处理失败");
        }

        // 重新获取处理后的申诉信息
        BillingAppeal processedAppeal = billingAppealService.getAppealById(id).orElse(appeal);

        AppealDto appealDto = convertToAppealDto(processedAppeal);

        log.info("申诉处理完成: AppealId={}, NewStatus={}", id, processedAppeal.getStatus());
        return ApiResponse.success(appealDto, "申诉处理成功");
    }

    /**
     * 获取申诉统计（管理端）
     * 
     * 返回申诉的统计信息，包括各状态的申诉数量、处理时效等。
     * 
     * @return 申诉统计信息
     */
    @GetMapping("/admin/statistics")
    public ApiResponse<Object> getAppealStatistics() {
        log.info("获取申诉统计信息");

        // 获取各状态申诉数量
        long pendingCount = billingAppealService.getAppealCountByStatus("PENDING");
        long approvedCount = billingAppealService.getAppealCountByStatus("APPROVED");
        long rejectedCount = billingAppealService.getAppealCountByStatus("REJECTED");
        long totalCount = pendingCount + approvedCount + rejectedCount;

        // 构建统计结果
        var statistics = new Object() {
            public final long total = totalCount;
            public final long pending = pendingCount;
            public final long approved = approvedCount;
            public final long rejected = rejectedCount;
            public final double approvalRate = totalCount > 0
                    ? (double) approvedCount / (approvedCount + rejectedCount) * 100
                    : 0.0;
        };

        log.info("申诉统计获取成功: Total={}, Pending={}, Approved={}, Rejected={}",
                totalCount, pendingCount, approvedCount, rejectedCount);
        return ApiResponse.success(statistics, "统计信息获取成功");
    }

    /**
     * 获取待处理申诉列表（管理端）
     * 
     * 快速获取所有待处理的申诉，用于管理员优先处理。
     * 
     * @param limit 返回数量限制，默认50
     * @return 待处理申诉列表
     */
    @GetMapping("/admin/pending")
    public ApiResponse<List<AppealDto>> getPendingAppeals(
            @RequestParam(defaultValue = "50") @Min(1) @Max(200) Integer limit) {

        log.info("获取待处理申诉列表: limit={}", limit);

        Pageable pageable = PageRequest.of(0, limit);
        Page<BillingAppeal> appeals = billingAppealService.getAppealsByStatus("PENDING", pageable);

        List<AppealDto> appealDtos = appeals.getContent().stream()
                .map(this::convertToAppealDto)
                .toList();

        log.info("待处理申诉获取成功: Count={}", appealDtos.size());
        return ApiResponse.success(appealDtos, "查询成功");
    }

    /**
     * 转换BillingAppeal实体为DTO
     * 
     * @param appeal 申诉实体
     * @return 申诉DTO
     */
    private AppealDto convertToAppealDto(BillingAppeal appeal) {
        return new AppealDto()
                .setId(appeal.getId())
                .setUserId(appeal.getUserId())
                .setUsageRecordId(appeal.getUsageRecordId())
                .setReason(appeal.getReason())
                .setUserDescription(appeal.getUserDescription())
                .setEvidenceUrls(appeal.getEvidenceUrls())
                .setStatus(appeal.getStatus())
                .setAdminComment(appeal.getAdminComment())
                .setSubmittedAt(appeal.getSubmittedAt())
                .setProcessedAt(appeal.getProcessedAt())
                .setRefundAmount(appeal.getRefundAmount())
                .setCreatedAt(appeal.getCreatedAt())
                .setUpdatedAt(appeal.getUpdatedAt());
    }

    /**
     * 转换创建DTO为实体
     * 
     * @param createDto 创建DTO
     * @return 申诉实体
     */
    private BillingAppeal convertToAppealEntity(AppealCreateDto createDto) {
        BillingAppeal appeal = new BillingAppeal();
        appeal.setUserId(createDto.getUserId());
        appeal.setUsageRecordId(createDto.getUsageRecordId());
        appeal.setReason(createDto.getReason());
        appeal.setUserDescription(createDto.getUserDescription());

        appeal.setStatus("PENDING"); // 默认为待处理状态
        return appeal;
    }
}