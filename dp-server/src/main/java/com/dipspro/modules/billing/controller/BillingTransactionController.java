package com.dipspro.modules.billing.controller;

import java.math.BigDecimal;
import java.time.LocalDate;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.billing.dto.TransactionDto;
import com.dipspro.modules.billing.dto.TransactionSummaryDto;
import com.dipspro.modules.billing.entity.BillingTransaction;
import com.dipspro.modules.billing.service.BillingTransactionService;
import com.dipspro.util.SecurityUtil;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 交易记录控制器
 * 
 * 提供交易记录相关的REST API接口，包括：
 * - 用户端：查看交易记录、交易详情、交易统计
 * - 管理端：查看所有交易、交易分析、财务报表
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/billing/transactions")
@RequiredArgsConstructor
@Validated
public class BillingTransactionController {

    private final BillingTransactionService billingTransactionService;

    /**
     * 获取用户交易记录（用户端）
     * 
     * 分页查询当前用户的所有交易记录，支持按类型、状态、时间筛选。
     * 
     * @param page      页码，从0开始
     * @param size      每页大小，默认20，最大100
     * @param type      交易类型筛选：RECHARGE, DEDUCTION, REFUND
     * @param status    交易状态筛选：SUCCESS, FAILED, PENDING
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 分页的交易记录列表
     */
    @GetMapping
    public ApiResponse<Page<TransactionDto>> getUserTransactions(
            @RequestParam(defaultValue = "0") @Min(0) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        log.info("查询用户交易记录: page={}, size={}, type={}, status={}, startDate={}, endDate={}",
                page, size, type, status, startDate, endDate);

        Long userId = SecurityUtil.getCurrentUserId();
        Pageable pageable = PageRequest.of(page, size);

        Page<BillingTransaction> transactions = billingTransactionService.getUserTransactions(
                userId, type, status, startDate, endDate, pageable);

        Page<TransactionDto> transactionDtos = transactions.map(this::convertToTransactionDto);

        log.info("用户交易记录查询完成: UserId={}, TotalTransactions={}", userId, transactions.getTotalElements());
        return ApiResponse.success(transactionDtos, "查询成功");
    }

    /**
     * 获取交易详情
     * 
     * 根据交易ID获取详细的交易信息，包括交易内容、状态变更等。
     * 用户只能查看自己的交易，管理员可以查看所有交易。
     * 
     * @param id 交易ID
     * @return 交易详情
     */
    @GetMapping("/{id}")
    public ApiResponse<TransactionDto> getTransactionDetail(@PathVariable @NotNull Long id) {
        log.info("获取交易详情: TransactionId={}", id);

        Long userId = SecurityUtil.getCurrentUserId();

        var transactionOpt = billingTransactionService.getTransactionById(id);
        if (transactionOpt.isEmpty()) {
            log.warn("交易不存在: TransactionId={}", id);
            return ApiResponse.error("交易记录不存在");
        }

        BillingTransaction transaction = transactionOpt.get();

        // 权限检查：用户只能查看自己的交易
        if (!transaction.getUserId().equals(userId)) {
            log.warn("无权查看交易: TransactionId={}, UserId={}, TransactionUserId={}",
                    id, userId, transaction.getUserId());
            return ApiResponse.error("无权查看该交易记录");
        }

        TransactionDto transactionDto = convertToTransactionDto(transaction);

        log.info("交易详情获取成功: TransactionId={}, UserId={}", id, userId);
        return ApiResponse.success(transactionDto, "查询成功");
    }

    /**
     * 获取用户交易统计（用户端）
     * 
     * 统计用户的交易数据，包括总充值、总消费、余额变化等。
     * 
     * @param period 统计周期：WEEK, MONTH, QUARTER, YEAR
     * @param count  统计期数，默认1期
     * @return 交易统计信息
     */
    @GetMapping("/summary")
    public ApiResponse<TransactionSummaryDto> getUserTransactionSummary(
            @RequestParam(defaultValue = "MONTH") String period,
            @RequestParam(defaultValue = "1") @Min(1) @Max(12) Integer count) {

        log.info("获取用户交易统计: period={}, count={}", period, count);

        Long userId = SecurityUtil.getCurrentUserId();

        Object summaryData = billingTransactionService.getUserTransactionSummary(userId, period, count);
        // 临时解决方案：创建基础的统计信息，后续完善具体的转换逻辑
        TransactionSummaryDto summary = TransactionSummaryDto.builder()
                .period(period)
                .totalAmount(BigDecimal.ZERO)
                .totalTransactions(0L)
                .build();

        log.info("用户交易统计获取成功: UserId={}, Period={}, Count={}", userId, period, count);
        return ApiResponse.success(summary, "统计信息获取成功");
    }

    /**
     * 分页查询交易记录（管理端）
     * 
     * 管理员可以分页查询所有交易记录，支持按用户、类型、状态、时间等条件筛选。
     * 
     * @param page      页码，从0开始
     * @param size      每页大小，默认20，最大100
     * @param userId    用户ID筛选
     * @param type      交易类型筛选
     * @param status    交易状态筛选
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param minAmount 最小金额筛选
     * @param maxAmount 最大金额筛选
     * @return 分页的交易记录列表
     */
    @GetMapping("/admin/list")
    public ApiResponse<Page<TransactionDto>> getTransactionsForAdmin(
            @RequestParam(defaultValue = "0") @Min(0) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) BigDecimal minAmount,
            @RequestParam(required = false) BigDecimal maxAmount) {

        log.info(
                "管理员查询交易: page={}, size={}, userId={}, type={}, status={}, startDate={}, endDate={}, minAmount={}, maxAmount={}",
                page, size, userId, type, status, startDate, endDate, minAmount, maxAmount);

        Pageable pageable = PageRequest.of(page, size);
        Page<BillingTransaction> transactions = billingTransactionService.getTransactionsForAdmin(
                userId, type, status, startDate, endDate, minAmount, maxAmount, pageable);

        Page<TransactionDto> transactionDtos = transactions.map(this::convertToTransactionDto);

        log.info("管理员交易查询完成: TotalTransactions={}", transactions.getTotalElements());
        return ApiResponse.success(transactionDtos, "查询成功");
    }

    /**
     * 获取交易统计（管理端）
     * 
     * 返回平台的整体交易统计信息，包括交易总量、金额分布、趋势分析等。
     * 
     * @param period 统计周期：DAY, WEEK, MONTH, QUARTER, YEAR
     * @param count  统计期数，默认1期
     * @return 交易统计信息
     */
    @GetMapping("/admin/statistics")
    public ApiResponse<Object> getTransactionStatistics(
            @RequestParam(defaultValue = "MONTH") String period,
            @RequestParam(defaultValue = "1") @Min(1) @Max(24) Integer count) {

        log.info("获取交易统计信息: period={}, count={}", period, count);

        // 获取统计数据
        var statistics = billingTransactionService.getTransactionStatistics(period, count);

        log.info("交易统计获取成功: Period={}, Count={}", period, count);
        return ApiResponse.success(statistics, "统计信息获取成功");
    }

    /**
     * 获取财务报表（管理端）
     * 
     * 生成指定时间段的财务报表，包括收入、支出、净利润等关键指标。
     * 
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @param reportType 报表类型：INCOME, EXPENSE, PROFIT
     * @return 财务报表数据
     */
    @GetMapping("/admin/financial-report")
    public ApiResponse<Object> getFinancialReport(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "INCOME") String reportType) {

        log.info("生成财务报表: startDate={}, endDate={}, reportType={}", startDate, endDate, reportType);

        // 验证日期范围
        if (startDate.isAfter(endDate)) {
            log.warn("日期范围错误: startDate={}, endDate={}", startDate, endDate);
            return ApiResponse.error("开始日期不能晚于结束日期");
        }

        // 生成财务报表
        var report = billingTransactionService.generateFinancialReport(startDate, endDate, reportType);

        log.info("财务报表生成成功: startDate={}, endDate={}, reportType={}", startDate, endDate, reportType);
        return ApiResponse.success(report, "财务报表生成成功");
    }

    /**
     * 获取异常交易（管理端）
     * 
     * 查询异常的交易记录，如失败的充值、异常的退款等，用于风险监控。
     * 
     * @param page        页码，从0开始
     * @param size        每页大小，默认20，最大100
     * @param anomalyType 异常类型：FAILED_RECHARGE, SUSPICIOUS_REFUND, LARGE_AMOUNT
     * @return 异常交易列表
     */
    @GetMapping("/admin/anomalies")
    public ApiResponse<Page<TransactionDto>> getAnomalousTransactions(
            @RequestParam(defaultValue = "0") @Min(0) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size,
            @RequestParam(required = false) String anomalyType) {

        log.info("查询异常交易: page={}, size={}, anomalyType={}", page, size, anomalyType);

        Pageable pageable = PageRequest.of(page, size);
        Page<BillingTransaction> transactions = billingTransactionService.getAnomalousTransactions(
                anomalyType, pageable);

        Page<TransactionDto> transactionDtos = transactions.map(this::convertToTransactionDto);

        log.info("异常交易查询完成: TotalAnomalies={}", transactions.getTotalElements());
        return ApiResponse.success(transactionDtos, "查询成功");
    }

    /**
     * 导出交易记录（管理端）
     * 
     * 导出指定条件的交易记录为Excel文件。
     * 
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param type      交易类型筛选
     * @param status    交易状态筛选
     * @return 导出结果
     */
    @GetMapping("/admin/export")
    public ApiResponse<Object> exportTransactions(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String status) {

        log.info("导出交易记录: startDate={}, endDate={}, type={}, status={}", startDate, endDate, type, status);

        // 验证日期范围
        if (startDate.isAfter(endDate)) {
            log.warn("日期范围错误: startDate={}, endDate={}", startDate, endDate);
            return ApiResponse.error("开始日期不能晚于结束日期");
        }

        // 导出交易记录
        Object exportResult = billingTransactionService.exportTransactions(startDate, endDate, type, status);

        log.info("交易记录导出成功: exportResult={}", exportResult);
        return ApiResponse.success(exportResult, "交易记录导出成功");
    }

    /**
     * 转换BillingTransaction实体为DTO
     * 
     * @param transaction 交易实体
     * @return 交易DTO
     */
    private TransactionDto convertToTransactionDto(BillingTransaction transaction) {
        // 临时简化实现，创建基础的TransactionDto
        TransactionDto dto = new TransactionDto();
        dto.setId(transaction.getId());
        dto.setUserId(transaction.getUserId());
        dto.setType(transaction.getType());
        dto.setAmount(transaction.getAmount());
        dto.setBalanceBefore(transaction.getBalanceBefore());
        dto.setBalanceAfter(transaction.getBalanceAfter());
        dto.setStatus(transaction.getStatus());
        dto.setDescription(transaction.getDescription());
        dto.setRelatedId(transaction.getRelatedId());
        dto.setRelatedType(transaction.getRelatedType());
        dto.setTransactionNo(transaction.getTransactionNo());
        dto.setPaymentMethod(transaction.getPaymentMethod());
        dto.setCreatedAt(transaction.getCreatedAt());
        dto.setUpdatedAt(transaction.getUpdatedAt());
        return dto;
    }
}