package com.dipspro.modules.billing.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户余额信息DTO
 * 
 * 用于返回用户的详细余额信息，包括各种类型的余额、
 * Token使用情况、账户状态等信息。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BalanceInfoDto {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 充值余额
     * 用户通过充值获得的余额
     */
    private BigDecimal rechargedBalance;

    /**
     * 赠送余额
     * 系统赠送或申诉退款的余额
     */
    private BigDecimal giftBalance;

    /**
     * 免费Token数量
     * 用户可用的免费Token配额
     */
    private Long freeTokens;

    /**
     * 总可用余额
     * 所有余额类型的总和
     */
    private BigDecimal totalBalance;

    /**
     * 今日已使用Token数量
     * 当天已消费的Token总数
     */
    private Long usedTokensToday;

    /**
     * 账户是否被冻结
     */
    private Boolean isFrozen;

    /**
     * 当前使用的套餐ID
     */
    private Long packageId;

    /**
     * 余额最后更新时间
     */
    private LocalDateTime lastUpdated;

    /**
     * 检查余额是否充足
     * 
     * @param requiredAmount 所需金额
     * @return 是否充足
     */
    public boolean isSufficient(BigDecimal requiredAmount) {
        if (requiredAmount == null || totalBalance == null) {
            return false;
        }
        return totalBalance.compareTo(requiredAmount) >= 0;
    }

    /**
     * 检查账户是否正常
     * 
     * @return 是否正常（未冻结）
     */
    public boolean isAccountNormal() {
        return !Boolean.TRUE.equals(isFrozen);
    }
}