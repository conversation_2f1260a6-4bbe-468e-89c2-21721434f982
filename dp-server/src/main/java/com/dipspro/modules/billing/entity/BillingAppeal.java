package com.dipspro.modules.billing.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import org.hibernate.annotations.Comment;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 计费申诉记录实体
 * 对应数据库表：b_billing_appeals
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "b_billing_appeals")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Comment("计费申诉记录表")
public class BillingAppeal {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("主键ID")
    private Long id;

    /**
     * 申诉单号
     */
    @Column(name = "appeal_no", length = 100, nullable = false, unique = true)
    @NotBlank(message = "申诉单号不能为空")
    @Comment("申诉单号")
    private String appealNo;

    /**
     * 申诉用户ID，关联users.id
     */
    @Column(name = "user_id", nullable = false)
    @NotNull(message = "申诉用户ID不能为空")
    @Comment("申诉用户ID，关联users.id")
    private Long userId;

    /**
     * 申诉的使用记录ID，关联b_billing_usage_records.id
     */
    @Column(name = "usage_record_id", nullable = false, unique = true)
    @NotNull(message = "使用记录ID不能为空")
    @Comment("申诉的使用记录ID，关联b_billing_usage_records.id")
    private Long usageRecordId;

    // 申诉内容
    /**
     * 申诉原因
     */
    @Column(name = "reason", columnDefinition = "TEXT", nullable = false)
    @NotBlank(message = "申诉原因不能为空")
    @Comment("申诉原因")
    private String reason;

    /**
     * 用户详细描述
     */
    @Column(name = "user_description", columnDefinition = "TEXT")
    @Comment("用户详细描述")
    private String userDescription;

    /**
     * 证据文件URLs（JSON数组）
     */
    @Column(name = "evidence_urls", columnDefinition = "TEXT")
    @Comment("证据文件URLs（JSON数组）")
    private String evidenceUrls;

    // 处理信息
    /**
     * 处理管理员ID，关联users.id
     */
    @Column(name = "admin_id")
    @Comment("处理管理员ID，关联users.id")
    private Long adminId;

    /**
     * 申诉状态：PENDING, APPROVED, REJECTED, CANCELLED
     */
    @Column(name = "status", length = 20)
    @Comment("申诉状态：PENDING, APPROVED, REJECTED, CANCELLED")
    private String status = "PENDING";

    /**
     * 管理员处理意见
     */
    @Column(name = "admin_comment", columnDefinition = "TEXT")
    @Comment("管理员处理意见")
    private String adminComment;

    // 退费信息
    /**
     * 退费金额
     */
    @Column(name = "refund_amount", precision = 10, scale = 6)
    @PositiveOrZero(message = "退费金额不能为负数")
    @Comment("退费金额")
    private BigDecimal refundAmount;

    /**
     * 退费交易记录ID，关联b_billing_transactions.id
     */
    @Column(name = "refund_transaction_id")
    @Comment("退费交易记录ID，关联b_billing_transactions.id")
    private Long refundTransactionId;

    // 时间字段
    /**
     * 提交时间
     */
    @Column(name = "submitted_at", nullable = false, updatable = false)
    @Comment("提交时间")
    private LocalDateTime submittedAt;

    /**
     * 处理时间
     */
    @Column(name = "processed_at")
    @Comment("处理时间")
    private LocalDateTime processedAt;

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false, updatable = false)
    @Comment("创建时间")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    @Comment("更新时间")
    private LocalDateTime updatedAt;

    /**
     * 创建时自动设置创建时间和更新时间
     */
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createdAt = now;
        this.updatedAt = now;
        if (this.submittedAt == null) {
            this.submittedAt = now;
        }
    }

    /**
     * 更新时自动设置更新时间
     */
    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 检查是否为待处理状态
     * 
     * @return 是否为待处理状态
     */
    public boolean isPending() {
        return "PENDING".equals(status);
    }

    /**
     * 检查是否已批准
     * 
     * @return 是否已批准
     */
    public boolean isApproved() {
        return "APPROVED".equals(status);
    }

    /**
     * 检查是否已拒绝
     * 
     * @return 是否已拒绝
     */
    public boolean isRejected() {
        return "REJECTED".equals(status);
    }

    /**
     * 检查是否已取消
     * 
     * @return 是否已取消
     */
    public boolean isCancelled() {
        return "CANCELLED".equals(status);
    }

    /**
     * 检查是否已处理完成
     * 
     * @return 是否已处理完成
     */
    public boolean isProcessed() {
        return !isPending();
    }

    /**
     * 检查是否有退费
     * 
     * @return 是否有退费
     */
    public boolean hasRefund() {
        return refundAmount != null && refundAmount.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 标记为已处理
     * 
     * @param status  处理状态
     * @param adminId 管理员ID
     * @param comment 处理意见
     */
    public void markAsProcessed(String status, Long adminId, String comment) {
        this.status = status;
        this.adminId = adminId;
        this.adminComment = comment;
        this.processedAt = LocalDateTime.now();
    }

    /**
     * 设置退费信息
     * 
     * @param refundAmount  退费金额
     * @param transactionId 退费交易ID
     */
    public void setRefundInfo(BigDecimal refundAmount, Long transactionId) {
        this.refundAmount = refundAmount;
        this.refundTransactionId = transactionId;
    }

}