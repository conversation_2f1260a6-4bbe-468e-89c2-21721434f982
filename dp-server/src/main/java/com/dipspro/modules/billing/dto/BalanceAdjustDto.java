package com.dipspro.modules.billing.dto;

import java.math.BigDecimal;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 余额调整DTO
 * 
 * 用于管理员调整用户余额时的请求参数，包含调整类型、金额和原因。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class BalanceAdjustDto {

    /**
     * 调整类型
     * INCREASE-增加, DECREASE-减少, SET-设置为指定值
     */
    @NotBlank(message = "调整类型不能为空")
    @Pattern(regexp = "^(INCREASE|DECREASE|SET)$", message = "调整类型只能是INCREASE、DECREASE或SET")
    private String adjustType;

    /**
     * 调整金额
     * 必须大于0
     */
    @NotNull(message = "调整金额不能为空")
    @DecimalMin(value = "0.01", message = "调整金额必须大于0")
    private BigDecimal amount;

    /**
     * 余额类型
     * CASH-现金余额, GIFT-赠送余额, TOTAL-总余额
     */
    @NotBlank(message = "余额类型不能为空")
    @Pattern(regexp = "^(CASH|GIFT|TOTAL)$", message = "余额类型只能是CASH、GIFT或TOTAL")
    private String balanceType;

    /**
     * 调整原因
     * 必须填写调整原因，用于审计
     */
    @NotBlank(message = "调整原因不能为空")
    @Size(min = 5, max = 500, message = "调整原因长度必须在5-500个字符之间")
    private String reason;

    /**
     * 管理员备注
     * 可选的额外说明信息
     */
    @Size(max = 1000, message = "管理员备注不能超过1000个字符")
    private String adminNote;

    /**
     * 是否发送通知
     * 是否给用户发送余额变动通知
     */
    private Boolean sendNotification = true;

    /**
     * 检查是否为增加操作
     * 
     * @return 是否增加
     */
    public boolean isIncrease() {
        return "INCREASE".equals(adjustType);
    }

    /**
     * 检查是否为减少操作
     * 
     * @return 是否减少
     */
    public boolean isDecrease() {
        return "DECREASE".equals(adjustType);
    }

    /**
     * 检查是否为设置操作
     * 
     * @return 是否设置
     */
    public boolean isSet() {
        return "SET".equals(adjustType);
    }

    /**
     * 检查是否为现金余额调整
     * 
     * @return 是否现金余额
     */
    public boolean isCashBalance() {
        return "CASH".equals(balanceType);
    }

    /**
     * 检查是否为赠送余额调整
     * 
     * @return 是否赠送余额
     */
    public boolean isGiftBalance() {
        return "GIFT".equals(balanceType);
    }

    /**
     * 检查是否为总余额调整
     * 
     * @return 是否总余额
     */
    public boolean isTotalBalance() {
        return "TOTAL".equals(balanceType);
    }

    /**
     * 获取调整类型显示文本
     * 
     * @return 调整类型显示文本
     */
    public String getAdjustTypeDisplay() {
        if (adjustType == null) {
            return "未知操作";
        }

        return switch (adjustType) {
            case "INCREASE" -> "增加";
            case "DECREASE" -> "减少";
            case "SET" -> "设置为";
            default -> "未知操作";
        };
    }

    /**
     * 获取余额类型显示文本
     * 
     * @return 余额类型显示文本
     */
    public String getBalanceTypeDisplay() {
        if (balanceType == null) {
            return "未知类型";
        }

        return switch (balanceType) {
            case "CASH" -> "现金余额";
            case "GIFT" -> "赠送余额";
            case "TOTAL" -> "总余额";
            default -> "未知类型";
        };
    }

    /**
     * 获取格式化的调整金额
     * 
     * @return 格式化的金额字符串
     */
    public String getFormattedAmount() {
        if (amount == null) {
            return "¥0.00";
        }

        String prefix = isIncrease() ? "+" : (isDecrease() ? "-" : "");
        return prefix + "¥" + amount.toString();
    }

    /**
     * 获取调整操作描述
     * 
     * @return 操作描述
     */
    public String getAdjustmentDescription() {
        StringBuilder desc = new StringBuilder();
        desc.append(getAdjustTypeDisplay()).append(getBalanceTypeDisplay());
        desc.append(" ").append(getFormattedAmount());

        if (reason != null && !reason.trim().isEmpty()) {
            String shortReason = reason.length() > 50 ? reason.substring(0, 50) + "..." : reason;
            desc.append(" | 原因: ").append(shortReason);
        }

        return desc.toString();
    }

    /**
     * 验证调整参数
     * 
     * @return 验证结果描述
     */
    public String validateAdjustment() {
        if (adjustType == null || adjustType.trim().isEmpty()) {
            return "调整类型不能为空";
        }

        if (!"INCREASE".equals(adjustType) && !"DECREASE".equals(adjustType) && !"SET".equals(adjustType)) {
            return "调整类型只能是INCREASE、DECREASE或SET";
        }

        if (amount == null) {
            return "调整金额不能为空";
        }

        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            return "调整金额必须大于0";
        }

        if (balanceType == null || balanceType.trim().isEmpty()) {
            return "余额类型不能为空";
        }

        if (!"CASH".equals(balanceType) && !"GIFT".equals(balanceType) && !"TOTAL".equals(balanceType)) {
            return "余额类型只能是CASH、GIFT或TOTAL";
        }

        if (reason == null || reason.trim().isEmpty()) {
            return "调整原因不能为空";
        }

        if (reason.length() < 5) {
            return "调整原因长度不能少于5个字符";
        }

        if (reason.length() > 500) {
            return "调整原因长度不能超过500个字符";
        }

        if (adminNote != null && adminNote.length() > 1000) {
            return "管理员备注不能超过1000个字符";
        }

        // 特殊业务规则验证
        if (isSet() && isTotalBalance()) {
            return "不能直接设置总余额，请分别设置现金余额和赠送余额";
        }

        return "验证通过";
    }

    /**
     * 获取风险等级
     * 根据调整金额和类型判断操作风险等级
     * 
     * @return 风险等级：LOW, MEDIUM, HIGH
     */
    public String getRiskLevel() {
        if (amount == null) {
            return "UNKNOWN";
        }

        // 大额调整为高风险
        if (amount.compareTo(BigDecimal.valueOf(10000)) > 0) {
            return "HIGH";
        }

        // 减少操作风险相对较高
        if (isDecrease() && amount.compareTo(BigDecimal.valueOf(1000)) > 0) {
            return "HIGH";
        }

        // 中等金额为中风险
        if (amount.compareTo(BigDecimal.valueOf(1000)) > 0) {
            return "MEDIUM";
        }

        return "LOW";
    }

    /**
     * 获取风险等级显示文本
     * 
     * @return 风险等级显示文本
     */
    public String getRiskLevelDisplay() {
        String riskLevel = getRiskLevel();
        return switch (riskLevel) {
            case "LOW" -> "低风险";
            case "MEDIUM" -> "中风险";
            case "HIGH" -> "高风险";
            default -> "未知风险";
        };
    }

    /**
     * 检查是否需要额外审批
     * 
     * @return 是否需要审批
     */
    public boolean needsApproval() {
        return "HIGH".equals(getRiskLevel()) ||
                (isDecrease() && amount.compareTo(BigDecimal.valueOf(5000)) > 0);
    }

    /**
     * 获取调整摘要信息
     * 
     * @return 调整摘要
     */
    public String getAdjustmentSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(getAdjustmentDescription());
        summary.append(" | 风险: ").append(getRiskLevelDisplay());

        if (needsApproval()) {
            summary.append(" | 需要审批");
        }

        if (Boolean.TRUE.equals(sendNotification)) {
            summary.append(" | 发送通知");
        }

        return summary.toString();
    }
}