package com.dipspro.modules.billing.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.dipspro.modules.billing.entity.BillingPackage;

/**
 * 计费套餐配置数据访问接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface BillingPackageRepository extends JpaRepository<BillingPackage, Long> {

        /**
         * 根据激活状态查询套餐列表
         * 
         * @param isActive 是否激活
         * @return 套餐列表
         */
        List<BillingPackage> findByIsActiveOrderBySortOrderAsc(Boolean isActive);

        /**
         * 查询所有激活的套餐，按排序权重排序
         * 
         * @return 激活的套餐列表
         */
        @Query("SELECT bp FROM BillingPackage bp WHERE bp.isActive = true ORDER BY bp.sortOrder ASC, bp.id ASC")
        List<BillingPackage> findActivePackagesOrderBySortOrder();

        /**
         * 根据套餐名称查询套餐
         * 
         * @param name 套餐名称
         * @return 套餐信息
         */
        Optional<BillingPackage> findByName(String name);

        /**
         * 检查套餐名称是否存在（排除指定ID）
         * 
         * @param name 套餐名称
         * @param id   要排除的套餐ID
         * @return 是否存在
         */
        boolean existsByNameAndIdNot(String name, Long id);

        /**
         * 检查套餐名称是否存在
         * 
         * @param name 套餐名称
         * @return 是否存在
         */
        boolean existsByName(String name);

        /**
         * 分页查询套餐列表
         * 
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<BillingPackage> findAllByOrderBySortOrderAscIdAsc(Pageable pageable);

        /**
         * 根据激活状态分页查询套餐列表
         * 
         * @param isActive 是否激活
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<BillingPackage> findByIsActiveOrderBySortOrderAscIdAsc(Boolean isActive, Pageable pageable);

        /**
         * 根据套餐名称模糊查询
         * 
         * @param name     套餐名称关键词
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<BillingPackage> findByNameContainingIgnoreCaseOrderBySortOrderAscIdAsc(String name, Pageable pageable);

        /**
         * 获取默认套餐（排序权重最小的激活套餐）
         * 
         * @return 默认套餐
         */
        @Query("SELECT bp FROM BillingPackage bp WHERE bp.isActive = true ORDER BY bp.sortOrder ASC, bp.id ASC")
        Optional<BillingPackage> findDefaultPackage();

        /**
         * 获取指定排序权重范围内的套餐数量
         * 
         * @param minOrder 最小排序权重
         * @param maxOrder 最大排序权重
         * @return 套餐数量
         */
        @Query("SELECT COUNT(bp) FROM BillingPackage bp WHERE bp.sortOrder >= :minOrder AND bp.sortOrder <= :maxOrder")
        long countBySortOrderBetween(@Param("minOrder") Integer minOrder, @Param("maxOrder") Integer maxOrder);

        /**
         * 查询指定价格范围内的套餐
         * 
         * @param minInputPrice 最小输入Token价格
         * @param maxInputPrice 最大输入Token价格
         * @param isActive      是否激活
         * @return 套餐列表
         */
        @Query("SELECT bp FROM BillingPackage bp WHERE bp.inputTokenPrice >= :minInputPrice " +
                        "AND bp.inputTokenPrice <= :maxInputPrice AND bp.isActive = :isActive " +
                        "ORDER BY bp.inputTokenPrice ASC, bp.sortOrder ASC")
        List<BillingPackage> findByInputTokenPriceRangeAndIsActive(
                        @Param("minInputPrice") java.math.BigDecimal minInputPrice,
                        @Param("maxInputPrice") java.math.BigDecimal maxInputPrice,
                        @Param("isActive") Boolean isActive);

        /**
         * 查询包含免费Token的套餐
         * 
         * @param isActive 是否激活
         * @return 套餐列表
         */
        @Query("SELECT bp FROM BillingPackage bp WHERE bp.freeTokens > 0 AND bp.isActive = :isActive " +
                        "ORDER BY bp.freeTokens DESC, bp.sortOrder ASC")
        List<BillingPackage> findPackagesWithFreeTokens(@Param("isActive") Boolean isActive);

        /**
         * 获取最大排序权重
         * 
         * @return 最大排序权重
         */
        @Query("SELECT COALESCE(MAX(bp.sortOrder), 0) FROM BillingPackage bp")
        Integer getMaxSortOrder();

        /**
         * 更新套餐激活状态
         * 
         * @param id       套餐ID
         * @param isActive 激活状态
         * @return 更新的记录数
         */
        @Query("UPDATE BillingPackage bp SET bp.isActive = :isActive, bp.updatedAt = CURRENT_TIMESTAMP WHERE bp.id = :id")
        int updateActiveStatus(@Param("id") Long id, @Param("isActive") Boolean isActive);
}