package com.dipspro.modules.billing.repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.dipspro.modules.billing.entity.PaymentRecord;

/**
 * 支付记录数据访问接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface PaymentRecordRepository extends JpaRepository<PaymentRecord, Long> {

        /**
         * 根据用户ID分页查询支付记录
         * 
         * @param userId   用户ID
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<PaymentRecord> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

        /**
         * 根据支付状态查询支付记录
         * 
         * @param status   支付状态
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<PaymentRecord> findByStatusOrderByCreatedAtDesc(String status, Pageable pageable);

        /**
         * 根据支付方式查询支付记录
         * 
         * @param paymentMethod 支付方式
         * @param pageable      分页参数
         * @return 分页结果
         */
        Page<PaymentRecord> findByPaymentMethodOrderByCreatedAtDesc(String paymentMethod, Pageable pageable);

        /**
         * 根据支付单号查询支付记录
         * 
         * @param paymentNo 支付单号
         * @return 支付记录
         */
        Optional<PaymentRecord> findByPaymentNo(String paymentNo);

        /**
         * 根据第三方交易号查询支付记录
         * 
         * @param platformTransactionNo 第三方交易号
         * @return 支付记录
         */
        Optional<PaymentRecord> findByPlatformTransactionNo(String platformTransactionNo);

        /**
         * 检查支付单号是否存在
         * 
         * @param paymentNo 支付单号
         * @return 是否存在
         */
        boolean existsByPaymentNo(String paymentNo);

        /**
         * 检查第三方交易号是否存在
         * 
         * @param platformTransactionNo 第三方交易号
         * @return 是否存在
         */
        boolean existsByPlatformTransactionNo(String platformTransactionNo);

        /**
         * 根据用户ID和支付状态查询支付记录
         * 
         * @param userId   用户ID
         * @param status   支付状态
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<PaymentRecord> findByUserIdAndStatusOrderByCreatedAtDesc(
                        Long userId, String status, Pageable pageable);

        /**
         * 根据用户ID和支付方式查询支付记录
         * 
         * @param userId        用户ID
         * @param paymentMethod 支付方式
         * @param pageable      分页参数
         * @return 分页结果
         */
        Page<PaymentRecord> findByUserIdAndPaymentMethodOrderByCreatedAtDesc(
                        Long userId, String paymentMethod, Pageable pageable);

        /**
         * 根据用户ID和时间范围查询支付记录
         * 
         * @param userId    用户ID
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @param pageable  分页参数
         * @return 分页结果
         */
        Page<PaymentRecord> findByUserIdAndCreatedAtBetweenOrderByCreatedAtDesc(
                        Long userId, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

        /**
         * 查询待支付的订单记录
         * 
         * @param pageable 分页参数
         * @return 分页结果
         */
        @Query("SELECT pr FROM PaymentRecord pr WHERE pr.status = 'PENDING' ORDER BY pr.createdAt ASC")
        Page<PaymentRecord> findPendingPayments(Pageable pageable);

        /**
         * 查询超时未支付的订单记录
         * 
         * @param timeoutTime 超时时间
         * @param pageable    分页参数
         * @return 分页结果
         */
        @Query("SELECT pr FROM PaymentRecord pr WHERE pr.status = 'PENDING' AND pr.createdAt < :timeoutTime " +
                        "ORDER BY pr.createdAt ASC")
        Page<PaymentRecord> findTimeoutPendingPayments(@Param("timeoutTime") LocalDateTime timeoutTime,
                        Pageable pageable);

        /**
         * 查询用户支付统计信息
         * 
         * @param userId 用户ID
         * @return 包含总支付次数、成功支付金额、失败支付次数的统计信息
         */
        @Query("SELECT COUNT(pr), " +
                        "SUM(CASE WHEN pr.status = 'SUCCESS' THEN pr.amount ELSE 0 END), " +
                        "SUM(CASE WHEN pr.status = 'FAILED' THEN 1 ELSE 0 END) " +
                        "FROM PaymentRecord pr WHERE pr.userId = :userId")
        Object[] getUserPaymentStats(@Param("userId") Long userId);

        /**
         * 查询用户指定时间范围内的支付统计
         * 
         * @param userId    用户ID
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 包含支付次数、成功金额、失败次数的统计信息
         */
        @Query("SELECT COUNT(pr), " +
                        "SUM(CASE WHEN pr.status = 'SUCCESS' THEN pr.amount ELSE 0 END), " +
                        "SUM(CASE WHEN pr.status = 'FAILED' THEN 1 ELSE 0 END) " +
                        "FROM PaymentRecord pr WHERE pr.userId = :userId " +
                        "AND pr.createdAt >= :startTime AND pr.createdAt <= :endTime")
        Object[] getUserPaymentStatsInPeriod(@Param("userId") Long userId,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 查询系统支付统计信息
         * 
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 按支付状态分组的统计信息
         */
        @Query("SELECT pr.status, COUNT(pr), SUM(pr.amount) " +
                        "FROM PaymentRecord pr WHERE pr.createdAt >= :startTime AND pr.createdAt <= :endTime " +
                        "GROUP BY pr.status ORDER BY SUM(pr.amount) DESC")
        List<Object[]> getSystemPaymentStats(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 查询支付方式统计信息
         * 
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 按支付方式分组的统计信息
         */
        @Query("SELECT pr.paymentMethod, COUNT(pr), SUM(CASE WHEN pr.status = 'SUCCESS' THEN pr.amount ELSE 0 END) "
                        +
                        "FROM PaymentRecord pr WHERE pr.createdAt >= :startTime AND pr.createdAt <= :endTime " +
                        "GROUP BY pr.paymentMethod ORDER BY SUM(CASE WHEN pr.status = 'SUCCESS' THEN pr.amount ELSE 0 END) DESC")
        List<Object[]> getPaymentMethodStats(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 查询用户今日支付统计
         * 
         * @param userId     用户ID
         * @param startOfDay 今日开始时间
         * @return 包含今日支付次数、今日成功金额的统计信息
         */
        @Query("SELECT COUNT(pr), SUM(CASE WHEN pr.status = 'SUCCESS' THEN pr.amount ELSE 0 END) " +
                        "FROM PaymentRecord pr WHERE pr.userId = :userId AND pr.createdAt >= :startOfDay")
        Object[] getUserTodayPaymentStats(@Param("userId") Long userId, @Param("startOfDay") LocalDateTime startOfDay);

        /**
         * 查询大额支付记录（金额超过指定阈值）
         * 
         * @param amountThreshold 金额阈值
         * @param pageable        分页参数
         * @return 分页结果
         */
        @Query("SELECT pr FROM PaymentRecord pr WHERE pr.amount >= :amountThreshold " +
                        "ORDER BY pr.amount DESC, pr.createdAt DESC")
        Page<PaymentRecord> findLargeAmountPayments(@Param("amountThreshold") BigDecimal amountThreshold,
                        Pageable pageable);

        /**
         * 查询失败支付记录
         * 
         * @param pageable 分页参数
         * @return 分页结果
         */
        @Query("SELECT pr FROM PaymentRecord pr WHERE pr.status = 'FAILED' ORDER BY pr.createdAt DESC")
        Page<PaymentRecord> findFailedPayments(Pageable pageable);

        /**
         * 更新支付状态和第三方交易信息
         * 
         * @param orderNo                 订单号
         * @param paymentStatus           支付状态
         * @param thirdPartyTransactionId 第三方交易号
         * @param paidAt                  支付时间
         * @return 更新的记录数
         */
        @Modifying
        @Query("UPDATE PaymentRecord pr SET pr.status = :status, " +
                        "pr.platformTransactionNo = :platformTransactionNo, pr.paidAt = :paidAt, " +
                        "pr.updatedAt = CURRENT_TIMESTAMP WHERE pr.paymentNo = :paymentNo")
        int updatePaymentStatus(@Param("paymentNo") String paymentNo,
                        @Param("status") String status,
                        @Param("platformTransactionNo") String platformTransactionNo,
                        @Param("paidAt") LocalDateTime paidAt);

        /**
         * 批量设置超时订单为失败状态
         * 
         * @param timeoutTime 超时时间
         * @return 更新的记录数
         */
        @Modifying
        @Query("UPDATE PaymentRecord pr SET pr.status = 'FAILED', pr.updatedAt = CURRENT_TIMESTAMP " +
                        "WHERE pr.status = 'PENDING' AND pr.createdAt < :timeoutTime")
        int markTimeoutPaymentsAsFailed(@Param("timeoutTime") LocalDateTime timeoutTime);

        /**
         * 查询用户最近的支付记录
         * 
         * @param userId   用户ID
         * @param pageable 分页参数
         * @return 最近的支付记录列表
         */
        @Query("SELECT pr FROM PaymentRecord pr WHERE pr.userId = :userId " +
                        "ORDER BY pr.createdAt DESC")
        List<PaymentRecord> findRecentPaymentsByUserId(@Param("userId") Long userId, Pageable pageable);

        /**
         * 查询活跃支付用户列表（指定时间范围内有支付记录的用户）
         * 
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 活跃用户ID列表
         */
        @Query("SELECT DISTINCT pr.userId FROM PaymentRecord pr " +
                        "WHERE pr.createdAt >= :startTime AND pr.createdAt <= :endTime")
        List<Long> getActivePaymentUsers(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 查询支付成功率
         * 
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 支付成功率（百分比）
         */
        @Query("SELECT CASE WHEN COUNT(pr) = 0 THEN 0 ELSE " +
                        "(CAST(SUM(CASE WHEN pr.status = 'SUCCESS' THEN 1 ELSE 0 END) AS DOUBLE) / COUNT(pr)) * 100 END "
                        +
                        "FROM PaymentRecord pr WHERE pr.createdAt >= :startTime AND pr.createdAt <= :endTime")
        Double getPaymentSuccessRate(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 查询用户支付成功率
         * 
         * @param userId 用户ID
         * @return 用户支付成功率（百分比）
         */
        @Query("SELECT CASE WHEN COUNT(pr) = 0 THEN 0 ELSE " +
                        "(CAST(SUM(CASE WHEN pr.status = 'SUCCESS' THEN 1 ELSE 0 END) AS DOUBLE) / COUNT(pr)) * 100 END "
                        +
                        "FROM PaymentRecord pr WHERE pr.userId = :userId")
        Double getUserPaymentSuccessRate(@Param("userId") Long userId);

        /**
         * 查询用户总支付金额
         * 
         * @param userId 用户ID
         * @return 总支付金额
         */
        @Query("SELECT COALESCE(SUM(pr.amount), 0) FROM PaymentRecord pr " +
                        "WHERE pr.userId = :userId")
        BigDecimal getUserTotalPaymentAmount(@Param("userId") Long userId);

        /**
         * 查询用户成功支付金额
         * 
         * @param userId 用户ID
         * @return 成功支付金额
         */
        @Query("SELECT COALESCE(SUM(pr.amount), 0) FROM PaymentRecord pr " +
                        "WHERE pr.userId = :userId AND pr.status = 'SUCCESS'")
        BigDecimal getUserSuccessPaymentAmount(@Param("userId") Long userId);

        /**
         * 查询用户总退款金额
         * 
         * @param userId 用户ID
         * @return 总退款金额
         */
        @Query("SELECT COALESCE(SUM(pr.refundAmount), 0) FROM PaymentRecord pr " +
                        "WHERE pr.userId = :userId AND pr.status = 'REFUNDED'")
        BigDecimal getUserTotalRefundAmount(@Param("userId") Long userId);

        /**
         * 查询用户月度支付统计
         * 
         * @param userId 用户ID
         * @param year   年份
         * @param month  月份
         * @return 包含支付次数、成功金额的统计信息
         */
        @Query("SELECT COUNT(pr), SUM(CASE WHEN pr.status = 'SUCCESS' THEN pr.amount ELSE 0 END) " +
                        "FROM PaymentRecord pr WHERE pr.userId = :userId " +
                        "AND EXTRACT(YEAR FROM pr.createdAt) = :year AND EXTRACT(MONTH FROM pr.createdAt) = :month")
        Object[] getUserMonthlyPaymentStats(@Param("userId") Long userId,
                        @Param("year") int year,
                        @Param("month") int month);

        /**
         * 删除指定时间之前的历史支付记录
         * 
         * @param beforeTime 时间阈值
         * @return 删除的记录数
         */
        @Modifying
        @Query("DELETE FROM PaymentRecord pr WHERE pr.createdAt < :beforeTime")
        int deletePaymentRecordsBeforeTime(@Param("beforeTime") LocalDateTime beforeTime);

        /**
         * 查询用户最大单笔支付记录
         * 
         * @param userId   用户ID
         * @param pageable 分页参数（用于限制结果数量）
         * @return 最大单笔支付记录列表
         */
        @Query("SELECT pr FROM PaymentRecord pr WHERE pr.userId = :userId AND pr.status = 'SUCCESS' " +
                        "ORDER BY pr.amount DESC")
        List<PaymentRecord> findUserMaxPaymentRecord(@Param("userId") Long userId, Pageable pageable);

        /**
         * 查询支付金额排行榜
         * 
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @param pageable  分页参数
         * @return 用户支付排行榜
         */
        @Query("SELECT pr.userId, SUM(pr.amount) as totalPayment FROM PaymentRecord pr " +
                        "WHERE pr.status = 'SUCCESS' AND pr.createdAt >= :startTime AND pr.createdAt <= :endTime "
                        +
                        "GROUP BY pr.userId ORDER BY totalPayment DESC")
        Page<Object[]> getPaymentRanking(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime,
                        Pageable pageable);
}