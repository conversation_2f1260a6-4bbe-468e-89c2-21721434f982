package com.dipspro.modules.billing.entity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import org.hibernate.annotations.Comment;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户余额实体
 * 对应数据库表：b_user_balances
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "b_user_balances")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Comment("用户余额表")
public class UserBalance {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("主键ID")
    private Long id;

    /**
     * 用户ID，关联users.id
     */
    @Column(name = "user_id", nullable = false, unique = true)
    @NotNull(message = "用户ID不能为空")
    @Comment("用户ID，关联users.id")
    private Long userId;

    /**
     * 当前套餐ID，关联b_billing_packages.id
     */
    @Column(name = "package_id")
    @Comment("当前套餐ID，关联b_billing_packages.id")
    private Long packageId;

    /**
     * 充值余额（人民币）
     */
    @Column(name = "recharged_balance", precision = 15, scale = 2)
    @PositiveOrZero(message = "充值余额不能为负数")
    @Comment("充值余额（人民币）")
    private BigDecimal rechargedBalance = BigDecimal.ZERO;

    /**
     * 充值余额（人民币）- 别名字段，用于兼容Service层调用
     */
    public BigDecimal getRechargeBalance() {
        return this.rechargedBalance;
    }

    /**
     * 设置充值余额（人民币）- 别名字段，用于兼容Service层调用
     */
    public void setRechargeBalance(BigDecimal rechargeBalance) {
        this.rechargedBalance = rechargeBalance;
    }

    /**
     * 赠送余额（人民币）
     */
    @Column(name = "gift_balance", precision = 15, scale = 2)
    @PositiveOrZero(message = "赠送余额不能为负数")
    @Comment("赠送余额（人民币）")
    private BigDecimal giftBalance = BigDecimal.ZERO;

    /**
     * 免费Token余额
     */
    @Column(name = "free_tokens")
    @PositiveOrZero(message = "免费Token余额不能为负数")
    @Comment("免费Token余额")
    private Long freeTokens = 0L;

    /**
     * 今日已使用Token数
     */
    @Column(name = "used_tokens_today")
    @PositiveOrZero(message = "今日已使用Token数不能为负数")
    @Comment("今日已使用Token数")
    private Long usedTokensToday = 0L;

    /**
     * 今日Token使用量 - 别名字段，用于兼容Service层调用
     */
    public Long getTodayTokenUsage() {
        return this.usedTokensToday;
    }

    /**
     * 设置今日Token使用量 - 别名字段，用于兼容Service层调用
     */
    public void setTodayTokenUsage(Long todayTokenUsage) {
        this.usedTokensToday = todayTokenUsage;
    }

    /**
     * 上次重置日期
     */
    @Column(name = "last_reset_date")
    @Comment("上次重置日期")
    private LocalDate lastResetDate;

    /**
     * 累计充值金额
     */
    @Column(name = "total_recharged", precision = 15, scale = 2)
    @PositiveOrZero(message = "累计充值金额不能为负数")
    @Comment("累计充值金额")
    private BigDecimal totalRecharged = BigDecimal.ZERO;

    /**
     * 累计消费金额
     */
    @Column(name = "total_consumed", precision = 15, scale = 2)
    @PositiveOrZero(message = "累计消费金额不能为负数")
    @Comment("累计消费金额")
    private BigDecimal totalConsumed = BigDecimal.ZERO;

    /**
     * 现金余额（等同于充值余额，兼容性字段）
     */
    public BigDecimal getCashBalance() {
        return this.rechargedBalance;
    }

    /**
     * 冻结余额
     */
    @Column(name = "frozen_balance", precision = 15, scale = 2)
    @PositiveOrZero(message = "冻结余额不能为负数")
    @Comment("冻结余额")
    private BigDecimal frozenBalance = BigDecimal.ZERO;

    /**
     * 最后交易时间
     */
    @Column(name = "last_transaction_at")
    @Comment("最后交易时间")
    private LocalDateTime lastTransactionAt;

    /**
     * 账户是否被冻结
     */
    @Column(name = "is_frozen")
    @Comment("账户是否被冻结")
    private Boolean isFrozen = false;

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false, updatable = false)
    @Comment("创建时间")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    @Comment("更新时间")
    private LocalDateTime updatedAt;

    /**
     * 创建时自动设置创建时间和更新时间
     */
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createdAt = now;
        this.updatedAt = now;
        if (this.lastResetDate == null) {
            this.lastResetDate = LocalDate.now();
        }
    }

    /**
     * 更新时自动设置更新时间
     */
    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 获取总余额（充值余额+赠送余额）
     * 
     * @return 总余额
     */
    public BigDecimal getTotalBalance() {
        return rechargedBalance.add(giftBalance);
    }

    /**
     * 检查是否需要重置每日Token使用量
     * 
     * @return 是否需要重置
     */
    public boolean needsDailyReset() {
        return lastResetDate == null || !lastResetDate.equals(LocalDate.now());
    }

    /**
     * 重置每日Token使用量
     */
    public void resetDailyUsage() {
        this.usedTokensToday = 0L;
        this.lastResetDate = LocalDate.now();
    }
}