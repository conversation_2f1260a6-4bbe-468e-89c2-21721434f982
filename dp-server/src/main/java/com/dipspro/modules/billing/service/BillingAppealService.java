package com.dipspro.modules.billing.service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.dipspro.modules.billing.entity.BillingAppeal;

/**
 * 计费申诉服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface BillingAppealService {

        /**
         * 创建申诉记录
         * 
         * @param userId        用户ID
         * @param usageRecordId 使用记录ID
         * @param appealReason  申诉原因
         * @param appealAmount  申诉金额
         * @param description   申诉描述
         * @return 创建的申诉记录
         */
        BillingAppeal createAppeal(Long userId, Long usageRecordId, String appealReason,
                        BigDecimal appealAmount, String description);

        /**
         * 根据ID查询申诉记录
         * 
         * @param id 申诉ID
         * @return 申诉记录
         */
        Optional<BillingAppeal> getAppealById(Long id);

        /**
         * 根据使用记录ID查询申诉记录
         * 
         * @param usageRecordId 使用记录ID
         * @return 申诉记录
         */
        Optional<BillingAppeal> getAppealByUsageRecordId(Long usageRecordId);

        /**
         * 检查使用记录是否已有申诉
         * 
         * @param usageRecordId 使用记录ID
         * @return 是否已有申诉
         */
        boolean hasAppealForUsageRecord(Long usageRecordId);

        /**
         * 查询用户申诉记录
         * 
         * @param userId   用户ID
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<BillingAppeal> getUserAppeals(Long userId, Pageable pageable);

        /**
         * 根据申诉状态查询申诉记录
         * 
         * @param status   申诉状态
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<BillingAppeal> getAppealsByStatus(String status, Pageable pageable);

        /**
         * 根据用户ID和申诉状态查询申诉记录
         * 
         * @param userId   用户ID
         * @param status   申诉状态
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<BillingAppeal> getUserAppealsByStatus(Long userId, String status, Pageable pageable);

        /**
         * 根据处理人员ID查询申诉记录
         * 
         * @param handlerId 处理人员ID
         * @param pageable  分页参数
         * @return 分页结果
         */
        Page<BillingAppeal> getAppealsByHandler(Long handlerId, Pageable pageable);

        /**
         * 查询待处理的申诉记录
         * 
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<BillingAppeal> getPendingAppeals(Pageable pageable);

        /**
         * 查询用户指定时间范围内的申诉记录
         * 
         * @param userId    用户ID
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @param pageable  分页参数
         * @return 分页结果
         */
        Page<BillingAppeal> getUserAppealsByDateRange(Long userId, LocalDateTime startTime,
                        LocalDateTime endTime, Pageable pageable);

        /**
         * 处理申诉（通过）
         * 
         * @param appealId     申诉ID
         * @param handlerId    处理人员ID
         * @param handleResult 处理结果
         * @return 是否成功
         */
        boolean approveAppeal(Long appealId, Long handlerId, String handleResult);

        /**
         * 处理申诉（拒绝）
         * 
         * @param appealId     申诉ID
         * @param handlerId    处理人员ID
         * @param handleResult 处理结果
         * @return 是否成功
         */
        boolean rejectAppeal(Long appealId, Long handlerId, String handleResult);

        /**
         * 分配申诉给处理人员
         * 
         * @param appealId  申诉ID
         * @param handlerId 处理人员ID
         * @return 是否成功
         */
        boolean assignAppealToHandler(Long appealId, Long handlerId);

        /**
         * 批量分配申诉给处理人员
         * 
         * @param appealIds 申诉ID列表
         * @param handlerId 处理人员ID
         * @return 分配成功的数量
         */
        int batchAssignAppealsToHandler(List<Long> appealIds, Long handlerId);

        /**
         * 查询用户申诉统计信息
         * 
         * @param userId 用户ID
         * @return 包含总申诉数、待处理数、已通过数、已拒绝数的统计信息
         */
        Object[] getUserAppealStats(Long userId);

        /**
         * 查询系统申诉统计信息
         * 
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 按状态分组的统计信息
         */
        List<Object[]> getSystemAppealStats(LocalDateTime startTime, LocalDateTime endTime);

        /**
         * 查询待处理申诉的数量
         * 
         * @return 待处理申诉数量
         */
        long getPendingAppealCount();

        /**
         * 查询用户当日申诉次数
         * 
         * @param userId 用户ID
         * @return 当日申诉次数
         */
        long getUserTodayAppealCount(Long userId);

        /**
         * 查询高金额申诉记录
         * 
         * @param amountThreshold 金额阈值
         * @param pageable        分页参数
         * @return 分页结果
         */
        Page<BillingAppeal> getHighAmountAppeals(BigDecimal amountThreshold, Pageable pageable);

        /**
         * 查询长时间未处理的申诉记录
         * 
         * @param hours    小时数阈值
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<BillingAppeal> getLongPendingAppeals(int hours, Pageable pageable);

        /**
         * 查询处理人员的申诉处理统计
         * 
         * @param handlerId 处理人员ID
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 包含处理总数、通过数、拒绝数的统计信息
         */
        Object[] getHandlerAppealStats(Long handlerId, LocalDateTime startTime, LocalDateTime endTime);

        /**
         * 根据申诉原因搜索申诉记录
         * 
         * @param keyword  关键词
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<BillingAppeal> searchAppealsByReason(String keyword, Pageable pageable);

        /**
         * 查询用户最近的申诉记录
         * 
         * @param userId 用户ID
         * @param limit  限制数量
         * @return 最近的申诉记录列表
         */
        List<BillingAppeal> getUserRecentAppeals(Long userId, int limit);

        /**
         * 查询活跃申诉用户列表
         * 
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 活跃用户ID列表
         */
        List<Long> getActiveAppealUsers(LocalDateTime startTime, LocalDateTime endTime);

        /**
         * 查询申诉处理效率统计
         * 
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 平均处理时间（小时）
         */
        Double getAverageHandleTimeInHours(LocalDateTime startTime, LocalDateTime endTime);

        /**
         * 查询用户申诉成功率
         * 
         * @param userId 用户ID
         * @return 申诉成功率（百分比）
         */
        Double getUserAppealSuccessRate(Long userId);

        /**
         * 撤销申诉
         * 
         * @param appealId 申诉ID
         * @param userId   用户ID
         * @param reason   撤销原因
         * @return 是否成功
         */
        boolean withdrawAppeal(Long appealId, Long userId, String reason);

        /**
         * 检查用户是否可以创建申诉
         * 
         * @param userId        用户ID
         * @param usageRecordId 使用记录ID
         * @return 检查结果消息，null表示可以创建
         */
        String checkCanCreateAppeal(Long userId, Long usageRecordId);

        /**
         * 验证申诉参数的有效性
         * 
         * @param userId        用户ID
         * @param usageRecordId 使用记录ID
         * @param appealReason  申诉原因
         * @param appealAmount  申诉金额
         * @return 验证结果消息，null表示验证通过
         */
        String validateAppealParameters(Long userId, Long usageRecordId, String appealReason, BigDecimal appealAmount);

        /**
         * 获取申诉状态列表
         * 
         * @return 支持的申诉状态列表
         */
        List<String> getSupportedAppealStatuses();

        /**
         * 获取申诉原因列表
         * 
         * @return 常见的申诉原因列表
         */
        List<String> getCommonAppealReasons();

        /**
         * 批量删除历史申诉记录
         * 
         * @param beforeTime 时间阈值
         * @return 删除的记录数
         */
        int deleteHistoryAppeals(LocalDateTime beforeTime);

        /**
         * 申诉自动处理（基于规则）
         * 
         * @param appealId 申诉ID
         * @return 是否自动处理成功
         */
        boolean autoProcessAppeal(Long appealId);

        /**
         * 获取申诉处理建议
         * 
         * @param appealId 申诉ID
         * @return 处理建议
         */
        String getAppealProcessingSuggestion(Long appealId);

        /**
         * 申诉升级处理
         * 
         * @param appealId         申诉ID
         * @param escalationReason 升级原因
         * @return 是否成功
         */
        boolean escalateAppeal(Long appealId, String escalationReason);

        // ==================== Controller需要的额外方法 ====================

        /**
         * 检查使用记录是否已有申诉
         * 
         * @param usageRecordId 使用记录ID
         * @return 是否已有申诉
         */
        boolean hasExistingAppeal(Long usageRecordId);

        /**
         * 获取所有申诉记录（管理端分页查询）
         * 
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<BillingAppeal> getAppeals(Pageable pageable);

        /**
         * 处理申诉（统一处理接口）
         * 
         * @param appealId 申诉ID
         * @param result   处理结果（APPROVE-通过, REJECT-拒绝）
         * @param note     处理备注
         * @return 是否成功
         */
        boolean processAppeal(Long appealId, String result, String note);

        /**
         * 根据状态获取申诉数量
         * 
         * @param status 申诉状态
         * @return 申诉数量
         */
        long getAppealCountByStatus(String status);
}