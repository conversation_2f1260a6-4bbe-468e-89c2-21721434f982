package com.dipspro.modules.billing.service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.dipspro.modules.billing.dto.BalanceHistoryDto;
import com.dipspro.modules.billing.entity.UserBalance;

/**
 * 用户余额服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface UserBalanceService {

    /**
     * 初始化用户余额
     * 
     * 为新用户创建初始余额记录，设置默认值和基础配置。
     * 该方法在用户首次使用计费功能时自动调用，确保每个用户都有对应的余额账户。
     * 
     * 初始化流程：
     * 1. 用户验证：检查用户是否存在且状态正常
     * 2. 重复检查：确保不重复创建用户余额记录
     * 3. 套餐验证：确认指定的套餐存在且可用
     * 4. 创建余额记录：设置初始余额为0，状态为正常
     * 5. 设置套餐关联：绑定用户的计费套餐
     * 6. 初始化免费额度：根据套餐配置设置免费Token
     * 7. 设置时间字段：记录创建和更新时间
     * 8. 保存记录：将余额记录保存到数据库
     * 
     * 初始设置：
     * - 充值余额：0.00元
     * - 赠送余额：0.00元
     * - 免费Token：根据套餐配置
     * - 今日使用：0个Token
     * - 账户状态：正常（未冻结）
     * - 上次重置日期：当前日期
     * 
     * 业务规则：
     * - 每个用户只能有一个余额账户
     * - 新用户默认使用指定的计费套餐
     * - 免费Token数量根据套餐配置自动设置
     * - 初始化后的账户状态为正常可用
     * 
     * @param userId    用户ID，必须是有效且未初始化余额的用户
     * @param packageId 计费套餐ID，必须是有效且激活的套餐
     * @return 创建的用户余额记录，包含初始配置信息
     * @throws IllegalArgumentException 当用户不存在或套餐无效时抛出
     * @throws IllegalStateException    当用户已有余额记录时抛出
     */
    UserBalance initializeUserBalance(Long userId, Long packageId);

    /**
     * 根据用户ID查询余额
     * 
     * @param userId 用户ID
     * @return 用户余额信息
     */
    Optional<UserBalance> getUserBalance(Long userId);

    /**
     * 获取或创建用户余额
     * 
     * @param userId 用户ID
     * @return 用户余额信息
     */
    UserBalance getOrCreateUserBalance(Long userId);

    /**
     * 用户充值
     * 
     * 为用户账户增加充值余额，通常在支付成功后调用。
     * 该方法会同时更新用户余额和创建对应的交易记录。
     * 
     * 充值流程：
     * 1. 参数验证：检查用户ID、充值金额等参数的有效性
     * 2. 用户余额获取：获取或创建用户余额记录
     * 3. 账户状态检查：确认账户未被冻结且状态正常
     * 4. 余额计算：将充值金额加入用户的充值余额
     * 5. 累计统计更新：更新用户的累计充值金额
     * 6. 保存余额变更：将更新后的余额保存到数据库
     * 7. 创建交易记录：生成RECHARGE类型的交易记录
     * 8. 记录操作日志：记录充值操作的详细信息
     * 
     * 余额类型说明：
     * - 充值余额：用户通过支付获得的余额，优先级较高
     * - 赠送余额：管理员赠送或活动获得的余额，消费时优先使用
     * - 免费Token：新用户或套餐包含的免费使用额度
     * 
     * 业务规则：
     * - 充值金额必须大于0且不超过单次限额
     * - 被冻结的账户无法进行充值操作
     * - 充值成功后会发送通知给用户
     * - 每次充值都会生成唯一的交易记录
     * 
     * 安全控制：
     * - 使用事务确保数据一致性
     * - 记录详细的操作日志便于审计
     * - 支持并发控制防止重复充值
     * 
     * @param userId      用户ID，必须是有效的用户标识
     * @param amount      充值金额，必须大于0，单位为人民币元
     * @param description 充值描述，用于说明充值来源，如"支付宝充值"
     * @return true表示充值成功，false表示充值失败
     * @throws IllegalArgumentException 当参数无效时抛出
     * @throws IllegalStateException    当账户被冻结或状态异常时抛出
     */
    boolean recharge(Long userId, BigDecimal amount, String description);

    /**
     * 赠送余额
     * 
     * @param userId      用户ID
     * @param amount      赠送金额
     * @param description 赠送描述
     * @return 是否成功
     */
    boolean giftBalance(Long userId, BigDecimal amount, String description);

    /**
     * 扣除余额
     * 
     * @param userId      用户ID
     * @param amount      扣除金额
     * @param description 扣除描述
     * @return 是否成功
     */
    boolean deductBalance(Long userId, BigDecimal amount, String description);

    /**
     * 检查余额是否充足
     * 
     * @param userId 用户ID
     * @param amount 需要的金额
     * @return 是否充足
     */
    boolean hasEnoughBalance(Long userId, BigDecimal amount);

    /**
     * 获取用户总余额
     * 
     * @param userId 用户ID
     * @return 总余额（充值余额 + 赠送余额）
     */
    BigDecimal getTotalBalance(Long userId);

    /**
     * 添加免费Token
     * 
     * @param userId      用户ID
     * @param tokens      Token数量
     * @param description 描述
     * @return 是否成功
     */
    boolean addFreeTokens(Long userId, Long tokens, String description);

    /**
     * 扣除免费Token
     * 
     * @param userId 用户ID
     * @param tokens Token数量
     * @return 是否成功
     */
    boolean deductFreeTokens(Long userId, Long tokens);

    /**
     * 检查免费Token是否充足
     * 
     * @param userId 用户ID
     * @param tokens 需要的Token数量
     * @return 是否充足
     */
    boolean hasEnoughFreeTokens(Long userId, Long tokens);

    /**
     * 更新今日Token使用量
     * 
     * @param userId 用户ID
     * @param tokens Token数量
     * @return 是否成功
     */
    boolean updateTodayTokenUsage(Long userId, Long tokens);

    /**
     * 重置用户每日Token使用量
     * 
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean resetDailyTokenUsage(Long userId);

    /**
     * 批量重置所有用户每日Token使用量
     * 
     * @return 重置的用户数量
     */
    int resetAllUsersDailyTokenUsage();

    /**
     * 更新用户套餐
     * 
     * @param userId    用户ID
     * @param packageId 新套餐ID
     * @return 是否成功
     */
    boolean updateUserPackage(Long userId, Long packageId);

    /**
     * 分页查询用户余额列表
     * 
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<UserBalance> getUserBalances(Pageable pageable);

    /**
     * 查询余额不足的用户
     * 
     * @param threshold 余额阈值
     * @return 余额不足的用户列表
     */
    List<UserBalance> getUsersWithLowBalance(BigDecimal threshold);

    /**
     * 查询有免费Token的用户
     * 
     * @return 有免费Token的用户列表
     */
    List<UserBalance> getUsersWithFreeTokens();

    /**
     * 查询用户余额统计信息
     * 
     * @return 包含总用户数、总充值金额、总消费金额的统计信息
     */
    Object[] getBalanceStatistics();

    /**
     * 查询指定时间范围内更新的用户余额
     * 
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param pageable  分页参数
     * @return 分页结果
     */
    Page<UserBalance> getUserBalancesByDateRange(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * 冻结用户余额（基础版本）
     * 
     * @param userId 用户ID
     * @param reason 冻结原因
     * @return 是否成功
     */
    boolean freezeUserBalance(Long userId, String reason);

    /**
     * 冻结用户余额（指定金额）
     * 
     * @param userId 用户ID
     * @param amount 冻结金额
     * @param reason 冻结原因
     * @return 是否成功
     */
    boolean freezeUserBalance(Long userId, BigDecimal amount, String reason);

    /**
     * 解冻用户余额（基础版本）
     * 
     * @param userId 用户ID
     * @param reason 解冻原因
     * @return 是否成功
     */
    boolean unfreezeUserBalance(Long userId, String reason);

    /**
     * 解冻用户余额（指定金额）
     * 
     * @param userId 用户ID
     * @param amount 解冻金额
     * @param reason 解冻原因
     * @return 是否成功
     */
    boolean unfreezeUserBalance(Long userId, BigDecimal amount, String reason);

    /**
     * 检查用户余额是否被冻结
     * 
     * @param userId 用户ID
     * @return 是否被冻结
     */
    boolean isUserBalanceFrozen(Long userId);

    /**
     * 计算用户可用余额（总余额 - 冻结金额）
     * 
     * @param userId 用户ID
     * @return 可用余额
     */
    BigDecimal getAvailableBalance(Long userId);

    /**
     * 余额转账（从一个用户转账给另一个用户）
     * 
     * @param fromUserId  转出用户ID
     * @param toUserId    转入用户ID
     * @param amount      转账金额
     * @param description 转账描述
     * @return 是否成功
     */
    boolean transferBalance(Long fromUserId, Long toUserId, BigDecimal amount, String description);

    /**
     * 获取用户余额变动历史（基础版本）
     * 
     * @param userId   用户ID
     * @param pageable 分页参数
     * @return 余额变动记录
     */
    Page<BalanceHistoryDto> getUserBalanceHistory(Long userId, Pageable pageable);

    /**
     * 获取用户余额变动历史（带类型筛选）
     * 
     * @param userId   用户ID
     * @param type     变动类型筛选
     * @param pageable 分页参数
     * @return 余额变动记录
     */
    Page<BalanceHistoryDto> getUserBalanceHistory(Long userId, String type, Pageable pageable);

    /**
     * 验证余额操作的有效性
     * 
     * @param userId        用户ID
     * @param amount        操作金额
     * @param operationType 操作类型
     * @return 验证结果消息，null表示验证通过
     */
    String validateBalanceOperation(Long userId, BigDecimal amount, String operationType);

    /**
     * 获取用户余额预警信息
     * 
     * @param userId 用户ID
     * @return 预警信息，null表示无预警
     */
    String getBalanceWarning(Long userId);

    // ==================== Controller需要的额外方法 ====================

    /**
     * 获取用户余额统计信息
     * 
     * @param userId 用户ID
     * @param period 统计周期
     * @return 统计信息
     */
    Object getUserBalanceStatistics(Long userId, String period);

    /**
     * 检查余额是否充足（指定类型）
     * 
     * @param userId      用户ID
     * @param amount      所需金额
     * @param balanceType 余额类型
     * @return 是否充足
     */
    boolean checkBalanceSufficient(Long userId, BigDecimal amount, String balanceType);

    /**
     * 管理端获取用户余额列表
     * 
     * @param userId     用户ID筛选
     * @param minBalance 最小余额筛选
     * @param maxBalance 最大余额筛选
     * @param orderBy    排序字段
     * @param pageable   分页参数
     * @return 分页结果
     */
    Page<UserBalance> getUserBalancesForAdmin(Long userId, BigDecimal minBalance,
            BigDecimal maxBalance, String orderBy, Pageable pageable);

    /**
     * 调整用户余额（管理端）
     * 
     * @param userId     用户ID
     * @param adjustType 调整类型
     * @param amount     调整金额
     * @param reason     调整原因
     * @return 调整后的余额记录
     */
    UserBalance adjustUserBalance(Long userId, String adjustType, BigDecimal amount, String reason);

    /**
     * 获取余额预警列表
     * 
     * @param alertType 预警类型
     * @param limit     返回数量限制
     * @return 预警列表
     */
    List<UserBalance> getBalanceAlerts(String alertType, Integer limit);

    /**
     * 批量调整用户余额
     * 
     * @param userIds   用户ID列表
     * @param adjustDto 调整参数
     * @return 批量操作结果
     */
    com.dipspro.modules.billing.dto.BatchAdjustResult batchAdjustBalance(java.util.List<Long> userIds,
            com.dipspro.modules.billing.dto.BalanceAdjustDto adjustDto);
}