package com.dipspro.modules.billing.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dipspro.modules.billing.entity.PaymentRecord;
import com.dipspro.modules.billing.repository.PaymentRecordRepository;
import com.dipspro.modules.billing.service.PaymentRecordService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 支付记录服务实现类
 * 
 * 根据DIPS Pro计费系统设计文档实现
 * 专注于核心支付记录功能，包括创建、查询和基本统计
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class PaymentRecordServiceImpl implements PaymentRecordService {

    private final PaymentRecordRepository paymentRecordRepository;

    // ==================== 核心支付创建方法 ====================

    /**
     * 创建支付记录
     * 
     * 这是核心的支付创建方法，用于记录用户的支付请求
     * 
     * @param userId            用户ID，不能为空
     * @param amount            支付金额，不能为空且必须大于0
     * @param paymentMethod     支付方式，支持ALIPAY/WECHAT/BANK
     * @param description       支付描述，可以为空
     * @param externalPaymentId 外部支付ID，可以为空
     * @return 创建的支付记录
     * @throws IllegalArgumentException 当参数验证失败时抛出
     */
    @Override
    public PaymentRecord createPaymentRecord(Long userId, BigDecimal amount, String paymentMethod,
            String description, String externalPaymentId) {
        log.info("创建支付记录: UserId={}, Amount={}, PaymentMethod={}, Description={}",
                userId, amount, paymentMethod, description);

        // 第一步：验证必要参数的有效性
        String validationError = validatePaymentParameters(userId, amount, paymentMethod);
        if (validationError != null) {
            throw new IllegalArgumentException(validationError);
        }

        // 第二步：创建支付记录对象并设置所有必要字段
        PaymentRecord paymentRecord = new PaymentRecord();
        paymentRecord.setUserId(userId);
        paymentRecord.setAmount(amount);
        paymentRecord.setPaymentMethod(paymentMethod);
        paymentRecord.setStatus("PENDING"); // 默认状态为待支付
        paymentRecord.setPaymentNo("PAY" + System.currentTimeMillis()
                + String.valueOf(userId).substring(0, Math.min(4, String.valueOf(userId).length())));

        // 第三步：保存支付记录到数据库
        PaymentRecord savedRecord = paymentRecordRepository.save(paymentRecord);

        log.info("支付记录创建成功: PaymentId={}, PaymentNo={}",
                savedRecord.getId(), savedRecord.getPaymentNo());
        return savedRecord;
    }

    @Override
    public boolean markPaymentSuccess(Long id, String transactionId, LocalDateTime paymentTime,
            String gatewayResponse) {
        log.info("标记支付成功: PaymentId={}, TransactionId={}", id, transactionId);

        Optional<PaymentRecord> paymentOpt = paymentRecordRepository.findById(id);
        if (paymentOpt.isEmpty()) {
            log.warn("支付记录不存在: PaymentId={}", id);
            return false;
        }

        PaymentRecord paymentRecord = paymentOpt.get();
        paymentRecord.markAsSuccess(transactionId, gatewayResponse);
        paymentRecord.setPaidAt(paymentTime);

        paymentRecordRepository.save(paymentRecord);

        log.info("支付成功标记完成: PaymentId={}, PaymentNo={}", id, paymentRecord.getPaymentNo());
        return true;
    }

    /**
     * 标记支付失败
     * 
     * 当支付网关返回失败状态时，更新支付记录状态为失败：
     * 1. 记录查找：根据支付记录ID查找对应的支付记录
     * 2. 状态验证：确保支付记录存在且可以标记为失败
     * 3. 信息更新：更新支付状态和失败原因
     * 4. 数据持久化：保存更新后的支付记录
     * 
     * 更新的字段包括：
     * - 支付状态：PENDING -> FAILED
     * - 失败原因：记录具体的失败原因，便于问题追溯
     * 
     * 失败后的处理：
     * - 支付记录状态变为FAILED，不可再次支付
     * - 需要重新创建支付记录才能再次支付
     * - 失败原因将用于用户提示和问题排查
     * 
     * @param id            支付记录ID，不能为空
     * @param failureReason 失败原因，不能为空
     * @return true表示更新成功，false表示支付记录不存在
     */
    @Override
    public boolean markPaymentFailed(Long id, String failureReason) {
        log.info("标记支付失败: PaymentId={}, Reason={}", id, failureReason);

        Optional<PaymentRecord> paymentOpt = paymentRecordRepository.findById(id);
        if (paymentOpt.isEmpty()) {
            log.warn("支付记录不存在: PaymentId={}", id);
            return false;
        }

        PaymentRecord paymentRecord = paymentOpt.get();
        paymentRecord.setStatus("FAILED");
        paymentRecord.setFailureReason(failureReason);

        paymentRecordRepository.save(paymentRecord);

        log.info("支付失败标记完成: PaymentId={}, PaymentNo={}", id, paymentRecord.getPaymentNo());
        return true;
    }

    @Override
    public boolean markPaymentCancelled(Long id, String cancelReason) {
        log.info("标记支付取消: PaymentId={}, Reason={}", id, cancelReason);

        Optional<PaymentRecord> paymentOpt = paymentRecordRepository.findById(id);
        if (paymentOpt.isEmpty()) {
            log.warn("支付记录不存在: PaymentId={}", id);
            return false;
        }

        PaymentRecord paymentRecord = paymentOpt.get();
        paymentRecord.setStatus("CANCELLED");
        paymentRecord.setCancelReason(cancelReason);

        paymentRecordRepository.save(paymentRecord);

        log.info("支付取消标记完成: PaymentId={}, PaymentNo={}", id, paymentRecord.getPaymentNo());
        return true;
    }

    @Override
    public boolean markPaymentRefunded(Long id, BigDecimal refundAmount, String refundReason) {
        log.info("标记支付退款: PaymentId={}, RefundAmount={}, Reason={}", id, refundAmount, refundReason);

        Optional<PaymentRecord> paymentOpt = paymentRecordRepository.findById(id);
        if (paymentOpt.isEmpty()) {
            log.warn("支付记录不存在: PaymentId={}", id);
            return false;
        }

        PaymentRecord paymentRecord = paymentOpt.get();
        paymentRecord.setStatus("REFUNDED");
        paymentRecord.setRefundAmount(refundAmount);
        paymentRecord.setRefundTime(LocalDateTime.now());
        paymentRecord.setRefundReason(refundReason);

        paymentRecordRepository.save(paymentRecord);

        log.info("支付退款标记完成: PaymentId={}, PaymentNo={}", id, paymentRecord.getPaymentNo());
        return true;
    }

    // ==================== 基本查询方法 ====================

    /**
     * 根据ID获取支付记录
     * 
     * 通过支付记录ID查找对应的支付详情，用于支付查询和验证
     * 
     * @param id 支付记录ID，不能为空
     * @return 支付记录的Optional包装，如果不存在则为空
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<PaymentRecord> getPaymentRecordById(Long id) {
        return paymentRecordRepository.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<PaymentRecord> getPaymentRecordByOrderNumber(String orderNumber) {
        if (orderNumber == null || orderNumber.trim().isEmpty()) {
            return Optional.empty();
        }
        return paymentRecordRepository.findByPaymentNo(orderNumber);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<PaymentRecord> getPaymentRecordByTransactionId(String transactionId) {
        if (transactionId == null || transactionId.trim().isEmpty()) {
            return Optional.empty();
        }
        return paymentRecordRepository.findByPlatformTransactionNo(transactionId);
    }

    /**
     * 获取用户支付记录（分页）
     * 
     * 分页查询指定用户的所有支付记录，按创建时间倒序排列，
     * 用于用户查看自己的支付历史
     * 
     * @param userId   用户ID，不能为空
     * @param pageable 分页参数
     * @return 用户支付记录的分页结果
     */
    @Override
    @Transactional(readOnly = true)
    public Page<PaymentRecord> getUserPaymentRecords(Long userId, Pageable pageable) {
        return paymentRecordRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PaymentRecord> getUserPaymentRecordsByStatus(Long userId, String status, Pageable pageable) {
        return paymentRecordRepository.findByUserIdAndStatusOrderByCreatedAtDesc(userId, status, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PaymentRecord> getUserPaymentRecordsByMethod(Long userId, String paymentMethod, Pageable pageable) {
        return paymentRecordRepository.findByUserIdAndPaymentMethodOrderByCreatedAtDesc(userId, paymentMethod,
                pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PaymentRecord> getUserPaymentRecordsByDateRange(Long userId, LocalDateTime startTime,
            LocalDateTime endTime, Pageable pageable) {
        return paymentRecordRepository.findByUserIdAndCreatedAtBetweenOrderByCreatedAtDesc(
                userId, startTime, endTime, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PaymentRecord> getPaymentRecordsByStatus(String status, Pageable pageable) {
        return paymentRecordRepository.findByStatusOrderByCreatedAtDesc(status, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PaymentRecord> getUserRecentPaymentRecords(Long userId, int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return paymentRecordRepository.findRecentPaymentsByUserId(userId, pageable);
    }

    // ==================== 基本统计方法 ====================

    @Override
    @Transactional(readOnly = true)
    public BigDecimal getUserTotalPaymentAmount(Long userId) {
        BigDecimal total = paymentRecordRepository.getUserTotalPaymentAmount(userId);
        return total != null ? total : BigDecimal.ZERO;
    }

    @Override
    @Transactional(readOnly = true)
    public BigDecimal getUserSuccessPaymentAmount(Long userId) {
        BigDecimal total = paymentRecordRepository.getUserSuccessPaymentAmount(userId);
        return total != null ? total : BigDecimal.ZERO;
    }

    @Override
    @Transactional(readOnly = true)
    public BigDecimal getUserTotalRefundAmount(Long userId) {
        BigDecimal total = paymentRecordRepository.getUserTotalRefundAmount(userId);
        return total != null ? total : BigDecimal.ZERO;
    }

    /**
     * 获取用户净支付金额
     * 
     * @param userId 用户ID
     * @return 净支付金额（成功支付 - 退款）
     */
    @Override
    @Transactional(readOnly = true)
    public BigDecimal getUserNetPaymentAmount(Long userId) {
        BigDecimal totalSuccess = getUserSuccessPaymentAmount(userId);
        BigDecimal totalRefund = getUserTotalRefundAmount(userId);
        return totalSuccess.subtract(totalRefund);
    }

    /**
     * 获取用户支付摘要信息
     * 
     * @param userId 用户ID
     * @return 支付摘要信息
     */
    @Override
    @Transactional(readOnly = true)
    public Object getUserPaymentSummary(Long userId) {
        BigDecimal totalPayment = getUserTotalPaymentAmount(userId);
        BigDecimal successPayment = getUserSuccessPaymentAmount(userId);
        BigDecimal totalRefund = getUserTotalRefundAmount(userId);
        BigDecimal netAmount = successPayment.subtract(totalRefund);

        // 创建一个简单的Map来返回摘要信息
        return java.util.Map.of(
                "userId", userId,
                "totalPayment", totalPayment,
                "successPayment", successPayment,
                "totalRefund", totalRefund,
                "netAmount", netAmount);
    }

    // ==================== 工具方法 ====================

    @Override
    @Transactional(readOnly = true)
    public String validatePaymentParameters(Long userId, BigDecimal amount, String paymentMethod) {
        if (userId == null) {
            log.warn("支付参数验证失败: 用户ID不能为空");
            return "用户ID不能为空";
        }

        if (amount == null) {
            log.warn("支付参数验证失败: 支付金额不能为空");
            return "支付金额不能为空";
        }

        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("支付参数验证失败: 支付金额必须大于0, Amount={}", amount);
            return "支付金额必须大于0";
        }

        if (paymentMethod == null || paymentMethod.trim().isEmpty()) {
            log.warn("支付参数验证失败: 支付方式不能为空");
            return "支付方式不能为空";
        }

        // 验证支付方式是否有效
        List<String> validMethods = getSupportedPaymentMethods();
        if (!validMethods.contains(paymentMethod.toUpperCase())) {
            log.warn("支付参数验证失败: 无效的支付方式, PaymentMethod={}", paymentMethod);
            return "无效的支付方式: " + paymentMethod;
        }

        return null; // 验证通过
    }

    @Override
    public List<String> getSupportedPaymentMethods() {
        return List.of("ALIPAY", "WECHAT", "BANK");
    }

    @Override
    public List<String> getSupportedPaymentStatuses() {
        return List.of("PENDING", "SUCCESS", "FAILED", "CANCELLED", "REFUNDED");
    }

    @Override
    public int deleteHistoryPaymentRecords(LocalDateTime beforeTime) {
        log.info("删除历史支付记录: BeforeTime={}", beforeTime);

        int deletedCount = paymentRecordRepository.deletePaymentRecordsBeforeTime(beforeTime);

        log.info("删除历史支付记录完成: 删除数量={}", deletedCount);
        return deletedCount;
    }
}