package com.dipspro.modules.billing.dto;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Token费用计算请求DTO
 * 
 * 用于Token费用计算接口的请求参数，包含输入和输出Token数量。
 * 计算结果将基于用户当前套餐的价格配置。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TokenCostCalculateDto {

    /**
     * 输入Token数量
     * 必须不为空且不能为负数
     */
    @NotNull(message = "输入Token数量不能为空")
    @Min(value = 0, message = "输入Token数量不能为负数")
    private Long inputTokens;

    /**
     * 输出Token数量
     * 必须不为空且不能为负数
     */
    @NotNull(message = "输出Token数量不能为空")
    @Min(value = 0, message = "输出Token数量不能为负数")
    private Long outputTokens;

    /**
     * 思维链Token数量（可选）
     * 用于支持模型的思维链Token计算
     */
    @Min(value = 0, message = "思维链Token数量不能为负数")
    private Long thoughtChainTokens;

    /**
     * 模型名称（可选）
     * 用于不同模型的Token计费
     */
    private String modelName;

    /**
     * 计算总Token数量
     * 
     * @return 总Token数量
     */
    public Long getTotalTokens() {
        long total = (inputTokens != null ? inputTokens : 0L) +
                (outputTokens != null ? outputTokens : 0L);
        if (thoughtChainTokens != null) {
            total += thoughtChainTokens;
        }
        return total;
    }

    /**
     * 检查Token数量是否有效
     * 
     * @return 是否有效
     */
    public boolean isValidTokens() {
        return getTotalTokens() > 0;
    }

    /**
     * 格式化Token数量显示
     * 
     * @return 格式化的Token数量字符串
     */
    public String getFormattedTokens() {
        long total = getTotalTokens();
        if (total < 1000) {
            return total + " tokens";
        } else if (total < 1000000) {
            return String.format("%.1fK tokens", total / 1000.0);
        } else {
            return String.format("%.1fM tokens", total / 1000000.0);
        }
    }
}