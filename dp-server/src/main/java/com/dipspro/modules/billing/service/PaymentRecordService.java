package com.dipspro.modules.billing.service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.dipspro.modules.billing.entity.PaymentRecord;

/**
 * 支付记录服务接口
 * 
 * 根据DIPS Pro计费系统设计文档实现
 * 专注于核心支付记录功能，包括创建、查询和基本统计
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface PaymentRecordService {

        // ==================== 核心支付创建方法 ====================

        /**
         * 创建支付记录
         * 
         * 创建新的支付记录，用于处理用户充值请求。
         * 该方法会生成唯一的支付单号，设置初始状态为PENDING，并记录支付相关信息。
         * 
         * 创建流程：
         * 1. 参数验证：检查用户ID、金额、支付方式等参数的有效性
         * 2. 用户验证：确认用户存在且状态正常
         * 3. 生成支付单号：创建唯一的支付订单编号
         * 4. 设置支付信息：记录支付方式、金额、描述等
         * 5. 初始化状态：设置支付状态为PENDING（待支付）
         * 6. 设置过期时间：根据支付方式设置合理的过期时间
         * 7. 保存记录：将支付记录保存到数据库
         * 
         * 支付方式说明：
         * - ALIPAY: 支付宝支付，适用于移动端和网页端
         * - WECHAT: 微信支付，支持扫码和移动端支付
         * - BANK: 银行卡支付，支持借记卡和信用卡
         * - ADMIN: 管理员手动充值，用于客服处理
         * 
         * 安全考虑：
         * - 支付金额必须大于0且不超过单次最大限额
         * - 外部支付ID用于防重复提交
         * - 记录客户端IP和用户代理信息
         * - 设置合理的支付过期时间
         * 
         * @param userId            用户ID，必须是有效的用户标识
         * @param amount            支付金额，必须大于0，单位为人民币元
         * @param paymentMethod     支付方式（ALIPAY-支付宝, WECHAT-微信, BANK-银行卡, ADMIN-管理员）
         * @param description       支付描述，用于标识充值用途，可为空
         * @param externalPaymentId 外部支付ID，用于防重复提交，可为空
         * @return 创建的支付记录，包含支付单号等信息
         * @throws IllegalArgumentException 当参数验证失败时抛出
         * @throws IllegalStateException    当用户状态异常时抛出
         */
        PaymentRecord createPaymentRecord(Long userId, BigDecimal amount, String paymentMethod,
                        String description, String externalPaymentId);

        /**
         * 支付成功处理
         * 
         * 处理支付网关的成功回调，更新支付记录状态并触发后续业务流程。
         * 这是支付流程中的关键环节，需要确保数据一致性和业务完整性。
         * 
         * 处理流程：
         * 1. 支付记录验证：检查支付记录是否存在且状态正确
         * 2. 重复处理检查：防止重复处理同一笔支付
         * 3. 更新支付状态：将状态从PENDING更新为SUCCESS
         * 4. 记录支付信息：保存第三方交易ID和支付时间
         * 5. 保存回调数据：记录网关返回的完整响应信息
         * 6. 触发充值流程：调用用户余额服务进行充值
         * 7. 创建交易记录：生成充值类型的交易记录
         * 8. 发送成功通知：向用户发送支付成功消息
         * 
         * 业务影响：
         * - 用户余额会增加相应的充值金额
         * - 生成RECHARGE类型的计费交易记录
         * - 用户会收到充值成功的通知
         * - 支付记录状态变为不可撤销的成功状态
         * 
         * 异常处理：
         * - 如果支付记录不存在或状态异常，返回false
         * - 如果充值过程失败，会回滚支付状态
         * - 记录详细的错误日志便于问题排查
         * 
         * @param id              支付记录ID，必须是有效的支付记录标识
         * @param transactionId   第三方支付平台的交易ID，用于对账和查询
         * @param paymentTime     实际支付完成时间，由支付网关提供
         * @param gatewayResponse 支付网关的完整响应信息，用于审计和问题排查
         * @return true表示处理成功，false表示处理失败
         * @throws IllegalArgumentException 当参数无效时抛出
         * @throws IllegalStateException    当支付记录状态异常时抛出
         */
        boolean markPaymentSuccess(Long id, String transactionId, LocalDateTime paymentTime, String gatewayResponse);

        /**
         * 支付失败处理
         * 
         * @param id            支付记录ID
         * @param failureReason 失败原因
         * @return 是否成功
         */
        boolean markPaymentFailed(Long id, String failureReason);

        /**
         * 支付取消处理
         * 
         * @param id           支付记录ID
         * @param cancelReason 取消原因
         * @return 是否成功
         */
        boolean markPaymentCancelled(Long id, String cancelReason);

        /**
         * 支付退款处理
         * 
         * @param id           支付记录ID
         * @param refundAmount 退款金额
         * @param refundReason 退款原因
         * @return 是否成功
         */
        boolean markPaymentRefunded(Long id, BigDecimal refundAmount, String refundReason);

        // ==================== 基本查询方法 ====================

        /**
         * 根据ID查询支付记录
         * 
         * @param id 支付记录ID
         * @return 支付记录
         */
        Optional<PaymentRecord> getPaymentRecordById(Long id);

        /**
         * 根据订单号查询支付记录
         * 
         * @param orderNumber 订单号
         * @return 支付记录
         */
        Optional<PaymentRecord> getPaymentRecordByOrderNumber(String orderNumber);

        /**
         * 根据交易ID查询支付记录
         * 
         * @param transactionId 交易ID
         * @return 支付记录
         */
        Optional<PaymentRecord> getPaymentRecordByTransactionId(String transactionId);

        /**
         * 查询用户支付记录
         * 
         * @param userId   用户ID
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<PaymentRecord> getUserPaymentRecords(Long userId, Pageable pageable);

        /**
         * 根据用户ID和支付状态查询支付记录
         * 
         * @param userId   用户ID
         * @param status   支付状态
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<PaymentRecord> getUserPaymentRecordsByStatus(Long userId, String status, Pageable pageable);

        /**
         * 根据用户ID和支付方式查询支付记录
         * 
         * @param userId        用户ID
         * @param paymentMethod 支付方式
         * @param pageable      分页参数
         * @return 分页结果
         */
        Page<PaymentRecord> getUserPaymentRecordsByMethod(Long userId, String paymentMethod, Pageable pageable);

        /**
         * 根据用户ID和时间范围查询支付记录
         * 
         * @param userId    用户ID
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @param pageable  分页参数
         * @return 分页结果
         */
        Page<PaymentRecord> getUserPaymentRecordsByDateRange(Long userId, LocalDateTime startTime,
                        LocalDateTime endTime, Pageable pageable);

        /**
         * 根据支付状态查询支付记录
         * 
         * @param status   支付状态
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<PaymentRecord> getPaymentRecordsByStatus(String status, Pageable pageable);

        /**
         * 查询用户最近的支付记录
         * 
         * @param userId 用户ID
         * @param limit  限制数量
         * @return 最近的支付记录列表
         */
        List<PaymentRecord> getUserRecentPaymentRecords(Long userId, int limit);

        // ==================== 基本统计方法 ====================

        /**
         * 查询用户总支付金额
         * 
         * @param userId 用户ID
         * @return 总支付金额
         */
        BigDecimal getUserTotalPaymentAmount(Long userId);

        /**
         * 查询用户成功支付金额
         * 
         * @param userId 用户ID
         * @return 成功支付金额
         */
        BigDecimal getUserSuccessPaymentAmount(Long userId);

        /**
         * 查询用户总退款金额
         * 
         * @param userId 用户ID
         * @return 总退款金额
         */
        BigDecimal getUserTotalRefundAmount(Long userId);

        /**
         * 计算用户净支付金额（成功支付 - 退款）
         * 
         * @param userId 用户ID
         * @return 净支付金额
         */
        BigDecimal getUserNetPaymentAmount(Long userId);

        /**
         * 获取用户支付摘要
         * 
         * @param userId 用户ID
         * @return 支付摘要信息
         */
        Object getUserPaymentSummary(Long userId);

        // ==================== 工具方法 ====================

        /**
         * 验证支付参数的有效性
         * 
         * @param userId        用户ID
         * @param amount        支付金额
         * @param paymentMethod 支付方式
         * @return 验证结果消息，null表示验证通过
         */
        String validatePaymentParameters(Long userId, BigDecimal amount, String paymentMethod);

        /**
         * 获取支持的支付方式列表
         * 
         * @return 支持的支付方式列表
         */
        List<String> getSupportedPaymentMethods();

        /**
         * 获取支持的支付状态列表
         * 
         * @return 支持的支付状态列表
         */
        List<String> getSupportedPaymentStatuses();

        /**
         * 批量删除历史支付记录
         * 
         * @param beforeTime 时间阈值
         * @return 删除的记录数
         */
        int deleteHistoryPaymentRecords(LocalDateTime beforeTime);
}