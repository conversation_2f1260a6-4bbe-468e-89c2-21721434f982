package com.dipspro.modules.billing.repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.dipspro.modules.billing.entity.BillingTransaction;

/**
 * 计费交易记录数据访问接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface BillingTransactionRepository extends JpaRepository<BillingTransaction, Long> {

        /**
         * 根据用户ID分页查询交易记录
         * 
         * @param userId   用户ID
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<BillingTransaction> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

        /**
         * 根据用户ID和交易类型查询交易记录
         * 
         * @param userId   用户ID
         * @param type     交易类型
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<BillingTransaction> findByUserIdAndTypeOrderByCreatedAtDesc(
                        Long userId, String type, Pageable pageable);

        /**
         * 根据交易类型查询交易记录
         * 
         * @param type     交易类型
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<BillingTransaction> findByTypeOrderByCreatedAtDesc(String type, Pageable pageable);

        /**
         * 根据外部交易ID查询交易记录
         * 
         * @param externalTransactionId 外部交易ID
         * @return 交易记录
         */
        Optional<BillingTransaction> findByExternalTransactionId(String externalTransactionId);

        /**
         * 检查外部交易ID是否存在
         * 
         * @param externalTransactionId 外部交易ID
         * @return 是否存在
         */
        boolean existsByExternalTransactionId(String externalTransactionId);

        /**
         * 根据用户ID和时间范围查询交易记录
         * 
         * @param userId    用户ID
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @param pageable  分页参数
         * @return 分页结果
         */
        Page<BillingTransaction> findByUserIdAndCreatedAtBetweenOrderByCreatedAtDesc(
                        Long userId, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

        /**
         * 查询用户指定时间范围内的交易统计
         * 
         * @param userId    用户ID
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 包含交易次数、总充值金额、总消费金额的统计信息
         */
        @Query("SELECT COUNT(bt), " +
                        "SUM(CASE WHEN bt.type = 'RECHARGE' THEN bt.amount ELSE 0 END), " +
                        "SUM(CASE WHEN bt.type = 'DEDUCTION' THEN bt.amount ELSE 0 END) " +
                        "FROM BillingTransaction bt WHERE bt.userId = :userId " +
                        "AND bt.createdAt >= :startTime AND bt.createdAt <= :endTime")
        Object[] getUserTransactionStats(@Param("userId") Long userId,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 查询用户月度交易统计
         * 
         * @param userId 用户ID
         * @param year   年份
         * @param month  月份
         * @return 包含交易次数、充值金额、消费金额的统计信息
         */
        @Query("SELECT COUNT(bt), " +
                        "SUM(CASE WHEN bt.type = 'RECHARGE' THEN bt.amount ELSE 0 END), " +
                        "SUM(CASE WHEN bt.type = 'DEDUCTION' THEN bt.amount ELSE 0 END) " +
                        "FROM BillingTransaction bt WHERE bt.userId = :userId " +
                        "AND EXTRACT(YEAR FROM bt.createdAt) = :year AND EXTRACT(MONTH FROM bt.createdAt) = :month")
        Object[] getUserMonthlyTransactionStats(@Param("userId") Long userId,
                        @Param("year") int year,
                        @Param("month") int month);

        /**
         * 查询指定时间范围内的系统交易统计
         * 
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 按交易类型分组的统计信息
         */
        @Query("SELECT bt.type, COUNT(bt), SUM(bt.amount) " +
                        "FROM BillingTransaction bt WHERE bt.createdAt >= :startTime AND bt.createdAt <= :endTime " +
                        "GROUP BY bt.type ORDER BY SUM(bt.amount) DESC")
        List<Object[]> getSystemTransactionStats(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 查询用户最近的交易记录
         * 
         * @param userId   用户ID
         * @param pageable 分页参数
         * @return 最近的交易记录列表
         */
        @Query("SELECT bt FROM BillingTransaction bt WHERE bt.userId = :userId " +
                        "ORDER BY bt.createdAt DESC")
        List<BillingTransaction> findRecentTransactionsByUserId(@Param("userId") Long userId, Pageable pageable);

        /**
         * 查询大额交易记录（金额超过指定阈值）
         * 
         * @param amountThreshold 金额阈值
         * @param pageable        分页参数
         * @return 分页结果
         */
        @Query("SELECT bt FROM BillingTransaction bt WHERE bt.amount >= :amountThreshold " +
                        "ORDER BY bt.amount DESC, bt.createdAt DESC")
        Page<BillingTransaction> findLargeAmountTransactions(@Param("amountThreshold") BigDecimal amountThreshold,
                        Pageable pageable);

        /**
         * 查询用户总充值金额
         * 
         * @param userId 用户ID
         * @return 总充值金额
         */
        @Query("SELECT COALESCE(SUM(bt.amount), 0) FROM BillingTransaction bt " +
                        "WHERE bt.userId = :userId AND bt.type = 'RECHARGE'")
        BigDecimal getUserTotalRechargeAmount(@Param("userId") Long userId);

        /**
         * 查询用户总消费金额
         * 
         * @param userId 用户ID
         * @return 总消费金额
         */
        @Query("SELECT COALESCE(SUM(bt.amount), 0) FROM BillingTransaction bt " +
                        "WHERE bt.userId = :userId AND bt.type = 'DEDUCTION'")
        BigDecimal getUserTotalConsumptionAmount(@Param("userId") Long userId);

        /**
         * 查询用户今日交易统计
         * 
         * @param userId     用户ID
         * @param startOfDay 今日开始时间
         * @return 包含今日充值金额、今日消费金额的统计信息
         */
        @Query("SELECT " +
                        "SUM(CASE WHEN bt.type = 'RECHARGE' THEN bt.amount ELSE 0 END), " +
                        "SUM(CASE WHEN bt.type = 'DEDUCTION' THEN bt.amount ELSE 0 END) " +
                        "FROM BillingTransaction bt WHERE bt.userId = :userId AND bt.createdAt >= :startOfDay")
        Object[] getUserTodayTransactionStats(@Param("userId") Long userId,
                        @Param("startOfDay") LocalDateTime startOfDay);

        /**
         * 查询活跃交易用户列表（指定时间范围内有交易记录的用户）
         * 
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 活跃用户ID列表
         */
        @Query("SELECT DISTINCT bt.userId FROM BillingTransaction bt " +
                        "WHERE bt.createdAt >= :startTime AND bt.createdAt <= :endTime")
        List<Long> getActiveTransactionUsers(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 查询充值排行榜
         * 
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @param pageable  分页参数
         * @return 用户充值排行榜
         */
        @Query("SELECT bt.userId, SUM(bt.amount) as totalRecharge FROM BillingTransaction bt " +
                        "WHERE bt.type = 'RECHARGE' AND bt.createdAt >= :startTime AND bt.createdAt <= :endTime "
                        +
                        "GROUP BY bt.userId ORDER BY totalRecharge DESC")
        Page<Object[]> getRechargeRanking(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime,
                        Pageable pageable);

        /**
         * 查询消费排行榜
         * 
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @param pageable  分页参数
         * @return 用户消费排行榜
         */
        @Query("SELECT bt.userId, SUM(bt.amount) as totalConsumption FROM BillingTransaction bt " +
                        "WHERE bt.type = 'DEDUCTION' AND bt.createdAt >= :startTime AND bt.createdAt <= :endTime "
                        +
                        "GROUP BY bt.userId ORDER BY totalConsumption DESC")
        Page<Object[]> getConsumptionRanking(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime,
                        Pageable pageable);

        /**
         * 根据描述关键词搜索交易记录
         * 
         * @param keyword  关键词
         * @param pageable 分页参数
         * @return 分页结果
         */
        @Query("SELECT bt FROM BillingTransaction bt WHERE bt.description LIKE %:keyword% " +
                        "ORDER BY bt.createdAt DESC")
        Page<BillingTransaction> searchByDescription(@Param("keyword") String keyword, Pageable pageable);

        /**
         * 查询异常交易记录（金额异常大或小）
         * 
         * @param minAmount 最小异常金额
         * @param maxAmount 最大异常金额
         * @param pageable  分页参数
         * @return 分页结果
         */
        @Query("SELECT bt FROM BillingTransaction bt WHERE bt.amount < :minAmount OR bt.amount > :maxAmount " +
                        "ORDER BY bt.createdAt DESC")
        Page<BillingTransaction> findAbnormalAmountTransactions(@Param("minAmount") BigDecimal minAmount,
                        @Param("maxAmount") BigDecimal maxAmount,
                        Pageable pageable);

        /**
         * 删除指定时间之前的历史交易记录
         * 
         * @param beforeTime 时间阈值
         * @return 删除的记录数
         */
        @Query("DELETE FROM BillingTransaction bt WHERE bt.createdAt < :beforeTime")
        int deleteTransactionsBeforeTime(@Param("beforeTime") LocalDateTime beforeTime);

        /**
         * 查询用户最大单笔充值记录
         * 
         * @param userId   用户ID
         * @param pageable 分页参数
         * @return 最大单笔充值记录列表（按金额降序排列）
         */
        @Query("SELECT bt FROM BillingTransaction bt WHERE bt.userId = :userId AND bt.type = 'RECHARGE' " +
                        "ORDER BY bt.amount DESC")
        List<BillingTransaction> findUserMaxRechargeTransaction(@Param("userId") Long userId, Pageable pageable);

        /**
         * 查询用户最大单笔消费记录
         * 
         * @param userId   用户ID
         * @param pageable 分页参数
         * @return 最大单笔消费记录列表（按金额降序排列）
         */
        @Query("SELECT bt FROM BillingTransaction bt WHERE bt.userId = :userId AND bt.type = 'DEDUCTION' " +
                        "ORDER BY bt.amount DESC")
        List<BillingTransaction> findUserMaxConsumptionTransaction(@Param("userId") Long userId, Pageable pageable);
}