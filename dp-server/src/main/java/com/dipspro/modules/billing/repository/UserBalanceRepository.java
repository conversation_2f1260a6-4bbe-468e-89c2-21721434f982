package com.dipspro.modules.billing.repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.dipspro.modules.billing.entity.UserBalance;

/**
 * 用户余额数据访问接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface UserBalanceRepository extends JpaRepository<UserBalance, Long> {

        /**
         * 根据用户ID查询用户余额
         * 
         * @param userId 用户ID
         * @return 用户余额信息
         */
        Optional<UserBalance> findByUserId(Long userId);

        /**
         * 检查用户余额是否存在
         * 
         * @param userId 用户ID
         * @return 是否存在
         */
        boolean existsByUserId(Long userId);

        /**
         * 根据套餐ID查询用户余额列表
         * 
         * @param packageId 套餐ID
         * @return 用户余额列表
         */
        List<UserBalance> findByPackageId(Long packageId);

        /**
         * 分页查询用户余额列表
         * 
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<UserBalance> findAllByOrderByUpdatedAtDesc(Pageable pageable);

        /**
         * 查询余额不足的用户（总余额小于指定金额）
         * 
         * @param threshold 余额阈值
         * @return 余额不足的用户列表
         */
        @Query("SELECT ub FROM UserBalance ub WHERE (ub.rechargedBalance + ub.giftBalance) < :threshold " +
                        "ORDER BY (ub.rechargedBalance + ub.giftBalance) ASC")
        List<UserBalance> findUsersWithLowBalance(@Param("threshold") BigDecimal threshold);

        /**
         * 查询有免费Token的用户
         * 
         * @return 有免费Token的用户列表
         */
        @Query("SELECT ub FROM UserBalance ub WHERE ub.freeTokens > 0 ORDER BY ub.freeTokens DESC")
        List<UserBalance> findUsersWithFreeTokens();

        /**
         * 查询需要重置每日使用量的用户
         * 
         * @param currentDate 当前日期
         * @return 需要重置的用户列表
         */
        @Query("SELECT ub FROM UserBalance ub WHERE ub.lastResetDate IS NULL OR ub.lastResetDate < :currentDate")
        List<UserBalance> findUsersNeedingDailyReset(@Param("currentDate") LocalDate currentDate);

        /**
         * 批量重置每日Token使用量
         * 
         * @param currentDate 当前日期
         * @return 更新的记录数
         */
        @Modifying
        @Query("UPDATE UserBalance ub SET ub.usedTokensToday = 0, ub.lastResetDate = :currentDate, " +
                        "ub.updatedAt = CURRENT_TIMESTAMP WHERE ub.lastResetDate IS NULL OR ub.lastResetDate < :currentDate")
        int resetDailyUsageForAllUsers(@Param("currentDate") LocalDate currentDate);

        /**
         * 更新用户充值余额
         * 
         * @param userId 用户ID
         * @param amount 充值金额
         * @return 更新的记录数
         */
        @Modifying
        @Query("UPDATE UserBalance ub SET ub.rechargedBalance = ub.rechargedBalance + :amount, " +
                        "ub.totalRecharged = ub.totalRecharged + :amount, ub.updatedAt = CURRENT_TIMESTAMP WHERE ub.userId = :userId")
        int addRechargedBalance(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

        /**
         * 更新用户赠送余额
         * 
         * @param userId 用户ID
         * @param amount 赠送金额
         * @return 更新的记录数
         */
        @Modifying
        @Query("UPDATE UserBalance ub SET ub.giftBalance = ub.giftBalance + :amount, " +
                        "ub.updatedAt = CURRENT_TIMESTAMP WHERE ub.userId = :userId")
        int addGiftBalance(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

        /**
         * 扣除用户余额（优先扣除赠送余额，再扣除充值余额）
         * 
         * @param userId 用户ID
         * @param amount 扣除金额
         * @return 更新的记录数
         */
        @Modifying
        @Query("UPDATE UserBalance ub SET " +
                        "ub.giftBalance = CASE WHEN ub.giftBalance >= :amount THEN ub.giftBalance - :amount ELSE 0 END, "
                        +
                        "ub.rechargedBalance = CASE WHEN ub.giftBalance >= :amount THEN ub.rechargedBalance " +
                        "ELSE ub.rechargedBalance - (:amount - ub.giftBalance) END, " +
                        "ub.totalConsumed = ub.totalConsumed + :amount, ub.updatedAt = CURRENT_TIMESTAMP " +
                        "WHERE ub.userId = :userId AND (ub.rechargedBalance + ub.giftBalance) >= :amount")
        int deductBalance(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

        /**
         * 更新用户免费Token
         * 
         * @param userId 用户ID
         * @param tokens Token数量
         * @return 更新的记录数
         */
        @Modifying
        @Query("UPDATE UserBalance ub SET ub.freeTokens = ub.freeTokens + :tokens, " +
                        "ub.updatedAt = CURRENT_TIMESTAMP WHERE ub.userId = :userId")
        int addFreeTokens(@Param("userId") Long userId, @Param("tokens") Long tokens);

        /**
         * 扣除用户免费Token
         * 
         * @param userId 用户ID
         * @param tokens Token数量
         * @return 更新的记录数
         */
        @Modifying
        @Query("UPDATE UserBalance ub SET ub.freeTokens = ub.freeTokens - :tokens, " +
                        "ub.updatedAt = CURRENT_TIMESTAMP WHERE ub.userId = :userId AND ub.freeTokens >= :tokens")
        int deductFreeTokens(@Param("userId") Long userId, @Param("tokens") Long tokens);

        /**
         * 更新用户今日Token使用量
         * 
         * @param userId 用户ID
         * @param tokens Token数量
         * @return 更新的记录数
         */
        @Modifying
        @Query("UPDATE UserBalance ub SET ub.usedTokensToday = ub.usedTokensToday + :tokens, " +
                        "ub.updatedAt = CURRENT_TIMESTAMP WHERE ub.userId = :userId")
        int addUsedTokensToday(@Param("userId") Long userId, @Param("tokens") Long tokens);

        /**
         * 更新用户套餐
         * 
         * @param userId    用户ID
         * @param packageId 套餐ID
         * @return 更新的记录数
         */
        @Modifying
        @Query("UPDATE UserBalance ub SET ub.packageId = :packageId, ub.updatedAt = CURRENT_TIMESTAMP WHERE ub.userId = :userId")
        int updateUserPackage(@Param("userId") Long userId, @Param("packageId") Long packageId);

        /**
         * 查询指定套餐的用户数量
         * 
         * @param packageId 套餐ID
         * @return 用户数量
         */
        long countByPackageId(Long packageId);

        /**
         * 查询余额统计信息
         * 
         * @return 包含总用户数、总充值金额、总消费金额的统计信息
         */
        @Query("SELECT COUNT(ub), SUM(ub.totalRecharged), SUM(ub.totalConsumed) FROM UserBalance ub")
        Object[] getBalanceStatistics();

        /**
         * 查询指定时间范围内更新的用户余额
         * 
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @param pageable  分页参数
         * @return 分页结果
         */
        @Query("SELECT ub FROM UserBalance ub WHERE ub.updatedAt >= :startDate AND ub.updatedAt <= :endDate " +
                        "ORDER BY ub.updatedAt DESC")
        Page<UserBalance> findByUpdatedAtBetween(@Param("startDate") java.time.LocalDateTime startDate,
                        @Param("endDate") java.time.LocalDateTime endDate,
                        Pageable pageable);

        /**
         * 重置所有用户的每日Token使用量
         * 
         * @return 更新的记录数
         */
        @Modifying
        @Query("UPDATE UserBalance ub SET ub.usedTokensToday = 0, ub.lastResetDate = CURRENT_DATE, " +
                        "ub.updatedAt = CURRENT_TIMESTAMP")
        int resetAllUsersDailyTokenUsage();

        /**
         * 查询免费Token大于指定数量的用户
         * 
         * @param threshold Token阈值
         * @return 用户余额列表
         */
        @Query("SELECT ub FROM UserBalance ub WHERE ub.freeTokens > :threshold ORDER BY ub.freeTokens DESC")
        List<UserBalance> findByFreeTokensGreaterThan(@Param("threshold") long threshold);

        /**
         * 查询指定时间范围内更新的用户余额（按更新时间降序）
         * 
         * @param startDate 开始时间
         * @param endDate   结束时间
         * @param pageable  分页参数
         * @return 分页结果
         */
        @Query("SELECT ub FROM UserBalance ub WHERE ub.updatedAt >= :startDate AND ub.updatedAt <= :endDate " +
                        "ORDER BY ub.updatedAt DESC")
        Page<UserBalance> findByUpdatedAtBetweenOrderByUpdatedAtDesc(
                        @Param("startDate") java.time.LocalDateTime startDate,
                        @Param("endDate") java.time.LocalDateTime endDate,
                        Pageable pageable);
}