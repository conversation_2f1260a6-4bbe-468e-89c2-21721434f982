package com.dipspro.modules.billing.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dipspro.modules.billing.dto.BalanceHistoryDto;
import com.dipspro.modules.billing.entity.BillingTransaction;
import com.dipspro.modules.billing.entity.UserBalance;
import com.dipspro.modules.billing.repository.BillingPackageRepository;
import com.dipspro.modules.billing.repository.BillingTransactionRepository;
import com.dipspro.modules.billing.repository.UserBalanceRepository;
import com.dipspro.modules.billing.service.UserBalanceService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户余额服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class UserBalanceServiceImpl implements UserBalanceService {

    private final UserBalanceRepository userBalanceRepository;
    private final BillingPackageRepository billingPackageRepository;
    private final BillingTransactionRepository billingTransactionRepository;

    /**
     * 初始化用户余额
     * 
     * 为新用户创建余额记录，这是用户首次使用系统时的必要步骤：
     * 1. 重复检查：防止为同一用户创建多个余额记录
     * 2. 套餐验证：确保指定的套餐存在且有效
     * 3. 记录创建：创建包含所有必要字段的余额记录
     * 4. 初始值设置：设置合理的初始余额和Token数量
     * 5. 数据持久化：保存到数据库并返回完整记录
     * 
     * 初始化的字段包括：
     * - 用户ID和套餐ID关联
     * - 充值余额和赠送余额（初始为0）
     * - 免费Token数量（初始为0）
     * - 今日Token使用量（初始为0）
     * - 冻结状态（初始为false）
     * 
     * @param userId    用户ID，不能为空
     * @param packageId 套餐ID，可以为null（使用默认套餐）
     * @return 创建的用户余额记录
     * @throws IllegalArgumentException 当指定的套餐不存在时抛出
     */
    @Override
    public UserBalance initializeUserBalance(Long userId, Long packageId) {
        log.info("初始化用户余额: UserId={}, PackageId={}", userId, packageId);

        // 第一步：检查用户余额是否已存在，避免重复创建
        Optional<UserBalance> existingBalance = userBalanceRepository.findByUserId(userId);
        if (existingBalance.isPresent()) {
            log.warn("用户余额已存在: UserId={}", userId);
            return existingBalance.get();
        }

        // 第二步：验证套餐是否存在（如果指定了套餐ID）
        if (packageId != null && !billingPackageRepository.existsById(packageId)) {
            throw new IllegalArgumentException("套餐不存在: " + packageId);
        }

        // 第三步：创建用户余额记录并设置初始值
        UserBalance userBalance = new UserBalance();
        userBalance.setUserId(userId);
        userBalance.setPackageId(packageId);
        userBalance.setRechargedBalance(BigDecimal.ZERO); // 充值余额初始为0
        userBalance.setGiftBalance(BigDecimal.ZERO); // 赠送余额初始为0
        userBalance.setFreeTokens(0L); // 免费Token初始为0
        userBalance.setUsedTokensToday(0L); // 今日使用量初始为0
        userBalance.setIsFrozen(false); // 初始状态为未冻结

        // 第四步：保存余额记录到数据库
        UserBalance savedBalance = userBalanceRepository.save(userBalance);
        log.info("成功初始化用户余额: UserId={}, BalanceId={}", userId, savedBalance.getId());

        return savedBalance;
    }

    /**
     * 获取用户余额记录
     * 
     * 根据用户ID查询用户的余额记录，如果用户尚未初始化余额则返回空
     * 
     * @param userId 用户ID，不能为空
     * @return 用户余额记录的Optional包装，如果不存在则为空
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<UserBalance> getUserBalance(Long userId) {
        return userBalanceRepository.findByUserId(userId);
    }

    /**
     * 获取或创建用户余额记录
     * 
     * 如果用户余额记录存在则直接返回，否则自动创建新的余额记录：
     * 1. 查询检查：检查用户是否已有余额记录
     * 2. 直接返回：如果存在则直接返回现有记录
     * 3. 套餐获取：获取系统默认套餐ID
     * 4. 自动创建：使用默认套餐创建新的余额记录
     * 
     * 此方法确保每个用户都有有效的余额记录，是系统的核心方法之一
     * 
     * @param userId 用户ID，不能为空
     * @return 用户余额记录（现有的或新创建的）
     */
    @Override
    public UserBalance getOrCreateUserBalance(Long userId) {
        // 第一步：查询用户是否已有余额记录
        Optional<UserBalance> existingBalance = getUserBalance(userId);
        if (existingBalance.isPresent()) {
            // 第二步：如果存在则直接返回现有记录
            return existingBalance.get();
        }

        // 第三步：获取系统默认套餐ID
        Long defaultPackageId = billingPackageRepository.findDefaultPackage()
                .map(pkg -> pkg.getId())
                .orElse(null);

        // 第四步：使用默认套餐创建新的余额记录
        return initializeUserBalance(userId, defaultPackageId);
    }

    /**
     * 用户充值
     * 
     * 为用户账户增加充值余额，这是用户获得余额的主要方式：
     * 1. 参数验证：检查充值金额是否有效（大于0）
     * 2. 用户检查：获取或创建用户余额记录
     * 3. 状态检查：确保用户余额未被冻结
     * 4. 余额更新：增加用户的充值余额
     * 5. 记录创建：创建充值交易记录用于审计
     * 
     * 充值余额优先级低于赠送余额，在扣费时会后被使用
     * 
     * @param userId      用户ID，不能为空
     * @param amount      充值金额，必须大于0
     * @param description 充值描述，可以为空
     * @return true表示充值成功，false表示充值失败
     */
    @Override
    public boolean recharge(Long userId, BigDecimal amount, String description) {
        log.info("用户充值: UserId={}, Amount={}, Description={}", userId, amount, description);

        // 第一步：验证充值金额的有效性
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("充值金额无效: Amount={}", amount);
            return false;
        }

        // 第二步：获取或创建用户余额记录
        UserBalance userBalance = getOrCreateUserBalance(userId);

        // 第三步：检查用户余额是否被冻结
        if (Boolean.TRUE.equals(userBalance.getIsFrozen())) {
            log.warn("用户余额已被冻结，无法充值: UserId={}", userId);
            return false;
        }

        // 第四步：更新用户的充值余额
        BigDecimal newRechargeBalance = userBalance.getRechargedBalance().add(amount);
        userBalance.setRechargedBalance(newRechargeBalance);
        userBalanceRepository.save(userBalance);

        // 第五步：创建充值交易记录用于审计和追踪
        createTransactionRecord(userId, "RECHARGE", amount, description, null);

        log.info("用户充值成功: UserId={}, Amount={}, NewBalance={}", userId, amount, newRechargeBalance);
        return true;
    }

    /**
     * 赠送余额
     * 
     * 为用户账户增加赠送余额，通常用于促销活动、补偿或奖励：
     * 1. 参数验证：检查赠送金额是否有效（大于0）
     * 2. 用户检查：获取或创建用户余额记录
     * 3. 余额更新：增加用户的赠送余额
     * 4. 记录创建：创建赠送交易记录用于审计
     * 
     * 赠送余额优先级高于充值余额，在扣费时会优先被使用
     * 
     * @param userId      用户ID，不能为空
     * @param amount      赠送金额，必须大于0
     * @param description 赠送描述，可以为空
     * @return true表示赠送成功，false表示赠送失败
     */
    @Override
    public boolean giftBalance(Long userId, BigDecimal amount, String description) {
        log.info("赠送余额: UserId={}, Amount={}, Description={}", userId, amount, description);

        // 第一步：验证赠送金额的有效性
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("赠送金额无效: Amount={}", amount);
            return false;
        }

        // 第二步：获取或创建用户余额记录
        UserBalance userBalance = getOrCreateUserBalance(userId);

        // 第三步：更新用户的赠送余额
        BigDecimal newGiftBalance = userBalance.getGiftBalance().add(amount);
        userBalance.setGiftBalance(newGiftBalance);
        userBalanceRepository.save(userBalance);

        // 第四步：创建赠送交易记录用于审计和追踪
        createTransactionRecord(userId, "GIFT", amount, description, null);

        log.info("赠送余额成功: UserId={}, Amount={}, NewGiftBalance={}", userId, amount, newGiftBalance);
        return true;
    }

    @Override
    public boolean deductBalance(Long userId, BigDecimal amount, String description) {
        log.info("扣除余额: UserId={}, Amount={}, Description={}", userId, amount, description);

        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("扣除金额无效: Amount={}", amount);
            return false;
        }

        UserBalance userBalance = getOrCreateUserBalance(userId);

        // 检查余额是否被冻结
        if (Boolean.TRUE.equals(userBalance.getIsFrozen())) {
            log.warn("用户余额已被冻结，无法扣费: UserId={}", userId);
            return false;
        }

        // 检查余额是否充足
        if (!hasEnoughBalance(userId, amount)) {
            log.warn("用户余额不足: UserId={}, RequiredAmount={}, AvailableBalance={}",
                    userId, amount, getTotalBalance(userId));
            return false;
        }

        // 优先扣除赠送余额，再扣除充值余额
        BigDecimal remainingAmount = amount;
        BigDecimal currentGiftBalance = userBalance.getGiftBalance();
        BigDecimal currentRechargeBalance = userBalance.getRechargeBalance();

        if (currentGiftBalance.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal giftDeduction = remainingAmount.min(currentGiftBalance);
            userBalance.setGiftBalance(currentGiftBalance.subtract(giftDeduction));
            remainingAmount = remainingAmount.subtract(giftDeduction);
        }

        if (remainingAmount.compareTo(BigDecimal.ZERO) > 0) {
            userBalance.setRechargeBalance(currentRechargeBalance.subtract(remainingAmount));
        }

        userBalanceRepository.save(userBalance);

        // 创建交易记录
        createTransactionRecord(userId, "DEDUCTION", amount, description, null);

        log.info("扣除余额成功: UserId={}, Amount={}, RemainingBalance={}",
                userId, amount, getTotalBalance(userId));
        return true;
    }

    /**
     * 检查用户余额是否充足
     * 
     * 验证用户的总余额（充值余额+赠送余额）是否足够支付指定金额：
     * 1. 参数验证：如果金额为null或小于等于0，则认为充足
     * 2. 余额获取：获取用户的总余额
     * 3. 余额比较：比较总余额与所需金额
     * 
     * @param userId 用户ID，不能为空
     * @param amount 需要检查的金额，可以为null
     * @return true表示余额充足，false表示余额不足
     */
    @Override
    @Transactional(readOnly = true)
    public boolean hasEnoughBalance(Long userId, BigDecimal amount) {
        // 第一步：如果金额无效，则认为余额充足
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return true;
        }

        // 第二步：获取用户的总余额
        BigDecimal totalBalance = getTotalBalance(userId);

        // 第三步：比较总余额与所需金额
        return totalBalance.compareTo(amount) >= 0;
    }

    /**
     * 获取用户总余额
     * 
     * 计算用户的总可用余额（充值余额+赠送余额）：
     * 1. 用户查询：查找用户余额记录
     * 2. 存在检查：如果用户不存在则返回0
     * 3. 余额计算：计算充值余额和赠送余额的总和
     * 
     * @param userId 用户ID，不能为空
     * @return 用户的总余额，如果用户不存在则返回0
     */
    @Override
    @Transactional(readOnly = true)
    public BigDecimal getTotalBalance(Long userId) {
        // 第一步：查找用户余额记录
        Optional<UserBalance> balanceOpt = getUserBalance(userId);
        if (balanceOpt.isEmpty()) {
            // 第二步：如果用户不存在则返回0
            return BigDecimal.ZERO;
        }

        // 第三步：计算充值余额和赠送余额的总和
        UserBalance userBalance = balanceOpt.get();
        return userBalance.getRechargeBalance().add(userBalance.getGiftBalance());
    }

    @Override
    public boolean addFreeTokens(Long userId, Long tokens, String description) {
        log.info("添加免费Token: UserId={}, Tokens={}, Description={}", userId, tokens, description);

        if (tokens == null || tokens <= 0) {
            log.warn("免费Token数量无效: Tokens={}", tokens);
            return false;
        }

        UserBalance userBalance = getOrCreateUserBalance(userId);

        Long newFreeTokens = userBalance.getFreeTokens() + tokens;
        userBalance.setFreeTokens(newFreeTokens);
        userBalanceRepository.save(userBalance);

        log.info("添加免费Token成功: UserId={}, Tokens={}, NewFreeTokens={}", userId, tokens, newFreeTokens);
        return true;
    }

    @Override
    public boolean deductFreeTokens(Long userId, Long tokens) {
        log.info("扣除免费Token: UserId={}, Tokens={}", userId, tokens);

        if (tokens == null || tokens <= 0) {
            log.warn("扣除Token数量无效: Tokens={}", tokens);
            return false;
        }

        UserBalance userBalance = getOrCreateUserBalance(userId);

        if (!hasEnoughFreeTokens(userId, tokens)) {
            log.warn("免费Token不足: UserId={}, RequiredTokens={}, AvailableTokens={}",
                    userId, tokens, userBalance.getFreeTokens());
            return false;
        }

        Long newFreeTokens = userBalance.getFreeTokens() - tokens;
        userBalance.setFreeTokens(newFreeTokens);
        userBalanceRepository.save(userBalance);

        log.info("扣除免费Token成功: UserId={}, Tokens={}, RemainingTokens={}", userId, tokens, newFreeTokens);
        return true;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasEnoughFreeTokens(Long userId, Long tokens) {
        if (tokens == null || tokens <= 0) {
            return true;
        }

        Optional<UserBalance> balanceOpt = getUserBalance(userId);
        if (balanceOpt.isEmpty()) {
            return false;
        }

        return balanceOpt.get().getFreeTokens() >= tokens;
    }

    @Override
    public boolean updateTodayTokenUsage(Long userId, Long tokens) {
        log.info("更新今日Token使用量: UserId={}, Tokens={}", userId, tokens);

        if (tokens == null || tokens < 0) {
            log.warn("Token使用量无效: Tokens={}", tokens);
            return false;
        }

        UserBalance userBalance = getOrCreateUserBalance(userId);

        Long newTodayUsage = userBalance.getTodayTokenUsage() + tokens;
        userBalance.setTodayTokenUsage(newTodayUsage);
        userBalanceRepository.save(userBalance);

        log.info("更新今日Token使用量成功: UserId={}, Tokens={}, TotalTodayUsage={}",
                userId, tokens, newTodayUsage);
        return true;
    }

    @Override
    public boolean resetDailyTokenUsage(Long userId) {
        log.info("重置用户每日Token使用量: UserId={}", userId);

        Optional<UserBalance> balanceOpt = getUserBalance(userId);
        if (balanceOpt.isEmpty()) {
            log.warn("用户余额不存在: UserId={}", userId);
            return false;
        }

        UserBalance userBalance = balanceOpt.get();
        userBalance.setTodayTokenUsage(0L);
        userBalanceRepository.save(userBalance);

        log.info("重置用户每日Token使用量成功: UserId={}", userId);
        return true;
    }

    @Override
    public int resetAllUsersDailyTokenUsage() {
        log.info("批量重置所有用户每日Token使用量");

        int updatedCount = userBalanceRepository.resetAllUsersDailyTokenUsage();

        log.info("批量重置所有用户每日Token使用量完成: 更新数量={}", updatedCount);
        return updatedCount;
    }

    @Override
    public boolean updateUserPackage(Long userId, Long packageId) {
        log.info("更新用户套餐: UserId={}, PackageId={}", userId, packageId);

        // 验证套餐是否存在
        if (packageId != null && !billingPackageRepository.existsById(packageId)) {
            log.warn("套餐不存在: PackageId={}", packageId);
            return false;
        }

        UserBalance userBalance = getOrCreateUserBalance(userId);
        userBalance.setPackageId(packageId);
        userBalanceRepository.save(userBalance);

        log.info("更新用户套餐成功: UserId={}, PackageId={}", userId, packageId);
        return true;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserBalance> getUserBalances(Pageable pageable) {
        return userBalanceRepository.findAllByOrderByUpdatedAtDesc(pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserBalance> getUsersWithLowBalance(BigDecimal threshold) {
        return userBalanceRepository.findUsersWithLowBalance(threshold);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserBalance> getUsersWithFreeTokens() {
        return userBalanceRepository.findByFreeTokensGreaterThan(0L);
    }

    @Override
    @Transactional(readOnly = true)
    public Object[] getBalanceStatistics() {
        return userBalanceRepository.getBalanceStatistics();
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserBalance> getUserBalancesByDateRange(LocalDateTime startDate, LocalDateTime endDate,
            Pageable pageable) {
        return userBalanceRepository.findByUpdatedAtBetweenOrderByUpdatedAtDesc(startDate, endDate, pageable);
    }

    @Override
    public boolean freezeUserBalance(Long userId, String reason) {
        log.info("冻结用户余额: UserId={}, Reason={}", userId, reason);

        UserBalance userBalance = getOrCreateUserBalance(userId);
        userBalance.setIsFrozen(true);
        userBalanceRepository.save(userBalance);

        log.info("冻结用户余额成功: UserId={}, Reason={}", userId, reason);
        return true;
    }

    @Override
    public boolean unfreezeUserBalance(Long userId, String reason) {
        log.info("解冻用户余额: UserId={}, Reason={}", userId, reason);

        UserBalance userBalance = getOrCreateUserBalance(userId);
        userBalance.setIsFrozen(false);
        userBalanceRepository.save(userBalance);

        log.info("解冻用户余额成功: UserId={}, Reason={}", userId, reason);
        return true;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isUserBalanceFrozen(Long userId) {
        Optional<UserBalance> balanceOpt = getUserBalance(userId);
        return balanceOpt.map(balance -> Boolean.TRUE.equals(balance.getIsFrozen())).orElse(false);
    }

    @Override
    @Transactional(readOnly = true)
    public BigDecimal getAvailableBalance(Long userId) {
        if (isUserBalanceFrozen(userId)) {
            return BigDecimal.ZERO;
        }
        return getTotalBalance(userId);
    }

    @Override
    public boolean transferBalance(Long fromUserId, Long toUserId, BigDecimal amount, String description) {
        log.info("余额转账: FromUserId={}, ToUserId={}, Amount={}, Description={}",
                fromUserId, toUserId, amount, description);

        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("转账金额无效: Amount={}", amount);
            return false;
        }

        if (fromUserId.equals(toUserId)) {
            log.warn("不能向自己转账: UserId={}", fromUserId);
            return false;
        }

        // 检查转出用户余额是否充足
        if (!hasEnoughBalance(fromUserId, amount)) {
            log.warn("转出用户余额不足: FromUserId={}, RequiredAmount={}, AvailableBalance={}",
                    fromUserId, amount, getTotalBalance(fromUserId));
            return false;
        }

        // 执行转账
        boolean deductSuccess = deductBalance(fromUserId, amount, "转账给用户" + toUserId + ": " + description);
        if (!deductSuccess) {
            log.warn("扣除转出用户余额失败: FromUserId={}", fromUserId);
            return false;
        }

        boolean addSuccess = giftBalance(toUserId, amount, "来自用户" + fromUserId + "的转账: " + description);
        if (!addSuccess) {
            log.error("增加转入用户余额失败，需要回滚: ToUserId={}", toUserId);
            // 回滚转出用户的扣费
            giftBalance(fromUserId, amount, "转账失败回滚: " + description);
            return false;
        }

        log.info("余额转账成功: FromUserId={}, ToUserId={}, Amount={}", fromUserId, toUserId, amount);
        return true;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<BalanceHistoryDto> getUserBalanceHistory(Long userId, Pageable pageable) {
        // 返回用户的交易记录作为余额变动历史
        Page<BillingTransaction> transactions = billingTransactionRepository.findByUserIdOrderByCreatedAtDesc(userId,
                pageable);
        return transactions.map(this::convertToBalanceHistoryDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<BalanceHistoryDto> getUserBalanceHistory(Long userId, String type, Pageable pageable) {
        Page<BillingTransaction> transactions;
        if (type != null && !type.trim().isEmpty()) {
            // 根据类型筛选交易记录
            transactions = billingTransactionRepository.findByUserIdAndTypeOrderByCreatedAtDesc(userId, type,
                    pageable);
        } else {
            // 不筛选，返回所有记录
            transactions = billingTransactionRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
        }
        return transactions.map(this::convertToBalanceHistoryDto);
    }

    /**
     * 转换交易记录为余额历史DTO
     */
    private BalanceHistoryDto convertToBalanceHistoryDto(BillingTransaction transaction) {
        return new BalanceHistoryDto()
                .setId(transaction.getId())
                .setTransactionNo(transaction.getTransactionNo())
                .setType(transaction.getType())
                .setAmount(transaction.getAmount())
                .setBalanceType(transaction.getBalanceType())
                .setBalanceBefore(transaction.getBalanceBefore())
                .setBalanceAfter(transaction.getBalanceAfter())
                .setDescription(transaction.getDescription())
                .setCreatedAt(transaction.getCreatedAt());
    }

    @Override
    @Transactional(readOnly = true)
    public String validateBalanceOperation(Long userId, BigDecimal amount, String operationType) {
        if (userId == null) {
            return "用户ID不能为空";
        }

        if (amount == null) {
            return "操作金额不能为空";
        }

        if (operationType == null || operationType.trim().isEmpty()) {
            return "操作类型不能为空";
        }

        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            return "操作金额必须大于0";
        }

        // 检查用户余额是否被冻结（对于扣费操作）
        if ("DEDUCTION".equals(operationType) && isUserBalanceFrozen(userId)) {
            return "用户余额已被冻结，无法执行扣费操作";
        }

        // 检查余额是否充足（对于扣费操作）
        if ("DEDUCTION".equals(operationType) && !hasEnoughBalance(userId, amount)) {
            return "用户余额不足";
        }

        return null; // 验证通过
    }

    @Override
    @Transactional(readOnly = true)
    public String getBalanceWarning(Long userId) {
        Optional<UserBalance> balanceOpt = getUserBalance(userId);
        if (balanceOpt.isEmpty()) {
            return "用户余额记录不存在";
        }

        UserBalance userBalance = balanceOpt.get();

        if (Boolean.TRUE.equals(userBalance.getIsFrozen())) {
            return "用户余额已被冻结";
        }

        BigDecimal totalBalance = getTotalBalance(userId);

        // 余额预警阈值
        BigDecimal warningThreshold = new BigDecimal("10.00");
        BigDecimal criticalThreshold = new BigDecimal("1.00");

        if (totalBalance.compareTo(criticalThreshold) <= 0) {
            return "余额严重不足，请及时充值";
        } else if (totalBalance.compareTo(warningThreshold) <= 0) {
            return "余额不足，建议充值";
        }

        return null; // 无预警
    }

    /**
     * 创建交易记录的私有方法
     */
    private void createTransactionRecord(Long userId, String transactionType, BigDecimal amount,
            String description, String externalTransactionId) {
        try {
            BillingTransaction transaction = new BillingTransaction();
            transaction.setUserId(userId);
            transaction.setTransactionType(transactionType);
            transaction.setAmount(amount);
            transaction.setDescription(description);
            transaction.setExternalTransactionId(externalTransactionId);

            billingTransactionRepository.save(transaction);
        } catch (Exception e) {
            log.error("创建交易记录失败: UserId={}, Type={}, Amount={}", userId, transactionType, amount, e);
            // 不抛出异常，避免影响主要业务流程
        }
    }

    // ==================== Controller需要的额外方法实现 ====================

    @Override
    @Transactional(readOnly = true)
    public Object getUserBalanceStatistics(Long userId, String period) {
        log.info("获取用户余额统计: UserId={}, Period={}", userId, period);

        UserBalance userBalance = getOrCreateUserBalance(userId);

        return new Object() {
            public final Long userId = userBalance.getUserId();
            public final BigDecimal totalBalance = getTotalBalance(userBalance.getUserId());
            public final BigDecimal rechargedBalance = userBalance.getRechargeBalance();
            public final BigDecimal giftBalance = userBalance.getGiftBalance();
            public final String statisticsPeriod = period;
            public final String message = "统计信息获取成功";
        };
    }

    @Override
    @Transactional(readOnly = true)
    public boolean checkBalanceSufficient(Long userId, BigDecimal amount, String balanceType) {
        log.info("检查用户余额: UserId={}, Amount={}, BalanceType={}", userId, amount, balanceType);

        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return true;
        }

        UserBalance userBalance = getOrCreateUserBalance(userId);

        return switch (balanceType) {
            case "CASH" -> userBalance.getRechargeBalance().compareTo(amount) >= 0;
            case "GIFT" -> userBalance.getGiftBalance().compareTo(amount) >= 0;
            case "TOTAL" -> getTotalBalance(userId).compareTo(amount) >= 0;
            default -> getTotalBalance(userId).compareTo(amount) >= 0;
        };
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserBalance> getUserBalancesForAdmin(Long userId, BigDecimal minBalance,
            BigDecimal maxBalance, String orderBy, Pageable pageable) {
        log.info("管理员查询用户余额: UserId={}, MinBalance={}, MaxBalance={}, OrderBy={}",
                userId, minBalance, maxBalance, orderBy);

        // 这里简化实现，实际应该根据参数构建动态查询
        if (userId != null) {
            Optional<UserBalance> balance = getUserBalance(userId);
            if (balance.isPresent()) {
                List<UserBalance> list = List.of(balance.get());
                return new org.springframework.data.domain.PageImpl<>(list, pageable, 1);
            } else {
                return Page.empty();
            }
        }

        return userBalanceRepository.findAllByOrderByUpdatedAtDesc(pageable);
    }

    @Override
    public UserBalance adjustUserBalance(Long userId, String adjustType, BigDecimal amount, String reason) {
        log.info("调整用户余额: UserId={}, AdjustType={}, Amount={}, Reason={}",
                userId, adjustType, amount, reason);

        UserBalance userBalance = getOrCreateUserBalance(userId);

        switch (adjustType) {
            case "INCREASE" -> {
                // 默认增加到赠送余额
                BigDecimal newGiftBalance = userBalance.getGiftBalance().add(amount);
                userBalance.setGiftBalance(newGiftBalance);
                createTransactionRecord(userId, "ADJUSTMENT", amount, "管理员调整: " + reason, null);
            }
            case "DECREASE" -> {
                // 优先从赠送余额扣除，再从充值余额扣除
                BigDecimal remainingAmount = amount;
                BigDecimal currentGiftBalance = userBalance.getGiftBalance();
                BigDecimal currentRechargeBalance = userBalance.getRechargeBalance();

                if (currentGiftBalance.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal giftDeduction = remainingAmount.min(currentGiftBalance);
                    userBalance.setGiftBalance(currentGiftBalance.subtract(giftDeduction));
                    remainingAmount = remainingAmount.subtract(giftDeduction);
                }

                if (remainingAmount.compareTo(BigDecimal.ZERO) > 0) {
                    userBalance.setRechargeBalance(currentRechargeBalance.subtract(remainingAmount));
                }

                createTransactionRecord(userId, "ADJUSTMENT", amount.negate(), "管理员调整: " + reason, null);
            }
            default -> throw new IllegalArgumentException("不支持的调整类型: " + adjustType);
        }

        return userBalanceRepository.save(userBalance);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserBalance> getBalanceAlerts(String alertType, Integer limit) {
        log.info("获取余额预警列表: AlertType={}, Limit={}", alertType, limit);

        // 这里简化实现，返回余额不足的用户
        BigDecimal threshold = new BigDecimal("100.00");
        List<UserBalance> lowBalanceUsers = getUsersWithLowBalance(threshold);

        // 限制返回数量
        if (limit != null && lowBalanceUsers.size() > limit) {
            return lowBalanceUsers.subList(0, limit);
        }

        return lowBalanceUsers;
    }

    @Override
    public com.dipspro.modules.billing.dto.BatchAdjustResult batchAdjustBalance(
            java.util.List<Long> userIds, com.dipspro.modules.billing.dto.BalanceAdjustDto adjustDto) {
        log.info("批量调整用户余额: UserCount={}, AdjustType={}, Amount={}",
                userIds.size(), adjustDto.getAdjustType(), adjustDto.getAmount());

        java.util.List<Long> successUserIds = new java.util.ArrayList<>();
        java.util.List<Long> failUserIds = new java.util.ArrayList<>();
        java.util.List<String> errorMessages = new java.util.ArrayList<>();

        for (Long userId : userIds) {
            try {
                adjustUserBalance(userId, adjustDto.getAdjustType(), adjustDto.getAmount(), adjustDto.getReason());
                successUserIds.add(userId);
            } catch (Exception e) {
                failUserIds.add(userId);
                errorMessages.add("用户" + userId + ": " + e.getMessage());
                log.warn("批量调整失败: UserId={}, Error={}", userId, e.getMessage());
            }
        }

        return new com.dipspro.modules.billing.dto.BatchAdjustResult()
                .setTotalCount(userIds.size())
                .setSuccessCount(successUserIds.size())
                .setFailCount(failUserIds.size())
                .setSuccessUserIds(successUserIds)
                .setFailUserIds(failUserIds)
                .setErrorMessages(errorMessages)
                .setSummary(String.format("批量调整完成: 总计%d个用户，成功%d个，失败%d个",
                        userIds.size(), successUserIds.size(), failUserIds.size()));
    }

    // 重载方法：冻结指定金额的余额
    @Override
    public boolean freezeUserBalance(Long userId, BigDecimal amount, String reason) {
        log.info("冻结用户余额: UserId={}, Amount={}, Reason={}", userId, amount, reason);

        UserBalance userBalance = getOrCreateUserBalance(userId);

        // 检查余额是否充足
        if (getTotalBalance(userId).compareTo(amount) < 0) {
            log.warn("用户余额不足，无法冻结: UserId={}, Amount={}, AvailableBalance={}",
                    userId, amount, getTotalBalance(userId));
            return false;
        }

        // 增加冻结金额
        BigDecimal currentFrozenBalance = userBalance.getFrozenBalance() != null ? userBalance.getFrozenBalance()
                : BigDecimal.ZERO;
        userBalance.setFrozenBalance(currentFrozenBalance.add(amount));
        userBalanceRepository.save(userBalance);

        // 创建冻结记录
        createTransactionRecord(userId, "FREEZE", amount, "余额冻结: " + reason, null);

        log.info("用户余额冻结成功: UserId={}, FrozenAmount={}", userId, amount);
        return true;
    }

    // 重载方法：解冻指定金额的余额
    @Override
    public boolean unfreezeUserBalance(Long userId, BigDecimal amount, String reason) {
        log.info("解冻用户余额: UserId={}, Amount={}, Reason={}", userId, amount, reason);

        UserBalance userBalance = getOrCreateUserBalance(userId);
        BigDecimal currentFrozenBalance = userBalance.getFrozenBalance() != null ? userBalance.getFrozenBalance()
                : BigDecimal.ZERO;

        // 检查冻结余额是否充足
        if (currentFrozenBalance.compareTo(amount) < 0) {
            log.warn("冻结余额不足，无法解冻: UserId={}, Amount={}, FrozenBalance={}",
                    userId, amount, currentFrozenBalance);
            return false;
        }

        // 减少冻结金额
        userBalance.setFrozenBalance(currentFrozenBalance.subtract(amount));
        userBalanceRepository.save(userBalance);

        // 创建解冻记录
        createTransactionRecord(userId, "UNFREEZE", amount, "余额解冻: " + reason, null);

        log.info("用户余额解冻成功: UserId={}, UnfrozenAmount={}", userId, amount);
        return true;
    }
}