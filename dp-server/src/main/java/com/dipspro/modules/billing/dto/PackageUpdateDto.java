package com.dipspro.modules.billing.dto;

import java.math.BigDecimal;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 套餐更新DTO
 * 
 * 用于管理员更新现有套餐时的请求参数，包含可更新的套餐信息。
 * 与创建DTO基本相同，但支持部分字段的选择性更新。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class PackageUpdateDto {

    /**
     * 套餐名称
     * 必须不为空，长度限制在2-50个字符
     */
    @NotBlank(message = "套餐名称不能为空")
    @Size(min = 2, max = 50, message = "套餐名称长度必须在2-50个字符之间")
    private String name;

    /**
     * 套餐描述
     * 详细描述套餐的特性和适用场景
     */
    @Size(max = 500, message = "套餐描述不能超过500个字符")
    private String description;

    /**
     * 输入Token价格（每千Token）
     * 必须不为空且不能为负数
     */
    @NotNull(message = "输入Token价格不能为空")
    @DecimalMin(value = "0.0", message = "输入Token价格不能为负数")
    private BigDecimal inputTokenPrice;

    /**
     * 输出Token价格（每千Token）
     * 必须不为空且不能为负数
     */
    @NotNull(message = "输出Token价格不能为空")
    @DecimalMin(value = "0.0", message = "输出Token价格不能为负数")
    private BigDecimal outputTokenPrice;

    /**
     * 免费Token数量
     * 用户每日可用的免费Token配额
     */
    @Min(value = 0, message = "免费Token数量不能为负数")
    private Long freeTokens;

    /**
     * 单次请求最大Token限制
     * 0表示无限制
     */
    @Min(value = 0, message = "单次请求Token限制不能为负数")
    private Long maxTokensPerRequest;

    /**
     * 每日Token使用限制
     * 0表示无限制
     */
    @Min(value = 0, message = "每日Token限制不能为负数")
    private Long dailyTokenLimit;

    /**
     * 是否激活
     * 控制套餐的可用状态
     */
    private Boolean isActive;

    /**
     * 是否为默认套餐
     * 系统默认套餐标识
     */
    private Boolean isDefault;

    /**
     * 排序权重
     * 数值越小排序越靠前
     */
    @Min(value = 0, message = "排序权重不能为负数")
    private Integer sortOrder;

    /**
     * 套餐特性
     * JSON格式的特性描述，如模型支持、并发限制等
     */
    @Size(max = 2000, message = "套餐特性描述不能超过2000个字符")
    private String features;

    /**
     * 使用限制
     * JSON格式的限制条件，如请求频率、使用时长等
     */
    @Size(max = 2000, message = "使用限制描述不能超过2000个字符")
    private String limitations;

    /**
     * 检查价格配置是否合理
     * 
     * @return 是否合理
     */
    public boolean isPriceConfigValid() {
        if (inputTokenPrice == null || outputTokenPrice == null) {
            return false;
        }

        // 输出价格通常应该高于或等于输入价格
        return outputTokenPrice.compareTo(inputTokenPrice) >= 0;
    }

    /**
     * 检查是否为免费套餐
     * 
     * @return 是否为免费套餐
     */
    public boolean isFreePackage() {
        return (inputTokenPrice != null && inputTokenPrice.compareTo(BigDecimal.ZERO) == 0)
                && (outputTokenPrice != null && outputTokenPrice.compareTo(BigDecimal.ZERO) == 0);
    }

    /**
     * 获取格式化的价格信息
     * 
     * @return 格式化的价格字符串
     */
    public String getFormattedPriceInfo() {
        if (isFreePackage()) {
            return "免费套餐";
        }

        return String.format("输入: ¥%s/1K tokens, 输出: ¥%s/1K tokens",
                inputTokenPrice.toString(), outputTokenPrice.toString());
    }

    /**
     * 检查是否尝试设置为默认套餐
     * 
     * @return 是否设置为默认
     */
    public boolean isSettingAsDefault() {
        return Boolean.TRUE.equals(isDefault);
    }

    /**
     * 检查是否尝试停用套餐
     * 
     * @return 是否停用
     */
    public boolean isDeactivating() {
        return Boolean.FALSE.equals(isActive);
    }

    /**
     * 获取状态变更描述
     * 
     * @return 状态变更描述
     */
    public String getStatusChangeDescription() {
        StringBuilder sb = new StringBuilder();

        if (isSettingAsDefault()) {
            sb.append("设置为默认套餐");
        }

        if (isDeactivating()) {
            if (sb.length() > 0)
                sb.append(", ");
            sb.append("停用套餐");
        } else if (Boolean.TRUE.equals(isActive)) {
            if (sb.length() > 0)
                sb.append(", ");
            sb.append("激活套餐");
        }

        return sb.length() > 0 ? sb.toString() : "无状态变更";
    }

    /**
     * 验证更新配置完整性
     * 
     * @return 验证结果描述
     */
    public String validateUpdateConfiguration() {
        if (name == null || name.trim().isEmpty()) {
            return "套餐名称不能为空";
        }

        if (inputTokenPrice == null || outputTokenPrice == null) {
            return "Token价格配置不完整";
        }

        if (inputTokenPrice.compareTo(BigDecimal.ZERO) < 0 ||
                outputTokenPrice.compareTo(BigDecimal.ZERO) < 0) {
            return "Token价格不能为负数";
        }

        if (!isPriceConfigValid()) {
            return "价格配置不合理，输出价格应不低于输入价格";
        }

        if (freeTokens != null && freeTokens < 0) {
            return "免费Token数量不能为负数";
        }

        if (maxTokensPerRequest != null && maxTokensPerRequest < 0) {
            return "单次请求Token限制不能为负数";
        }

        if (dailyTokenLimit != null && dailyTokenLimit < 0) {
            return "每日Token限制不能为负数";
        }

        if (sortOrder != null && sortOrder < 0) {
            return "排序权重不能为负数";
        }

        // 特殊验证：不能同时停用并设为默认
        if (isDeactivating() && isSettingAsDefault()) {
            return "不能将停用的套餐设置为默认套餐";
        }

        return "配置有效";
    }
}