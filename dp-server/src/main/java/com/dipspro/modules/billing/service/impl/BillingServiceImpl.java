package com.dipspro.modules.billing.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dipspro.modules.billing.entity.BillingPackage;
import com.dipspro.modules.billing.entity.BillingUsageRecord;
import com.dipspro.modules.billing.entity.UserBalance;
import com.dipspro.modules.billing.repository.BillingPackageRepository;
import com.dipspro.modules.billing.repository.BillingUsageRecordRepository;
import com.dipspro.modules.billing.repository.UserBalanceRepository;
import com.dipspro.modules.billing.service.BillingService;
import com.dipspro.modules.billing.service.BillingTransactionService;
import com.dipspro.modules.billing.service.UserBalanceService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 核心计费服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class BillingServiceImpl implements BillingService {

    private final BillingUsageRecordRepository billingUsageRecordRepository;
    private final BillingPackageRepository billingPackageRepository;
    private final UserBalanceRepository userBalanceRepository;
    private final UserBalanceService userBalanceService;
    private final BillingTransactionService billingTransactionService;

    /**
     * 执行计费操作
     * <p>
     * 这是核心计费方法，负责处理AI对话的Token计费流程：
     * 1. 参数验证：检查用户ID、消息ID、Token数量等参数的有效性
     * 2. 重复检查：防止同一消息被重复计费
     * 3. 套餐获取：获取用户当前使用的计费套餐
     * 4. 费用计算：根据输入输出Token数量和套餐价格计算总费用
     * 5. 余额检查：确保用户余额充足以支付本次计费
     * 6. 记录创建：创建详细的计费使用记录
     * 7. 余额扣除：从用户余额中扣除相应费用
     * 8. 统计更新：更新用户今日Token使用量统计
     * 9. 交易记录：创建对应的扣费交易记录
     *
     * @param userId       用户ID，不能为空
     * @param messageId    消息ID，用于关联聊天消息，不能为空
     * @param inputTokens  输入Token数量，可以为null（默认为0）
     * @param outputTokens 输出Token数量，可以为null（默认为0）
     * @param requestTime  请求开始时间，用于计算响应时长
     * @param responseTime 响应结束时间，用于计算响应时长
     * @return 创建的计费使用记录
     * @throws IllegalArgumentException 当参数验证失败时抛出
     * @throws IllegalStateException    当余额不足或扣费失败时抛出
     */
    @Override
    public BillingUsageRecord performBilling(Long userId, UUID messageId, Long inputTokens, Long outputTokens,
            LocalDateTime requestTime, LocalDateTime responseTime) {
        log.info("执行计费操作: UserId={}, MessageId={}, InputTokens={}, OutputTokens={}",
                userId, messageId, inputTokens, outputTokens);

        // 第一步：验证计费参数的有效性
        String validationError = validateBillingParameters(userId, messageId, inputTokens, outputTokens);
        if (validationError != null) {
            throw new IllegalArgumentException(validationError);
        }

        // 第二步：检查消息是否已被计费，防止重复计费
        if (isMessageBilled(messageId)) {
            log.warn("消息已被计费，跳过重复计费: MessageId={}", messageId);
            return getBillingRecordByMessageId(messageId).orElse(null);
        }

        // 第三步：获取用户余额信息，如果不存在则自动创建
        UserBalance userBalance = userBalanceService.getOrCreateUserBalance(userId);

        // 第四步：获取用户当前使用的计费套餐
        BillingPackage billingPackage = getBillingPackageForUser(userBalance);

        // 第五步：根据套餐价格计算本次对话的总费用
        BigDecimal totalCost = calculateTokenCostWithPackage(billingPackage, inputTokens, outputTokens);

        // 第六步：检查用户余额是否充足支付本次费用
        if (!userBalanceService.hasEnoughBalance(userId, totalCost)) {
            String errorMsg = String.format("用户余额不足: UserId=%d, RequiredAmount=%s, AvailableBalance=%s",
                    userId, totalCost, userBalanceService.getTotalBalance(userId));
            log.warn(errorMsg);
            throw new IllegalStateException(errorMsg);
        }

        // 第七步：创建详细的计费使用记录
        BillingUsageRecord usageRecord = new BillingUsageRecord();
        usageRecord.setUserId(userId);
        usageRecord.setMessageId(messageId);
        usageRecord.setPackageId(billingPackage.getId());
        usageRecord.setInputTokens(inputTokens != null ? inputTokens : 0L);
        usageRecord.setOutputTokens(outputTokens != null ? outputTokens : 0L);
        usageRecord
                .setTotalTokens((inputTokens != null ? inputTokens : 0L) + (outputTokens != null ? outputTokens : 0L));
        usageRecord.setInputTokenPrice(billingPackage.getInputTokenPrice());
        usageRecord.setOutputTokenPrice(billingPackage.getOutputTokenPrice());
        usageRecord.setTotalCost(totalCost);
        usageRecord.setBillingType("TOKEN_BASED"); // 标记为基于Token的计费
        usageRecord.setRequestTime(requestTime);
        usageRecord.setResponseTime(responseTime);

        // 计算并记录响应时间（毫秒）
        if (requestTime != null && responseTime != null) {
            long durationMs = java.time.Duration.between(requestTime, responseTime).toMillis();
            usageRecord.setDurationMs(durationMs);
        }

        // 第八步：保存计费记录到数据库
        BillingUsageRecord savedRecord = billingUsageRecordRepository.save(usageRecord);

        // 第九步：从用户余额中扣除相应费用
        boolean deductSuccess = userBalanceService.deductBalance(userId, totalCost,
                String.format("计费扣费: MessageId=%s, Tokens=%d", messageId, usageRecord.getTotalTokens()));

        if (!deductSuccess) {
            log.error("扣除用户余额失败，计费记录已创建但余额未扣除: RecordId={}", savedRecord.getId());
            throw new IllegalStateException("扣除用户余额失败");
        }

        // 第十步：更新用户今日Token使用量统计
        userBalanceService.updateTodayTokenUsage(userId, usageRecord.getTotalTokens());

        // 第十一步：创建对应的扣费交易记录，用于财务追踪
        billingTransactionService.createDeductionTransaction(userId, totalCost,
                String.format("AI对话计费: %d tokens", usageRecord.getTotalTokens()), savedRecord.getId());

        log.info("计费操作完成: UserId={}, MessageId={}, TotalCost={}, RecordId={}",
                userId, messageId, totalCost, savedRecord.getId());

        return savedRecord;
    }

    /**
     * 异步执行计费操作
     * <p>
     * 这是计费操作的异步版本，适用于不需要立即获取计费结果的场景：
     * 1. 在后台线程中执行计费操作，不阻塞主线程
     * 2. 如果计费成功，正常完成流程
     * 3. 如果计费失败，记录错误日志并调用失败处理逻辑
     * 4. 适用于批量计费、延迟计费等场景
     * <p>
     * 注意：由于是异步执行，调用方无法直接获取计费结果，
     * 需要通过其他方式（如查询数据库）来确认计费状态
     *
     * @param userId       用户ID，不能为空
     * @param messageId    消息ID，用于关联聊天消息，不能为空
     * @param inputTokens  输入Token数量，可以为null（默认为0）
     * @param outputTokens 输出Token数量，可以为null（默认为0）
     * @param requestTime  请求开始时间，用于计算响应时长
     * @param responseTime 响应结束时间，用于计算响应时长
     */
    @Override
    @Async
    public void performBillingAsync(Long userId, UUID messageId, Long inputTokens, Long outputTokens,
            LocalDateTime requestTime, LocalDateTime responseTime) {
        try {
            // 调用同步计费方法执行实际的计费逻辑
            performBilling(userId, messageId, inputTokens, outputTokens, requestTime, responseTime);
        } catch (Exception e) {
            // 记录异步计费失败的详细信息
            log.error("异步计费失败: UserId={}, MessageId={}", userId, messageId, e);
            // 调用失败处理逻辑，记录失败原因和相关数据
            handleBillingFailure(userId, messageId, e.getMessage(), inputTokens, outputTokens);
        }
    }

    /**
     * 计费预检查
     * <p>
     * 在实际计费前检查用户是否有足够余额支付预估费用：
     * 1. 用户信息获取：获取或创建用户余额记录
     * 2. 套餐信息获取：获取用户当前使用的计费套餐
     * 3. 费用预估：根据预估Token数量计算所需费用
     * 4. 余额检查：验证用户余额是否充足
     * 5. 异常处理：捕获并记录任何异常情况
     * <p>
     * 此方法通常在AI对话开始前调用，避免在对话过程中因余额不足而中断
     *
     * @param userId                用户ID，不能为空
     * @param estimatedInputTokens  预估输入Token数量，可以为null
     * @param estimatedOutputTokens 预估输出Token数量，可以为null
     * @return true表示余额充足可以进行计费，false表示余额不足或检查失败
     */
    @Override
    @Transactional(readOnly = true)
    public boolean preCheckBilling(Long userId, Long estimatedInputTokens, Long estimatedOutputTokens) {
        try {
            // 第一步：获取用户余额信息，如果不存在则自动创建
            UserBalance userBalance = userBalanceService.getOrCreateUserBalance(userId);

            // 第二步：获取用户当前使用的计费套餐
            BillingPackage billingPackage = getBillingPackageForUser(userBalance);

            // 第三步：根据预估Token数量计算所需费用
            BigDecimal estimatedCost = calculateTokenCostWithPackage(billingPackage, estimatedInputTokens,
                    estimatedOutputTokens);

            // 第四步：检查用户余额是否充足支付预估费用
            boolean hasEnoughBalance = userBalanceService.hasEnoughBalance(userId, estimatedCost);

            log.debug("计费预检查: UserId={}, EstimatedCost={}, HasEnoughBalance={}",
                    userId, estimatedCost, hasEnoughBalance);

            return hasEnoughBalance;
        } catch (Exception e) {
            // 第五步：异常处理，记录错误并返回false确保安全
            log.error("计费预检查失败: UserId={}", userId, e);
            return false;
        }
    }

    /**
     * 计算Token费用
     * <p>
     * 根据用户当前套餐计算指定Token数量的费用：
     * 1. 用户信息获取：获取或创建用户余额记录
     * 2. 套餐信息获取：获取用户当前使用的计费套餐
     * 3. 费用计算：使用套餐价格计算总费用
     * <p>
     * 此方法用于费用预估、账单计算等场景
     *
     * @param userId       用户ID，不能为空
     * @param inputTokens  输入Token数量，可以为null（默认为0）
     * @param outputTokens 输出Token数量，可以为null（默认为0）
     * @return 计算得出的总费用
     */
    @Override
    @Transactional(readOnly = true)
    public BigDecimal calculateTokenCost(Long userId, Long inputTokens, Long outputTokens) {
        // 第一步：获取用户余额信息，如果不存在则自动创建
        UserBalance userBalance = userBalanceService.getOrCreateUserBalance(userId);

        // 第二步：获取用户当前使用的计费套餐
        BillingPackage billingPackage = getBillingPackageForUser(userBalance);

        // 第三步：使用套餐价格计算总费用
        return calculateTokenCostWithPackage(billingPackage, inputTokens, outputTokens);
    }

    /**
     * 根据消息ID获取计费记录
     * <p>
     * 通过消息ID查找对应的计费记录，用于验证消息是否已计费或获取计费详情
     *
     * @param messageId 消息ID，不能为空
     * @return 计费记录的Optional包装，如果不存在则为空
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<BillingUsageRecord> getBillingRecordByMessageId(UUID messageId) {
        return billingUsageRecordRepository.findByMessageId(messageId);
    }

    /**
     * 检查消息是否已被计费
     * <p>
     * 快速检查指定消息是否已经存在计费记录，用于防止重复计费
     *
     * @param messageId 消息ID，不能为空
     * @return true表示已计费，false表示未计费
     */
    @Override
    @Transactional(readOnly = true)
    public boolean isMessageBilled(UUID messageId) {
        return billingUsageRecordRepository.existsByMessageId(messageId);
    }

    /**
     * 获取用户计费记录（分页）
     * <p>
     * 分页查询指定用户的所有计费记录，按创建时间倒序排列
     *
     * @param userId   用户ID，不能为空
     * @param pageable 分页参数
     * @return 用户计费记录的分页结果
     */
    @Override
    @Transactional(readOnly = true)
    public Page<BillingUsageRecord> getUserBillingRecords(Long userId, Pageable pageable) {
        return billingUsageRecordRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
    }

    /**
     * 获取用户指定时间范围内的计费记录（分页）
     * <p>
     * 分页查询用户在指定时间范围内的计费记录，按创建时间倒序排列
     *
     * @param userId    用户ID，不能为空
     * @param startTime 开始时间，不能为空
     * @param endTime   结束时间，不能为空
     * @param pageable  分页参数
     * @return 指定时间范围内的计费记录分页结果
     */
    @Override
    @Transactional(readOnly = true)
    public Page<BillingUsageRecord> getUserBillingRecordsByDateRange(Long userId, LocalDateTime startTime,
            LocalDateTime endTime, Pageable pageable) {
        return billingUsageRecordRepository.findByUserIdAndCreatedAtBetweenOrderByCreatedAtDesc(
                userId, startTime, endTime, pageable);
    }

    /**
     * 获取用户Token使用统计
     * <p>
     * 统计指定用户在指定时间范围内的Token使用情况和费用支出：
     * 1. 时间范围过滤：在指定的开始时间和结束时间范围内查询
     * 2. 用户过滤：只统计指定用户的记录
     * 3. 数据汇总：计算输入Token总数、输出Token总数、总费用
     * 4. 空值处理：如果没有使用记录，各项统计值为null或0
     * <p>
     * 返回数据格式：Object[]数组，包含：
     * - [0]: 输入Token总数（Long类型）
     * - [1]: 输出Token总数（Long类型）
     * - [2]: 总费用（BigDecimal类型）
     * <p>
     * 应用场景：
     * - 用户使用情况分析
     * - 费用统计报表
     * - 套餐使用评估
     * - 用户行为分析
     *
     * @param userId    用户ID，不能为空
     * @param startTime 开始时间，不能为空
     * @param endTime   结束时间，不能为空
     * @return 包含输入Token、输出Token、总费用的统计信息数组
     */
    @Override
    @Transactional(readOnly = true)
    public Object[] getUserTokenUsageStats(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        return billingUsageRecordRepository.getUserTokenUsageStats(userId, startTime, endTime);
    }

    /**
     * 获取用户今日Token使用量
     * <p>
     * 计算指定用户今天（从00:00到23:59）的总Token使用量，
     * 用于日使用量限制检查和统计展示
     *
     * @param userId 用户ID，不能为空
     * @return 今日总Token使用量，如果没有使用记录则返回null
     */
    @Override
    @Transactional(readOnly = true)
    public Long getUserTodayTokenUsage(Long userId) {
        // 计算今天的开始和结束时间
        LocalDateTime startOfDay = LocalDateTime.now().toLocalDate().atStartOfDay();
        LocalDateTime endOfDay = startOfDay.plusDays(1);

        return billingUsageRecordRepository.getUserTotalTokensInDateRange(userId, startOfDay, endOfDay);
    }

    /**
     * 获取用户今日费用使用量
     * <p>
     * 统计指定用户从今日零点开始到当前时间的费用支出总额：
     * 1. 时间计算：获取今日零点作为统计起始时间，次日零点作为结束时间
     * 2. 数据查询：查询从今日零点到次日零点的所有计费记录
     * 3. 费用汇总：计算所有计费记录的总费用
     * 4. 空值处理：如果没有使用记录则返回null
     * <p>
     * 应用场景：
     * - 用户今日费用支出展示
     * - 每日预算控制
     * - 费用趋势分析
     * - 消费能力评估
     * - 实时费用统计
     *
     * @param userId 用户ID，不能为空
     * @return 今日总费用支出，如果没有使用记录则返回null
     */
    @Override
    @Transactional(readOnly = true)
    public BigDecimal getUserTodayCostUsage(Long userId) {
        // 计算今天的开始和结束时间
        LocalDateTime startOfDay = LocalDateTime.now().toLocalDate().atStartOfDay();
        LocalDateTime endOfDay = startOfDay.plusDays(1);

        return billingUsageRecordRepository.getUserTotalCostInDateRange(userId, startOfDay, endOfDay);
    }

    /**
     * 获取系统使用统计
     * <p>
     * 统计整个系统在指定时间范围内的使用情况，提供系统级的数据分析：
     * 1. 时间范围过滤：在指定的开始时间和结束时间范围内查询
     * 2. 全系统统计：统计所有用户的使用情况
     * 3. 多维度汇总：计算活跃用户数、Token总数、费用总额
     * 4. 数据聚合：提供系统整体的运营数据
     * <p>
     * 返回数据格式：Object[]数组，包含：
     * - [0]: 活跃用户总数（Long类型）
     * - [1]: Token使用总数（Long类型）
     * - [2]: 费用总额（BigDecimal类型）
     * <p>
     * 应用场景：
     * - 系统运营数据分析
     * - 业务增长趋势监控
     * - 收入统计报表
     * - 用户活跃度分析
     * - 系统性能评估
     *
     * @param startTime 开始时间，不能为空
     * @param endTime   结束时间，不能为空
     * @return 包含活跃用户数、Token总数、费用总额的系统统计信息数组
     */
    @Override
    @Transactional(readOnly = true)
    public Object[] getSystemUsageStats(LocalDateTime startTime, LocalDateTime endTime) {
        return billingUsageRecordRepository.getSystemUsageStats(startTime, endTime);
    }

    /**
     * 获取用户使用次数统计
     * <p>
     * 统计指定用户在指定时间范围内的计费记录总数，用于分析用户活跃度：
     * 1. 时间范围查询：在指定的开始时间和结束时间范围内查询
     * 2. 用户过滤：只统计指定用户的记录
     * 3. 计数统计：返回符合条件的记录总数
     * <p>
     * 应用场景：
     * - 用户活跃度分析
     * - 使用频率统计
     * - 用户行为分析
     * - 套餐使用情况评估
     *
     * @param userId    用户ID，不能为空
     * @param startTime 开始时间，不能为空
     * @param endTime   结束时间，不能为空
     * @return 使用次数，如果没有记录则返回0
     */
    @Override
    @Transactional(readOnly = true)
    public Long getUserUsageCount(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        return billingUsageRecordRepository.countByUserIdAndCreatedAtBetween(userId, startTime, endTime);
    }

    /**
     * 获取活跃用户列表
     * <p>
     * 查询在指定时间范围内有计费记录的所有用户ID列表：
     * 1. 时间范围过滤：在指定时间范围内查询
     * 2. 用户去重：使用DISTINCT确保每个用户ID只出现一次
     * 3. 活跃定义：有任何计费记录即视为活跃用户
     * <p>
     * 应用场景：
     * - 活跃用户统计
     * - 用户留存分析
     * - 营销活动目标用户筛选
     * - 系统使用情况监控
     *
     * @param startTime 开始时间，不能为空
     * @param endTime   结束时间，不能为空
     * @return 活跃用户ID列表，如果没有活跃用户则返回空列表
     */
    @Override
    @Transactional(readOnly = true)
    public List<Long> getActiveUsers(LocalDateTime startTime, LocalDateTime endTime) {
        return billingUsageRecordRepository.findActiveUsersInDateRange(startTime, endTime);
    }

    /**
     * 获取用户最近的计费记录
     * <p>
     * 查询指定用户最近的若干条计费记录，按创建时间倒序排列：
     * 1. 用户过滤：只查询指定用户的记录
     * 2. 时间排序：按创建时间倒序排列，最新的记录在前
     * 3. 数量限制：限制返回的记录数量
     * <p>
     * 应用场景：
     * - 用户最近使用情况展示
     * - 快速查看用户消费历史
     * - 用户行为分析
     * - 客服查询用户记录
     *
     * @param userId 用户ID，不能为空
     * @param limit  限制返回的记录数量，必须大于0
     * @return 最近的计费记录列表，按时间倒序排列
     */
    @Override
    @Transactional(readOnly = true)
    public List<BillingUsageRecord> getUserRecentBillingRecords(Long userId, int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return billingUsageRecordRepository.findTopByUserIdOrderByCreatedAtDesc(userId, pageable);
    }

    /**
     * 获取高消费计费记录
     * <p>
     * 查询费用超过指定阈值的计费记录，用于监控异常消费：
     * 1. 费用过滤：只查询总费用大于等于阈值的记录
     * 2. 费用排序：按总费用倒序排列，费用最高的在前
     * 3. 时间排序：费用相同时按创建时间倒序排列
     * 4. 分页支持：支持分页查询大量数据
     * <p>
     * 应用场景：
     * - 异常消费监控
     * - 高价值用户识别
     * - 费用异常告警
     * - 财务审计和分析
     *
     * @param costThreshold 费用阈值，不能为空且应大于0
     * @param pageable      分页参数，包含页码、页大小等信息
     * @return 高消费记录的分页结果，按费用倒序排列
     */
    @Override
    @Transactional(readOnly = true)
    public Page<BillingUsageRecord> getHighCostBillingRecords(BigDecimal costThreshold, Pageable pageable) {
        return billingUsageRecordRepository.findByTotalCostGreaterThanOrderByTotalCostDesc(costThreshold, pageable);
    }

    /**
     * 获取异常计费记录
     * <p>
     * 查询Token数量或费用异常的计费记录，用于系统监控和问题排查：
     * 1. 异常条件：Token总数超过maxTokens或总费用超过maxCost
     * 2. 多条件过滤：满足任一异常条件即被筛选出来
     * 3. 时间排序：按创建时间倒序排列，最新的异常在前
     * 4. 分页支持：支持分页查询大量异常数据
     * <p>
     * 异常情况包括：
     * - Token数量异常：可能是计算错误或恶意使用
     * - 费用异常：可能是价格配置错误或系统bug
     * - 数据异常：可能是数据损坏或非法操作
     *
     * @param maxTokens 最大Token数量阈值，超过此值视为异常
     * @param maxCost   最大费用阈值，超过此值视为异常
     * @param pageable  分页参数，包含页码、页大小等信息
     * @return 异常记录的分页结果，按时间倒序排列
     */
    @Override
    @Transactional(readOnly = true)
    public Page<BillingUsageRecord> getAbnormalBillingRecords(Long maxTokens, BigDecimal maxCost, Pageable pageable) {
        return billingUsageRecordRepository.findAbnormalRecords(maxTokens, maxCost, pageable);
    }

    /**
     * 根据计费类型统计使用情况
     * <p>
     * 按计费类型分组统计指定时间范围内的使用情况：
     * 1. 时间范围过滤：在指定时间范围内查询
     * 2. 类型分组：按billingType字段分组统计
     * 3. 多维统计：统计记录数、Token总数、费用总额
     * 4. 费用排序：按费用总额倒序排列
     * <p>
     * 返回数据格式：Object[]数组，包含：
     * - [0]: 计费类型（如TOKEN_BASED、FAILED等）
     * - [1]: 记录数量
     * - [2]: Token总数
     * - [3]: 费用总额
     *
     * @param startTime 开始时间，不能为空
     * @param endTime   结束时间，不能为空
     * @return 按计费类型分组的统计信息列表，按费用倒序排列
     */
    @Override
    @Transactional(readOnly = true)
    public List<Object[]> getUsageStatsByBillingType(LocalDateTime startTime, LocalDateTime endTime) {
        return billingUsageRecordRepository.getUsageStatsByBillingType(startTime, endTime);
    }

    /**
     * 获取用户月度使用统计
     * <p>
     * 统计指定用户在指定年月的使用情况，提供月度数据分析：
     * 1. 用户过滤：只统计指定用户的记录
     * 2. 月度过滤：使用EXTRACT函数提取年月进行过滤
     * 3. 综合统计：统计使用次数、Token总数、费用总额
     * 4. 数据汇总：提供完整的月度使用报告
     * <p>
     * 返回数据格式：Object[]数组，包含：
     * - [0]: 使用次数（记录数量）
     * - [1]: Token总数（输入+输出Token）
     * - [2]: 费用总额
     * <p>
     * 应用场景：
     * - 用户月度账单生成
     * - 用户消费趋势分析
     * - 套餐使用情况评估
     * - 财务月度报表
     * - 用户行为分析
     *
     * @param userId 用户ID，不能为空
     * @param year   年份，如2024
     * @param month  月份，1-12
     * @return 包含使用次数、Token总数、费用总额的统计信息
     */
    @Override
    @Transactional(readOnly = true)
    public Object[] getUserMonthlyStats(Long userId, int year, int month) {
        return billingUsageRecordRepository.getUserMonthlyStats(userId, year, month);
    }

    /**
     * 获取用户最高单次消费记录
     * <p>
     * 查询指定用户历史上单次消费最高的计费记录：
     * 1. 用户过滤：只查询指定用户的记录
     * 2. 费用排序：按总费用倒序排列
     * 3. 取第一条：返回费用最高的记录
     * <p>
     * 应用场景：
     * - 用户消费峰值分析
     * - 异常消费检测
     * - 用户消费能力评估
     * - 套餐推荐依据
     *
     * @param userId 用户ID，不能为空
     * @return 最高单次消费记录的Optional包装，如果用户没有消费记录则为空
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<BillingUsageRecord> getUserMaxCostRecord(Long userId) {
        return billingUsageRecordRepository.findTopByUserIdOrderByTotalCostDesc(userId);
    }

    /**
     * 获取用户消费排行榜
     * <p>
     * 查询指定时间范围内用户消费排行榜，按总消费金额排序：
     * 1. 时间范围过滤：在指定时间范围内查询
     * 2. 用户分组：按用户ID分组统计
     * 3. 费用汇总：计算每个用户的总消费金额
     * 4. 排序排名：按总消费金额倒序排列
     * 5. 分页支持：支持分页查询大量用户数据
     * <p>
     * 返回数据格式：Object[]数组，包含：
     * - [0]: 用户ID（Long类型）
     * - [1]: 总消费金额（BigDecimal类型）
     * <p>
     * 应用场景：
     * - 高价值用户识别
     * - 用户消费能力分析
     * - 营销活动目标用户筛选
     * - 用户等级划分依据
     * - VIP用户服务优化
     * - 收入贡献度分析
     *
     * @param startTime 开始时间，不能为空
     * @param endTime   结束时间，不能为空
     * @param pageable  分页参数，包含页码、页大小等信息
     * @return 用户消费排行榜的分页结果，按消费金额倒序排列
     */
    @Override
    @Transactional(readOnly = true)
    public Page<Object[]> getUserCostRanking(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable) {
        return billingUsageRecordRepository.getUserCostRanking(startTime, endTime, pageable);
    }

    /**
     * 重新计费
     * <p>
     * 对已存在的计费记录进行重新计算和调整，通常用于纠正计费错误：
     * 1. 记录验证：检查计费记录和套餐是否存在
     * 2. 费用计算：计算原费用、新费用和差额
     * 3. 余额检查：如需额外扣费，检查用户余额是否充足
     * 4. 记录更新：更新计费记录的Token数量和费用
     * 5. 余额调整：根据费用差额进行扣费或退款
     * 6. 交易记录：创建相应的交易记录用于审计
     *
     * @param recordId        计费记录ID，不能为空
     * @param newInputTokens  新的输入Token数量，可以为null
     * @param newOutputTokens 新的输出Token数量，可以为null
     * @param reason          重新计费的原因，用于记录和审计
     * @return true表示重新计费成功，false表示失败
     */
    @Override
    public boolean rebilling(Long recordId, Long newInputTokens, Long newOutputTokens, String reason) {
        log.info("重新计费: RecordId={}, NewInputTokens={}, NewOutputTokens={}, Reason={}",
                recordId, newInputTokens, newOutputTokens, reason);

        // 第一步：验证计费记录是否存在
        Optional<BillingUsageRecord> recordOpt = billingUsageRecordRepository.findById(recordId);
        if (recordOpt.isEmpty()) {
            log.warn("计费记录不存在: RecordId={}", recordId);
            return false;
        }

        BillingUsageRecord record = recordOpt.get();
        Long userId = record.getUserId();

        // 第二步：获取并验证套餐信息
        Optional<BillingPackage> packageOpt = billingPackageRepository.findById(record.getPackageId());
        if (packageOpt.isEmpty()) {
            log.warn("套餐不存在: PackageId={}", record.getPackageId());
            return false;
        }

        BillingPackage billingPackage = packageOpt.get();

        // 第三步：计算原费用、新费用和差额
        BigDecimal oldCost = record.getTotalCost();
        BigDecimal newCost = calculateTokenCostWithPackage(billingPackage, newInputTokens, newOutputTokens);
        BigDecimal costDifference = newCost.subtract(oldCost);

        // 第四步：如果需要额外扣费，检查余额是否充足
        if (costDifference.compareTo(BigDecimal.ZERO) > 0) {
            if (!userBalanceService.hasEnoughBalance(userId, costDifference)) {
                log.warn("重新计费余额不足: UserId={}, RequiredAmount={}", userId, costDifference);
                return false;
            }
        }

        // 第五步：更新计费记录的Token数量和费用
        record.setInputTokens(newInputTokens != null ? newInputTokens : 0L);
        record.setOutputTokens(newOutputTokens != null ? newOutputTokens : 0L);
        record.setTotalTokens(
                (newInputTokens != null ? newInputTokens : 0L) + (newOutputTokens != null ? newOutputTokens : 0L));
        record.setTotalCost(newCost);
        billingUsageRecordRepository.save(record);

        // 第六步：根据费用差额进行余额调整和交易记录
        if (costDifference.compareTo(BigDecimal.ZERO) > 0) {
            // 需要额外扣费
            userBalanceService.deductBalance(userId, costDifference,
                    String.format("重新计费额外扣费: RecordId=%d, Reason=%s", recordId, reason));
            billingTransactionService.createDeductionTransaction(userId, costDifference,
                    String.format("重新计费额外扣费: %s", reason), recordId);
        } else if (costDifference.compareTo(BigDecimal.ZERO) < 0) {
            // 需要退还费用
            BigDecimal refundAmount = costDifference.abs();
            userBalanceService.giftBalance(userId, refundAmount,
                    String.format("重新计费退款: RecordId=%d, Reason=%s", recordId, reason));
            billingTransactionService.createRefundTransaction(userId, refundAmount,
                    String.format("重新计费退款: %s", reason), recordId);
        }

        log.info("重新计费完成: RecordId={}, OldCost={}, NewCost={}, CostDifference={}",
                recordId, oldCost, newCost, costDifference);
        return true;
    }

    /**
     * 撤销计费
     * <p>
     * 完全撤销一条计费记录，包括退还费用和删除记录：
     * 1. 记录验证：检查计费记录是否存在
     * 2. 费用退还：将计费金额以赠送余额的形式退还给用户
     * 3. 交易记录：创建退款交易记录用于审计追踪
     * 4. 记录删除：从数据库中删除原计费记录
     * 5. 日志记录：记录撤销操作的详细信息
     * <p>
     * 注意事项：
     * - 撤销操作不可逆，请谨慎使用
     * - 退还的费用以赠送余额形式返还
     * - 会创建相应的交易记录用于财务追踪
     * - 适用于错误计费、系统故障等情况
     * <p>
     * 应用场景：
     * - 错误计费的纠正
     * - 系统故障导致的重复计费
     * - 用户申诉成功后的退费
     * - 管理员手动调整
     *
     * @param recordId 计费记录ID，不能为空
     * @param reason   撤销原因，用于记录和审计
     * @return true表示撤销成功，false表示记录不存在或撤销失败
     */
    @Override
    public boolean reverseBilling(Long recordId, String reason) {
        log.info("撤销计费: RecordId={}, Reason={}", recordId, reason);

        // 第一步：验证计费记录是否存在
        Optional<BillingUsageRecord> recordOpt = billingUsageRecordRepository.findById(recordId);
        if (recordOpt.isEmpty()) {
            log.warn("计费记录不存在: RecordId={}", recordId);
            return false;
        }

        // 第二步：获取记录信息并计算退款金额
        BillingUsageRecord record = recordOpt.get();
        Long userId = record.getUserId();
        BigDecimal refundAmount = record.getTotalCost();

        // 第三步：退还费用（以赠送余额形式）
        userBalanceService.giftBalance(userId, refundAmount,
                String.format("撤销计费退款: RecordId=%d, Reason=%s", recordId, reason));

        // 第四步：创建退款交易记录用于审计
        billingTransactionService.createRefundTransaction(userId, refundAmount,
                String.format("撤销计费退款: %s", reason), recordId);

        // 第五步：删除原计费记录
        billingUsageRecordRepository.deleteById(recordId);

        log.info("撤销计费完成: RecordId={}, RefundAmount={}", recordId, refundAmount);
        return true;
    }

    /**
     * 批量删除历史计费记录
     * <p>
     * 删除指定时间之前的历史计费记录，用于数据清理和存储优化：
     * 1. 时间过滤：删除创建时间早于指定时间的记录
     * 2. 批量操作：使用数据库批量删除提高效率
     * 3. 计数统计：返回实际删除的记录数量
     * 4. 日志记录：记录删除操作的详细信息
     * <p>
     * 注意事项：
     * - 删除操作不可逆，请谨慎使用
     * - 建议在删除前先备份重要数据
     * - 删除历史记录可能影响统计分析
     * - 建议在系统维护期间执行
     * <p>
     * 应用场景：
     * - 定期数据清理
     * - 存储空间优化
     * - 合规性要求（数据保留期限）
     * - 系统性能优化
     *
     * @param beforeTime 时间阈值，早于此时间的记录将被删除
     * @return 实际删除的记录数量
     */
    @Override
    public int deleteHistoryBillingRecords(LocalDateTime beforeTime) {
        log.info("删除历史计费记录: BeforeTime={}", beforeTime);

        // 执行批量删除操作
        int deletedCount = billingUsageRecordRepository.deleteByCreatedAtBefore(beforeTime);

        log.info("删除历史计费记录完成: 删除数量={}", deletedCount);
        return deletedCount;
    }

    /**
     * 验证计费参数的有效性
     * <p>
     * 对计费操作的输入参数进行全面验证，确保数据的合法性：
     * 1. 必填参数检查：验证用户ID和消息ID不能为空
     * 2. Token数量检查：确保至少有一种Token数量不为空
     * 3. 数值范围检查：验证Token数量不能为负数
     * 4. 业务逻辑检查：确保参数符合业务规则
     * <p>
     * 验证规则：
     * - 用户ID：必须提供，用于标识计费对象
     * - 消息ID：必须提供，用于关联聊天消息
     * - Token数量：输入和输出Token不能同时为空
     * - 数值有效性：Token数量不能为负数
     *
     * @param userId       用户ID，不能为空
     * @param messageId    消息ID，不能为空
     * @param inputTokens  输入Token数量，可以为null但不能为负数
     * @param outputTokens 输出Token数量，可以为null但不能为负数
     * @return 验证结果消息，null表示验证通过，非null表示验证失败的具体原因
     */
    @Override
    @Transactional(readOnly = true)
    public String validateBillingParameters(Long userId, UUID messageId, Long inputTokens, Long outputTokens) {
        // 第一步：验证用户ID
        if (userId == null) {
            return "用户ID不能为空";
        }

        // 第二步：验证消息ID
        if (messageId == null) {
            return "消息ID不能为空";
        }

        // 第三步：验证Token数量不能同时为空
        if (inputTokens == null && outputTokens == null) {
            return "输入Token和输出Token不能同时为空";
        }

        // 第四步：验证输入Token数量的有效性
        if (inputTokens != null && inputTokens < 0) {
            return "输入Token数量不能为负数";
        }

        // 第五步：验证输出Token数量的有效性
        if (outputTokens != null && outputTokens < 0) {
            return "输出Token数量不能为负数";
        }

        return null; // 验证通过
    }

    /**
     * 获取计费配置信息
     * <p>
     * 获取指定用户的完整计费配置信息，包括套餐、余额、价格等：
     * 1. 用户信息获取：获取或创建用户余额记录
     * 2. 套餐信息获取：获取用户当前使用的计费套餐
     * 3. 配置信息组装：将各种配置信息组装成统一的对象
     * 4. 实时数据获取：获取最新的余额和使用情况
     * <p>
     * 返回的配置信息包括：
     * - 用户基本信息：用户ID
     * - 套餐信息：套餐ID、名称、价格
     * - 余额信息：总余额、免费Token、今日使用量
     * - 状态信息：账户冻结状态
     * <p>
     * 应用场景：
     * - 前端页面展示用户计费信息
     * - 计费前的配置检查
     * - 用户账户状态查询
     * - 客服查询用户信息
     *
     * @param userId 用户ID，不能为空
     * @return 包含完整计费配置信息的对象
     */
    @Override
    @Transactional(readOnly = true)
    public Object getBillingConfiguration(Long userId) {
        // 第一步：获取用户余额信息
        UserBalance userBalance = userBalanceService.getOrCreateUserBalance(userId);

        // 第二步：获取用户当前套餐信息
        BillingPackage billingPackage = getBillingPackageForUser(userBalance);

        // 第三步：组装配置信息对象
        return new Object() {
            public final Long userId = userBalance.getUserId();
            public final Long packageId = billingPackage.getId();
            public final String packageName = billingPackage.getName();
            public final BigDecimal inputTokenPrice = billingPackage.getInputTokenPrice();
            public final BigDecimal outputTokenPrice = billingPackage.getOutputTokenPrice();
            public final BigDecimal totalBalance = userBalanceService.getTotalBalance(userId);
            public final Long freeTokens = userBalance.getFreeTokens();
            public final Long todayTokenUsage = userBalance.getUsedTokensToday();
            public final Boolean isFrozen = userBalance.getIsFrozen();
        };
    }

    /**
     * 根据消息ID列表获取计费使用记录
     * <p>
     * 批量查询多个消息的计费使用记录，主要用于轮次Token用量统计功能。
     * 该方法支持查询多个消息的计费记录，用于计算整个轮次的Token用量和费用。
     * <p>
     * 应用场景：
     * - 轮次Token用量统计
     * - 批量查询计费记录
     * - 对话费用分析
     * - Token使用报表生成
     *
     * @param messageIds 消息ID列表，不能为空
     * @return 对应的计费使用记录列表，按创建时间升序排列
     */
    @Override
    @Transactional(readOnly = true)
    public List<BillingUsageRecord> getUsageRecordsByMessageIds(List<UUID> messageIds) {
        log.debug("根据消息ID列表获取计费使用记录: messageIds.size={}", messageIds.size());

        if (messageIds == null || messageIds.isEmpty()) {
            log.warn("消息ID列表为空，返回空结果");
            return new ArrayList<>();
        }

        // 使用Repository的findByMessageIdIn方法批量查询
        List<BillingUsageRecord> records = billingUsageRecordRepository.findByMessageIdIn(messageIds);

        log.debug("查询到计费记录数量: {}", records.size());
        return records;
    }

    /**
     * 计费失败处理
     * <p>
     * 处理计费过程中发生的各种失败情况，记录失败信息并执行补偿逻辑：
     * 1. 失败日志记录：详细记录失败的原因和相关参数
     * 2. 失败记录创建：创建标记为失败的计费记录用于追踪
     * 3. 补偿逻辑执行：可扩展的失败补偿机制
     * 4. 异常处理：确保失败处理本身不会导致系统异常
     * <p>
     * 失败记录特点：
     * - 状态标记为FAILED
     * - 费用设置为0（不扣费）
     * - 保留原始Token数量信息
     * - 记录失败时间
     * <p>
     * 应用场景：
     * - 网络异常导致的计费失败
     * - 数据库异常导致的计费失败
     * - 业务逻辑异常导致的计费失败
     * - 第三方服务异常导致的计费失败
     *
     * @param userId       用户ID，不能为空
     * @param messageId    消息ID，不能为空
     * @param errorMessage 错误信息，描述失败原因
     * @param inputTokens  输入Token数量，可能为null
     * @param outputTokens 输出Token数量，可能为null
     */
    @Override
    public void handleBillingFailure(Long userId, UUID messageId, String errorMessage, Long inputTokens,
            Long outputTokens) {
        log.error("计费失败处理: UserId={}, MessageId={}, Error={}, InputTokens={}, OutputTokens={}",
                userId, messageId, errorMessage, inputTokens, outputTokens);

        // 可以在这里实现计费失败的补偿逻辑
        // 例如：记录失败日志、发送告警、重试机制等

        // 创建失败记录（可选）
        try {
            // 第一步：创建失败记录对象
            BillingUsageRecord failureRecord = new BillingUsageRecord();
            failureRecord.setUserId(userId);
            failureRecord.setMessageId(messageId);
            failureRecord.setInputTokens(inputTokens != null ? inputTokens : 0L);
            failureRecord.setOutputTokens(outputTokens != null ? outputTokens : 0L);
            failureRecord.setTotalTokens(
                    (inputTokens != null ? inputTokens : 0L) + (outputTokens != null ? outputTokens : 0L));
            failureRecord.setTotalCost(BigDecimal.ZERO); // 失败记录不产生费用
            failureRecord.setBillingType("FAILED"); // 标记为失败类型
            failureRecord.setRequestTime(LocalDateTime.now());
            failureRecord.setResponseTime(LocalDateTime.now());

            // 第二步：保存失败记录到数据库
            billingUsageRecordRepository.save(failureRecord);
        } catch (Exception e) {
            // 第三步：异常处理，确保失败处理本身不会导致系统异常
            log.error("保存计费失败记录时发生异常", e);
        }
    }

    /**
     * 获取用户的计费套餐
     * <p>
     * 根据用户余额记录获取对应的计费套餐，支持套餐降级处理：
     * 1. 套餐ID获取：从用户余额记录中获取套餐ID
     * 2. 套餐查询：根据套餐ID查询具体的套餐信息
     * 3. 有效性检查：验证套餐是否存在且有效
     * 4. 降级处理：如果用户套餐无效，自动使用系统默认套餐
     * 5. 异常处理：如果连默认套餐都不存在，抛出异常
     * <p>
     * 套餐选择优先级：
     * 1. 用户指定的套餐（如果存在且有效）
     * 2. 系统默认套餐（作为降级选择）
     * 3. 抛出异常（如果没有任何可用套餐）
     *
     * @param userBalance 用户余额记录，包含套餐ID信息
     * @return 有效的计费套餐对象
     * @throws IllegalStateException 当没有可用的计费套餐时抛出
     */
    private BillingPackage getBillingPackageForUser(UserBalance userBalance) {
        // 第一步：获取用户指定的套餐ID
        Long packageId = userBalance.getPackageId();

        // 第二步：如果用户有指定套餐，尝试查询该套餐
        if (packageId != null) {
            Optional<BillingPackage> packageOpt = billingPackageRepository.findById(packageId);
            if (packageOpt.isPresent()) {
                // 第三步：如果用户套餐存在且有效，直接返回
                return packageOpt.get();
            }
        }

        // 第四步：如果用户没有指定套餐或套餐不存在，使用默认套餐
        return billingPackageRepository.findDefaultPackage()
                .orElseThrow(() -> new IllegalStateException("没有可用的计费套餐"));
    }

    /**
     * 使用指定套餐计算Token费用
     * <p>
     * 根据套餐价格和Token数量计算总费用，支持精确的小数计算：
     * 1. 输入费用计算：输入Token数量 × 输入Token单价 ÷ 1000
     * 2. 输出费用计算：输出Token数量 × 输出Token单价 ÷ 1000
     * 3. 总费用计算：输入费用 + 输出费用
     * 4. 精度控制：使用6位小数精度，四舍五入模式
     * 5. 空值处理：Token数量为null时按0处理
     * <p>
     * 计费规则说明：
     * - 套餐价格是每千Token的价格
     * - 实际费用 = Token数量 × 单价 ÷ 1000
     * - 输入Token和输出Token分别计费
     * - 最终费用保留6位小数
     *
     * @param billingPackage 计费套餐，包含输入输出Token的单价
     * @param inputTokens    输入Token数量，可以为null（按0处理）
     * @param outputTokens   输出Token数量，可以为null（按0处理）
     * @return 计算得出的总费用，精确到6位小数
     */
    private BigDecimal calculateTokenCostWithPackage(BillingPackage billingPackage, Long inputTokens,
            Long outputTokens) {
        // 第一步：计算输入Token费用
        BigDecimal inputCost = billingPackage.getInputTokenPrice()
                .multiply(BigDecimal.valueOf(inputTokens != null ? inputTokens : 0L))
                .divide(BigDecimal.valueOf(1000), 6, RoundingMode.HALF_UP); // 价格是每千Token

        // 第二步：计算输出Token费用
        BigDecimal outputCost = billingPackage.getOutputTokenPrice()
                .multiply(BigDecimal.valueOf(outputTokens != null ? outputTokens : 0L))
                .divide(BigDecimal.valueOf(1000), 6, RoundingMode.HALF_UP); // 价格是每千Token

        // 第三步：计算总费用
        return inputCost.add(outputCost);
    }
}