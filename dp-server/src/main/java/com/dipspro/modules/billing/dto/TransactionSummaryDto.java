package com.dipspro.modules.billing.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 交易统计DTO
 * 
 * 用于返回交易统计信息，包含各种交易数据的汇总和分析。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionSummaryDto {

    /**
     * 统计周期
     */
    private String period;

    /**
     * 统计开始日期
     */
    private LocalDate startDate;

    /**
     * 统计结束日期
     */
    private LocalDate endDate;

    /**
     * 交易总笔数
     */
    private Long totalTransactions;

    /**
     * 成功交易笔数
     */
    private Long successTransactions;

    /**
     * 失败交易笔数
     */
    private Long failedTransactions;

    /**
     * 总交易金额
     */
    private BigDecimal totalAmount;

    /**
     * 充值总金额
     */
    private BigDecimal totalRechargeAmount;

    /**
     * 扣费总金额
     */
    private BigDecimal totalDeductionAmount;

    /**
     * 退款总金额
     */
    private BigDecimal totalRefundAmount;

    /**
     * 充值笔数
     */
    private Long rechargeCount;

    /**
     * 扣费笔数
     */
    private Long deductionCount;

    /**
     * 退款笔数
     */
    private Long refundCount;

    /**
     * 平均交易金额
     */
    private BigDecimal averageTransactionAmount;

    /**
     * 最大交易金额
     */
    private BigDecimal maxTransactionAmount;

    /**
     * 最小交易金额
     */
    private BigDecimal minTransactionAmount;

    /**
     * 交易成功率（百分比）
     */
    private Double successRate;

    /**
     * 日均交易笔数
     */
    private Double dailyAverageTransactions;

    /**
     * 日均交易金额
     */
    private BigDecimal dailyAverageAmount;

    /**
     * 每日交易统计列表
     */
    private List<DailyTransactionStat> dailyStats;

    /**
     * 每月交易统计列表
     */
    private List<MonthlyTransactionStat> monthlyStats;

    /**
     * 获取格式化的总交易金额
     * 
     * @return 格式化的金额字符串
     */
    public String getFormattedTotalAmount() {
        return totalAmount != null ? "¥" + totalAmount.toString() : "¥0.00";
    }

    /**
     * 获取格式化的充值总金额
     * 
     * @return 格式化的金额字符串
     */
    public String getFormattedTotalRechargeAmount() {
        return totalRechargeAmount != null ? "¥" + totalRechargeAmount.toString() : "¥0.00";
    }

    /**
     * 获取格式化的扣费总金额
     * 
     * @return 格式化的金额字符串
     */
    public String getFormattedTotalDeductionAmount() {
        return totalDeductionAmount != null ? "¥" + totalDeductionAmount.toString() : "¥0.00";
    }

    /**
     * 获取格式化的退款总金额
     * 
     * @return 格式化的金额字符串
     */
    public String getFormattedTotalRefundAmount() {
        return totalRefundAmount != null ? "¥" + totalRefundAmount.toString() : "¥0.00";
    }

    /**
     * 获取格式化的平均交易金额
     * 
     * @return 格式化的金额字符串
     */
    public String getFormattedAverageTransactionAmount() {
        return averageTransactionAmount != null ? "¥" + averageTransactionAmount.toString() : "¥0.00";
    }

    /**
     * 获取格式化的成功率
     * 
     * @return 格式化的成功率字符串
     */
    public String getFormattedSuccessRate() {
        return successRate != null ? String.format("%.2f%%", successRate) : "0.00%";
    }

    /**
     * 获取净收入（充值 - 退款）
     * 
     * @return 净收入金额
     */
    public BigDecimal getNetIncome() {
        BigDecimal recharge = totalRechargeAmount != null ? totalRechargeAmount : BigDecimal.ZERO;
        BigDecimal refund = totalRefundAmount != null ? totalRefundAmount : BigDecimal.ZERO;
        return recharge.subtract(refund);
    }

    /**
     * 获取格式化的净收入
     * 
     * @return 格式化的净收入字符串
     */
    public String getFormattedNetIncome() {
        BigDecimal netIncome = getNetIncome();
        String prefix = netIncome.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "";
        return prefix + "¥" + netIncome.toString();
    }

    /**
     * 获取统计周期显示文本
     * 
     * @return 周期显示文本
     */
    public String getPeriodDisplay() {
        if (period == null) {
            return "未知周期";
        }

        return switch (period) {
            case "DAY" -> "日";
            case "WEEK" -> "周";
            case "MONTH" -> "月";
            case "QUARTER" -> "季度";
            case "YEAR" -> "年";
            default -> period;
        };
    }

    /**
     * 获取统计摘要信息
     * 
     * @return 统计摘要
     */
    public String getSummaryInfo() {
        StringBuilder summary = new StringBuilder();
        summary.append("统计周期: ").append(getPeriodDisplay()).append(" | ");
        summary.append("总交易: ").append(totalTransactions != null ? totalTransactions : 0).append("笔 | ");
        summary.append("总金额: ").append(getFormattedTotalAmount()).append(" | ");
        summary.append("成功率: ").append(getFormattedSuccessRate());

        return summary.toString();
    }

    /**
     * 检查是否有交易数据
     * 
     * @return 是否有数据
     */
    public boolean hasTransactionData() {
        return totalTransactions != null && totalTransactions > 0;
    }

    /**
     * 获取交易类型分布信息
     * 
     * @return 交易类型分布
     */
    public String getTransactionTypeDistribution() {
        if (!hasTransactionData()) {
            return "无交易数据";
        }

        StringBuilder distribution = new StringBuilder();

        if (rechargeCount != null && rechargeCount > 0) {
            double rechargePercent = (double) rechargeCount / totalTransactions * 100;
            distribution.append(String.format("充值: %d笔(%.1f%%) ", rechargeCount, rechargePercent));
        }

        if (deductionCount != null && deductionCount > 0) {
            double deductionPercent = (double) deductionCount / totalTransactions * 100;
            distribution.append(String.format("扣费: %d笔(%.1f%%) ", deductionCount, deductionPercent));
        }

        if (refundCount != null && refundCount > 0) {
            double refundPercent = (double) refundCount / totalTransactions * 100;
            distribution.append(String.format("退款: %d笔(%.1f%%) ", refundCount, refundPercent));
        }

        return distribution.toString().trim();
    }

    /**
     * 日交易统计内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DailyTransactionStat {
        private LocalDate date;
        private Long transactionCount;
        private BigDecimal totalAmount;
        private Long successCount;
        private Long failedCount;

        public String getFormattedTotalAmount() {
            return totalAmount != null ? "¥" + totalAmount.toString() : "¥0.00";
        }

        public Double getSuccessRate() {
            if (transactionCount == null || transactionCount == 0) {
                return 0.0;
            }
            return (double) (successCount != null ? successCount : 0) / transactionCount * 100;
        }
    }

    /**
     * 月交易统计内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MonthlyTransactionStat {
        private String month;
        private Long transactionCount;
        private BigDecimal totalAmount;
        private BigDecimal rechargeAmount;
        private BigDecimal deductionAmount;
        private BigDecimal refundAmount;

        public String getFormattedTotalAmount() {
            return totalAmount != null ? "¥" + totalAmount.toString() : "¥0.00";
        }

        public BigDecimal getNetIncome() {
            BigDecimal recharge = rechargeAmount != null ? rechargeAmount : BigDecimal.ZERO;
            BigDecimal refund = refundAmount != null ? refundAmount : BigDecimal.ZERO;
            return recharge.subtract(refund);
        }
    }
}