package com.dipspro.modules.billing.dto;

import java.math.BigDecimal;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 充值请求DTO
 * 
 * 用于用户发起充值时的请求参数，包含充值金额、
 * 支付方式等必要信息。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RechargeRequestDto {

    /**
     * 充值金额
     * 必须大于0，最小充值金额为0.01元
     */
    @NotNull(message = "充值金额不能为空")
    @DecimalMin(value = "0.01", message = "充值金额不能小于0.01元")
    private BigDecimal amount;

    /**
     * 支付方式
     * 支持的支付方式：WECHAT(微信支付)、ALIPAY(支付宝)
     */
    @NotBlank(message = "支付方式不能为空")
    private String paymentMethod;

    /**
     * 客户端类型
     * 用于区分不同的客户端：WEB、MOBILE、APP等
     */
    private String clientType;

    /**
     * 回调地址
     * 支付完成后的回调地址，可选
     */
    private String callbackUrl;

    /**
     * 备注信息
     * 用户备注或特殊说明，可选
     */
    private String remark;

    /**
     * 检查支付方式是否有效
     * 
     * @return 是否有效
     */
    public boolean isValidPaymentMethod() {
        return "WECHAT".equals(paymentMethod) || "ALIPAY".equals(paymentMethod);
    }

    /**
     * 格式化金额显示
     * 
     * @return 格式化后的金额字符串
     */
    public String getFormattedAmount() {
        return amount != null ? "¥" + amount.toString() : "¥0.00";
    }
}