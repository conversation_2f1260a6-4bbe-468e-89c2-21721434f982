package com.dipspro.modules.billing.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 使用统计DTO
 * 
 * 用于返回用户在指定时间周期内的Token使用统计数据，
 * 包括总量、费用、趋势分析等信息。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class UsageStatisticsDto {

    /**
     * 统计周期：day, week, month, year
     */
    private String period;

    /**
     * 统计开始时间
     */
    private LocalDateTime startTime;

    /**
     * 统计结束时间
     */
    private LocalDateTime endTime;

    /**
     * 总Token消费量
     */
    private Long totalTokens;

    /**
     * 输入Token总数
     */
    private Long totalInputTokens;

    /**
     * 输出Token总数
     */
    private Long totalOutputTokens;

    /**
     * 思维链Token总数
     */
    private Long totalThoughtChainTokens;

    /**
     * 总费用支出
     */
    private BigDecimal totalCost;

    /**
     * 输入费用总计
     */
    private BigDecimal totalInputCost;

    /**
     * 输出费用总计
     */
    private BigDecimal totalOutputCost;

    /**
     * 对话次数统计
     */
    private Long conversationCount;

    /**
     * 成功对话次数
     */
    private Long successfulConversationCount;

    /**
     * 失败对话次数
     */
    private Long failedConversationCount;

    /**
     * 平均每次对话Token消费
     */
    private Double averageTokensPerConversation;

    /**
     * 平均每次对话费用
     */
    private BigDecimal averageCostPerConversation;

    /**
     * 最高单次消费Token数
     */
    private Long maxTokensPerConversation;

    /**
     * 最高单次消费金额
     */
    private BigDecimal maxCostPerConversation;

    /**
     * 日度趋势数据
     */
    private List<DailyUsageData> dailyTrend;

    /**
     * 模型使用分布
     */
    private List<ModelUsageData> modelDistribution;

    /**
     * 获取成功率
     * 
     * @return 成功率百分比
     */
    public Double getSuccessRate() {
        if (conversationCount == null || conversationCount == 0) {
            return 0.0;
        }
        double successCount = successfulConversationCount != null ? successfulConversationCount : 0.0;
        return (successCount / conversationCount) * 100;
    }

    /**
     * 格式化总费用显示
     * 
     * @return 格式化的费用字符串
     */
    public String getFormattedTotalCost() {
        return totalCost != null ? "¥" + totalCost.toString() : "¥0.00";
    }

    /**
     * 格式化总Token显示
     * 
     * @return 格式化的Token数量字符串
     */
    public String getFormattedTotalTokens() {
        if (totalTokens == null || totalTokens == 0) {
            return "0 tokens";
        }

        long tokens = totalTokens;
        if (tokens < 1000) {
            return tokens + " tokens";
        } else if (tokens < 1000000) {
            return String.format("%.1fK tokens", tokens / 1000.0);
        } else {
            return String.format("%.1fM tokens", tokens / 1000000.0);
        }
    }

    /**
     * 获取周期显示文本
     * 
     * @return 周期显示文本
     */
    public String getPeriodDisplay() {
        switch (period) {
            case "day":
                return "今日";
            case "week":
                return "本周";
            case "month":
                return "本月";
            case "year":
                return "今年";
            default:
                return period;
        }
    }

    /**
     * 日度使用数据
     */
    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DailyUsageData {
        /**
         * 日期
         */
        private String date;

        /**
         * 当日Token消费量
         */
        private Long tokens;

        /**
         * 当日费用
         */
        private BigDecimal cost;

        /**
         * 当日对话次数
         */
        private Long conversations;
    }

    /**
     * 模型使用数据
     */
    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ModelUsageData {
        /**
         * 模型名称
         */
        private String modelName;

        /**
         * 使用次数
         */
        private Long usageCount;

        /**
         * Token消费量
         */
        private Long tokens;

        /**
         * 费用支出
         */
        private BigDecimal cost;

        /**
         * 使用占比
         */
        private Double percentage;
    }
}