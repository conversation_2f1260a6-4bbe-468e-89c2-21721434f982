package com.dipspro.modules.billing.service.impl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dipspro.modules.billing.entity.BillingAppeal;
import com.dipspro.modules.billing.entity.BillingUsageRecord;
import com.dipspro.modules.billing.repository.BillingAppealRepository;
import com.dipspro.modules.billing.repository.BillingUsageRecordRepository;
import com.dipspro.modules.billing.service.BillingAppealService;
import com.dipspro.modules.billing.service.BillingService;
import com.dipspro.modules.billing.service.UserBalanceService;
import com.dipspro.util.BillingUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 计费申诉服务实现类
 * 
 * 提供完整的申诉管理功能，包括：
 * - 申诉创建和验证
 * - 申诉处理和审核
 * - 申诉查询和统计
 * - 申诉状态管理
 * - 申诉数据分析
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class BillingAppealServiceImpl implements BillingAppealService {

    private final BillingAppealRepository billingAppealRepository;
    private final BillingUsageRecordRepository billingUsageRecordRepository;
    private final BillingService billingService;
    private final UserBalanceService userBalanceService;

    // 申诉状态常量
    private static final String STATUS_PENDING = "PENDING";
    private static final String STATUS_APPROVED = "APPROVED";
    private static final String STATUS_REJECTED = "REJECTED";
    private static final String STATUS_CANCELLED = "CANCELLED";

    // 支持的申诉状态列表
    private static final List<String> SUPPORTED_STATUSES = Arrays.asList(
            STATUS_PENDING, STATUS_APPROVED, STATUS_REJECTED, STATUS_CANCELLED);

    // 常见申诉原因列表
    private static final List<String> COMMON_REASONS = Arrays.asList(
            "计费错误", "系统故障", "服务质量问题", "重复扣费", "其他原因");

    /**
     * 创建申诉记录
     * 
     * 用户对使用记录有异议时，可以通过此方法创建申诉：
     * 1. 参数验证：检查用户ID、使用记录ID、申诉原因等必要参数
     * 2. 权限验证：确保用户只能申诉自己的使用记录
     * 3. 重复检查：防止对同一使用记录创建多个申诉
     * 4. 记录创建：创建完整的申诉记录，包含申诉详情
     * 5. 状态设置：初始状态设置为PENDING（待处理）
     * 6. 申诉单号：自动生成唯一的申诉单号
     * 
     * @param userId        用户ID，不能为空
     * @param usageRecordId 使用记录ID，不能为空
     * @param appealReason  申诉原因，不能为空
     * @param appealAmount  申诉金额，不能为空且必须大于0
     * @param description   详细描述，可以为空
     * @return 创建的申诉记录
     * @throws IllegalArgumentException 当参数验证失败或权限不足时抛出
     */
    @Override
    public BillingAppeal createAppeal(Long userId, Long usageRecordId, String appealReason,
            BigDecimal appealAmount, String description) {
        log.info("创建申诉记录: UserId={}, UsageRecordId={}, AppealReason={}, AppealAmount={}",
                userId, usageRecordId, appealReason, appealAmount);

        // 第一步：验证必要参数的有效性
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (usageRecordId == null) {
            throw new IllegalArgumentException("使用记录ID不能为空");
        }
        if (appealReason == null || appealReason.trim().isEmpty()) {
            throw new IllegalArgumentException("申诉原因不能为空");
        }
        if (appealAmount == null || appealAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("申诉金额必须大于0");
        }

        // 第二步：验证使用记录是否存在
        Optional<BillingUsageRecord> recordOpt = billingUsageRecordRepository.findById(usageRecordId);
        if (recordOpt.isEmpty()) {
            throw new IllegalArgumentException("使用记录不存在");
        }

        BillingUsageRecord usageRecord = recordOpt.get();

        // 第三步：验证用户是否有权限申诉该记录（只能申诉自己的使用记录）
        if (!usageRecord.getUserId().equals(userId)) {
            throw new IllegalArgumentException("无权限申诉该使用记录");
        }

        // 第四步：检查是否已存在申诉，防止重复申诉
        boolean hasAppeal = billingAppealRepository.existsByUsageRecordId(usageRecordId);
        if (hasAppeal) {
            throw new IllegalArgumentException("该使用记录已存在申诉");
        }

        // 第五步：创建申诉记录对象并设置所有必要字段
        BillingAppeal appeal = new BillingAppeal();
        appeal.setAppealNo(BillingUtil.generateAppealNo());
        appeal.setUserId(userId);
        appeal.setUsageRecordId(usageRecordId);
        appeal.setReason(appealReason);
        appeal.setUserDescription(description);
        appeal.setStatus(STATUS_PENDING); // 初始状态为待处理

        // 第六步：保存申诉记录到数据库
        BillingAppeal savedAppeal = billingAppealRepository.save(appeal);

        log.info("申诉记录创建成功: AppealId={}, AppealNo={}", savedAppeal.getId(), savedAppeal.getAppealNo());
        return savedAppeal;
    }

    /**
     * 根据ID查询申诉记录
     * 
     * 通过申诉ID查找对应的申诉详情，用于申诉查询和验证
     * 
     * @param id 申诉ID，不能为空
     * @return 申诉记录的Optional包装，如果不存在则为空
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<BillingAppeal> getAppealById(Long id) {
        log.debug("根据ID查询申诉记录: AppealId={}", id);
        return billingAppealRepository.findById(id);
    }

    /**
     * 根据使用记录ID查询申诉记录
     * 
     * 通过使用记录ID查找对应的申诉记录，用于检查是否已申诉
     * 
     * @param usageRecordId 使用记录ID，不能为空
     * @return 申诉记录的Optional包装，如果不存在则为空
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<BillingAppeal> getAppealByUsageRecordId(Long usageRecordId) {
        log.debug("根据使用记录ID查询申诉记录: UsageRecordId={}", usageRecordId);
        return billingAppealRepository.findByUsageRecordId(usageRecordId);
    }

    /**
     * 检查使用记录是否已有申诉
     * 
     * 检查指定的使用记录是否已经存在申诉，用于防止重复申诉
     * 
     * @param usageRecordId 使用记录ID，不能为空
     * @return 是否已有申诉，true表示已存在申诉，false表示未申诉
     */
    @Override
    @Transactional(readOnly = true)
    public boolean hasAppealForUsageRecord(Long usageRecordId) {
        log.debug("检查使用记录是否已有申诉: UsageRecordId={}", usageRecordId);
        return billingAppealRepository.existsByUsageRecordId(usageRecordId);
    }

    /**
     * 查询用户申诉记录
     * 
     * 分页查询指定用户的所有申诉记录，按创建时间倒序排列，
     * 用于用户查看自己的申诉历史
     * 
     * @param userId   用户ID，不能为空
     * @param pageable 分页参数
     * @return 用户申诉记录的分页结果
     */
    @Override
    @Transactional(readOnly = true)
    public Page<BillingAppeal> getUserAppeals(Long userId, Pageable pageable) {
        log.debug("查询用户申诉记录: UserId={}, Page={}, Size={}", userId, pageable.getPageNumber(), pageable.getPageSize());
        return billingAppealRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
    }

    /**
     * 根据申诉状态查询申诉记录
     * 
     * 分页查询系统中指定状态的所有申诉记录，按创建时间倒序排列，
     * 用于管理员查看和处理申诉
     * 
     * @param status   申诉状态，不能为空
     * @param pageable 分页参数
     * @return 指定状态的申诉记录分页结果
     */
    @Override
    @Transactional(readOnly = true)
    public Page<BillingAppeal> getAppealsByStatus(String status, Pageable pageable) {
        log.debug("根据状态查询申诉记录: Status={}, Page={}, Size={}", status, pageable.getPageNumber(), pageable.getPageSize());
        return billingAppealRepository.findByStatusOrderByCreatedAtDesc(status, pageable);
    }

    /**
     * 根据用户ID和申诉状态查询申诉记录
     * 
     * 分页查询用户指定状态的申诉记录，按创建时间倒序排列，
     * 用于按状态筛选查看申诉历史
     * 
     * @param userId   用户ID，不能为空
     * @param status   申诉状态，不能为空
     * @param pageable 分页参数
     * @return 指定状态的申诉记录分页结果
     */
    @Override
    @Transactional(readOnly = true)
    public Page<BillingAppeal> getUserAppealsByStatus(Long userId, String status, Pageable pageable) {
        log.debug("根据用户ID和状态查询申诉记录: UserId={}, Status={}, Page={}, Size={}",
                userId, status, pageable.getPageNumber(), pageable.getPageSize());
        return billingAppealRepository.findByUserIdAndStatusOrderByCreatedAtDesc(userId, status, pageable);
    }

    /**
     * 根据处理人员ID查询申诉记录
     * 
     * 分页查询指定处理人员处理的申诉记录，按创建时间倒序排列，
     * 用于查看处理人员的工作记录
     * 
     * @param handlerId 处理人员ID，不能为空
     * @param pageable  分页参数
     * @return 处理人员的申诉记录分页结果
     */
    @Override
    @Transactional(readOnly = true)
    public Page<BillingAppeal> getAppealsByHandler(Long handlerId, Pageable pageable) {
        log.debug("根据处理人员ID查询申诉记录: HandlerId={}, Page={}, Size={}",
                handlerId, pageable.getPageNumber(), pageable.getPageSize());
        return billingAppealRepository.findByAdminIdOrderByCreatedAtDesc(handlerId, pageable);
    }

    /**
     * 查询待处理的申诉记录
     * 
     * 分页查询状态为PENDING的申诉记录，按创建时间正序排列（先进先出），
     * 用于管理员处理申诉队列
     * 
     * @param pageable 分页参数
     * @return 待处理申诉记录的分页结果
     */
    @Override
    @Transactional(readOnly = true)
    public Page<BillingAppeal> getPendingAppeals(Pageable pageable) {
        log.debug("查询待处理申诉记录: Page={}, Size={}", pageable.getPageNumber(), pageable.getPageSize());
        return billingAppealRepository.findPendingAppeals(pageable);
    }

    /**
     * 查询用户指定时间范围内的申诉记录
     * 
     * 分页查询用户在指定时间范围内的申诉记录，按创建时间倒序排列，
     * 用于统计分析和历史查询
     * 
     * @param userId    用户ID，不能为空
     * @param startTime 开始时间，不能为空
     * @param endTime   结束时间，不能为空
     * @param pageable  分页参数
     * @return 指定时间范围内的申诉记录分页结果
     */
    @Override
    @Transactional(readOnly = true)
    public Page<BillingAppeal> getUserAppealsByDateRange(Long userId, LocalDateTime startTime,
            LocalDateTime endTime, Pageable pageable) {
        log.debug("查询用户指定时间范围内的申诉记录: UserId={}, StartTime={}, EndTime={}", userId, startTime, endTime);
        return billingAppealRepository.findByUserIdAndCreatedAtBetweenOrderByCreatedAtDesc(
                userId, startTime, endTime, pageable);
    }

    /**
     * 处理申诉（通过）
     * 
     * 批准用户的申诉，执行相应的退款或调整操作：
     * 1. 验证申诉记录是否存在且可处理
     * 2. 更新申诉状态为已批准
     * 3. 记录处理人员和处理时间
     * 4. 执行退款操作（如果需要）
     * 
     * @param appealId     申诉ID，不能为空
     * @param handlerId    处理人员ID，不能为空
     * @param handleResult 处理结果说明，可以为空
     * @return 是否处理成功，true表示成功，false表示失败
     */
    @Override
    public boolean approveAppeal(Long appealId, Long handlerId, String handleResult) {
        log.info("批准申诉: AppealId={}, HandlerId={}, HandleResult={}", appealId, handlerId, handleResult);

        Optional<BillingAppeal> appealOpt = billingAppealRepository.findById(appealId);
        if (appealOpt.isEmpty()) {
            log.warn("申诉记录不存在: AppealId={}", appealId);
            return false;
        }

        BillingAppeal appeal = appealOpt.get();
        if (!STATUS_PENDING.equals(appeal.getStatus())) {
            log.warn("申诉状态不允许处理: AppealId={}, Status={}", appealId, appeal.getStatus());
            return false;
        }

        // 更新申诉状态
        appeal.markAsProcessed(STATUS_APPROVED, handlerId, handleResult);
        billingAppealRepository.save(appeal);

        log.info("申诉批准成功: AppealId={}", appealId);
        return true;
    }

    /**
     * 处理申诉（拒绝）
     * 
     * 拒绝用户的申诉，不执行任何退款操作：
     * 1. 验证申诉记录是否存在且可处理
     * 2. 更新申诉状态为已拒绝
     * 3. 记录处理人员和处理时间
     * 4. 记录拒绝原因
     * 
     * @param appealId     申诉ID，不能为空
     * @param handlerId    处理人员ID，不能为空
     * @param handleResult 拒绝原因说明，可以为空
     * @return 是否处理成功，true表示成功，false表示失败
     */
    @Override
    public boolean rejectAppeal(Long appealId, Long handlerId, String handleResult) {
        log.info("拒绝申诉: AppealId={}, HandlerId={}, HandleResult={}", appealId, handlerId, handleResult);

        Optional<BillingAppeal> appealOpt = billingAppealRepository.findById(appealId);
        if (appealOpt.isEmpty()) {
            log.warn("申诉记录不存在: AppealId={}", appealId);
            return false;
        }

        BillingAppeal appeal = appealOpt.get();
        if (!STATUS_PENDING.equals(appeal.getStatus())) {
            log.warn("申诉状态不允许处理: AppealId={}, Status={}", appealId, appeal.getStatus());
            return false;
        }

        // 更新申诉状态
        appeal.markAsProcessed(STATUS_REJECTED, handlerId, handleResult);
        billingAppealRepository.save(appeal);

        log.info("申诉拒绝成功: AppealId={}", appealId);
        return true;
    }

    /**
     * 分配申诉给处理人员
     * 
     * 将待处理的申诉分配给指定的处理人员：
     * 1. 验证申诉记录是否存在且为待处理状态
     * 2. 更新处理人员信息
     * 3. 记录分配时间
     * 
     * @param appealId  申诉ID，不能为空
     * @param handlerId 处理人员ID，不能为空
     * @return 是否分配成功，true表示成功，false表示失败
     */
    @Override
    public boolean assignAppealToHandler(Long appealId, Long handlerId) {
        log.info("分配申诉给处理人员: AppealId={}, HandlerId={}", appealId, handlerId);

        Optional<BillingAppeal> appealOpt = billingAppealRepository.findById(appealId);
        if (appealOpt.isEmpty()) {
            log.warn("申诉记录不存在: AppealId={}", appealId);
            return false;
        }

        BillingAppeal appeal = appealOpt.get();
        if (!STATUS_PENDING.equals(appeal.getStatus())) {
            log.warn("申诉状态不允许分配: AppealId={}, Status={}", appealId, appeal.getStatus());
            return false;
        }

        // 更新处理人员
        appeal.setAdminId(handlerId);
        billingAppealRepository.save(appeal);

        log.info("申诉分配成功: AppealId={}, HandlerId={}", appealId, handlerId);
        return true;
    }

    /**
     * 批量分配申诉给处理人员
     * 
     * 将多个待处理的申诉批量分配给指定的处理人员：
     * 1. 遍历申诉ID列表
     * 2. 验证每个申诉记录的状态
     * 3. 批量更新处理人员信息
     * 
     * @param appealIds 申诉ID列表，不能为空
     * @param handlerId 处理人员ID，不能为空
     * @return 成功分配的申诉数量
     */
    @Override
    public int batchAssignAppealsToHandler(List<Long> appealIds, Long handlerId) {
        log.info("批量分配申诉给处理人员: AppealIds={}, HandlerId={}", appealIds, handlerId);

        if (appealIds == null || appealIds.isEmpty()) {
            log.warn("申诉ID列表为空");
            return 0;
        }

        int successCount = 0;
        for (Long appealId : appealIds) {
            if (assignAppealToHandler(appealId, handlerId)) {
                successCount++;
            }
        }

        log.info("批量分配申诉完成: 总数={}, 成功数={}", appealIds.size(), successCount);
        return successCount;
    }

    /**
     * 查询用户申诉统计信息
     * 
     * 获取用户的申诉统计数据，包括总申诉数、各状态申诉数等：
     * 1. 查询用户总申诉数
     * 2. 查询各状态的申诉数量
     * 3. 计算申诉成功率
     * 
     * @param userId 用户ID，不能为空
     * @return 包含总申诉数、待处理数、已通过数、已拒绝数的统计信息
     */
    @Override
    @Transactional(readOnly = true)
    public Object[] getUserAppealStats(Long userId) {
        log.debug("查询用户申诉统计信息: UserId={}", userId);
        return billingAppealRepository.getUserAppealStats(userId);
    }

    /**
     * 查询系统申诉统计信息
     * 
     * 获取系统在指定时间范围内的申诉统计数据：
     * 1. 按状态分组统计申诉数量
     * 2. 计算处理效率指标
     * 3. 分析申诉趋势
     * 
     * @param startTime 开始时间，不能为空
     * @param endTime   结束时间，不能为空
     * @return 按状态分组的统计信息列表
     */
    @Override
    @Transactional(readOnly = true)
    public List<Object[]> getSystemAppealStats(LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("查询系统申诉统计信息: StartTime={}, EndTime={}", startTime, endTime);
        return billingAppealRepository.getSystemAppealStats(startTime, endTime);
    }

    /**
     * 查询待处理申诉的数量
     * 
     * 统计当前系统中状态为PENDING的申诉记录总数，
     * 用于监控申诉处理工作量
     * 
     * @return 待处理申诉数量
     */
    @Override
    @Transactional(readOnly = true)
    public long getPendingAppealCount() {
        log.debug("查询待处理申诉数量");
        return billingAppealRepository.countByStatus(STATUS_PENDING);
    }

    /**
     * 查询用户当日申诉次数
     * 
     * 统计用户在当天已提交的申诉次数，
     * 用于申诉频率控制和防止滥用
     * 
     * @param userId 用户ID，不能为空
     * @return 当日申诉次数
     */
    @Override
    @Transactional(readOnly = true)
    public long getUserTodayAppealCount(Long userId) {
        log.debug("查询用户当日申诉次数: UserId={}", userId);
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        LocalDateTime endOfDay = startOfDay.plusDays(1);
        return billingAppealRepository.countByUserIdAndCreatedAtBetween(userId, startOfDay, endOfDay);
    }

    /**
     * 查询高金额申诉记录
     * 
     * 分页查询申诉金额超过指定阈值的申诉记录，
     * 按申诉金额倒序排列，用于重点关注高价值申诉
     * 
     * @param amountThreshold 金额阈值，不能为空
     * @param pageable        分页参数
     * @return 高金额申诉记录的分页结果
     */
    @Override
    @Transactional(readOnly = true)
    public Page<BillingAppeal> getHighAmountAppeals(BigDecimal amountThreshold, Pageable pageable) {
        log.debug("查询高金额申诉记录: AmountThreshold={}", amountThreshold);
        return billingAppealRepository.findHighAmountAppeals(amountThreshold, pageable);
    }

    /**
     * 查询长时间未处理的申诉记录
     * 
     * 分页查询超过指定小时数仍未处理的申诉记录，
     * 按创建时间正序排列，用于及时处理超时申诉
     * 
     * @param hours    小时数阈值
     * @param pageable 分页参数
     * @return 长时间未处理申诉记录的分页结果
     */
    @Override
    @Transactional(readOnly = true)
    public Page<BillingAppeal> getLongPendingAppeals(int hours, Pageable pageable) {
        log.debug("查询长时间未处理的申诉记录: Hours={}", hours);
        LocalDateTime beforeTime = LocalDateTime.now().minusHours(hours);
        return billingAppealRepository.findLongPendingAppeals(beforeTime, pageable);
    }

    /**
     * 查询处理人员的申诉处理统计
     * 
     * 获取指定处理人员在指定时间范围内的处理统计：
     * 1. 处理总数
     * 2. 通过数量
     * 3. 拒绝数量
     * 4. 处理效率
     * 
     * @param handlerId 处理人员ID，不能为空
     * @param startTime 开始时间，不能为空
     * @param endTime   结束时间，不能为空
     * @return 包含处理总数、通过数、拒绝数的统计信息
     */
    @Override
    @Transactional(readOnly = true)
    public Object[] getHandlerAppealStats(Long handlerId, LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("查询处理人员申诉处理统计: HandlerId={}, StartTime={}, EndTime={}", handlerId, startTime, endTime);
        return billingAppealRepository.getHandlerAppealStats(handlerId, startTime, endTime);
    }

    /**
     * 根据申诉原因搜索申诉记录
     * 
     * 分页搜索申诉原因包含指定关键词的申诉记录，
     * 用于按原因分类查看和分析申诉
     * 
     * @param keyword  搜索关键词，不能为空
     * @param pageable 分页参数
     * @return 匹配的申诉记录分页结果
     */
    @Override
    @Transactional(readOnly = true)
    public Page<BillingAppeal> searchAppealsByReason(String keyword, Pageable pageable) {
        log.debug("根据申诉原因搜索申诉记录: Keyword={}", keyword);
        return billingAppealRepository.searchByReason(keyword, pageable);
    }

    /**
     * 查询用户最近的申诉记录
     * 
     * 获取用户最近提交的指定数量的申诉记录，
     * 按创建时间倒序排列，用于快速查看用户申诉历史
     * 
     * @param userId 用户ID，不能为空
     * @param limit  限制数量，必须大于0
     * @return 最近的申诉记录列表
     */
    @Override
    @Transactional(readOnly = true)
    public List<BillingAppeal> getUserRecentAppeals(Long userId, int limit) {
        log.debug("查询用户最近的申诉记录: UserId={}, Limit={}", userId, limit);
        Pageable pageable = PageRequest.of(0, limit);
        return billingAppealRepository.findTopByUserIdOrderByCreatedAtDesc(userId, pageable);
    }

    /**
     * 查询活跃申诉用户列表
     * 
     * 获取在指定时间范围内提交过申诉的用户ID列表，
     * 用于用户行为分析和申诉趋势统计
     * 
     * @param startTime 开始时间，不能为空
     * @param endTime   结束时间，不能为空
     * @return 活跃用户ID列表
     */
    @Override
    @Transactional(readOnly = true)
    public List<Long> getActiveAppealUsers(LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("查询活跃申诉用户列表: StartTime={}, EndTime={}", startTime, endTime);
        return billingAppealRepository.findActiveUsersInDateRange(startTime, endTime);
    }

    /**
     * 查询申诉处理效率统计
     * 
     * 计算指定时间范围内申诉的平均处理时间，
     * 用于评估申诉处理效率和服务质量
     * 
     * @param startTime 开始时间，不能为空
     * @param endTime   结束时间，不能为空
     * @return 平均处理时间（小时），null表示无数据
     */
    @Override
    @Transactional(readOnly = true)
    public Double getAverageHandleTimeInHours(LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("查询申诉处理效率统计: StartTime={}, EndTime={}", startTime, endTime);
        return billingAppealRepository.getAverageHandleTimeInHours(startTime, endTime);
    }

    /**
     * 查询用户申诉成功率
     * 
     * 计算用户历史申诉的成功率（批准数/总处理数），
     * 用于评估用户申诉的合理性
     * 
     * @param userId 用户ID，不能为空
     * @return 申诉成功率（百分比），null表示无数据
     */
    @Override
    @Transactional(readOnly = true)
    public Double getUserAppealSuccessRate(Long userId) {
        log.debug("查询用户申诉成功率: UserId={}", userId);
        return billingAppealRepository.getUserAppealSuccessRate(userId);
    }

    /**
     * 撤销申诉
     * 
     * 用户主动撤销自己提交的申诉：
     * 1. 验证申诉记录是否存在
     * 2. 验证用户权限
     * 3. 检查申诉状态是否允许撤销
     * 4. 更新申诉状态为已取消
     * 
     * @param appealId 申诉ID，不能为空
     * @param userId   用户ID，不能为空
     * @param reason   撤销原因，可以为空
     * @return 是否撤销成功，true表示成功，false表示失败
     */
    @Override
    public boolean withdrawAppeal(Long appealId, Long userId, String reason) {
        log.info("撤销申诉: AppealId={}, UserId={}, Reason={}", appealId, userId, reason);

        Optional<BillingAppeal> appealOpt = billingAppealRepository.findById(appealId);
        if (appealOpt.isEmpty()) {
            log.warn("申诉记录不存在: AppealId={}", appealId);
            return false;
        }

        BillingAppeal appeal = appealOpt.get();
        if (!appeal.getUserId().equals(userId)) {
            log.warn("无权限撤销申诉: AppealId={}, UserId={}", appealId, userId);
            return false;
        }

        if (!STATUS_PENDING.equals(appeal.getStatus())) {
            log.warn("申诉状态不允许撤销: AppealId={}, Status={}", appealId, appeal.getStatus());
            return false;
        }

        // 更新申诉状态为已取消
        appeal.setStatus(STATUS_CANCELLED);
        appeal.setAdminComment(reason != null ? "用户撤销: " + reason : "用户撤销");
        billingAppealRepository.save(appeal);

        log.info("申诉撤销成功: AppealId={}", appealId);
        return true;
    }

    /**
     * 检查用户是否可以创建申诉
     * 
     * 验证用户是否满足创建申诉的条件：
     * 1. 检查当日申诉次数限制
     * 2. 检查使用记录是否已申诉
     * 3. 检查使用记录状态
     * 
     * @param userId        用户ID，不能为空
     * @param usageRecordId 使用记录ID，不能为空
     * @return 检查结果消息，null表示可以创建
     */
    @Override
    @Transactional(readOnly = true)
    public String checkCanCreateAppeal(Long userId, Long usageRecordId) {
        log.debug("检查用户是否可以创建申诉: UserId={}, UsageRecordId={}", userId, usageRecordId);

        // 检查当日申诉次数限制（假设限制为5次）
        long todayCount = getUserTodayAppealCount(userId);
        if (todayCount >= 5) {
            return "当日申诉次数已达上限";
        }

        // 检查使用记录是否已申诉
        if (hasAppealForUsageRecord(usageRecordId)) {
            return "该使用记录已存在申诉";
        }

        // 检查使用记录是否存在
        Optional<BillingUsageRecord> recordOpt = billingUsageRecordRepository.findById(usageRecordId);
        if (recordOpt.isEmpty()) {
            return "使用记录不存在";
        }

        BillingUsageRecord record = recordOpt.get();
        if (!record.getUserId().equals(userId)) {
            return "无权限申诉该使用记录";
        }

        return null; // 可以创建申诉
    }

    /**
     * 验证申诉参数的有效性
     * 
     * 验证创建申诉时提供的参数是否有效：
     * 1. 检查必要参数是否为空
     * 2. 检查申诉金额是否合理
     * 3. 检查申诉原因是否有效
     * 
     * @param userId        用户ID，不能为空
     * @param usageRecordId 使用记录ID，不能为空
     * @param appealReason  申诉原因，不能为空
     * @param appealAmount  申诉金额，不能为空
     * @return 验证结果消息，null表示验证通过
     */
    @Override
    @Transactional(readOnly = true)
    public String validateAppealParameters(Long userId, Long usageRecordId, String appealReason,
            BigDecimal appealAmount) {
        log.debug("验证申诉参数: UserId={}, UsageRecordId={}, AppealReason={}, AppealAmount={}",
                userId, usageRecordId, appealReason, appealAmount);

        if (userId == null) {
            return "用户ID不能为空";
        }

        if (usageRecordId == null) {
            return "使用记录ID不能为空";
        }

        if (appealReason == null || appealReason.trim().isEmpty()) {
            return "申诉原因不能为空";
        }

        if (appealAmount == null || appealAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return "申诉金额必须大于0";
        }

        // 检查申诉原因是否在常见原因列表中
        if (!COMMON_REASONS.contains(appealReason)) {
            return "申诉原因不在支持的范围内";
        }

        return null; // 验证通过
    }

    /**
     * 获取申诉状态列表
     * 
     * 返回系统支持的所有申诉状态，
     * 用于前端下拉选择和状态筛选
     * 
     * @return 支持的申诉状态列表
     */
    @Override
    @Transactional(readOnly = true)
    public List<String> getSupportedAppealStatuses() {
        log.debug("获取支持的申诉状态列表");
        return SUPPORTED_STATUSES;
    }

    /**
     * 获取申诉原因列表
     * 
     * 返回系统预定义的常见申诉原因，
     * 用于前端下拉选择和原因标准化
     * 
     * @return 常见的申诉原因列表
     */
    @Override
    @Transactional(readOnly = true)
    public List<String> getCommonAppealReasons() {
        log.debug("获取常见申诉原因列表");
        return COMMON_REASONS;
    }

    /**
     * 批量删除历史申诉记录
     * 
     * 删除指定时间之前的申诉记录，用于数据清理：
     * 1. 只删除已处理完成的申诉
     * 2. 保留重要的申诉记录
     * 3. 记录删除操作日志
     * 
     * @param beforeTime 时间阈值，删除此时间之前的记录
     * @return 删除的记录数量
     */
    @Override
    public int deleteHistoryAppeals(LocalDateTime beforeTime) {
        log.info("批量删除历史申诉记录: BeforeTime={}", beforeTime);

        // 只删除已处理完成的申诉记录
        int deletedCount = billingAppealRepository.deleteProcessedAppealsBefore(beforeTime);

        log.info("历史申诉记录删除完成: 删除数量={}", deletedCount);
        return deletedCount;
    }

    /**
     * 申诉自动处理（基于规则）
     * 
     * 根据预设规则自动处理申诉：
     * 1. 检查申诉金额是否在自动处理范围内
     * 2. 检查申诉原因是否符合自动处理条件
     * 3. 执行自动批准或拒绝操作
     * 
     * @param appealId 申诉ID，不能为空
     * @return 是否自动处理成功，true表示成功，false表示需要人工处理
     */
    @Override
    public boolean autoProcessAppeal(Long appealId) {
        log.info("申诉自动处理: AppealId={}", appealId);

        Optional<BillingAppeal> appealOpt = billingAppealRepository.findById(appealId);
        if (appealOpt.isEmpty()) {
            log.warn("申诉记录不存在: AppealId={}", appealId);
            return false;
        }

        BillingAppeal appeal = appealOpt.get();
        if (!STATUS_PENDING.equals(appeal.getStatus())) {
            log.warn("申诉状态不允许自动处理: AppealId={}, Status={}", appealId, appeal.getStatus());
            return false;
        }

        // 简单的自动处理规则：小额申诉自动批准
        if (appeal.getRefundAmount() != null && appeal.getRefundAmount().compareTo(new BigDecimal("10.00")) <= 0) {
            appeal.markAsProcessed(STATUS_APPROVED, 0L, "系统自动批准：小额申诉");
            billingAppealRepository.save(appeal);
            log.info("申诉自动批准: AppealId={}", appealId);
            return true;
        }

        log.debug("申诉不符合自动处理条件: AppealId={}", appealId);
        return false;
    }

    /**
     * 获取申诉处理建议
     * 
     * 基于申诉内容和历史数据提供处理建议：
     * 1. 分析申诉原因的合理性
     * 2. 检查用户历史申诉记录
     * 3. 提供处理建议和参考信息
     * 
     * @param appealId 申诉ID，不能为空
     * @return 处理建议文本
     */
    @Override
    @Transactional(readOnly = true)
    public String getAppealProcessingSuggestion(Long appealId) {
        log.debug("获取申诉处理建议: AppealId={}", appealId);

        Optional<BillingAppeal> appealOpt = billingAppealRepository.findById(appealId);
        if (appealOpt.isEmpty()) {
            return "申诉记录不存在";
        }

        BillingAppeal appeal = appealOpt.get();
        StringBuilder suggestion = new StringBuilder();

        // 检查用户申诉成功率
        Double successRate = getUserAppealSuccessRate(appeal.getUserId());
        if (successRate != null) {
            suggestion.append(String.format("用户历史申诉成功率: %.1f%%. ", successRate));
            if (successRate > 80) {
                suggestion.append("建议优先处理. ");
            } else if (successRate < 20) {
                suggestion.append("建议谨慎处理. ");
            }
        }

        // 检查申诉金额
        if (appeal.getRefundAmount() != null) {
            if (appeal.getRefundAmount().compareTo(new BigDecimal("100.00")) > 0) {
                suggestion.append("高金额申诉，建议详细审核. ");
            }
        }

        return suggestion.toString();
    }

    /**
     * 申诉升级处理
     * 
     * 将申诉升级到更高级别的处理人员：
     * 1. 记录升级原因
     * 2. 更新处理优先级
     * 3. 通知相关人员
     * 
     * @param appealId         申诉ID，不能为空
     * @param escalationReason 升级原因，不能为空
     * @return 是否升级成功，true表示成功，false表示失败
     */
    @Override
    public boolean escalateAppeal(Long appealId, String escalationReason) {
        log.info("申诉升级处理: AppealId={}, EscalationReason={}", appealId, escalationReason);

        Optional<BillingAppeal> appealOpt = billingAppealRepository.findById(appealId);
        if (appealOpt.isEmpty()) {
            log.warn("申诉记录不存在: AppealId={}", appealId);
            return false;
        }

        BillingAppeal appeal = appealOpt.get();

        // 添加升级备注
        String currentComment = appeal.getAdminComment();
        String escalationComment = String.format("[%s] 申诉升级: %s",
                LocalDateTime.now().toString(), escalationReason);

        if (currentComment != null && !currentComment.trim().isEmpty()) {
            appeal.setAdminComment(currentComment + "\n" + escalationComment);
        } else {
            appeal.setAdminComment(escalationComment);
        }

        billingAppealRepository.save(appeal);

        log.info("申诉升级成功: AppealId={}", appealId);
        return true;
    }

    // ==================== Controller需要的额外方法实现 ====================

    /**
     * 检查使用记录是否已有申诉
     * 
     * @param usageRecordId 使用记录ID
     * @return 是否已有申诉
     */
    @Override
    @Transactional(readOnly = true)
    public boolean hasExistingAppeal(Long usageRecordId) {
        log.debug("检查使用记录是否已有申诉: UsageRecordId={}", usageRecordId);
        return billingAppealRepository.existsByUsageRecordId(usageRecordId);
    }

    /**
     * 获取所有申诉记录（管理端分页查询）
     * 
     * @param pageable 分页参数
     * @return 分页结果
     */
    @Override
    @Transactional(readOnly = true)
    public Page<BillingAppeal> getAppeals(Pageable pageable) {
        log.debug("获取所有申诉记录: Page={}, Size={}", pageable.getPageNumber(), pageable.getPageSize());
        return billingAppealRepository.findAllByOrderByCreatedAtDesc(pageable);
    }

    /**
     * 处理申诉（统一处理接口）
     * 
     * @param appealId 申诉ID
     * @param result   处理结果（APPROVE-通过, REJECT-拒绝）
     * @param note     处理备注
     * @return 是否成功
     */
    @Override
    public boolean processAppeal(Long appealId, String result, String note) {
        log.info("处理申诉: AppealId={}, Result={}, Note={}", appealId, result, note);

        // 获取当前用户ID作为处理人员ID（应该从SecurityContext获取）
        Long handlerId = 1L; // TODO: 从SecurityContext获取当前管理员ID

        if ("APPROVE".equalsIgnoreCase(result) || "APPROVED".equalsIgnoreCase(result)) {
            return approveAppeal(appealId, handlerId, note);
        } else if ("REJECT".equalsIgnoreCase(result) || "REJECTED".equalsIgnoreCase(result)) {
            return rejectAppeal(appealId, handlerId, note);
        } else {
            log.warn("不支持的处理结果: Result={}", result);
            return false;
        }
    }

    /**
     * 根据状态获取申诉数量
     * 
     * @param status 申诉状态
     * @return 申诉数量
     */
    @Override
    @Transactional(readOnly = true)
    public long getAppealCountByStatus(String status) {
        log.debug("根据状态获取申诉数量: Status={}", status);
        return billingAppealRepository.countByStatus(status);
    }
}