package com.dipspro.modules.billing.repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.dipspro.modules.billing.entity.BillingUsageRecord;

/**
 * 计费使用记录数据访问接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface BillingUsageRecordRepository extends JpaRepository<BillingUsageRecord, Long> {

        /**
         * 根据用户ID分页查询使用记录
         * 
         * @param userId   用户ID
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<BillingUsageRecord> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

        /**
         * 根据用户ID和时间范围查询使用记录
         * 
         * @param userId    用户ID
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @param pageable  分页参数
         * @return 分页结果
         */
        Page<BillingUsageRecord> findByUserIdAndCreatedAtBetweenOrderByCreatedAtDesc(
                        Long userId, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

        /**
         * 根据消息ID查询使用记录
         * 
         * @param messageId 消息ID
         * @return 使用记录
         */
        Optional<BillingUsageRecord> findByMessageId(UUID messageId);

        /**
         * 根据用户ID和消息ID查询使用记录
         * 
         * @param userId    用户ID
         * @param messageId 消息ID
         * @return 使用记录
         */
        Optional<BillingUsageRecord> findByUserIdAndMessageId(Long userId, UUID messageId);

        /**
         * 检查消息ID是否已经被计费
         * 
         * @param messageId 消息ID
         * @return 是否存在
         */
        boolean existsByMessageId(UUID messageId);

        /**
         * 根据消息ID列表批量查询计费记录
         * 
         * @param messageIds 消息ID列表
         * @return 计费记录列表
         */
        List<BillingUsageRecord> findByMessageIdIn(List<UUID> messageIds);

        /**
         * 根据计费方式查询使用记录
         * 
         * @param billingType 计费方式
         * @param pageable    分页参数
         * @return 分页结果
         */
        Page<BillingUsageRecord> findByBillingTypeOrderByCreatedAtDesc(String billingType, Pageable pageable);

        /**
         * 查询用户指定时间范围内的Token消费统计
         * 
         * @param userId    用户ID
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 包含输入Token、输出Token、总费用的统计信息
         */
        @Query("SELECT SUM(bur.inputTokens), SUM(bur.outputTokens), SUM(bur.totalCost) " +
                        "FROM BillingUsageRecord bur WHERE bur.userId = :userId " +
                        "AND bur.createdAt >= :startTime AND bur.createdAt <= :endTime")
        Object[] getUserTokenUsageStats(@Param("userId") Long userId,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 查询用户今日Token使用量
         * 
         * @param userId     用户ID
         * @param startOfDay 今日开始时间
         * @return 今日Token使用总量
         */
        @Query("SELECT COALESCE(SUM(bur.inputTokens + bur.outputTokens), 0) FROM BillingUsageRecord bur " +
                        "WHERE bur.userId = :userId AND bur.createdAt >= :startOfDay")
        Long getUserTodayTokenUsage(@Param("userId") Long userId, @Param("startOfDay") LocalDateTime startOfDay);

        /**
         * 查询用户今日费用消费
         * 
         * @param userId     用户ID
         * @param startOfDay 今日开始时间
         * @return 今日费用消费总额
         */
        @Query("SELECT COALESCE(SUM(bur.totalCost), 0) FROM BillingUsageRecord bur " +
                        "WHERE bur.userId = :userId AND bur.createdAt >= :startOfDay")
        BigDecimal getUserTodayCostUsage(@Param("userId") Long userId, @Param("startOfDay") LocalDateTime startOfDay);

        /**
         * 查询指定时间范围内的使用统计
         * 
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 包含总用户数、总Token数、总费用的统计信息
         */
        @Query("SELECT COUNT(DISTINCT bur.userId), SUM(bur.inputTokens + bur.outputTokens), SUM(bur.totalCost) " +
                        "FROM BillingUsageRecord bur WHERE bur.createdAt >= :startTime AND bur.createdAt <= :endTime")
        Object[] getUsageStatistics(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 查询用户使用次数统计
         * 
         * @param userId    用户ID
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 使用次数
         */
        @Query("SELECT COUNT(bur) FROM BillingUsageRecord bur WHERE bur.userId = :userId " +
                        "AND bur.createdAt >= :startTime AND bur.createdAt <= :endTime")
        Long getUserUsageCount(@Param("userId") Long userId,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 查询活跃用户列表（指定时间范围内有使用记录的用户）
         * 
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 活跃用户ID列表
         */
        @Query("SELECT DISTINCT bur.userId FROM BillingUsageRecord bur " +
                        "WHERE bur.createdAt >= :startTime AND bur.createdAt <= :endTime")
        List<Long> getActiveUsers(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 查询用户最近的使用记录
         * 
         * @param userId 用户ID
         * @param limit  限制数量
         * @return 最近的使用记录列表
         */
        @Query("SELECT bur FROM BillingUsageRecord bur WHERE bur.userId = :userId " +
                        "ORDER BY bur.createdAt DESC")
        List<BillingUsageRecord> findRecentUsageByUserId(@Param("userId") Long userId, Pageable pageable);

        /**
         * 查询高消费使用记录（费用超过指定阈值）
         * 
         * @param costThreshold 费用阈值
         * @param pageable      分页参数
         * @return 分页结果
         */
        @Query("SELECT bur FROM BillingUsageRecord bur WHERE bur.totalCost >= :costThreshold " +
                        "ORDER BY bur.totalCost DESC, bur.createdAt DESC")
        Page<BillingUsageRecord> findHighCostUsageRecords(@Param("costThreshold") BigDecimal costThreshold,
                        Pageable pageable);

        /**
         * 查询异常使用记录（Token数量或费用异常）
         * 
         * @param maxTokens 最大Token数量阈值
         * @param maxCost   最大费用阈值
         * @param pageable  分页参数
         * @return 分页结果
         */
        @Query("SELECT bur FROM BillingUsageRecord bur WHERE " +
                        "(bur.inputTokens + bur.outputTokens) > :maxTokens OR bur.totalCost > :maxCost " +
                        "ORDER BY bur.createdAt DESC")
        Page<BillingUsageRecord> findAbnormalUsageRecords(@Param("maxTokens") Long maxTokens,
                        @Param("maxCost") BigDecimal maxCost,
                        Pageable pageable);

        /**
         * 根据计费方式统计使用情况
         * 
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 按计费方式分组的统计信息
         */
        @Query("SELECT bur.billingType, COUNT(bur), SUM(bur.inputTokens + bur.outputTokens), SUM(bur.totalCost) " +
                        "FROM BillingUsageRecord bur WHERE bur.createdAt >= :startTime AND bur.createdAt <= :endTime " +
                        "GROUP BY bur.billingType ORDER BY SUM(bur.totalCost) DESC")
        List<Object[]> getUsageStatsByBillingType(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 查询用户月度使用统计
         * 
         * @param userId 用户ID
         * @param year   年份
         * @param month  月份
         * @return 包含使用次数、Token总数、费用总额的统计信息
         */
        @Query("SELECT COUNT(bur), SUM(bur.inputTokens + bur.outputTokens), SUM(bur.totalCost) " +
                        "FROM BillingUsageRecord bur WHERE bur.userId = :userId " +
                        "AND EXTRACT(YEAR FROM bur.createdAt) = :year AND EXTRACT(MONTH FROM bur.createdAt) = :month")
        Object[] getUserMonthlyStats(@Param("userId") Long userId,
                        @Param("year") int year,
                        @Param("month") int month);

        /**
         * 删除指定时间之前的历史记录
         * 
         * @param beforeTime 时间阈值
         * @return 删除的记录数
         */
        @Query("DELETE FROM BillingUsageRecord bur WHERE bur.createdAt < :beforeTime")
        int deleteRecordsBeforeTime(@Param("beforeTime") LocalDateTime beforeTime);

        /**
         * 查询用户最高单次消费记录
         * 
         * @param userId   用户ID
         * @param pageable 分页参数（用于限制结果数量）
         * @return 最高单次消费记录列表
         */
        @Query("SELECT bur FROM BillingUsageRecord bur WHERE bur.userId = :userId " +
                        "ORDER BY bur.totalCost DESC")
        List<BillingUsageRecord> findUserMaxCostRecord(@Param("userId") Long userId, Pageable pageable);

        /**
         * 查询系统整体消费排行榜
         * 
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @param pageable  分页参数
         * @return 用户消费排行榜
         */
        @Query("SELECT bur.userId, SUM(bur.totalCost) as totalCost FROM BillingUsageRecord bur " +
                        "WHERE bur.createdAt >= :startTime AND bur.createdAt <= :endTime " +
                        "GROUP BY bur.userId ORDER BY totalCost DESC")
        Page<Object[]> getUserCostRanking(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime,
                        Pageable pageable);

        /**
         * 查询用户指定时间范围内的Token总数
         * 
         * @param userId    用户ID
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return Token总数
         */
        @Query("SELECT COALESCE(SUM(bur.inputTokens + bur.outputTokens), 0) FROM BillingUsageRecord bur " +
                        "WHERE bur.userId = :userId AND bur.createdAt >= :startTime AND bur.createdAt < :endTime")
        Long getUserTotalTokensInDateRange(@Param("userId") Long userId,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 查询用户指定时间范围内的费用总额
         * 
         * @param userId    用户ID
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 费用总额
         */
        @Query("SELECT COALESCE(SUM(bur.totalCost), 0) FROM BillingUsageRecord bur " +
                        "WHERE bur.userId = :userId AND bur.createdAt >= :startTime AND bur.createdAt < :endTime")
        BigDecimal getUserTotalCostInDateRange(@Param("userId") Long userId,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 查询系统使用统计
         * 
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 包含活跃用户数、Token总数、费用总额的统计信息
         */
        @Query("SELECT COUNT(DISTINCT bur.userId), SUM(bur.inputTokens + bur.outputTokens), SUM(bur.totalCost) " +
                        "FROM BillingUsageRecord bur WHERE bur.createdAt >= :startTime AND bur.createdAt <= :endTime")
        Object[] getSystemUsageStats(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 统计用户在指定时间范围内的记录数量
         * 
         * @param userId    用户ID
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 记录数量
         */
        Long countByUserIdAndCreatedAtBetween(Long userId, LocalDateTime startTime, LocalDateTime endTime);

        /**
         * 查询指定时间范围内的活跃用户ID列表
         * 
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 活跃用户ID列表
         */
        @Query("SELECT DISTINCT bur.userId FROM BillingUsageRecord bur " +
                        "WHERE bur.createdAt >= :startTime AND bur.createdAt <= :endTime")
        List<Long> findActiveUsersInDateRange(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 查询费用超过阈值的记录
         * 
         * @param costThreshold 费用阈值
         * @param pageable      分页参数
         * @return 分页结果
         */
        Page<BillingUsageRecord> findByTotalCostGreaterThanOrderByTotalCostDesc(BigDecimal costThreshold,
                        Pageable pageable);

        /**
         * 查询异常记录（Token数量或费用异常）
         * 
         * @param maxTokens 最大Token数量阈值
         * @param maxCost   最大费用阈值
         * @param pageable  分页参数
         * @return 分页结果
         */
        @Query("SELECT bur FROM BillingUsageRecord bur WHERE " +
                        "(bur.inputTokens + bur.outputTokens) > :maxTokens OR bur.totalCost > :maxCost " +
                        "ORDER BY bur.createdAt DESC")
        Page<BillingUsageRecord> findAbnormalRecords(@Param("maxTokens") Long maxTokens,
                        @Param("maxCost") BigDecimal maxCost,
                        Pageable pageable);

        /**
         * 查询用户费用最高的记录
         * 
         * @param userId 用户ID
         * @return 费用最高的记录
         */
        Optional<BillingUsageRecord> findTopByUserIdOrderByTotalCostDesc(Long userId);

        /**
         * 查询用户最近的使用记录（按创建时间倒序）
         * 
         * @param userId   用户ID
         * @param pageable 分页参数（用于限制数量）
         * @return 最近的使用记录列表
         */
        List<BillingUsageRecord> findTopByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

        /**
         * 删除指定时间之前的历史记录
         * 
         * @param beforeTime 时间阈值
         * @return 删除的记录数
         */
        @Modifying
        @Query("DELETE FROM BillingUsageRecord bur WHERE bur.createdAt < :beforeTime")
        int deleteByCreatedAtBefore(@Param("beforeTime") LocalDateTime beforeTime);
}