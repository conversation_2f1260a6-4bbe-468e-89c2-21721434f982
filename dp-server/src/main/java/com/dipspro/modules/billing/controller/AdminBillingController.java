package com.dipspro.modules.billing.controller;

import java.math.BigDecimal;
import java.time.LocalDate;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.billing.dto.AppealDto;
import com.dipspro.modules.billing.dto.AppealProcessDto;
import com.dipspro.modules.billing.dto.BalanceAdjustDto;
import com.dipspro.modules.billing.dto.BillingPackageDto;
import com.dipspro.modules.billing.dto.PackageCreateDto;
import com.dipspro.modules.billing.dto.PackageUpdateDto;
import com.dipspro.modules.billing.dto.TransactionDto;
import com.dipspro.modules.billing.dto.UserBalanceDto;
import com.dipspro.modules.billing.entity.BillingAppeal;
import com.dipspro.modules.billing.entity.BillingPackage;
import com.dipspro.modules.billing.entity.BillingTransaction;
import com.dipspro.modules.billing.entity.UserBalance;
import com.dipspro.modules.billing.service.BillingAppealService;
import com.dipspro.modules.billing.service.BillingPackageService;
import com.dipspro.modules.billing.service.BillingTransactionService;
import com.dipspro.modules.billing.service.UserBalanceService;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 管理端计费功能控制器
 * 
 * 统一提供管理端计费相关的REST API接口，包括：
 * - 用户余额管理：查看、调整、冻结、解冻
 * - 交易记录管理：查看、统计、分析、导出
 * - 申诉处理：审核、处理、统计
 * - 套餐管理：创建、更新、激活、停用
 * - 财务报表：收支统计、异常检测
 * 
 * <AUTHOR> Pro Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/billing")
@RequiredArgsConstructor
@Validated
@PreAuthorize("hasRole('ADMIN')")
public class AdminBillingController {

    private final UserBalanceService userBalanceService;
    private final BillingTransactionService billingTransactionService;
    private final BillingAppealService billingAppealService;
    private final BillingPackageService billingPackageService;

    // ==================== 用户余额管理 ====================

    /**
     * 分页查询用户余额（管理端）
     * 
     * 管理员可以分页查询所有用户的余额信息，支持按余额范围、用户等筛选。
     * 
     * @param page       页码，从0开始
     * @param size       每页大小，默认20，最大100
     * @param userId     用户ID筛选
     * @param minBalance 最小余额筛选
     * @param maxBalance 最大余额筛选
     * @param orderBy    排序字段：TOTAL_BALANCE, CASH_BALANCE, CREATED_AT
     * @return 分页的用户余额列表
     */
    @GetMapping("/users/balances")
    public ApiResponse<Page<UserBalanceDto>> getUserBalances(
            @RequestParam(defaultValue = "0") @Min(0) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) BigDecimal minBalance,
            @RequestParam(required = false) BigDecimal maxBalance,
            @RequestParam(defaultValue = "TOTAL_BALANCE") String orderBy) {

        log.info("管理员查询用户余额: page={}, size={}, userId={}, minBalance={}, maxBalance={}, orderBy={}",
                page, size, userId, minBalance, maxBalance, orderBy);

        Pageable pageable = PageRequest.of(page, size);
        Page<UserBalance> balances = userBalanceService.getUserBalancesForAdmin(
                userId, minBalance, maxBalance, orderBy, pageable);

        Page<UserBalanceDto> balanceDtos = balances.map(this::convertToUserBalanceDto);

        log.info("管理员余额查询完成: TotalUsers={}", balances.getTotalElements());
        return ApiResponse.success(balanceDtos, "查询成功");
    }

    /**
     * 获取指定用户余额详情（管理端）
     * 
     * 管理员查看指定用户的详细余额信息和操作历史。
     * 
     * @param userId 用户ID
     * @return 用户余额详情
     */
    @GetMapping("/users/{userId}/balance")
    public ApiResponse<UserBalanceDto> getUserBalanceDetail(@PathVariable @NotNull Long userId) {
        log.info("管理员获取用户余额详情: UserId={}", userId);

        var balanceOpt = userBalanceService.getUserBalance(userId);
        if (balanceOpt.isEmpty()) {
            log.warn("用户余额不存在: UserId={}", userId);
            return ApiResponse.error("用户余额信息不存在");
        }

        UserBalance balance = balanceOpt.get();
        UserBalanceDto balanceDto = convertToUserBalanceDto(balance);

        log.info("用户余额详情获取成功: UserId={}, TotalBalance={}", userId, balance.getTotalBalance());
        return ApiResponse.success(balanceDto, "查询成功");
    }

    /**
     * 调整用户余额（管理端）
     * 
     * 管理员可以调整用户的余额，包括增加或减少余额。
     * 所有调整操作都会记录在交易历史中。
     * 
     * @param userId    用户ID
     * @param adjustDto 余额调整参数
     * @return 调整后的余额信息
     */
    @PutMapping("/users/{userId}/balance/adjust")
    public ApiResponse<UserBalanceDto> adjustUserBalance(
            @PathVariable @NotNull Long userId,
            @Valid @RequestBody BalanceAdjustDto adjustDto) {

        log.info("管理员调整用户余额: UserId={}, Type={}, Amount={}",
                userId, adjustDto.getAdjustType(), adjustDto.getAmount());

        UserBalance adjustedBalance = userBalanceService.adjustUserBalance(
                userId, adjustDto.getAdjustType(), adjustDto.getAmount(), adjustDto.getReason());

        UserBalanceDto balanceDto = convertToUserBalanceDto(adjustedBalance);

        log.info("用户余额调整成功: UserId={}, NewTotalBalance={}", userId, adjustedBalance.getTotalBalance());
        return ApiResponse.success(balanceDto, "余额调整成功");
    }

    // ==================== 交易记录管理 ====================

    /**
     * 分页查询交易记录（管理端）
     * 
     * 管理员可以分页查询所有交易记录，支持按用户、类型、状态、时间等条件筛选。
     * 
     * @param page      页码，从0开始
     * @param size      每页大小，默认20，最大100
     * @param userId    用户ID筛选
     * @param type      交易类型筛选
     * @param status    交易状态筛选
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param minAmount 最小金额筛选
     * @param maxAmount 最大金额筛选
     * @return 分页的交易记录列表
     */
    @GetMapping("/transactions")
    public ApiResponse<Page<TransactionDto>> getTransactions(
            @RequestParam(defaultValue = "0") @Min(0) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) BigDecimal minAmount,
            @RequestParam(required = false) BigDecimal maxAmount) {

        log.info(
                "管理员查询交易: page={}, size={}, userId={}, type={}, status={}, startDate={}, endDate={}, minAmount={}, maxAmount={}",
                page, size, userId, type, status, startDate, endDate, minAmount, maxAmount);

        Pageable pageable = PageRequest.of(page, size);
        Page<BillingTransaction> transactions = billingTransactionService.getTransactionsForAdmin(
                userId, type, status, startDate, endDate, minAmount, maxAmount, pageable);

        Page<TransactionDto> transactionDtos = transactions.map(this::convertToTransactionDto);

        log.info("管理员交易查询完成: TotalTransactions={}", transactions.getTotalElements());
        return ApiResponse.success(transactionDtos, "查询成功");
    }

    // ==================== 申诉处理 ====================

    /**
     * 分页查询申诉（管理端）
     * 
     * 管理员可以分页查询所有申诉，支持按状态、用户、时间等条件筛选。
     * 
     * @param page   页码，从0开始
     * @param size   每页大小，默认20，最大100
     * @param status 申诉状态筛选
     * @param userId 用户ID筛选
     * @param reason 申诉原因关键词搜索
     * @return 分页的申诉列表
     */
    @GetMapping("/appeals")
    public ApiResponse<Page<AppealDto>> getAppeals(
            @RequestParam(defaultValue = "0") @Min(0) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) String reason) {

        log.info("管理员查询申诉: page={}, size={}, status={}, userId={}, reason={}",
                page, size, status, userId, reason);

        Pageable pageable = PageRequest.of(page, size);
        Page<BillingAppeal> appeals;

        if (reason != null && !reason.trim().isEmpty()) {
            // 按原因搜索
            appeals = billingAppealService.searchAppealsByReason(reason.trim(), pageable);
        } else if (status != null && !status.trim().isEmpty()) {
            // 按状态筛选
            appeals = billingAppealService.getAppealsByStatus(status, pageable);
        } else if (userId != null) {
            // 按用户筛选
            appeals = billingAppealService.getUserAppeals(userId, pageable);
        } else {
            // 查询全部
            appeals = billingAppealService.getAppeals(pageable);
        }

        Page<AppealDto> appealDtos = appeals.map(this::convertToAppealDto);

        log.info("管理员申诉查询完成: TotalAppeals={}", appeals.getTotalElements());
        return ApiResponse.success(appealDtos, "查询成功");
    }

    /**
     * 处理申诉（管理端）
     * 
     * 管理员审核和处理用户申诉，可以批准或拒绝申诉。
     * 批准的申诉会自动进行退费处理。
     * 
     * @param id         申诉ID
     * @param processDto 申诉处理参数
     * @return 处理后的申诉信息
     */
    @PutMapping("/appeals/{id}/process")
    public ApiResponse<AppealDto> processAppeal(
            @PathVariable @NotNull Long id,
            @Valid @RequestBody AppealProcessDto processDto) {

        log.info("管理员处理申诉: AppealId={}, Action={}", id, processDto.getAction());

        boolean success = billingAppealService.processAppeal(
                id, processDto.getAction(), processDto.getAdminComment());

        if (!success) {
            log.warn("申诉处理失败: AppealId={}", id);
            return ApiResponse.error("申诉处理失败");
        }

        // 重新获取处理后的申诉信息
        var appealOpt = billingAppealService.getAppealById(id);
        if (appealOpt.isEmpty()) {
            log.error("申诉处理后无法获取申诉信息: AppealId={}", id);
            return ApiResponse.error("申诉信息获取失败");
        }

        BillingAppeal processedAppeal = appealOpt.get();
        AppealDto appealDto = convertToAppealDto(processedAppeal);

        log.info("申诉处理完成: AppealId={}, Status={}", id, processedAppeal.getStatus());
        return ApiResponse.success(appealDto, "申诉处理成功");
    }

    // ==================== 套餐管理 ====================

    /**
     * 分页查询套餐（管理端）
     * 
     * 管理员可以分页查询所有套餐，包括已停用的套餐。
     * 支持按状态筛选和名称搜索。
     * 
     * @param page   页码，从0开始
     * @param size   每页大小，默认20，最大100
     * @param status 状态筛选：true(激活)、false(停用)、null(全部)
     * @param name   套餐名称搜索关键词
     * @return 分页的套餐列表
     */
    @GetMapping("/packages")
    public ApiResponse<Page<BillingPackageDto>> getPackages(
            @RequestParam(defaultValue = "0") @Min(0) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size,
            @RequestParam(required = false) Boolean status,
            @RequestParam(required = false) String name) {

        log.info("管理员查询套餐: page={}, size={}, status={}, name={}", page, size, status, name);

        Pageable pageable = PageRequest.of(page, size);
        Page<BillingPackage> packages;

        if (name != null && !name.trim().isEmpty()) {
            // 按名称搜索
            packages = billingPackageService.searchPackagesByName(name.trim(), pageable);
        } else if (status != null) {
            // 按状态筛选
            packages = billingPackageService.getPackagesByStatus(status, pageable);
        } else {
            // 查询全部
            packages = billingPackageService.getPackages(pageable);
        }

        Page<BillingPackageDto> packageDtos = packages.map(this::convertToPackageDto);

        log.info("管理员套餐查询完成: TotalPackages={}", packages.getTotalElements());
        return ApiResponse.success(packageDtos, "套餐查询成功");
    }

    /**
     * 创建套餐（管理端）
     * 
     * 管理员创建新的计费套餐，需要配置价格、特性、限制等信息。
     * 创建前会进行配置验证，确保套餐信息完整有效。
     * 
     * @param createDto 套餐创建参数
     * @return 创建的套餐信息
     */
    @PostMapping("/packages")
    public ApiResponse<BillingPackageDto> createPackage(@Valid @RequestBody PackageCreateDto createDto) {
        log.info("创建套餐: Name={}, InputPrice={}, OutputPrice={}",
                createDto.getName(), createDto.getInputTokenPrice(), createDto.getOutputTokenPrice());

        // 转换DTO为实体
        BillingPackage billingPackage = convertToPackageEntity(createDto);

        // 创建套餐
        BillingPackage createdPackage = billingPackageService.createPackage(billingPackage);
        BillingPackageDto packageDto = convertToPackageDto(createdPackage);

        log.info("套餐创建成功: PackageId={}, Name={}", createdPackage.getId(), createdPackage.getName());
        return ApiResponse.success(packageDto, "套餐创建成功");
    }

    /**
     * 更新套餐（管理端）
     * 
     * 管理员更新现有套餐的配置信息。
     * 更新前会验证套餐是否存在以及配置的有效性。
     * 
     * @param id        套餐ID
     * @param updateDto 套餐更新参数
     * @return 更新后的套餐信息
     */
    @PutMapping("/packages/{id}")
    public ApiResponse<BillingPackageDto> updatePackage(
            @PathVariable @NotNull Long id,
            @Valid @RequestBody PackageUpdateDto updateDto) {

        log.info("更新套餐: PackageId={}, Name={}", id, updateDto.getName());

        // 转换DTO为实体
        BillingPackage updatePackage = convertToPackageEntity(updateDto);
        updatePackage.setId(id);

        // 更新套餐
        BillingPackage updatedPackage = billingPackageService.updatePackage(id, updatePackage);
        BillingPackageDto packageDto = convertToPackageDto(updatedPackage);

        log.info("套餐更新成功: PackageId={}, Name={}", id, updatedPackage.getName());
        return ApiResponse.success(packageDto, "套餐更新成功");
    }

    // ==================== 数据转换方法 ====================

    private UserBalanceDto convertToUserBalanceDto(UserBalance balance) {
        return new UserBalanceDto()
                .setId(balance.getId())
                .setUserId(balance.getUserId())
                .setCashBalance(balance.getRechargedBalance())
                .setGiftBalance(balance.getGiftBalance())
                .setTotalBalance(balance.getTotalBalance())
                .setFrozenAmount(balance.getFrozenBalance())
                .setTotalRechargeAmount(balance.getTotalRecharged())
                .setTotalConsumeAmount(balance.getTotalConsumed())
                .setStatus(balance.getIsFrozen() ? "FROZEN" : "NORMAL")
                .setLastTransactionAt(balance.getLastTransactionAt())
                .setCreatedAt(balance.getCreatedAt())
                .setUpdatedAt(balance.getUpdatedAt());
    }

    private TransactionDto convertToTransactionDto(BillingTransaction transaction) {
        return new TransactionDto()
                .setId(transaction.getId())
                .setUserId(transaction.getUserId())
                .setTransactionNo(transaction.getTransactionNo())
                .setType(transaction.getType())
                .setAmount(transaction.getAmount())
                .setBalanceAfter(transaction.getBalanceAfter())
                .setStatus(transaction.getStatus())
                .setDescription(transaction.getDescription())
                .setRelatedType(transaction.getRelatedType())
                .setRelatedId(transaction.getRelatedId())
                .setCreatedAt(transaction.getCreatedAt())
                .setUpdatedAt(transaction.getUpdatedAt());
    }

    private AppealDto convertToAppealDto(BillingAppeal appeal) {
        return new AppealDto()
                .setId(appeal.getId())
                .setUserId(appeal.getUserId())
                .setUsageRecordId(appeal.getUsageRecordId())
                .setReason(appeal.getReason())
                .setUserDescription(appeal.getUserDescription())
                .setEvidenceUrls(appeal.getEvidenceUrls())
                .setStatus(appeal.getStatus())
                .setAdminComment(appeal.getAdminComment())
                .setRefundAmount(appeal.getRefundAmount())
                .setSubmittedAt(appeal.getSubmittedAt())
                .setProcessedAt(appeal.getProcessedAt())
                .setCreatedAt(appeal.getCreatedAt())
                .setUpdatedAt(appeal.getUpdatedAt());
    }

    private BillingPackageDto convertToPackageDto(BillingPackage billingPackage) {
        return new BillingPackageDto()
                .setId(billingPackage.getId())
                .setName(billingPackage.getName())
                .setDescription(billingPackage.getDescription())
                .setInputTokenPrice(billingPackage.getInputTokenPrice())
                .setOutputTokenPrice(billingPackage.getOutputTokenPrice())
                .setFreeTokens(billingPackage.getFreeTokens())
                .setMaxTokensPerRequest(billingPackage.getMaxTokensPerRequest())
                .setDailyTokenLimit(billingPackage.getDailyTokenLimit())
                .setIsActive(billingPackage.getIsActive())
                .setSortOrder(billingPackage.getSortOrder())
                .setCreatedAt(billingPackage.getCreatedAt())
                .setUpdatedAt(billingPackage.getUpdatedAt());
    }

    private BillingPackage convertToPackageEntity(PackageCreateDto createDto) {
        return new BillingPackage()
                .setName(createDto.getName())
                .setDescription(createDto.getDescription())
                .setInputTokenPrice(createDto.getInputTokenPrice())
                .setOutputTokenPrice(createDto.getOutputTokenPrice())
                .setFreeTokens(createDto.getFreeTokens())
                .setMaxTokensPerRequest(createDto.getMaxTokensPerRequest())
                .setDailyTokenLimit(createDto.getDailyTokenLimit())
                .setIsActive(true)
                .setSortOrder(createDto.getSortOrder());
    }

    private BillingPackage convertToPackageEntity(PackageUpdateDto updateDto) {
        return new BillingPackage()
                .setName(updateDto.getName())
                .setDescription(updateDto.getDescription())
                .setInputTokenPrice(updateDto.getInputTokenPrice())
                .setOutputTokenPrice(updateDto.getOutputTokenPrice())
                .setFreeTokens(updateDto.getFreeTokens())
                .setMaxTokensPerRequest(updateDto.getMaxTokensPerRequest())
                .setDailyTokenLimit(updateDto.getDailyTokenLimit())
                .setIsActive(updateDto.getIsActive())
                .setSortOrder(updateDto.getSortOrder());
    }
}