package com.dipspro.modules.billing.dto;

import java.math.BigDecimal;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 申诉处理DTO
 * 
 * 用于管理员处理申诉时的请求参数，包含处理动作和相关说明。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class AppealProcessDto {

    /**
     * 处理动作
     * APPROVE-批准, REJECT-拒绝
     */
    @NotBlank(message = "处理动作不能为空")
    @Pattern(regexp = "^(APPROVE|REJECT)$", message = "处理动作只能是APPROVE或REJECT")
    private String action;

    /**
     * 管理员备注
     * 处理申诉时的说明和理由
     */
    @NotBlank(message = "管理员备注不能为空")
    @Size(max = 1000, message = "管理员备注不能超过1000个字符")
    private String adminComment;

    /**
     * 退款金额
     * 批准申诉时的实际退款金额，拒绝时可不填
     */
    @DecimalMin(value = "0.0", message = "退款金额不能为负数")
    private BigDecimal refundAmount;

    /**
     * 检查是否为批准动作
     * 
     * @return 是否批准
     */
    public boolean isApproval() {
        return "APPROVE".equals(action);
    }

    /**
     * 检查是否为拒绝动作
     * 
     * @return 是否拒绝
     */
    public boolean isRejection() {
        return "REJECT".equals(action);
    }

    /**
     * 检查是否有退款金额
     * 
     * @return 是否有退款
     */
    public boolean hasRefundAmount() {
        return refundAmount != null && refundAmount.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 验证处理信息是否完整
     * 
     * @return 验证结果描述
     */
    public String validateProcessInfo() {
        if (action == null || action.trim().isEmpty()) {
            return "处理动作不能为空";
        }

        if (!"APPROVE".equals(action) && !"REJECT".equals(action)) {
            return "处理动作只能是APPROVE或REJECT";
        }

        if (adminComment == null || adminComment.trim().isEmpty()) {
            return "管理员备注不能为空";
        }

        if (adminComment.length() > 1000) {
            return "管理员备注不能超过1000个字符";
        }

        // 批准申诉但没有退款金额的检查
        if (isApproval() && !hasRefundAmount()) {
            return "批准申诉时建议填写退款金额";
        }

        // 拒绝申诉但填写了退款金额的检查
        if (isRejection() && hasRefundAmount()) {
            return "拒绝申诉时不应填写退款金额";
        }

        if (refundAmount != null && refundAmount.compareTo(BigDecimal.ZERO) < 0) {
            return "退款金额不能为负数";
        }

        return "验证通过";
    }

    /**
     * 获取处理动作的显示文本
     * 
     * @return 动作显示文本
     */
    public String getActionDisplay() {
        if (action == null) {
            return "未知动作";
        }

        return switch (action) {
            case "APPROVE" -> "批准";
            case "REJECT" -> "拒绝";
            default -> "未知动作";
        };
    }

    /**
     * 获取格式化的退款金额
     * 
     * @return 格式化的退款金额字符串
     */
    public String getFormattedRefundAmount() {
        if (!hasRefundAmount()) {
            return "无退款";
        }

        return "¥" + refundAmount.toString();
    }

    /**
     * 获取处理摘要
     * 
     * @return 处理摘要信息
     */
    public String getProcessSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("处理动作: ").append(getActionDisplay());

        if (hasRefundAmount()) {
            summary.append(" | 退款: ").append(getFormattedRefundAmount());
        }

        if (adminComment != null && !adminComment.trim().isEmpty()) {
            String shortNote = adminComment.length() > 50 ? adminComment.substring(0, 50) + "..." : adminComment;
            summary.append(" | 备注: ").append(shortNote);
        }

        return summary.toString();
    }

    /**
     * 检查处理结果的合理性
     * 
     * @param requestedAmount 用户期望的退款金额
     * @return 合理性检查结果
     */
    public String checkReasonableness(BigDecimal requestedAmount) {
        if (isRejection()) {
            return "拒绝申诉，无需检查退款金额";
        }

        if (!hasRefundAmount()) {
            return "批准申诉但未设置退款金额";
        }

        if (requestedAmount == null) {
            return "用户未提出退款要求，但设置了退款金额";
        }

        // 检查退款金额是否超过用户期望
        int comparison = refundAmount.compareTo(requestedAmount);
        if (comparison > 0) {
            BigDecimal excess = refundAmount.subtract(requestedAmount);
            return "退款金额超过用户期望 ¥" + excess.toString();
        } else if (comparison < 0) {
            BigDecimal shortage = requestedAmount.subtract(refundAmount);
            return "退款金额少于用户期望 ¥" + shortage.toString();
        } else {
            return "退款金额与用户期望一致";
        }
    }
}