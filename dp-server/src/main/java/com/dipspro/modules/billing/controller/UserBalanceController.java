package com.dipspro.modules.billing.controller;

import java.math.BigDecimal;
import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.billing.dto.BalanceAdjustDto;
import com.dipspro.modules.billing.dto.BalanceHistoryDto;
import com.dipspro.modules.billing.dto.BatchAdjustResult;
import com.dipspro.modules.billing.dto.UserBalanceDto;
import com.dipspro.modules.billing.entity.UserBalance;
import com.dipspro.modules.billing.service.UserBalanceService;
import com.dipspro.util.SecurityUtil;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户余额管理控制器
 * 
 * 提供用户余额相关的REST API接口，包括：
 * - 用户端：查看余额信息、余额历史、余额统计
 * - 管理端：余额调整、余额审计、批量操作
 * 
 * <AUTHOR> Pro Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/billing")
@RequiredArgsConstructor
@Validated
public class UserBalanceController {

    private final UserBalanceService userBalanceService;

    /**
     * 获取当前用户余额信息（用户端）
     * 
     * 获取当前登录用户的详细余额信息，包括现金余额、赠送余额、
     * 冻结余额等各种余额类型的详细信息。
     * 
     * @return 用户余额信息
     */
    @GetMapping("/balance")
    public ApiResponse<UserBalanceDto> getMyBalance() {
        log.info("获取用户余额信息");

        Long userId = SecurityUtil.getCurrentUserId();

        var balanceOpt = userBalanceService.getUserBalance(userId);
        if (balanceOpt.isEmpty()) {
            log.warn("用户余额不存在: UserId={}", userId);
            return ApiResponse.error("用户余额信息不存在");
        }

        UserBalance balance = balanceOpt.get();
        UserBalanceDto balanceDto = convertToUserBalanceDto(balance);

        log.info("用户余额信息获取成功: UserId={}, TotalBalance={}", userId, balance.getTotalBalance());
        return ApiResponse.success(balanceDto, "查询成功");
    }

    /**
     * 获取用户余额历史（用户端）
     * 
     * 分页查询当前用户的余额变动历史记录，按时间倒序排列。
     * 
     * @param page 页码，从0开始
     * @param size 每页大小，默认20，最大100
     * @param type 变动类型筛选：INCOME, EXPENSE, TRANSFER
     * @return 分页的余额历史记录
     */
    @GetMapping("/balance/history")
    public ApiResponse<Page<BalanceHistoryDto>> getMyBalanceHistory(
            @RequestParam(defaultValue = "0") @Min(0) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size,
            @RequestParam(required = false) String type) {

        log.info("查询用户余额历史: page={}, size={}, type={}", page, size, type);

        Long userId = SecurityUtil.getCurrentUserId();
        Pageable pageable = PageRequest.of(page, size);

        Page<BalanceHistoryDto> history = userBalanceService.getUserBalanceHistory(
                userId, type, pageable);

        log.info("用户余额历史查询完成: UserId={}, TotalRecords={}", userId, history.getTotalElements());
        return ApiResponse.success(history, "查询成功");
    }

    /**
     * 获取用户余额统计（用户端）
     * 
     * 获取用户的余额统计信息，包括各类型余额的分布、变动趋势等。
     * 
     * @param period 统计周期：WEEK, MONTH, QUARTER, YEAR
     * @return 余额统计信息
     */
    @GetMapping("/balance/statistics")
    public ApiResponse<Object> getMyBalanceStatistics(
            @RequestParam(defaultValue = "MONTH") String period) {

        log.info("获取用户余额统计: period={}", period);

        Long userId = SecurityUtil.getCurrentUserId();

        var statistics = userBalanceService.getUserBalanceStatistics(userId, period);

        log.info("用户余额统计获取成功: UserId={}, Period={}", userId, period);
        return ApiResponse.success(statistics, "统计信息获取成功");
    }

    /**
     * 检查余额是否充足（用户端）
     * 
     * 检查用户余额是否足够支付指定金额，用于前端预检查。
     * 
     * @param amount      需要检查的金额
     * @param balanceType 余额类型：CASH, GIFT, TOTAL
     * @return 余额检查结果
     */
    @GetMapping("/balance/check")
    public ApiResponse<Object> checkBalance(
            @RequestParam @NotNull BigDecimal amount,
            @RequestParam(defaultValue = "TOTAL") String balanceType) {

        log.info("检查用户余额: amount={}, balanceType={}", amount, balanceType);

        Long userId = SecurityUtil.getCurrentUserId();

        boolean sufficient = userBalanceService.checkBalanceSufficient(userId, amount, balanceType);

        var result = new Object() {
            public final boolean sufficientResult = sufficient;
            public final BigDecimal checkAmount = amount;
            public final String balanceTypeValue = balanceType;
            public final String message = sufficient ? "余额充足" : "余额不足";
        };

        log.info("用户余额检查完成: UserId={}, Amount={}, Sufficient={}", userId, amount, sufficient);
        return ApiResponse.success(result, "检查完成");
    }

    /**
     * 发起充值（用户端）
     * 
     * 用户发起充值请求，系统生成支付订单并返回支付信息。
     * 支持多种支付方式（微信、支付宝等）。
     * 
     * @param amount        充值金额
     * @param paymentMethod 支付方式：WECHAT, ALIPAY
     * @return 支付订单信息
     */
    @PostMapping("/recharge")
    public ApiResponse<Object> initiateRecharge(
            @RequestParam @NotNull BigDecimal amount,
            @RequestParam @NotNull String paymentMethod) {

        log.info("用户发起充值: Amount={}, PaymentMethod={}", amount, paymentMethod);

        Long userId = SecurityUtil.getCurrentUserId();

        // 参数验证
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            return ApiResponse.error("充值金额必须大于0");
        }

        if (amount.compareTo(new BigDecimal("10000")) > 0) {
            return ApiResponse.error("单次充值金额不能超过10000元");
        }

        if (!List.of("WECHAT", "ALIPAY").contains(paymentMethod)) {
            return ApiResponse.error("不支持的支付方式");
        }

        // TODO: 集成支付服务，生成支付订单
        // PaymentOrderDto paymentOrder = paymentService.createPaymentOrder(userId,
        // amount, paymentMethod);

        // 临时返回，待支付模块完成后替换
        var result = new Object() {
            public final BigDecimal chargeAmount = amount;
            public final String paymentMethodValue = paymentMethod;
            public final Long userIdValue = userId;
            public final String message = "充值功能开发中，敬请期待";
            public final String paymentNo = "PAY" + System.currentTimeMillis();
        };

        log.info("充值请求已记录: UserId={}, Amount={}, PaymentMethod={}", userId, amount, paymentMethod);
        return ApiResponse.success(result, "充值请求已提交，支付功能开发中");
    }

    /**
     * 分页查询用户余额（管理端）
     * 
     * 管理员可以分页查询所有用户的余额信息，支持按余额范围、用户等筛选。
     * 
     * @param page       页码，从0开始
     * @param size       每页大小，默认20，最大100
     * @param userId     用户ID筛选
     * @param minBalance 最小余额筛选
     * @param maxBalance 最大余额筛选
     * @param orderBy    排序字段：TOTAL_BALANCE, CASH_BALANCE, CREATED_AT
     * @return 分页的用户余额列表
     */
    @GetMapping("/admin/list")
    public ApiResponse<Page<UserBalanceDto>> getUserBalancesForAdmin(
            @RequestParam(defaultValue = "0") @Min(0) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) BigDecimal minBalance,
            @RequestParam(required = false) BigDecimal maxBalance,
            @RequestParam(defaultValue = "TOTAL_BALANCE") String orderBy) {

        log.info("管理员查询用户余额: page={}, size={}, userId={}, minBalance={}, maxBalance={}, orderBy={}",
                page, size, userId, minBalance, maxBalance, orderBy);

        Pageable pageable = PageRequest.of(page, size);
        Page<UserBalance> balances = userBalanceService.getUserBalancesForAdmin(
                userId, minBalance, maxBalance, orderBy, pageable);

        Page<UserBalanceDto> balanceDtos = balances.map(this::convertToUserBalanceDto);

        log.info("管理员余额查询完成: TotalUsers={}", balances.getTotalElements());
        return ApiResponse.success(balanceDtos, "查询成功");
    }

    /**
     * 获取指定用户余额详情（管理端）
     * 
     * 管理员查看指定用户的详细余额信息和操作历史。
     * 
     * @param userId 用户ID
     * @return 用户余额详情
     */
    @GetMapping("/admin/user/{userId}")
    public ApiResponse<UserBalanceDto> getUserBalanceDetail(@PathVariable @NotNull Long userId) {
        log.info("获取用户余额详情: UserId={}", userId);

        var balanceOpt = userBalanceService.getUserBalance(userId);
        if (balanceOpt.isEmpty()) {
            log.warn("用户余额不存在: UserId={}", userId);
            return ApiResponse.error("用户余额信息不存在");
        }

        UserBalance balance = balanceOpt.get();
        UserBalanceDto balanceDto = convertToUserBalanceDto(balance);

        log.info("用户余额详情获取成功: UserId={}, TotalBalance={}", userId, balance.getTotalBalance());
        return ApiResponse.success(balanceDto, "查询成功");
    }

    /**
     * 调整用户余额（管理端）
     * 
     * 管理员手动调整用户余额，支持增加或减少现金余额、赠送余额等。
     * 需要提供调整原因和审批流程。
     * 
     * @param userId    用户ID
     * @param adjustDto 余额调整参数
     * @return 调整结果
     */
    @PutMapping("/admin/user/{userId}/adjust")
    public ApiResponse<UserBalanceDto> adjustUserBalance(
            @PathVariable @NotNull Long userId,
            @Valid @RequestBody BalanceAdjustDto adjustDto) {

        log.info("调整用户余额: UserId={}, AdjustType={}, Amount={}, Reason={}",
                userId, adjustDto.getAdjustType(), adjustDto.getAmount(), adjustDto.getReason());

        // 验证调整参数
        String validationResult = adjustDto.validateAdjustment();
        if (!"验证通过".equals(validationResult)) {
            log.warn("余额调整参数验证失败: {}", validationResult);
            return ApiResponse.error(validationResult);
        }

        // 执行余额调整
        UserBalance adjustedBalance = userBalanceService.adjustUserBalance(
                userId, adjustDto.getAdjustType(), adjustDto.getAmount(), adjustDto.getReason());

        UserBalanceDto balanceDto = convertToUserBalanceDto(adjustedBalance);

        log.info("用户余额调整完成: UserId={}, NewTotalBalance={}", userId, adjustedBalance.getTotalBalance());
        return ApiResponse.success(balanceDto, "余额调整成功");
    }

    /**
     * 冻结用户余额（管理端）
     * 
     * 管理员冻结用户的指定金额余额，冻结后该部分余额不可使用。
     * 
     * @param userId 用户ID
     * @param amount 冻结金额
     * @param reason 冻结原因
     * @return 冻结结果
     */
    @PostMapping("/admin/user/{userId}/freeze")
    public ApiResponse<UserBalanceDto> freezeUserBalance(
            @PathVariable @NotNull Long userId,
            @RequestParam @NotNull BigDecimal amount,
            @RequestParam @NotNull String reason) {

        log.info("冻结用户余额: UserId={}, Amount={}, Reason={}", userId, amount, reason);

        // 验证冻结金额
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("冻结金额无效: Amount={}", amount);
            return ApiResponse.error("冻结金额必须大于0");
        }

        // 执行余额冻结
        boolean freezeSuccess = userBalanceService.freezeUserBalance(userId, amount, reason);
        if (!freezeSuccess) {
            log.warn("用户余额冻结失败: UserId={}, Amount={}", userId, amount);
            return ApiResponse.error("余额冻结失败");
        }

        // 重新获取用户余额信息
        UserBalance balance = userBalanceService.getOrCreateUserBalance(userId);
        UserBalanceDto balanceDto = convertToUserBalanceDto(balance);

        log.info("用户余额冻结完成: UserId={}, FrozenAmount={}", userId, amount);
        return ApiResponse.success(balanceDto, "余额冻结成功");
    }

    /**
     * 解冻用户余额（管理端）
     * 
     * 管理员解冻用户的冻结余额，解冻后余额可正常使用。
     * 
     * @param userId 用户ID
     * @param amount 解冻金额
     * @param reason 解冻原因
     * @return 解冻结果
     */
    @PostMapping("/admin/user/{userId}/unfreeze")
    public ApiResponse<UserBalanceDto> unfreezeUserBalance(
            @PathVariable @NotNull Long userId,
            @RequestParam @NotNull BigDecimal amount,
            @RequestParam @NotNull String reason) {

        log.info("解冻用户余额: UserId={}, Amount={}, Reason={}", userId, amount, reason);

        // 验证解冻金额
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("解冻金额无效: Amount={}", amount);
            return ApiResponse.error("解冻金额必须大于0");
        }

        // 执行余额解冻
        boolean unfreezeSuccess = userBalanceService.unfreezeUserBalance(userId, amount, reason);
        if (!unfreezeSuccess) {
            log.warn("用户余额解冻失败: UserId={}, Amount={}", userId, amount);
            return ApiResponse.error("余额解冻失败");
        }

        // 重新获取用户余额信息
        UserBalance balance = userBalanceService.getOrCreateUserBalance(userId);
        UserBalanceDto balanceDto = convertToUserBalanceDto(balance);

        log.info("用户余额解冻完成: UserId={}, UnfrozenAmount={}", userId, amount);
        return ApiResponse.success(balanceDto, "余额解冻成功");
    }

    /**
     * 获取余额统计报告（管理端）
     * 
     * 生成平台整体的余额统计报告，包括总余额、用户分布、异常账户等。
     * 
     * @return 余额统计报告
     */
    @GetMapping("/admin/statistics")
    public ApiResponse<Object> getBalanceStatistics() {
        log.info("获取余额统计报告");

        var statistics = userBalanceService.getBalanceStatistics();

        log.info("余额统计报告获取成功");
        return ApiResponse.success(statistics, "统计报告生成成功");
    }

    /**
     * 获取余额预警列表（管理端）
     * 
     * 获取需要关注的余额账户，如余额过低、异常变动等。
     * 
     * @param alertType 预警类型：LOW_BALANCE, ABNORMAL_CHANGE, LARGE_AMOUNT
     * @param limit     返回数量限制，默认50
     * @return 预警账户列表
     */
    @GetMapping("/admin/alerts")
    public ApiResponse<List<UserBalanceDto>> getBalanceAlerts(
            @RequestParam(required = false) String alertType,
            @RequestParam(defaultValue = "50") @Min(1) @Max(200) Integer limit) {

        log.info("获取余额预警列表: alertType={}, limit={}", alertType, limit);

        List<UserBalance> alertBalances = userBalanceService.getBalanceAlerts(alertType, limit);

        List<UserBalanceDto> balanceDtos = alertBalances.stream()
                .map(this::convertToUserBalanceDto)
                .toList();

        log.info("余额预警列表获取成功: AlertCount={}", balanceDtos.size());
        return ApiResponse.success(balanceDtos, "查询成功");
    }

    /**
     * 批量调整用户余额（管理端）
     * 
     * 批量调整多个用户的余额，用于促销活动或补偿操作。
     * 
     * @param userIds   用户ID列表
     * @param adjustDto 调整参数
     * @return 批量调整结果
     */
    @PostMapping("/admin/batch-adjust")
    public ApiResponse<BatchAdjustResult> batchAdjustBalance(
            @RequestParam List<Long> userIds,
            @Valid @RequestBody BalanceAdjustDto adjustDto) {

        log.info("批量调整用户余额: UserCount={}, AdjustType={}, Amount={}",
                userIds.size(), adjustDto.getAdjustType(), adjustDto.getAmount());

        // 验证调整参数
        String validationResult = adjustDto.validateAdjustment();
        if (!"验证通过".equals(validationResult)) {
            log.warn("批量余额调整参数验证失败: {}", validationResult);
            return ApiResponse.error(validationResult);
        }

        // 执行批量调整
        var result = userBalanceService.batchAdjustBalance(userIds, adjustDto);

        log.info("批量余额调整完成: SuccessCount={}, FailCount={}",
                result.getSuccessCount(), result.getFailCount());
        return ApiResponse.success(result, "批量调整完成");
    }

    /**
     * 转换UserBalance实体为DTO
     * 
     * @param balance 用户余额实体
     * @return 用户余额DTO
     */
    private UserBalanceDto convertToUserBalanceDto(UserBalance balance) {
        return new UserBalanceDto()
                .setId(balance.getId())
                .setUserId(balance.getUserId())
                .setCashBalance(balance.getCashBalance())
                .setGiftBalance(balance.getGiftBalance())
                .setFrozenAmount(balance.getFrozenBalance())
                .setTotalBalance(balance.getTotalBalance())
                .setLastTransactionAt(balance.getLastTransactionAt())
                .setCreatedAt(balance.getCreatedAt())
                .setUpdatedAt(balance.getUpdatedAt());
    }
}