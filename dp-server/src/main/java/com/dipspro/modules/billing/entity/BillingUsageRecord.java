package com.dipspro.modules.billing.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

import org.hibernate.annotations.Comment;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 计费使用记录实体
 * 对应数据库表：b_billing_usage_records
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "b_billing_usage_records")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Comment("计费使用记录表（以消息为单位）")
public class BillingUsageRecord {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("主键ID")
    private Long id;

    /**
     * 用户ID，关联users.id
     */
    @Column(name = "user_id", nullable = false)
    @NotNull(message = "用户ID不能为空")
    @Comment("用户ID，关联users.id")
    private Long userId;

    /**
     * 消息ID，关联chat_messages.id
     */
    @Column(name = "message_id", nullable = false)
    @NotNull(message = "消息ID不能为空")
    @Comment("消息ID，关联chat_messages.id（消息内容通过此ID关联获取）")
    private UUID messageId;

    /**
     * 对话ID
     */
    @Column(name = "conversation_id")
    @Comment("对话ID")
    private UUID conversationId;

    /**
     * 消息角色：USER, ASSISTANT
     */
    @Column(name = "message_role", length = 20)
    @Comment("消息角色：USER, ASSISTANT")
    private String messageRole;

    /**
     * 使用的模型名称
     */
    @Column(name = "model_name", length = 50, nullable = false)
    @NotNull(message = "模型名称不能为空")
    @Comment("使用的模型名称")
    private String modelName;

    // Token统计
    /**
     * 输入Token数量
     */
    @Column(name = "input_tokens", nullable = false)
    @PositiveOrZero(message = "输入Token数量不能为负数")
    @Comment("输入Token数量")
    private Long inputTokens = 0L;

    /**
     * 输出Token数量
     */
    @Column(name = "output_tokens", nullable = false)
    @PositiveOrZero(message = "输出Token数量不能为负数")
    @Comment("输出Token数量")
    private Long outputTokens = 0L;

    /**
     * 思维链Token数量
     */
    @Column(name = "thought_chain_tokens")
    @PositiveOrZero(message = "思维链Token数量不能为负数")
    @Comment("思维链Token数量")
    private Long thoughtChainTokens = 0L;

    /**
     * 总Token数量
     */
    @Column(name = "total_tokens", nullable = false)
    @PositiveOrZero(message = "总Token数量不能为负数")
    @Comment("总Token数量")
    private Long totalTokens = 0L;

    // 费用计算
    /**
     * 输入费用
     */
    @Column(name = "input_cost", precision = 10, scale = 6, nullable = false)
    @NotNull(message = "输入费用不能为空")
    @PositiveOrZero(message = "输入费用不能为负数")
    @Comment("输入费用")
    private BigDecimal inputCost = BigDecimal.ZERO;

    /**
     * 输出费用
     */
    @Column(name = "output_cost", precision = 10, scale = 6, nullable = false)
    @NotNull(message = "输出费用不能为空")
    @PositiveOrZero(message = "输出费用不能为负数")
    @Comment("输出费用")
    private BigDecimal outputCost = BigDecimal.ZERO;

    /**
     * 总费用
     */
    @Column(name = "total_cost", precision = 10, scale = 6, nullable = false)
    @NotNull(message = "总费用不能为空")
    @PositiveOrZero(message = "总费用不能为负数")
    @Comment("总费用")
    private BigDecimal totalCost = BigDecimal.ZERO;

    // 状态管理
    /**
     * 状态：SUCCESS, FAILED, PENDING
     */
    @Column(name = "status", length = 20)
    @Comment("状态：SUCCESS, FAILED, PENDING")
    private String status = "SUCCESS";

    /**
     * 计费状态：BILLED, UNBILLED, REFUNDED
     */
    @Column(name = "billing_status", length = 20)
    @Comment("计费状态：BILLED, UNBILLED, REFUNDED")
    private String billingStatus = "BILLED";

    /**
     * 计费类型：TOKEN_BASED, FAILED, MANUAL
     */
    @Column(name = "billing_type", length = 20)
    @Comment("计费类型：TOKEN_BASED, FAILED, MANUAL")
    private String billingType = "TOKEN_BASED";

    // 套餐信息
    /**
     * 使用的套餐ID，关联b_billing_packages.id
     */
    @Column(name = "package_id")
    @Comment("使用的套餐ID，关联b_billing_packages.id")
    private Long packageId;

    /**
     * 计费时的输入Token单价
     */
    @Column(name = "input_token_price", precision = 10, scale = 6)
    @Comment("计费时的输入Token单价")
    private BigDecimal inputTokenPrice;

    /**
     * 计费时的输出Token单价
     */
    @Column(name = "output_token_price", precision = 10, scale = 6)
    @Comment("计费时的输出Token单价")
    private BigDecimal outputTokenPrice;

    // 申诉相关
    /**
     * 是否已申诉
     */
    @Column(name = "is_appealed")
    @Comment("是否已申诉")
    private Boolean isAppealed = false;

    /**
     * 申诉状态：PENDING, APPROVED, REJECTED
     */
    @Column(name = "appeal_status", length = 20)
    @Comment("申诉状态：PENDING, APPROVED, REJECTED")
    private String appealStatus;

    // 时间字段
    /**
     * 请求时间
     */
    @Column(name = "request_time")
    @Comment("请求时间")
    private LocalDateTime requestTime;

    /**
     * 响应时间
     */
    @Column(name = "response_time")
    @Comment("响应时间")
    private LocalDateTime responseTime;

    /**
     * 处理时长（毫秒）
     */
    @Column(name = "duration_ms")
    @PositiveOrZero(message = "处理时长不能为负数")
    @Comment("处理时长（毫秒）")
    private Long durationMs;

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false, updatable = false)
    @Comment("创建时间")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    @Comment("更新时间")
    private LocalDateTime updatedAt;

    /**
     * 创建时自动设置创建时间和更新时间
     */
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createdAt = now;
        this.updatedAt = now;
        calculateTotals();
    }

    /**
     * 更新时自动设置更新时间
     */
    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
        calculateTotals();
    }

    /**
     * 计算处理时长
     */
    public void calculateDuration() {
        if (requestTime != null && responseTime != null) {
            this.durationMs = java.time.Duration.between(requestTime, responseTime).toMillis();
        }
    }

    /**
     * 计算总费用和总Token数量
     * 自动在保存和更新时调用
     */
    private void calculateTotals() {
        // 计算总费用
        BigDecimal inputCostValue = this.inputCost != null ? this.inputCost : BigDecimal.ZERO;
        BigDecimal outputCostValue = this.outputCost != null ? this.outputCost : BigDecimal.ZERO;
        this.totalCost = inputCostValue.add(outputCostValue);

        // 计算总Token数量
        Long inputTokensValue = this.inputTokens != null ? this.inputTokens : 0L;
        Long outputTokensValue = this.outputTokens != null ? this.outputTokens : 0L;
        Long thoughtChainTokensValue = this.thoughtChainTokens != null ? this.thoughtChainTokens : 0L;
        this.totalTokens = inputTokensValue + outputTokensValue + thoughtChainTokensValue;
    }

    /**
     * 检查是否计费成功
     * 
     * @return 是否计费成功
     */
    public boolean isBilledSuccessfully() {
        return "SUCCESS".equals(status) && "BILLED".equals(billingStatus);
    }

    /**
     * 检查是否可以申诉
     * 
     * @return 是否可以申诉
     */
    public boolean canAppeal() {
        return isBilledSuccessfully() && !Boolean.TRUE.equals(isAppealed);
    }

    /**
     * 手动重新计算总费用和总Token数量
     * 
     * @return 当前实例，支持链式调用
     */
    public BillingUsageRecord recalculateTotals() {
        calculateTotals();
        return this;
    }
}