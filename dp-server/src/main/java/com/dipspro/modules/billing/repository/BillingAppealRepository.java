package com.dipspro.modules.billing.repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.dipspro.modules.billing.entity.BillingAppeal;

/**
 * 计费申诉记录数据访问接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface BillingAppealRepository extends JpaRepository<BillingAppeal, Long> {

        /**
         * 分页查询所有申诉记录（按创建时间倒序）
         * 
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<BillingAppeal> findAllByOrderByCreatedAtDesc(Pageable pageable);

        /**
         * 根据用户ID分页查询申诉记录
         * 
         * @param userId   用户ID
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<BillingAppeal> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

        /**
         * 根据使用记录ID查询申诉记录
         * 
         * @param usageRecordId 使用记录ID
         * @return 申诉记录
         */
        Optional<BillingAppeal> findByUsageRecordId(Long usageRecordId);

        /**
         * 检查使用记录是否已有申诉
         * 
         * @param usageRecordId 使用记录ID
         * @return 是否存在
         */
        boolean existsByUsageRecordId(Long usageRecordId);

        /**
         * 根据申诉状态查询申诉记录
         * 
         * @param status   申诉状态
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<BillingAppeal> findByStatusOrderByCreatedAtDesc(String status, Pageable pageable);

        /**
         * 根据用户ID和申诉状态查询申诉记录
         * 
         * @param userId   用户ID
         * @param status   申诉状态
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<BillingAppeal> findByUserIdAndStatusOrderByCreatedAtDesc(Long userId, String status, Pageable pageable);

        /**
         * 根据处理人员ID查询申诉记录
         * 
         * @param adminId  处理人员ID
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<BillingAppeal> findByAdminIdOrderByCreatedAtDesc(Long adminId, Pageable pageable);

        /**
         * 查询待处理的申诉记录
         * 
         * @param pageable 分页参数
         * @return 分页结果
         */
        @Query("SELECT ba FROM BillingAppeal ba WHERE ba.status = 'PENDING' ORDER BY ba.createdAt ASC")
        Page<BillingAppeal> findPendingAppeals(Pageable pageable);

        /**
         * 查询用户指定时间范围内的申诉记录
         * 
         * @param userId    用户ID
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @param pageable  分页参数
         * @return 分页结果
         */
        Page<BillingAppeal> findByUserIdAndCreatedAtBetweenOrderByCreatedAtDesc(
                        Long userId, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

        /**
         * 查询用户申诉统计信息
         * 
         * @param userId 用户ID
         * @return 包含总申诉数、待处理数、已通过数、已拒绝数的统计信息
         */
        @Query("SELECT COUNT(ba), " +
                        "SUM(CASE WHEN ba.status = 'PENDING' THEN 1 ELSE 0 END), " +
                        "SUM(CASE WHEN ba.status = 'APPROVED' THEN 1 ELSE 0 END), " +
                        "SUM(CASE WHEN ba.status = 'REJECTED' THEN 1 ELSE 0 END) " +
                        "FROM BillingAppeal ba WHERE ba.userId = :userId")
        Object[] getUserAppealStats(@Param("userId") Long userId);

        /**
         * 查询系统申诉统计信息
         * 
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 按状态分组的统计信息
         */
        @Query("SELECT ba.status, COUNT(ba), SUM(ba.refundAmount) " +
                        "FROM BillingAppeal ba WHERE ba.createdAt >= :startTime AND ba.createdAt <= :endTime " +
                        "GROUP BY ba.status ORDER BY COUNT(ba) DESC")
        List<Object[]> getSystemAppealStats(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 查询待处理申诉的数量
         * 
         * @return 待处理申诉数量
         */
        @Query("SELECT COUNT(ba) FROM BillingAppeal ba WHERE ba.status = 'PENDING'")
        long countPendingAppeals();

        /**
         * 查询用户当日申诉次数
         * 
         * @param userId     用户ID
         * @param startOfDay 今日开始时间
         * @return 当日申诉次数
         */
        @Query("SELECT COUNT(ba) FROM BillingAppeal ba WHERE ba.userId = :userId AND ba.createdAt >= :startOfDay")
        long countUserTodayAppeals(@Param("userId") Long userId, @Param("startOfDay") LocalDateTime startOfDay);

        /**
         * 查询高金额申诉记录（申诉金额超过指定阈值）
         * 
         * @param amountThreshold 金额阈值
         * @param pageable        分页参数
         * @return 分页结果
         */
        @Query("SELECT ba FROM BillingAppeal ba WHERE ba.refundAmount >= :amountThreshold " +
                        "ORDER BY ba.refundAmount DESC, ba.createdAt DESC")
        Page<BillingAppeal> findHighAmountAppeals(@Param("amountThreshold") BigDecimal amountThreshold,
                        Pageable pageable);

        /**
         * 查询长时间未处理的申诉记录
         * 
         * @param beforeTime 时间阈值
         * @param pageable   分页参数
         * @return 分页结果
         */
        @Query("SELECT ba FROM BillingAppeal ba WHERE ba.status = 'PENDING' AND ba.createdAt < :beforeTime " +
                        "ORDER BY ba.createdAt ASC")
        Page<BillingAppeal> findLongPendingAppeals(@Param("beforeTime") LocalDateTime beforeTime,
                        Pageable pageable);

        /**
         * 更新申诉状态和处理结果
         * 
         * @param id           申诉ID
         * @param status       新状态
         * @param adminId      处理人员ID
         * @param adminComment 处理意见
         * @param processedAt  处理时间
         * @return 更新的记录数
         */
        @Modifying
        @Query("UPDATE BillingAppeal ba SET ba.status = :status, ba.adminId = :adminId, " +
                        "ba.adminComment = :adminComment, ba.processedAt = :processedAt, ba.updatedAt = CURRENT_TIMESTAMP "
                        +
                        "WHERE ba.id = :id")
        int updateAppealStatus(@Param("id") Long id,
                        @Param("status") String status,
                        @Param("adminId") Long adminId,
                        @Param("adminComment") String adminComment,
                        @Param("processedAt") LocalDateTime processedAt);

        /**
         * 批量分配申诉给处理人员
         * 
         * @param appealIds 申诉ID列表
         * @param adminId   处理人员ID
         * @return 更新的记录数
         */
        @Modifying
        @Query("UPDATE BillingAppeal ba SET ba.adminId = :adminId, ba.updatedAt = CURRENT_TIMESTAMP " +
                        "WHERE ba.id IN :appealIds AND ba.status = 'PENDING'")
        int assignAppealsToHandler(@Param("appealIds") List<Long> appealIds, @Param("adminId") Long adminId);

        /**
         * 查询处理人员的申诉处理统计
         * 
         * @param adminId   处理人员ID
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 包含处理总数、通过数、拒绝数的统计信息
         */
        @Query("SELECT COUNT(ba), " +
                        "SUM(CASE WHEN ba.status = 'APPROVED' THEN 1 ELSE 0 END), " +
                        "SUM(CASE WHEN ba.status = 'REJECTED' THEN 1 ELSE 0 END) " +
                        "FROM BillingAppeal ba WHERE ba.adminId = :adminId " +
                        "AND ba.processedAt >= :startTime AND ba.processedAt <= :endTime")
        Object[] getHandlerAppealStats(@Param("adminId") Long adminId,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 根据申诉原因搜索申诉记录
         * 
         * @param keyword  关键词
         * @param pageable 分页参数
         * @return 分页结果
         */
        @Query("SELECT ba FROM BillingAppeal ba WHERE ba.reason LIKE %:keyword% " +
                        "ORDER BY ba.createdAt DESC")
        Page<BillingAppeal> searchByAppealReason(@Param("keyword") String keyword, Pageable pageable);

        /**
         * 查询用户最近的申诉记录
         * 
         * @param userId   用户ID
         * @param pageable 分页参数
         * @return 最近的申诉记录列表
         */
        @Query("SELECT ba FROM BillingAppeal ba WHERE ba.userId = :userId " +
                        "ORDER BY ba.createdAt DESC")
        List<BillingAppeal> findRecentAppealsByUserId(@Param("userId") Long userId, Pageable pageable);

        /**
         * 查询活跃申诉用户列表（指定时间范围内有申诉记录的用户）
         * 
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 活跃用户ID列表
         */
        @Query("SELECT DISTINCT ba.userId FROM BillingAppeal ba " +
                        "WHERE ba.createdAt >= :startTime AND ba.createdAt <= :endTime")
        List<Long> getActiveAppealUsers(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 查询申诉处理效率统计（平均处理时间）
         * 
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 平均处理时间（小时）
         */
        @Query(value = "SELECT AVG(EXTRACT(EPOCH FROM (processed_at - created_at)) / 3600) " +
                        "FROM b_billing_appeals WHERE status IN ('APPROVED', 'REJECTED') " +
                        "AND processed_at >= :startTime AND processed_at <= :endTime", nativeQuery = true)
        Double getAverageHandleTimeInHours(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 删除指定时间之前的历史申诉记录
         * 
         * @param beforeTime 时间阈值
         * @return 删除的记录数
         */
        @Modifying
        @Query("DELETE FROM BillingAppeal ba WHERE ba.createdAt < :beforeTime")
        int deleteAppealsBeforeTime(@Param("beforeTime") LocalDateTime beforeTime);

        /**
         * 查询用户申诉成功率
         * 
         * @param userId 用户ID
         * @return 申诉成功率（百分比）
         */
        @Query("SELECT CASE WHEN COUNT(ba) = 0 THEN 0 ELSE " +
                        "(CAST(SUM(CASE WHEN ba.status = 'APPROVED' THEN 1 ELSE 0 END) AS DOUBLE) / COUNT(ba)) * 100 END "
                        +
                        "FROM BillingAppeal ba WHERE ba.userId = :userId AND ba.status IN ('APPROVED', 'REJECTED')")
        Double getUserAppealSuccessRate(@Param("userId") Long userId);

        /**
         * 根据状态统计申诉数量
         * 
         * @param status 申诉状态
         * @return 申诉数量
         */
        long countByStatus(String status);

        /**
         * 统计用户在指定时间范围内的申诉数量
         * 
         * @param userId    用户ID
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 申诉数量
         */
        long countByUserIdAndCreatedAtBetween(Long userId, LocalDateTime startTime, LocalDateTime endTime);

        /**
         * 根据申诉原因搜索申诉记录
         * 
         * @param keyword  关键词
         * @param pageable 分页参数
         * @return 分页结果
         */
        @Query("SELECT ba FROM BillingAppeal ba WHERE ba.reason LIKE CONCAT('%', :keyword, '%') ORDER BY ba.createdAt DESC")
        Page<BillingAppeal> searchByReason(@Param("keyword") String keyword, Pageable pageable);

        /**
         * 查询用户最近的申诉记录（限制数量）
         * 
         * @param userId   用户ID
         * @param pageable 分页参数（用于限制数量）
         * @return 最近的申诉记录列表
         */
        @Query("SELECT ba FROM BillingAppeal ba WHERE ba.userId = :userId " +
                        "ORDER BY ba.createdAt DESC")
        List<BillingAppeal> findTopByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId, Pageable pageable);

        /**
         * 查询指定时间范围内的活跃申诉用户
         * 
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 活跃用户ID列表
         */
        @Query("SELECT DISTINCT ba.userId FROM BillingAppeal ba " +
                        "WHERE ba.createdAt >= :startTime AND ba.createdAt <= :endTime")
        List<Long> findActiveUsersInDateRange(@Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * 删除指定时间之前已处理的申诉记录
         * 
         * @param beforeTime 时间阈值
         * @return 删除的记录数
         */
        @Modifying
        @Query("DELETE FROM BillingAppeal ba WHERE ba.createdAt < :beforeTime " +
                        "AND ba.status IN ('APPROVED', 'REJECTED')")
        int deleteProcessedAppealsBefore(@Param("beforeTime") LocalDateTime beforeTime);
}