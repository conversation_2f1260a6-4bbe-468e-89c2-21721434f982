package com.dipspro.modules.billing.controller;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dipspro.common.dto.ApiResponse;
import com.dipspro.modules.billing.dto.BillingConfigDto;
import com.dipspro.modules.billing.dto.TokenCostCalculateDto;
import com.dipspro.modules.billing.dto.UsageRecordDto;
import com.dipspro.modules.billing.dto.UsageStatisticsDto;
import com.dipspro.modules.billing.entity.BillingUsageRecord;
import com.dipspro.modules.billing.service.BillingService;
import com.dipspro.modules.billing.service.UserBalanceService;
import com.dipspro.util.SecurityUtil;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 核心计费功能控制器
 * 
 * 提供用户端计费相关的REST API接口，包括：
 * - 余额查询和管理
 * - Token使用记录查询
 * - 费用计算和预估
 * - 使用统计数据
 * - 计费配置信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/billing")
@RequiredArgsConstructor
@Validated
public class BillingController {

    private final BillingService billingService;
    private final UserBalanceService userBalanceService;

    /**
     * 获取用户使用记录
     * 
     * 分页查询用户的Token使用记录，支持按时间范围和状态筛选。
     * 记录包含对话详情、Token消费、费用计算等完整信息。
     * 
     * @param page      页码，从0开始
     * @param size      每页大小，默认20，最大100
     * @param startDate 开始时间，格式：yyyy-MM-dd HH:mm:ss
     * @param endDate   结束时间，格式：yyyy-MM-dd HH:mm:ss
     * @param status    计费状态筛选：SUCCESS, FAILED, PENDING
     * @return 分页的使用记录列表
     */
    @GetMapping("/usage")
    public ApiResponse<Page<UsageRecordDto>> getUserUsageRecords(
            @RequestParam(defaultValue = "0") @Min(0) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String status) {

        log.info("查询用户使用记录: page={}, size={}, startDate={}, endDate={}, status={}",
                page, size, startDate, endDate, status);

        Long userId = SecurityUtil.getCurrentUserId();
        Pageable pageable = PageRequest.of(page, size);

        Page<BillingUsageRecord> records;

        // 根据查询条件选择不同的查询方法
        if (startDate != null && endDate != null) {
            LocalDateTime start = LocalDateTime.parse(startDate.replace(" ", "T"));
            LocalDateTime end = LocalDateTime.parse(endDate.replace(" ", "T"));
            records = billingService.getUserBillingRecordsByDateRange(userId, start, end, pageable);
        } else {
            records = billingService.getUserBillingRecords(userId, pageable);
        }

        // 转换为DTO
        Page<UsageRecordDto> recordDtos = records.map(this::convertToUsageRecordDto);

        log.info("用户使用记录查询完成: UserId={}, TotalRecords={}", userId, records.getTotalElements());
        return ApiResponse.success(recordDtos, "查询成功");
    }

    /**
     * 获取使用记录详情
     * 
     * 根据记录ID获取单条使用记录的详细信息，包括对话内容、
     * Token计算详情、费用明细等完整信息。
     * 
     * @param id 使用记录ID
     * @return 使用记录详情
     */
    @GetMapping("/usage/{id}")
    public ApiResponse<UsageRecordDto> getUsageRecordDetail(@PathVariable @NotNull Long id) {
        log.info("获取使用记录详情: RecordId={}", id);

        Long userId = SecurityUtil.getCurrentUserId();

        // 获取记录详情并验证权限
        var recordOpt = billingService.getBillingRecordByMessageId(null); // TODO: 需要根据ID查询的方法

        // TODO: 实现根据ID查询并验证用户权限的逻辑
        // 临时返回空数据
        return ApiResponse.success(null, "记录详情查询功能开发中");
    }

    /**
     * 获取使用统计数据
     * 
     * 获取用户在指定时间周期内的Token使用统计，包括：
     * - 总Token消费量
     * - 总费用支出
     * - 对话次数统计
     * - 平均Token消费
     * - 趋势分析数据
     * 
     * @param period 统计周期：day, week, month
     * @return 统计数据
     */
    @GetMapping("/usage/statistics")
    public ApiResponse<UsageStatisticsDto> getUsageStatistics(
            @RequestParam(defaultValue = "month") String period) {

        log.info("获取使用统计数据: period={}", period);

        Long userId = SecurityUtil.getCurrentUserId();

        // TODO: 实现统计数据计算逻辑
        UsageStatisticsDto statistics = new UsageStatisticsDto()
                .setPeriod(period)
                .setTotalTokens(0L)
                .setTotalCost(BigDecimal.ZERO)
                .setConversationCount(0L)
                .setAverageTokensPerConversation(0.0);

        log.info("使用统计查询完成: UserId={}, Period={}", userId, period);
        return ApiResponse.success(statistics, "统计数据获取成功");
    }

    /**
     * 计算Token费用
     * 
     * 根据指定的Token数量计算所需费用，用于费用预估。
     * 计算基于用户当前套餐的价格配置。
     * 
     * @param request Token计算请求参数
     * @return 计算结果，包含详细的费用明细
     */
    @PostMapping("/calculate-cost")
    public ApiResponse<Object> calculateTokenCost(@Valid @RequestBody TokenCostCalculateDto request) {
        log.info("计算Token费用: InputTokens={}, OutputTokens={}",
                request.getInputTokens(), request.getOutputTokens());

        Long userId = SecurityUtil.getCurrentUserId();

        // 计算总费用
        BigDecimal totalCost = billingService.calculateTokenCost(
                userId, request.getInputTokens(), request.getOutputTokens());

        // 构建返回结果
        BigDecimal calculatedCost = totalCost;
        var result = new Object() {
            public final Long inputTokens = request.getInputTokens();
            public final Long outputTokens = request.getOutputTokens();
            public final BigDecimal totalCost = calculatedCost;
            public final String formattedCost = "¥" + calculatedCost.toString();
        };

        log.info("Token费用计算完成: UserId={}, TotalCost={}", userId, totalCost);
        return ApiResponse.success(result, "费用计算成功");
    }

    /**
     * 预检查计费
     * 
     * 在发送消息前检查用户余额是否充足支付预估费用。
     * 用于前端界面的余额不足提醒和发送按钮状态控制。
     * 
     * @param inputTokens  预估输入Token数量
     * @param outputTokens 预估输出Token数量
     * @return 检查结果：true表示余额充足，false表示余额不足
     */
    @GetMapping("/precheck")
    public ApiResponse<Boolean> precheckBilling(
            @RequestParam @Min(0) Long inputTokens,
            @RequestParam @Min(0) Long outputTokens) {

        log.debug("预检查计费: InputTokens={}, OutputTokens={}", inputTokens, outputTokens);

        Long userId = SecurityUtil.getCurrentUserId();

        // 执行预检查
        boolean sufficient = billingService.preCheckBilling(userId, inputTokens, outputTokens);

        log.debug("预检查完成: UserId={}, Sufficient={}", userId, sufficient);
        return ApiResponse.success(sufficient, sufficient ? "余额充足" : "余额不足");
    }

    /**
     * 获取计费配置信息
     * 
     * 获取用户当前的计费配置，包括：
     * - 当前使用的套餐信息
     * - 价格配置（输入/输出Token单价）
     * - 余额信息
     * - 免费Token配额
     * - 账户状态
     * 
     * @return 计费配置信息
     */
    @GetMapping("/configuration")
    public ApiResponse<BillingConfigDto> getBillingConfiguration() {
        log.info("获取计费配置信息");

        Long userId = SecurityUtil.getCurrentUserId();

        // 获取计费配置
        Object configObj = billingService.getBillingConfiguration(userId);

        // TODO: 将Object转换为BillingConfigDto
        BillingConfigDto config = new BillingConfigDto()
                .setUserId(userId);

        log.info("计费配置查询完成: UserId={}", userId);
        return ApiResponse.success(config, "配置信息获取成功");
    }

    /**
     * 转换BillingUsageRecord为UsageRecordDto
     * 
     * @param record 计费使用记录实体
     * @return 使用记录DTO
     */
    private UsageRecordDto convertToUsageRecordDto(BillingUsageRecord record) {
        return new UsageRecordDto()
                .setId(record.getId())
                .setMessageId(record.getMessageId())
                .setConversationId(record.getConversationId())
                .setModelName(record.getModelName())
                .setInputTokens(record.getInputTokens())
                .setOutputTokens(record.getOutputTokens())
                .setThoughtChainTokens(record.getThoughtChainTokens())
                .setTotalTokens(record.getTotalTokens())
                .setInputCost(record.getInputCost())
                .setOutputCost(record.getOutputCost())
                .setTotalCost(record.getTotalCost())
                .setStatus(record.getStatus())
                .setBillingStatus(record.getBillingStatus())
                .setPackageId(record.getPackageId())
                .setInputTokenPrice(record.getInputTokenPrice())
                .setOutputTokenPrice(record.getOutputTokenPrice())
                .setIsAppealed(record.getIsAppealed())
                .setAppealStatus(record.getAppealStatus())
                .setRequestTime(record.getRequestTime())
                .setResponseTime(record.getResponseTime())
                .setDurationMs(record.getDurationMs())
                .setCreatedAt(record.getCreatedAt());
    }
}