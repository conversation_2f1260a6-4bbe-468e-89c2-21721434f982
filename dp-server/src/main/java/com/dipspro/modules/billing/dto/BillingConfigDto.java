package com.dipspro.modules.billing.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 计费配置DTO
 * 
 * 用于返回用户当前的计费配置信息，包括套餐配置、
 * 价格信息、余额状态等关键配置数据。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class BillingConfigDto {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 当前套餐ID
     */
    private Long packageId;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 套餐描述
     */
    private String packageDescription;

    /**
     * 输入Token单价（每千Token）
     */
    private BigDecimal inputTokenPrice;

    /**
     * 输出Token单价（每千Token）
     */
    private BigDecimal outputTokenPrice;

    /**
     * 思维链Token单价（每千Token）
     */
    private BigDecimal thoughtChainTokenPrice;

    /**
     * 总可用余额
     */
    private BigDecimal totalBalance;

    /**
     * 充值余额
     */
    private BigDecimal rechargedBalance;

    /**
     * 赠送余额
     */
    private BigDecimal giftBalance;

    /**
     * 免费Token数量
     */
    private Long freeTokens;

    /**
     * 今日已用Token数量
     */
    private Long todayTokenUsage;

    /**
     * 每日免费Token配额
     */
    private Long dailyFreeTokens;

    /**
     * 账户状态是否冻结
     */
    private Boolean isFrozen;

    /**
     * 套餐是否激活
     */
    private Boolean isPackageActive;

    /**
     * 套餐创建时间
     */
    private LocalDateTime packageCreatedAt;

    /**
     * 配置最后更新时间
     */
    private LocalDateTime lastUpdated;

    /**
     * 最小充值金额
     */
    private BigDecimal minRechargeAmount;

    /**
     * 最大充值金额
     */
    private BigDecimal maxRechargeAmount;

    /**
     * 余额不足阈值
     */
    private BigDecimal lowBalanceThreshold;

    /**
     * 支持的支付方式
     */
    private String[] supportedPaymentMethods;

    /**
     * 检查账户是否正常
     * 
     * @return 是否正常（未冻结且套餐激活）
     */
    public boolean isAccountNormal() {
        return !Boolean.TRUE.equals(isFrozen) && Boolean.TRUE.equals(isPackageActive);
    }

    /**
     * 检查余额是否充足
     * 
     * @param requiredAmount 所需金额
     * @return 是否充足
     */
    public boolean isSufficientBalance(BigDecimal requiredAmount) {
        if (requiredAmount == null || totalBalance == null) {
            return false;
        }
        return totalBalance.compareTo(requiredAmount) >= 0;
    }

    /**
     * 检查是否余额不足
     * 
     * @return 是否余额不足
     */
    public boolean isLowBalance() {
        if (totalBalance == null || lowBalanceThreshold == null) {
            return false;
        }
        return totalBalance.compareTo(lowBalanceThreshold) <= 0;
    }

    /**
     * 计算Token费用
     * 
     * @param inputTokens        输入Token数量
     * @param outputTokens       输出Token数量
     * @param thoughtChainTokens 思维链Token数量
     * @return 总费用
     */
    public BigDecimal calculateTokenCost(Long inputTokens, Long outputTokens, Long thoughtChainTokens) {
        BigDecimal inputCost = inputTokenPrice != null && inputTokens != null
                ? inputTokenPrice.multiply(BigDecimal.valueOf(inputTokens)).divide(BigDecimal.valueOf(1000))
                : BigDecimal.ZERO;

        BigDecimal outputCost = outputTokenPrice != null && outputTokens != null
                ? outputTokenPrice.multiply(BigDecimal.valueOf(outputTokens)).divide(BigDecimal.valueOf(1000))
                : BigDecimal.ZERO;

        BigDecimal thoughtChainCost = thoughtChainTokenPrice != null && thoughtChainTokens != null
                ? thoughtChainTokenPrice.multiply(BigDecimal.valueOf(thoughtChainTokens))
                        .divide(BigDecimal.valueOf(1000))
                : BigDecimal.ZERO;

        return inputCost.add(outputCost).add(thoughtChainCost);
    }

    /**
     * 获取剩余免费Token
     * 
     * @return 剩余免费Token数量
     */
    public Long getRemainingFreeTokens() {
        if (freeTokens == null) {
            return 0L;
        }
        Long usedToday = todayTokenUsage != null ? todayTokenUsage : 0L;
        return Math.max(0L, freeTokens - usedToday);
    }

    /**
     * 格式化总余额显示
     * 
     * @return 格式化的余额字符串
     */
    public String getFormattedTotalBalance() {
        return totalBalance != null ? "¥" + totalBalance.toString() : "¥0.00";
    }

    /**
     * 格式化输入Token价格显示
     * 
     * @return 格式化的价格字符串
     */
    public String getFormattedInputTokenPrice() {
        return inputTokenPrice != null ? "¥" + inputTokenPrice.toString() + "/1K tokens" : "免费";
    }

    /**
     * 格式化输出Token价格显示
     * 
     * @return 格式化的价格字符串
     */
    public String getFormattedOutputTokenPrice() {
        return outputTokenPrice != null ? "¥" + outputTokenPrice.toString() + "/1K tokens" : "免费";
    }

    /**
     * 获取账户状态显示文本
     * 
     * @return 状态显示文本
     */
    public String getAccountStatusDisplay() {
        if (Boolean.TRUE.equals(isFrozen)) {
            return "已冻结";
        } else if (!Boolean.TRUE.equals(isPackageActive)) {
            return "套餐未激活";
        } else {
            return "正常";
        }
    }
}