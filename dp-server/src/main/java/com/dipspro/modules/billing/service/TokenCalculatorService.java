package com.dipspro.modules.billing.service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * Token计算服务接口
 * 
 * 根据DIPS Pro计费系统设计文档实现，负责AI对话中Token的精确计算。
 * 使用JTokkit库进行Token计算，支持多种模型和编码格式，提供缓存机制以提升性能。
 * 
 * 主要功能：
 * - 输入/输出/思维链Token的精确计算
 * - 多种AI模型的Token计算支持
 * - 异步和批量Token计算
 * - Token计算结果缓存
 * - Token计算性能优化
 * 
 * 支持的模型编码：
 * - GPT-4o: O200K_BASE编码
 * - GPT-4/GPT-3.5: CL100K_BASE编码
 * - 其他模型: 默认CL100K_BASE编码
 * 
 * 使用场景：
 * - AI对话Token计费
 * - Token使用量预估
 * - 费用计算基础数据
 * - 用户消费统计分析
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface TokenCalculatorService {

    /**
     * 计算输入Token数量
     * 
     * 精确计算用户输入内容的Token数量，用于计费和统计。
     * 该方法会根据指定的AI模型选择合适的编码格式进行计算。
     * 
     * 计算流程：
     * 1. 验证输入参数的有效性
     * 2. 根据模型名称选择对应的编码器
     * 3. 使用JTokkit库进行Token编码
     * 4. 缓存计算结果以提升性能
     * 5. 返回精确的Token数量
     * 
     * 性能优化：
     * - 相同内容的计算结果会被缓存
     * - 缓存有效期为1小时
     * - 支持并发计算提升性能
     * 
     * @param content 输入内容，不能为null或空字符串
     * @param model   AI模型名称，如"gpt-4o"、"gpt-3.5-turbo"等
     * @return Token数量，如果输入为空则返回0
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    long calculateInputTokens(String content, String model);

    /**
     * 计算输出Token数量
     * 
     * 精确计算AI模型输出内容的Token数量，用于计费和统计。
     * 输出Token通常比输入Token价格更高，需要精确计算以确保计费准确性。
     * 
     * 计算特点：
     * 1. 输出Token计费价格通常高于输入Token
     * 2. 包含AI模型生成的完整响应内容
     * 3. 支持流式输出的Token累计计算
     * 4. 处理特殊字符和编码格式
     * 
     * 应用场景：
     * - AI对话响应的Token计费
     * - 用户消费统计
     * - 费用预估和账单生成
     * - 模型性能分析
     * 
     * @param content 输出内容，AI模型生成的响应文本
     * @param model   AI模型名称，必须与输入时使用的模型一致
     * @return Token数量，精确到个位数
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    long calculateOutputTokens(String content, String model);

    /**
     * 计算思维链Token数量
     * 
     * 计算AI模型思维链（intermediate steps）的Token数量。
     * 思维链是AI模型在生成响应过程中的中间推理步骤，通常以JSON格式存储。
     * 
     * 思维链特点：
     * 1. 包含AI模型的推理过程和中间步骤
     * 2. 通常以JSON格式存储在数据库中
     * 3. Token数量可能很大，需要精确计算
     * 4. 是计费的重要组成部分
     * 
     * 计算策略：
     * - 解析JSON格式的思维链内容
     * - 提取所有文本内容进行Token计算
     * - 考虑JSON结构本身的Token占用
     * - 与主要输出内容分开计算和计费
     * 
     * @param thoughtChain 思维链内容，通常为JSON格式的推理步骤
     * @param model        AI模型名称，确保使用正确的编码格式
     * @return Token数量，如果思维链为空则返回0
     * @throws IllegalArgumentException 当参数格式错误时抛出
     */
    long calculateThoughtChainTokens(String thoughtChain, String model);

    /**
     * 批量计算Token数量
     * 
     * 异步批量计算多个文本内容的Token数量，提升计算效率。
     * 适用于需要同时计算大量文本Token的场景，如批量数据处理、历史数据迁移等。
     * 
     * 批量处理优势：
     * 1. 并行计算提升整体性能
     * 2. 减少单次调用的开销
     * 3. 统一的错误处理机制
     * 4. 支持不同模型的混合计算
     * 
     * 使用场景：
     * - 历史对话数据的Token补算
     * - 批量费用计算和统计
     * - 数据迁移和清理
     * - 性能测试和基准测试
     * 
     * @param requests 计算请求列表，包含内容、模型、计算类型等信息
     * @return 异步计算结果，包含每个请求对应的Token数量
     * @throws IllegalArgumentException 当请求列表为空或包含无效请求时抛出
     */
    CompletableFuture<List<TokenCalculationResult>> calculateTokensBatch(List<TokenCalculationRequest> requests);

    /**
     * 异步计算Token数量
     * 
     * 异步计算指定内容的Token数量，不阻塞调用线程。
     * 适用于大文本内容的Token计算，避免阻塞主线程影响用户体验。
     * 
     * 异步处理特点：
     * 1. 不阻塞调用线程，提升响应性能
     * 2. 支持回调函数处理计算结果
     * 3. 适合处理大文本或复杂内容
     * 4. 提供错误处理和异常回调
     * 
     * 性能考虑：
     * - 使用专用线程池处理计算任务
     * - 避免创建过多线程影响系统性能
     * - 支持超时控制防止长时间阻塞
     * 
     * @param content  待计算的文本内容
     * @param model    AI模型名称
     * @param callback 计算完成后的回调函数，接收Token数量作为参数
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    void calculateTokensAsync(String content, String model, Consumer<Long> callback);

    /**
     * 预估Token数量
     * 
     * 快速预估文本内容的Token数量，用于用户界面显示和余额检查。
     * 相比精确计算，预估方法速度更快但精度稍低，适用于实时显示场景。
     * 
     * 预估策略：
     * 1. 基于字符数的快速估算
     * 2. 使用历史数据的统计模型
     * 3. 考虑不同语言的Token密度差异
     * 4. 提供上下界范围估算
     * 
     * 应用场景：
     * - 用户输入时的实时Token预估
     * - 发送前的余额充足性检查
     * - 用户界面的Token使用量显示
     * - 费用预估和提醒
     * 
     * @param content 待预估的文本内容
     * @param model   AI模型名称
     * @return 预估的Token数量，可能存在±10%的误差
     */
    long estimateTokens(String content, String model);

    /**
     * 获取Token计算统计信息
     * 
     * 获取Token计算服务的运行统计信息，用于监控和性能分析。
     * 包括计算次数、缓存命中率、平均计算时间等关键指标。
     * 
     * 统计信息包括：
     * - 总计算次数和缓存命中次数
     * - 平均计算时间和最大计算时间
     * - 不同模型的使用频率统计
     * - 错误次数和异常情况统计
     * 
     * @return 包含各项统计指标的Map对象
     */
    Map<String, Object> getCalculationStatistics();

    /**
     * 清除Token计算缓存
     * 
     * 清除所有或指定模型的Token计算缓存，释放内存空间。
     * 适用于内存清理、缓存更新、性能优化等场景。
     * 
     * @param model 模型名称，如果为null则清除所有缓存
     * @return 清除的缓存条目数量
     */
    int clearCalculationCache(String model);

    /**
     * 验证模型是否支持
     * 
     * 检查指定的AI模型是否被Token计算服务支持。
     * 用于参数验证和错误预防。
     * 
     * @param model 模型名称
     * @return true表示支持，false表示不支持
     */
    boolean isSupportedModel(String model);

    /**
     * 获取支持的模型列表
     * 
     * 获取当前Token计算服务支持的所有AI模型列表。
     * 
     * @return 支持的模型名称列表
     */
    List<String> getSupportedModels();

    /**
     * Token计算请求类
     * 
     * 用于批量计算时封装单个计算请求的参数。
     */
    class TokenCalculationRequest {
        private String content;
        private String model;
        private String type; // INPUT, OUTPUT, THOUGHT_CHAIN

        public TokenCalculationRequest(String content, String model, String type) {
            this.content = content;
            this.model = model;
            this.type = type;
        }

        // Getters
        public String getContent() {
            return content;
        }

        public String getModel() {
            return model;
        }

        public String getType() {
            return type;
        }
    }

    /**
     * Token计算结果类
     * 
     * 封装Token计算的结果和相关信息。
     */
    class TokenCalculationResult {
        private String content;
        private String model;
        private String type;
        private long tokenCount;
        private boolean fromCache;
        private long calculationTimeMs;

        public TokenCalculationResult(String content, String model, String type,
                long tokenCount, boolean fromCache, long calculationTimeMs) {
            this.content = content;
            this.model = model;
            this.type = type;
            this.tokenCount = tokenCount;
            this.fromCache = fromCache;
            this.calculationTimeMs = calculationTimeMs;
        }

        // Getters
        public String getContent() {
            return content;
        }

        public String getModel() {
            return model;
        }

        public String getType() {
            return type;
        }

        public long getTokenCount() {
            return tokenCount;
        }

        public boolean isFromCache() {
            return fromCache;
        }

        public long getCalculationTimeMs() {
            return calculationTimeMs;
        }
    }
}