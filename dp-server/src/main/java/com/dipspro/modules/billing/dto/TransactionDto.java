package com.dipspro.modules.billing.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 交易记录DTO
 * 
 * 用于返回交易记录信息给前端，包含交易的完整信息和状态。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class TransactionDto {

    /**
     * 交易ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 交易类型
     * RECHARGE-充值, DEDUCTION-扣费, REFUND-退款, TRANSFER-转账
     */
    private String type;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * 交易前余额
     */
    private BigDecimal balanceBefore;

    /**
     * 交易后余额
     */
    private BigDecimal balanceAfter;

    /**
     * 交易状态
     * SUCCESS-成功, FAILED-失败, PENDING-处理中
     */
    private String status;

    /**
     * 交易描述
     */
    private String description;

    /**
     * 关联ID
     * 关联的业务记录ID（如使用记录ID、充值订单ID等）
     */
    private Long relatedId;

    /**
     * 关联类型
     * USAGE-使用记录, RECHARGE-充值订单, REFUND-退款等
     */
    private String relatedType;

    /**
     * 交易流水号
     */
    private String transactionNo;

    /**
     * 支付方式
     * ALIPAY-支付宝, WECHAT-微信, BANK-银行卡等
     */
    private String paymentMethod;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 获取交易类型显示文本
     * 
     * @return 类型显示文本
     */
    public String getTypeDisplay() {
        if (type == null) {
            return "未知类型";
        }

        return switch (type) {
            case "RECHARGE" -> "充值";
            case "DEDUCTION" -> "扣费";
            case "REFUND" -> "退款";
            case "TRANSFER" -> "转账";
            default -> "未知类型";
        };
    }

    /**
     * 获取交易状态显示文本
     * 
     * @return 状态显示文本
     */
    public String getStatusDisplay() {
        if (status == null) {
            return "未知状态";
        }

        return switch (status) {
            case "SUCCESS" -> "成功";
            case "FAILED" -> "失败";
            case "PENDING" -> "处理中";
            default -> "未知状态";
        };
    }

    /**
     * 检查交易是否成功
     * 
     * @return 是否成功
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(status);
    }

    /**
     * 检查交易是否失败
     * 
     * @return 是否失败
     */
    public boolean isFailed() {
        return "FAILED".equals(status);
    }

    /**
     * 检查交易是否处理中
     * 
     * @return 是否处理中
     */
    public boolean isPending() {
        return "PENDING".equals(status);
    }

    /**
     * 检查是否为收入类交易
     * 
     * @return 是否为收入
     */
    public boolean isIncome() {
        return "RECHARGE".equals(type) || "REFUND".equals(type);
    }

    /**
     * 检查是否为支出类交易
     * 
     * @return 是否为支出
     */
    public boolean isExpense() {
        return "DEDUCTION".equals(type);
    }

    /**
     * 获取格式化的交易金额
     * 
     * @return 格式化的金额字符串
     */
    public String getFormattedAmount() {
        if (amount == null) {
            return "¥0.00";
        }

        String prefix = isIncome() ? "+" : (isExpense() ? "-" : "");
        return prefix + "¥" + amount.toString();
    }

    /**
     * 获取格式化的交易前余额
     * 
     * @return 格式化的余额字符串
     */
    public String getFormattedBalanceBefore() {
        return balanceBefore != null ? "¥" + balanceBefore.toString() : "¥0.00";
    }

    /**
     * 获取格式化的交易后余额
     * 
     * @return 格式化的余额字符串
     */
    public String getFormattedBalanceAfter() {
        return balanceAfter != null ? "¥" + balanceAfter.toString() : "¥0.00";
    }

    /**
     * 获取余额变化金额
     * 
     * @return 余额变化金额
     */
    public BigDecimal getBalanceChange() {
        if (balanceBefore == null || balanceAfter == null) {
            return BigDecimal.ZERO;
        }

        return balanceAfter.subtract(balanceBefore);
    }

    /**
     * 获取格式化的余额变化
     * 
     * @return 格式化的余额变化字符串
     */
    public String getFormattedBalanceChange() {
        BigDecimal change = getBalanceChange();
        String prefix = change.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "";
        return prefix + "¥" + change.toString();
    }

    /**
     * 获取交易摘要信息
     * 
     * @return 交易摘要
     */
    public String getTransactionSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(getTypeDisplay()).append(" ");
        summary.append(getFormattedAmount());
        summary.append(" | 状态: ").append(getStatusDisplay());

        if (description != null && !description.trim().isEmpty()) {
            String shortDesc = description.length() > 30 ? description.substring(0, 30) + "..." : description;
            summary.append(" | ").append(shortDesc);
        }

        return summary.toString();
    }

    /**
     * 获取关联类型显示文本
     * 
     * @return 关联类型显示文本
     */
    public String getRelatedTypeDisplay() {
        if (relatedType == null) {
            return "无关联";
        }

        return switch (relatedType) {
            case "USAGE" -> "使用记录";
            case "RECHARGE" -> "充值订单";
            case "REFUND" -> "退款处理";
            case "APPEAL" -> "申诉退款";
            default -> relatedType;
        };
    }

    /**
     * 获取支付方式显示文本
     * 
     * @return 支付方式显示文本
     */
    public String getPaymentMethodDisplay() {
        if (paymentMethod == null) {
            return "无";
        }

        return switch (paymentMethod) {
            case "ALIPAY" -> "支付宝";
            case "WECHAT" -> "微信支付";
            case "BANK" -> "银行卡";
            case "BALANCE" -> "余额支付";
            default -> paymentMethod;
        };
    }

    /**
     * 检查是否有关联记录
     * 
     * @return 是否有关联
     */
    public boolean hasRelatedRecord() {
        return relatedId != null && relatedType != null;
    }

    /**
     * 获取交易详情描述
     * 
     * @return 交易详情
     */
    public String getTransactionDetail() {
        StringBuilder detail = new StringBuilder();
        detail.append("交易类型: ").append(getTypeDisplay()).append("\n");
        detail.append("交易金额: ").append(getFormattedAmount()).append("\n");
        detail.append("交易状态: ").append(getStatusDisplay()).append("\n");
        detail.append("余额变化: ").append(getFormattedBalanceChange()).append("\n");

        if (hasRelatedRecord()) {
            detail.append("关联记录: ").append(getRelatedTypeDisplay()).append("(").append(relatedId).append(")")
                    .append("\n");
        }

        if (paymentMethod != null) {
            detail.append("支付方式: ").append(getPaymentMethodDisplay()).append("\n");
        }

        if (description != null && !description.trim().isEmpty()) {
            detail.append("交易说明: ").append(description);
        }

        return detail.toString();
    }
}