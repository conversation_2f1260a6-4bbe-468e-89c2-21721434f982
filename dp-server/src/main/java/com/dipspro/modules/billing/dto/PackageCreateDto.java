package com.dipspro.modules.billing.dto;

import java.math.BigDecimal;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 套餐创建DTO
 * 
 * 用于创建新的计费套餐时的请求参数，包含套餐的基本信息和价格配置。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class PackageCreateDto {

    /**
     * 套餐名称
     * 必须唯一，用于标识不同的套餐
     */
    @NotBlank(message = "套餐名称不能为空")
    @Size(min = 2, max = 100, message = "套餐名称长度必须在2-100个字符之间")
    private String name;

    /**
     * 套餐描述
     * 详细说明套餐的特点和适用场景
     */
    @Size(max = 500, message = "套餐描述不能超过500个字符")
    private String description;

    /**
     * 输入Token价格（每千Token）
     * 用户输入内容的计费单价
     */
    @NotNull(message = "输入Token价格不能为空")
    @DecimalMin(value = "0.0", message = "输入Token价格不能为负数")
    private BigDecimal inputTokenPrice;

    /**
     * 输出Token价格（每千Token）
     * AI生成内容的计费单价，通常高于输入价格
     */
    @NotNull(message = "输出Token价格不能为空")
    @DecimalMin(value = "0.0", message = "输出Token价格不能为负数")
    private BigDecimal outputTokenPrice;

    /**
     * 免费Token数量
     * 用户每日可用的免费Token配额
     */
    @Min(value = 0, message = "免费Token数量不能为负数")
    private Long freeTokens;

    /**
     * 单次请求最大Token限制
     * 0表示无限制
     */
    @Min(value = 0, message = "单次请求Token限制不能为负数")
    private Long maxTokensPerRequest;

    /**
     * 每日Token使用限制
     * 0表示无限制
     */
    @Min(value = 0, message = "每日Token限制不能为负数")
    private Long dailyTokenLimit;

    /**
     * 是否激活
     * 默认为true，创建后立即可用
     */
    private Boolean isActive = true;

    /**
     * 排序顺序
     * 用于前端显示时的排序，数字越小越靠前
     */
    @Min(value = 0, message = "排序顺序不能为负数")
    private Integer sortOrder;

    /**
     * 是否为默认套餐
     * 默认为false，需要手动设置
     */
    private Boolean isDefault = false;

    /**
     * 套餐特性
     * JSON格式的特性描述，如模型支持、并发限制等
     */
    @Size(max = 2000, message = "套餐特性描述不能超过2000个字符")
    private String features;

    /**
     * 使用限制
     * JSON格式的限制条件，如请求频率、使用时长等
     */
    @Size(max = 2000, message = "使用限制描述不能超过2000个字符")
    private String limitations;

    /**
     * 检查价格配置是否合理
     * 
     * @return 是否合理
     */
    public boolean isPriceConfigValid() {
        if (inputTokenPrice == null || outputTokenPrice == null) {
            return false;
        }

        // 输出价格通常应该高于或等于输入价格
        return outputTokenPrice.compareTo(inputTokenPrice) >= 0;
    }

    /**
     * 检查是否为免费套餐
     * 
     * @return 是否为免费套餐
     */
    public boolean isFreePackage() {
        return (inputTokenPrice != null && inputTokenPrice.compareTo(BigDecimal.ZERO) == 0)
                && (outputTokenPrice != null && outputTokenPrice.compareTo(BigDecimal.ZERO) == 0);
    }

    /**
     * 获取格式化的价格信息
     * 
     * @return 格式化的价格字符串
     */
    public String getFormattedPriceInfo() {
        if (isFreePackage()) {
            return "免费套餐";
        }

        return String.format("输入: ¥%s/1K tokens, 输出: ¥%s/1K tokens",
                inputTokenPrice.toString(), outputTokenPrice.toString());
    }

    /**
     * 获取套餐类型标识
     * 
     * @return 套餐类型
     */
    public String getPackageType() {
        if (isFreePackage()) {
            return "FREE";
        } else if (Boolean.TRUE.equals(isDefault)) {
            return "DEFAULT";
        } else {
            return "STANDARD";
        }
    }

    /**
     * 验证套餐配置完整性
     * 
     * @return 验证结果描述
     */
    public String validateConfiguration() {
        if (name == null || name.trim().isEmpty()) {
            return "套餐名称不能为空";
        }

        if (inputTokenPrice == null || outputTokenPrice == null) {
            return "Token价格配置不完整";
        }

        if (inputTokenPrice.compareTo(BigDecimal.ZERO) < 0 ||
                outputTokenPrice.compareTo(BigDecimal.ZERO) < 0) {
            return "Token价格不能为负数";
        }

        if (!isPriceConfigValid()) {
            return "价格配置不合理，输出价格应不低于输入价格";
        }

        if (freeTokens != null && freeTokens < 0) {
            return "免费Token数量不能为负数";
        }

        if (maxTokensPerRequest != null && maxTokensPerRequest < 0) {
            return "单次请求Token限制不能为负数";
        }

        if (dailyTokenLimit != null && dailyTokenLimit < 0) {
            return "每日Token限制不能为负数";
        }

        if (sortOrder != null && sortOrder < 0) {
            return "排序顺序不能为负数";
        }

        return "配置有效";
    }
}