package com.dipspro.modules.billing.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 余额历史记录DTO
 * 
 * 用于返回用户余额变动历史记录，包含变动详情和相关信息。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class BalanceHistoryDto {

    /**
     * 历史记录ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 交易流水号
     */
    private String transactionNo;

    /**
     * 变动类型
     * INCOME-收入, EXPENSE-支出, TRANSFER-转账, ADJUST-调整
     */
    private String type;

    /**
     * 变动金额
     */
    private BigDecimal amount;

    /**
     * 变动前余额
     */
    private BigDecimal balanceBefore;

    /**
     * 变动后余额
     */
    private BigDecimal balanceAfter;

    /**
     * 余额类型
     * CASH-现金余额, GIFT-赠送余额, TOTAL-总余额
     */
    private String balanceType;

    /**
     * 变动原因
     */
    private String reason;

    /**
     * 详细描述
     */
    private String description;

    /**
     * 关联ID
     * 关联的业务记录ID（如交易ID、申诉ID等）
     */
    private Long relatedId;

    /**
     * 关联类型
     * TRANSACTION-交易, APPEAL-申诉, ADJUSTMENT-管理员调整等
     */
    private String relatedType;

    /**
     * 操作员ID
     * 执行操作的管理员ID（如果是管理员操作）
     */
    private Long operatorId;

    /**
     * 操作员名称
     */
    private String operatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 获取变动类型显示文本
     * 
     * @return 类型显示文本
     */
    public String getTypeDisplay() {
        if (type == null) {
            return "未知类型";
        }

        return switch (type) {
            case "INCOME" -> "收入";
            case "EXPENSE" -> "支出";
            case "TRANSFER" -> "转账";
            case "ADJUST" -> "调整";
            default -> "未知类型";
        };
    }

    /**
     * 获取余额类型显示文本
     * 
     * @return 余额类型显示文本
     */
    public String getBalanceTypeDisplay() {
        if (balanceType == null) {
            return "未知类型";
        }

        return switch (balanceType) {
            case "CASH" -> "现金余额";
            case "GIFT" -> "赠送余额";
            case "TOTAL" -> "总余额";
            default -> "未知类型";
        };
    }

    /**
     * 检查是否为收入类变动
     * 
     * @return 是否为收入
     */
    public boolean isIncome() {
        return "INCOME".equals(type);
    }

    /**
     * 检查是否为支出类变动
     * 
     * @return 是否为支出
     */
    public boolean isExpense() {
        return "EXPENSE".equals(type);
    }

    /**
     * 检查是否为转账类变动
     * 
     * @return 是否为转账
     */
    public boolean isTransfer() {
        return "TRANSFER".equals(type);
    }

    /**
     * 检查是否为调整类变动
     * 
     * @return 是否为调整
     */
    public boolean isAdjustment() {
        return "ADJUST".equals(type);
    }

    /**
     * 获取格式化的变动金额
     * 
     * @return 格式化的金额字符串
     */
    public String getFormattedAmount() {
        if (amount == null) {
            return "¥0.00";
        }

        String prefix = isIncome() ? "+" : (isExpense() ? "-" : "");
        return prefix + "¥" + amount.toString();
    }

    /**
     * 获取格式化的变动前余额
     * 
     * @return 格式化的余额字符串
     */
    public String getFormattedBalanceBefore() {
        return balanceBefore != null ? "¥" + balanceBefore.toString() : "¥0.00";
    }

    /**
     * 获取格式化的变动后余额
     * 
     * @return 格式化的余额字符串
     */
    public String getFormattedBalanceAfter() {
        return balanceAfter != null ? "¥" + balanceAfter.toString() : "¥0.00";
    }

    /**
     * 获取余额实际变化金额
     * 
     * @return 余额变化金额
     */
    public BigDecimal getBalanceChange() {
        if (balanceBefore == null || balanceAfter == null) {
            return BigDecimal.ZERO;
        }

        return balanceAfter.subtract(balanceBefore);
    }

    /**
     * 获取格式化的余额变化
     * 
     * @return 格式化的余额变化字符串
     */
    public String getFormattedBalanceChange() {
        BigDecimal change = getBalanceChange();
        String prefix = change.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "";
        return prefix + "¥" + change.toString();
    }

    /**
     * 获取关联类型显示文本
     * 
     * @return 关联类型显示文本
     */
    public String getRelatedTypeDisplay() {
        if (relatedType == null) {
            return "无关联";
        }

        return switch (relatedType) {
            case "TRANSACTION" -> "交易记录";
            case "APPEAL" -> "申诉处理";
            case "ADJUSTMENT" -> "管理员调整";
            case "RECHARGE" -> "充值订单";
            case "REFUND" -> "退款处理";
            default -> relatedType;
        };
    }

    /**
     * 检查是否有关联记录
     * 
     * @return 是否有关联
     */
    public boolean hasRelatedRecord() {
        return relatedId != null && relatedType != null;
    }

    /**
     * 检查是否为管理员操作
     * 
     * @return 是否为管理员操作
     */
    public boolean isAdminOperation() {
        return operatorId != null && operatorName != null;
    }

    /**
     * 获取操作员信息
     * 
     * @return 操作员信息
     */
    public String getOperatorInfo() {
        if (!isAdminOperation()) {
            return "系统自动";
        }

        return operatorName + "(ID:" + operatorId + ")";
    }

    /**
     * 获取余额变动摘要
     * 
     * @return 变动摘要
     */
    public String getBalanceChangeSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(getTypeDisplay()).append(" ");
        summary.append(getFormattedAmount()).append(" | ");
        summary.append(getBalanceTypeDisplay());

        if (reason != null && !reason.trim().isEmpty()) {
            String shortReason = reason.length() > 30 ? reason.substring(0, 30) + "..." : reason;
            summary.append(" | ").append(shortReason);
        }

        return summary.toString();
    }

    /**
     * 获取详细的变动信息
     * 
     * @return 详细变动信息
     */
    public String getDetailedChangeInfo() {
        StringBuilder detail = new StringBuilder();
        detail.append("变动类型: ").append(getTypeDisplay()).append("\n");
        detail.append("余额类型: ").append(getBalanceTypeDisplay()).append("\n");
        detail.append("变动金额: ").append(getFormattedAmount()).append("\n");
        detail.append("余额变化: ").append(getFormattedBalanceBefore())
                .append(" → ").append(getFormattedBalanceAfter()).append("\n");

        if (hasRelatedRecord()) {
            detail.append("关联记录: ").append(getRelatedTypeDisplay())
                    .append("(").append(relatedId).append(")").append("\n");
        }

        if (isAdminOperation()) {
            detail.append("操作员: ").append(getOperatorInfo()).append("\n");
        }

        if (reason != null && !reason.trim().isEmpty()) {
            detail.append("变动原因: ").append(reason).append("\n");
        }

        if (description != null && !description.trim().isEmpty()) {
            detail.append("详细描述: ").append(description);
        }

        return detail.toString();
    }

    /**
     * 获取风险等级
     * 根据变动金额和类型判断风险等级
     * 
     * @return 风险等级：LOW, MEDIUM, HIGH
     */
    public String getRiskLevel() {
        if (amount == null) {
            return "UNKNOWN";
        }

        // 大额变动为高风险
        if (amount.compareTo(BigDecimal.valueOf(50000)) > 0) {
            return "HIGH";
        }

        // 管理员调整且金额较大为高风险
        if (isAdjustment() && isAdminOperation() && amount.compareTo(BigDecimal.valueOf(10000)) > 0) {
            return "HIGH";
        }

        // 中等金额为中风险
        if (amount.compareTo(BigDecimal.valueOf(5000)) > 0) {
            return "MEDIUM";
        }

        return "LOW";
    }

    /**
     * 检查是否为异常变动
     * 
     * @return 是否异常
     */
    public boolean isAbnormal() {
        // 大额变动
        if (amount != null && amount.compareTo(BigDecimal.valueOf(100000)) > 0) {
            return true;
        }

        // 频繁的管理员调整
        if (isAdjustment() && isAdminOperation()) {
            return true;
        }

        return false;
    }

    /**
     * 获取时间显示格式
     * 
     * @return 格式化的时间字符串
     */
    public String getFormattedCreatedAt() {
        if (createdAt == null) {
            return "未知时间";
        }

        return createdAt.toString().replace("T", " ");
    }
}