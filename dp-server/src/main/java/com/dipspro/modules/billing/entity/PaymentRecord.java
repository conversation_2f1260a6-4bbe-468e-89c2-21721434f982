package com.dipspro.modules.billing.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import org.hibernate.annotations.Comment;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 支付记录实体
 * 对应数据库表：b_payment_records
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "b_payment_records")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Comment("支付记录表")
public class PaymentRecord {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("主键ID")
    private Long id;

    /**
     * 支付单号
     */
    @Column(name = "payment_no", length = 100, nullable = false, unique = true)
    @NotBlank(message = "支付单号不能为空")
    @Comment("支付单号")
    private String paymentNo;

    /**
     * 用户ID，关联users.id
     */
    @Column(name = "user_id", nullable = false)
    @NotNull(message = "用户ID不能为空")
    @Comment("用户ID，关联users.id")
    private Long userId;

    // 支付信息
    /**
     * 支付方式：WECHAT, ALIPAY, ADMIN
     */
    @Column(name = "payment_method", length = 20, nullable = false)
    @NotBlank(message = "支付方式不能为空")
    @Comment("支付方式：WECHAT, ALIPAY, ADMIN")
    private String paymentMethod;

    /**
     * 支付平台：WECHAT_PAY, ALIPAY
     */
    @Column(name = "payment_platform", length = 20)
    @Comment("支付平台：WECHAT_PAY, ALIPAY")
    private String paymentPlatform;

    /**
     * 第三方平台订单号
     */
    @Column(name = "platform_order_no", length = 100, unique = true)
    @Comment("第三方平台订单号")
    private String platformOrderNo;

    /**
     * 第三方平台交易号
     */
    @Column(name = "platform_transaction_no", length = 100)
    @Comment("第三方平台交易号")
    private String platformTransactionNo;

    // 金额信息
    /**
     * 支付金额
     */
    @Column(name = "amount", precision = 15, scale = 2, nullable = false)
    @NotNull(message = "支付金额不能为空")
    @PositiveOrZero(message = "支付金额不能为负数")
    @Comment("支付金额")
    private BigDecimal amount;

    /**
     * 货币代码
     */
    @Column(name = "currency", length = 3)
    @Comment("货币代码")
    private String currency = "CNY";

    // 状态信息
    /**
     * 支付状态：PENDING, SUCCESS, FAILED, CANCELLED, REFUNDED
     */
    @Column(name = "status", length = 20)
    @Comment("支付状态：PENDING, SUCCESS, FAILED, CANCELLED, REFUNDED")
    private String status = "PENDING";

    /**
     * 失败原因
     */
    @Column(name = "failure_reason", columnDefinition = "TEXT")
    @Comment("失败原因")
    private String failureReason;

    /**
     * 取消原因
     */
    @Column(name = "cancel_reason", columnDefinition = "TEXT")
    @Comment("取消原因")
    private String cancelReason;

    /**
     * 退款金额
     */
    @Column(name = "refund_amount", precision = 15, scale = 2)
    @Comment("退款金额")
    private BigDecimal refundAmount;

    /**
     * 退款时间
     */
    @Column(name = "refund_time")
    @Comment("退款时间")
    private LocalDateTime refundTime;

    /**
     * 退款原因
     */
    @Column(name = "refund_reason", columnDefinition = "TEXT")
    @Comment("退款原因")
    private String refundReason;

    // 回调数据
    /**
     * 支付平台回调数据
     */
    @Column(name = "callback_data", columnDefinition = "TEXT")
    @Comment("支付平台回调数据")
    private String callbackData;

    /**
     * 回调时间
     */
    @Column(name = "callback_time")
    @Comment("回调时间")
    private LocalDateTime callbackTime;

    // 客户端信息
    /**
     * 客户端IP（使用字符串存储以兼容IPv4和IPv6）
     */
    @Column(name = "client_ip")
    @Comment("客户端IP")
    private String clientIp;

    /**
     * 用户代理
     */
    @Column(name = "user_agent", columnDefinition = "TEXT")
    @Comment("用户代理")
    private String userAgent;

    // 时间字段
    /**
     * 支付过期时间
     */
    @Column(name = "expired_at")
    @Comment("支付过期时间")
    private LocalDateTime expiredAt;

    /**
     * 支付完成时间
     */
    @Column(name = "paid_at")
    @Comment("支付完成时间")
    private LocalDateTime paidAt;

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false, updatable = false)
    @Comment("创建时间")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    @Comment("更新时间")
    private LocalDateTime updatedAt;

    /**
     * 创建时自动设置创建时间和更新时间
     */
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createdAt = now;
        this.updatedAt = now;
    }

    /**
     * 更新时自动设置更新时间
     */
    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 检查是否为待支付状态
     * 
     * @return 是否为待支付状态
     */
    public boolean isPending() {
        return "PENDING".equals(status);
    }

    /**
     * 检查是否支付成功
     * 
     * @return 是否支付成功
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(status);
    }

    /**
     * 检查是否支付失败
     * 
     * @return 是否支付失败
     */
    public boolean isFailed() {
        return "FAILED".equals(status);
    }

    /**
     * 检查是否已取消
     * 
     * @return 是否已取消
     */
    public boolean isCancelled() {
        return "CANCELLED".equals(status);
    }

    /**
     * 检查是否已退款
     * 
     * @return 是否已退款
     */
    public boolean isRefunded() {
        return "REFUNDED".equals(status);
    }

    /**
     * 检查是否已过期
     * 
     * @return 是否已过期
     */
    public boolean isExpired() {
        return expiredAt != null && LocalDateTime.now().isAfter(expiredAt);
    }

    /**
     * 检查是否为微信支付
     * 
     * @return 是否为微信支付
     */
    public boolean isWechatPay() {
        return "WECHAT".equals(paymentMethod);
    }

    /**
     * 检查是否为支付宝支付
     * 
     * @return 是否为支付宝支付
     */
    public boolean isAlipay() {
        return "ALIPAY".equals(paymentMethod);
    }

    /**
     * 检查是否为管理员充值
     * 
     * @return 是否为管理员充值
     */
    public boolean isAdminRecharge() {
        return "ADMIN".equals(paymentMethod);
    }

    /**
     * 标记为支付成功
     * 
     * @param platformTransactionNo 第三方交易号
     * @param callbackData          回调数据
     */
    public void markAsSuccess(String platformTransactionNo, String callbackData) {
        this.status = "SUCCESS";
        this.platformTransactionNo = platformTransactionNo;
        this.callbackData = callbackData;
        this.callbackTime = LocalDateTime.now();
        this.paidAt = LocalDateTime.now();
    }

    /**
     * 标记为支付失败
     * 
     * @param callbackData 失败原因
     */
    public void markAsFailed(String callbackData) {
        this.status = "FAILED";
        this.callbackData = callbackData;
        this.callbackTime = LocalDateTime.now();
    }

    /**
     * 设置过期时间（默认30分钟后过期）
     */
    public void setDefaultExpiry() {
        this.expiredAt = LocalDateTime.now().plusMinutes(30);
    }
}