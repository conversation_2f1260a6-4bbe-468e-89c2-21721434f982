package com.dipspro.modules.billing.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 申诉展示DTO
 * 
 * 用于返回申诉信息给前端，包含申诉的完整信息和处理状态。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class AppealDto {

    /**
     * 申诉ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 使用记录ID
     */
    private Long usageRecordId;

    /**
     * 申诉原因
     */
    private String reason;

    /**
     * 申诉详细描述
     */
    private String userDescription;

    /**
     * 证据文件URLs（JSON数组）
     */
    private String evidenceUrls;

    /**
     * 申诉状态
     * PENDING-待处理, APPROVED-已批准, REJECTED-已拒绝
     */
    private String status;

    /**
     * 管理员备注
     */
    private String adminComment;

    /**
     * 提交时间
     */
    private LocalDateTime submittedAt;

    /**
     * 处理时间
     */
    private LocalDateTime processedAt;

    /**
     * 实际退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 获取状态显示文本
     * 
     * @return 状态显示文本
     */
    public String getStatusDisplay() {
        if (status == null) {
            return "未知状态";
        }

        return switch (status) {
            case "PENDING" -> "待处理";
            case "APPROVED" -> "已批准";
            case "REJECTED" -> "已拒绝";
            default -> "未知状态";
        };
    }

    /**
     * 检查申诉是否已处理
     * 
     * @return 是否已处理
     */
    public boolean isProcessed() {
        return "APPROVED".equals(status) || "REJECTED".equals(status);
    }

    /**
     * 检查申诉是否被批准
     * 
     * @return 是否被批准
     */
    public boolean isApproved() {
        return "APPROVED".equals(status);
    }

    /**
     * 检查申诉是否被拒绝
     * 
     * @return 是否被拒绝
     */
    public boolean isRejected() {
        return "REJECTED".equals(status);
    }

    /**
     * 检查是否有实际退款
     * 
     * @return 是否有退款
     */
    public boolean hasRefund() {
        return refundAmount != null && refundAmount.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 获取格式化的实际退款金额
     * 
     * @return 格式化的退款金额字符串
     */
    public String getFormattedRefundAmount() {
        if (refundAmount == null || refundAmount.compareTo(BigDecimal.ZERO) == 0) {
            return "无退款";
        }

        return "¥" + refundAmount.toString();
    }

    /**
     * 获取申诉处理结果描述
     * 
     * @return 处理结果描述
     */
    public String getProcessResultDescription() {
        if (!isProcessed()) {
            return "待处理";
        }

        StringBuilder result = new StringBuilder();
        result.append(getStatusDisplay());

        if (isApproved() && hasRefund()) {
            result.append(" - 退款: ").append(getFormattedRefundAmount());
        }

        if (adminComment != null && !adminComment.trim().isEmpty()) {
            result.append(" | 备注: ").append(adminComment);
        }

        return result.toString();
    }

    /**
     * 计算处理时长（小时）
     * 
     * @return 处理时长，未处理返回null
     */
    public Long getProcessingHours() {
        if (!isProcessed() || processedAt == null || createdAt == null) {
            return null;
        }

        return java.time.Duration.between(createdAt, processedAt).toHours();
    }

    /**
     * 获取申诉摘要信息
     * 
     * @return 申诉摘要
     */
    public String getAppealSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("申诉原因: ").append(reason);
        summary.append(" | 状态: ").append(getStatusDisplay());

        if (hasRefund()) {
            summary.append(" | 退款: ").append(getFormattedRefundAmount());
        }

        return summary.toString();
    }

}