package com.dipspro.modules.billing.service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.dipspro.modules.billing.entity.BillingUsageRecord;

/**
 * 核心计费服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface BillingService {

        /**
         * 执行计费操作
         * <p>
         * 这是核心计费方法，负责处理AI对话的Token计费流程。
         * 该方法会验证参数、检查重复计费、计算费用、扣除余额并创建计费记录。
         * <p>
         * 执行流程：
         * 1. 参数验证：检查用户ID、消息ID、Token数量等参数的有效性
         * 2. 重复检查：防止同一消息被重复计费
         * 3. 套餐获取：获取用户当前使用的计费套餐
         * 4. 费用计算：根据输入输出Token数量和套餐价格计算总费用
         * 5. 余额检查：确保用户余额充足以支付本次计费
         * 6. 记录创建：创建详细的计费使用记录
         * 7. 余额扣除：从用户余额中扣除相应费用
         * 8. 交易记录：创建对应的扣费交易记录
         *
         * @param userId       用户ID，不能为空
         * @param messageId    消息ID，用于关联聊天消息，不能为空
         * @param inputTokens  输入Token数量，可以为null（默认为0）
         * @param outputTokens 输出Token数量，可以为null（默认为0）
         * @param requestTime  请求开始时间，用于计算响应时长
         * @param responseTime 响应结束时间，用于计算响应时长
         * @return 创建的计费使用记录
         * @throws IllegalArgumentException 当参数验证失败时抛出
         * @throws IllegalStateException    当余额不足或扣费失败时抛出
         */
        BillingUsageRecord performBilling(Long userId, UUID messageId, Long inputTokens, Long outputTokens,
                        LocalDateTime requestTime, LocalDateTime responseTime);

        /**
         * 异步执行计费操作
         * <p>
         * 这是计费操作的异步版本，适用于不需要立即获取计费结果的场景。
         * 在后台线程中执行计费操作，不阻塞主线程，提高系统响应性能。
         * <p>
         * 执行特点：
         * 1. 异步处理：在后台线程中执行，不阻塞调用线程
         * 2. 错误处理：如果计费失败，会记录错误日志并调用失败处理逻辑
         * 3. 无返回值：调用方无法直接获取计费结果
         * 4. 适用场景：批量计费、延迟计费、高并发场景
         * <p>
         * 注意事项：
         * - 由于是异步执行，调用方无法直接获取计费结果
         * - 需要通过其他方式（如查询数据库）来确认计费状态
         * - 失败情况会通过日志记录和失败处理机制处理
         *
         * @param userId       用户ID，不能为空
         * @param messageId    消息ID，用于关联聊天消息，不能为空
         * @param inputTokens  输入Token数量，可以为null（默认为0）
         * @param outputTokens 输出Token数量，可以为null（默认为0）
         * @param requestTime  请求开始时间，用于计算响应时长
         * @param responseTime 响应结束时间，用于计算响应时长
         */
        void performBillingAsync(Long userId, UUID messageId, Long inputTokens, Long outputTokens,
                        LocalDateTime requestTime, LocalDateTime responseTime);

        /**
         * 预检查计费（检查余额是否充足）
         * <p>
         * 在实际计费前检查用户是否有足够余额支付预估费用，避免在对话过程中因余额不足而中断。
         * 这是一个轻量级的检查方法，不会实际扣费，只做余额验证。
         * <p>
         * 检查流程：
         * 1. 用户信息获取：获取或创建用户余额记录
         * 2. 套餐信息获取：获取用户当前使用的计费套餐
         * 3. 费用预估：根据预估Token数量计算所需费用
         * 4. 余额检查：验证用户余额是否充足
         * 5. 异常处理：捕获并记录任何异常情况
         * <p>
         * 应用场景：
         * - AI对话开始前的余额检查
         * - 批量操作前的预检查
         * - 用户界面余额状态显示
         * - 防止无效请求的预过滤
         *
         * @param userId                用户ID，不能为空
         * @param estimatedInputTokens  预估输入Token数量，可以为null
         * @param estimatedOutputTokens 预估输出Token数量，可以为null
         * @return 检查结果，true表示余额充足可以计费，false表示余额不足
         */
        boolean preCheckBilling(Long userId, Long estimatedInputTokens, Long estimatedOutputTokens);

        /**
         * 计算Token费用
         * <p>
         * 根据用户当前套餐计算指定Token数量的费用，用于费用预估、账单计算等场景。
         * 该方法不会实际扣费，只进行费用计算。
         * <p>
         * 计算流程：
         * 1. 用户信息获取：获取或创建用户余额记录
         * 2. 套餐信息获取：获取用户当前使用的计费套餐
         * 3. 费用计算：使用套餐价格计算总费用
         * <p>
         * 计费规则：
         * - 套餐价格是每千Token的价格
         * - 实际费用 = Token数量 × 单价 ÷ 1000
         * - 输入Token和输出Token分别计费
         * - 最终费用保留6位小数
         * <p>
         * 应用场景：
         * - 费用预估和展示
         * - 账单计算
         * - 套餐比较分析
         * - 用户消费预测
         *
         * @param userId       用户ID，不能为空
         * @param inputTokens  输入Token数量，可以为null（默认为0）
         * @param outputTokens 输出Token数量，可以为null（默认为0）
         * @return 计算得出的总费用，精确到6位小数
         */
        BigDecimal calculateTokenCost(Long userId, Long inputTokens, Long outputTokens);

        /**
         * 根据消息ID查询计费记录
         *
         * @param messageId 消息ID
         * @return 计费记录
         */
        Optional<BillingUsageRecord> getBillingRecordByMessageId(UUID messageId);

        /**
         * 检查消息是否已被计费
         *
         * @param messageId 消息ID
         * @return 是否已计费
         */
        boolean isMessageBilled(UUID messageId);

        /**
         * 查询用户计费记录
         *
         * @param userId   用户ID
         * @param pageable 分页参数
         * @return 分页结果
         */
        Page<BillingUsageRecord> getUserBillingRecords(Long userId, Pageable pageable);

        /**
         * 查询用户指定时间范围内的计费记录
         *
         * @param userId    用户ID
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @param pageable  分页参数
         * @return 分页结果
         */
        Page<BillingUsageRecord> getUserBillingRecordsByDateRange(Long userId, LocalDateTime startTime,
                        LocalDateTime endTime, Pageable pageable);

        /**
         * 查询用户Token使用统计
         *
         * @param userId    用户ID
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 包含输入Token、输出Token、总费用的统计信息
         */
        Object[] getUserTokenUsageStats(Long userId, LocalDateTime startTime, LocalDateTime endTime);

        /**
         * 查询用户今日Token使用量
         *
         * @param userId 用户ID
         * @return 今日Token使用总量
         */
        Long getUserTodayTokenUsage(Long userId);

        /**
         * 查询用户今日费用消费
         *
         * @param userId 用户ID
         * @return 今日费用消费总额
         */
        BigDecimal getUserTodayCostUsage(Long userId);

        /**
         * 查询系统使用统计
         *
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 包含总用户数、总Token数、总费用的统计信息
         */
        Object[] getSystemUsageStats(LocalDateTime startTime, LocalDateTime endTime);

        /**
         * 查询用户使用次数统计
         *
         * @param userId    用户ID
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 使用次数
         */
        Long getUserUsageCount(Long userId, LocalDateTime startTime, LocalDateTime endTime);

        /**
         * 查询活跃用户列表
         *
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 活跃用户ID列表
         */
        List<Long> getActiveUsers(LocalDateTime startTime, LocalDateTime endTime);

        /**
         * 查询用户最近的计费记录
         *
         * @param userId 用户ID
         * @param limit  限制数量
         * @return 最近的计费记录列表
         */
        List<BillingUsageRecord> getUserRecentBillingRecords(Long userId, int limit);

        /**
         * 查询高消费计费记录
         *
         * @param costThreshold 费用阈值
         * @param pageable      分页参数
         * @return 分页结果
         */
        Page<BillingUsageRecord> getHighCostBillingRecords(BigDecimal costThreshold, Pageable pageable);

        /**
         * 查询异常计费记录
         *
         * @param maxTokens 最大Token数量阈值
         * @param maxCost   最大费用阈值
         * @param pageable  分页参数
         * @return 分页结果
         */
        Page<BillingUsageRecord> getAbnormalBillingRecords(Long maxTokens, BigDecimal maxCost, Pageable pageable);

        /**
         * 根据计费方式统计使用情况
         *
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @return 按计费方式分组的统计信息
         */
        List<Object[]> getUsageStatsByBillingType(LocalDateTime startTime, LocalDateTime endTime);

        /**
         * 查询用户月度使用统计
         *
         * @param userId 用户ID
         * @param year   年份
         * @param month  月份
         * @return 包含使用次数、Token总数、费用总额的统计信息
         */
        Object[] getUserMonthlyStats(Long userId, int year, int month);

        /**
         * 查询用户最高单次消费记录
         *
         * @param userId 用户ID
         * @return 最高单次消费记录
         */
        Optional<BillingUsageRecord> getUserMaxCostRecord(Long userId);

        /**
         * 查询系统整体消费排行榜
         *
         * @param startTime 开始时间
         * @param endTime   结束时间
         * @param pageable  分页参数
         * @return 用户消费排行榜
         */
        Page<Object[]> getUserCostRanking(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

        /**
         * 重新计费（用于修正错误的计费记录）
         *
         * @param recordId        计费记录ID
         * @param newInputTokens  新的输入Token数量
         * @param newOutputTokens 新的输出Token数量
         * @param reason          重新计费原因
         * @return 是否成功
         */
        boolean rebilling(Long recordId, Long newInputTokens, Long newOutputTokens, String reason);

        /**
         * 撤销计费（退还费用）
         *
         * @param recordId 计费记录ID
         * @param reason   撤销原因
         * @return 是否成功
         */
        boolean reverseBilling(Long recordId, String reason);

        /**
         * 批量删除历史计费记录
         *
         * @param beforeTime 时间阈值
         * @return 删除的记录数
         */
        int deleteHistoryBillingRecords(LocalDateTime beforeTime);

        /**
         * 验证计费参数的有效性
         *
         * @param userId       用户ID
         * @param messageId    消息ID
         * @param inputTokens  输入Token数量
         * @param outputTokens 输出Token数量
         * @return 验证结果消息，null表示验证通过
         */
        String validateBillingParameters(Long userId, UUID messageId, Long inputTokens, Long outputTokens);

        /**
         * 获取计费配置信息
         *
         * @param userId 用户ID
         * @return 计费配置信息
         */
        Object getBillingConfiguration(Long userId);

        /**
         * 计费失败处理
         *
         * @param userId       用户ID
         * @param messageId    消息ID
         * @param errorMessage 错误信息
         * @param inputTokens  输入Token数量
         * @param outputTokens 输出Token数量
         */
        void handleBillingFailure(Long userId, UUID messageId, String errorMessage, Long inputTokens,
                        Long outputTokens);

        /**
         * 根据消息ID列表获取计费使用记录
         * 
         * @param messageIds 消息ID列表
         * @return 计费使用记录列表
         */
        List<BillingUsageRecord> getUsageRecordsByMessageIds(List<UUID> messageIds);
}