package com.dipspro.aspect;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import com.dipspro.modules.system.entity.SysOperationLog;
import com.dipspro.modules.system.repository.SysOperationLogRepository;
import com.dipspro.util.IpUtils;
import com.dipspro.util.JsonUtils;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * 操作日志切面
 */
@Slf4j
@Aspect
@Component
public class OperationLogAspect {

    @Autowired
    private SysOperationLogRepository operationLogRepository;

    /**
     * 定义切点：拦截所有Controller方法
     */
    @Pointcut("execution(* com.dipspro.modules.*.controller.*.*(..))")
    public void operationLogPointcut() {
    }

    /**
     * 环绕通知：记录操作日志
     */
    @Around("operationLogPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes != null ? attributes.getRequest() : null;
        
        // 添加详细的AOP日志记录
        String methodName = joinPoint.getSignature().getName();
        String targetClassName = joinPoint.getTarget().getClass().getSimpleName();
        String requestInfo = request != null ? request.getMethod() + " " + request.getRequestURI() : "无请求信息";
        
        log.debug("=== AOP拦截开始 === 类: {}, 方法: {}, 请求: {}", targetClassName, methodName, requestInfo);
        
        SysOperationLog operationLog = new SysOperationLog();
        
        if (request != null) {
            // 设置请求信息
            operationLog.setRequestMethod(request.getMethod());
            operationLog.setRequestUrl(request.getRequestURI());
            operationLog.setIpAddress(IpUtils.getClientIp(request));
            operationLog.setUserAgent(request.getHeader("User-Agent"));
            
            // 设置操作类型
            String operationType = getOperationType(request.getMethod(), joinPoint.getSignature().getName());
            operationLog.setOperationType(operationType);
            
            // 设置模块信息
            operationLog.setModule(targetClassName.replace("Controller", ""));
            
            // 设置请求参数 - 过滤掉不应该序列化的参数
            Object[] args = joinPoint.getArgs();
            if (args != null && args.length > 0) {
                List<Object> filteredArgs = filterSerializableArgs(args);
                operationLog.setRequestParams(JsonUtils.toJsonSafe(filteredArgs));
            }
        }
        
        Object result = null;
        try {
            log.debug("=== AOP执行目标方法开始 === 类: {}, 方法: {}", targetClassName, methodName);
            // 执行目标方法
            result = joinPoint.proceed();
            log.debug("=== AOP执行目标方法成功 === 类: {}, 方法: {}", targetClassName, methodName);
            
            // 设置成功状态
            operationLog.setStatus(1);
            operationLog.setResponseResult(JsonUtils.toJsonSafe(result));
            
        } catch (Exception e) {
            log.error("=== AOP执行目标方法失败 === 类: {}, 方法: {}, 错误: {}", targetClassName, methodName, e.getMessage());
            // 设置失败状态
            operationLog.setStatus(0);
            operationLog.setErrorMsg(e.getMessage());
            throw e;
        } finally {
            // 计算耗时
            long endTime = System.currentTimeMillis();
            operationLog.setCostTime(endTime - startTime);
            operationLog.setCreatedTime(LocalDateTime.now());
            
            log.debug("=== AOP保存操作日志开始 === 类: {}, 方法: {}, 耗时: {}ms", targetClassName, methodName, (endTime - startTime));
            // 异步保存日志
            try {
                operationLogRepository.save(operationLog);
                log.debug("=== AOP保存操作日志成功 === 类: {}, 方法: {}", targetClassName, methodName);
            } catch (Exception e) {
                log.error("=== AOP保存操作日志失败 === 类: {}, 方法: {}, 错误: {}", targetClassName, methodName, e.getMessage(), e);
            }
            log.debug("=== AOP拦截结束 === 类: {}, 方法: {}", targetClassName, methodName);
        }
        
        return result;
    }

    /**
     * 过滤掉不应该序列化的参数
     */
    private List<Object> filterSerializableArgs(Object[] args) {
        List<Object> filteredArgs = new ArrayList<>();
        
        for (Object arg : args) {
            if (arg == null) {
                filteredArgs.add(null);
            } else if (isSerializableArg(arg)) {
                filteredArgs.add(arg);
            } else {
                // 对于不可序列化的参数，添加简单描述
                filteredArgs.add(arg.getClass().getSimpleName() + "@" + Integer.toHexString(arg.hashCode()));
            }
        }
        
        return filteredArgs;
    }

    /**
     * 判断参数是否应该被序列化
     */
    private boolean isSerializableArg(Object arg) {
        // 排除Servlet相关对象
        if (arg instanceof HttpServletRequest || 
            arg instanceof HttpServletResponse) {
            return false;
        }
        
        // 排除文件上传对象
        if (arg instanceof MultipartFile) {
            return false;
        }
        
        // 排除Spring框架内部对象
        String className = arg.getClass().getName();
        if (className.startsWith("org.springframework.") ||
            className.startsWith("jakarta.servlet.") ||
            className.startsWith("javax.servlet.")) {
            return false;
        }
        
        return true;
    }

    /**
     * 根据HTTP方法和方法名判断操作类型
     */
    private String getOperationType(String httpMethod, String methodName) {
        if ("POST".equals(httpMethod)) {
            if (methodName.contains("login")) {
                return "LOGIN";
            }
            return "CREATE";
        } else if ("PUT".equals(httpMethod) || "PATCH".equals(httpMethod)) {
            return "UPDATE";
        } else if ("DELETE".equals(httpMethod)) {
            return "DELETE";
        } else if ("GET".equals(httpMethod)) {
            return "QUERY";
        } else {
            return "OTHER";
        }
    }
} 