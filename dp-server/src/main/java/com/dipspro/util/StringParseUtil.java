package com.dipspro.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

/**
 * 字符串解析工具类
 * 提供字符串清理、分割等功能
 * 
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
public class StringParseUtil {

    private StringParseUtil() {
        // 防止实例化
    }

    // 常用分隔符的正则表达式模式
    private static final Pattern DELIMITER_PATTERN = Pattern.compile("[\\s,，。；;]+");

    // 前后需要清理的特殊字符模式（包括空格、各种标点符号）
    private static final Pattern TRIM_PATTERN = Pattern
            .compile("^[\\s,，。；;!！?？：:()（）\\[\\]【】{}｛｝\"']+|[\\s,，。；;!！?？：:()（）\\[\\]【】{}｛｝\"']+$");

    /**
     * 解析客户特征字符串为手机号列表
     * 支持多种分隔符：空格、中英文逗号、中英文句号、中英文分号等
     * 
     * @param customerFeatures 客户特征字符串
     * @return 清理后的手机号列表
     */
    public static List<String> parseCustomerFeatures(String customerFeatures) {
        if (customerFeatures == null || customerFeatures.trim().isEmpty()) {
            log.debug("输入的客户特征字符串为空");
            return new ArrayList<>();
        }

        log.debug("开始解析客户特征字符串，原始长度: {}", customerFeatures.length());

        // 1. 使用正则表达式分割字符串
        String[] parts = DELIMITER_PATTERN.split(customerFeatures);

        // 2. 清理每个部分并过滤空值
        List<String> result = Arrays.stream(parts)
                .map(StringParseUtil::cleanString)
                .filter(s -> !s.isEmpty())
                .distinct() // 去重
                .collect(Collectors.toList());

        log.debug("解析完成，原始字符串长度: {}, 分割后数量: {}, 清理后数量: {}",
                customerFeatures.length(), parts.length, result.size());

        if (log.isTraceEnabled() && !result.isEmpty()) {
            log.trace("解析结果前3项: {}", result.stream()
                    .limit(3)
                    .map(MaskUtil::maskMobile)
                    .collect(Collectors.joining(", ")));
        }

        return result;
    }

    /**
     * 清理字符串，去除前后的空格和特殊字符
     * 
     * @param input 输入字符串
     * @return 清理后的字符串
     */
    public static String cleanString(String input) {
        if (input == null) {
            return "";
        }

        // 去除前后的特殊字符和空格
        String cleaned = TRIM_PATTERN.matcher(input).replaceAll("");

        return cleaned.trim();
    }

    /**
     * 验证手机号格式（简单验证）
     * 
     * @param mobile 手机号
     * @return 是否为有效的手机号格式
     */
    public static boolean isValidMobile(String mobile) {
        if (mobile == null || mobile.length() != 11) {
            return false;
        }

        // 简单验证：11位数字，以1开头
        return mobile.matches("^1[3-9]\\d{9}$");
    }

    /**
     * 过滤有效的手机号
     * 
     * @param mobiles 手机号列表
     * @return 有效的手机号列表
     */
    public static List<String> filterValidMobiles(List<String> mobiles) {
        if (mobiles == null || mobiles.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> validMobiles = mobiles.stream()
                .filter(StringParseUtil::isValidMobile)
                .collect(Collectors.toList());

        int invalidCount = mobiles.size() - validMobiles.size();
        if (invalidCount > 0) {
            log.warn("过滤掉 {} 个无效的手机号", invalidCount);
        }

        return validMobiles;
    }

    /**
     * 解析并验证客户特征字符串
     * 组合了解析和验证功能
     * 
     * @param customerFeatures 客户特征字符串
     * @return 有效的手机号列表
     */
    public static List<String> parseAndValidateCustomerFeatures(String customerFeatures) {
        List<String> parsed = parseCustomerFeatures(customerFeatures);
        return filterValidMobiles(parsed);
    }
}