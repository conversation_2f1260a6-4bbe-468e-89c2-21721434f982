package com.dipspro.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 时间周期转换工具类
 * 
 * 提供将中文时间描述转换为标准格式的静态工具方法
 * 
 * 支持的转换格式：
 * - 年度：2025年度、2025年全年 → 2025-0112
 * - 季度：2025年一季度、2025年第一季度 → 2025-0103
 * - 半年度：2025年半年度、2025年上半年 → 2025-0106
 * - 月度范围：2025年1-5月、2025年1月至5月 → 2025-0105
 * - 单月：2025年5月 → 2025-05
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
public class DatePeriodUtils {

    /**
     * 私有构造函数，防止实例化
     */
    private DatePeriodUtils() {
        // 工具类不应被实例化
    }

    /**
     * 时间周期转换方法
     * 
     * 支持的输入格式：
     * - 年度：2025年度、2025年全年 → 2025-0112
     * - 季度：2025年一季度、2025年第一季度 → 2025-0103
     * - 半年度：2025年半年度、2025年上半年 → 2025-0106
     * - 月度范围：2025年1-5月、2025年1月至5月 → 2025-0105
     * - 单月：2025年5月 → 2025-05
     * 
     * @param dataPeriod 输入的时间周期字符串
     * @return 转换后的标准格式字符串
     */
    public static String transform(String dataPeriod) {
        // 输入参数验证
        if (StringUtils.isBlank(dataPeriod)) {
            log.warn("时间周期参数为空，返回原值");
            return dataPeriod;
        }

        log.debug("开始转换时间周期，输入: {}", dataPeriod);

        try {
            // 1. 字符串预处理，去除所有空格
            String cleanInput = dataPeriod.replaceAll("\\s+", "");

            // 2. 提取年份
            String year = extractYear(cleanInput);
            if (year == null) {
                log.warn("无法提取年份，返回原值: {}", dataPeriod);
                return dataPeriod;
            }

            // 3. 识别时间类型并转换
            String result = convertTimePeriod(cleanInput, year);

            log.debug("时间周期转换完成，输入: {} → 输出: {}", dataPeriod, result);
            return result;

        } catch (Exception e) {
            log.error("时间周期转换异常，输入: {}", dataPeriod, e);
            return dataPeriod;
        }
    }

    /**
     * 提取年份
     * 
     * @param input 输入字符串
     * @return 年份字符串，如果无法提取则返回null
     */
    private static String extractYear(String input) {
        // 使用正则表达式匹配4位数字年份
        Pattern pattern = Pattern.compile("(\\d{4})");
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    /**
     * 转换时间周期
     * 
     * @param input 清理后的输入字符串
     * @param year  年份
     * @return 转换后的格式
     */
    private static String convertTimePeriod(String input, String year) {
        // 年度类型识别
        if (input.contains("年度") || input.contains("全年")) {
            return year + "-0112";
        }

        // 季度类型识别
        if (input.contains("季度")) {
            return convertQuarter(input, year);
        }

        // 半年度类型识别
        if (input.contains("半年") || input.contains("半年度")) {
            return convertHalfYear(input, year);
        }

        // 月度范围识别（1-5月、1月至5月等）
        String rangeResult = convertMonthRange(input, year);
        if (rangeResult != null) {
            return rangeResult;
        }

        // 单月识别
        String singleMonthResult = convertSingleMonth(input, year);
        if (singleMonthResult != null) {
            return singleMonthResult;
        }

        // 如果都不匹配，返回原值
        log.warn("无法识别时间周期类型: {}", input);
        return input;
    }

    /**
     * 转换季度
     * 
     * @param input 输入字符串
     * @param year  年份
     * @return 转换后的季度格式
     */
    private static String convertQuarter(String input, String year) {
        // 中文数字映射
        if (input.contains("一季度") || input.contains("第一季度") || input.contains("1季度")) {
            return year + "-0103";
        } else if (input.contains("二季度") || input.contains("第二季度") || input.contains("2季度")) {
            return year + "-0106";
        } else if (input.contains("三季度") || input.contains("第三季度") || input.contains("3季度")) {
            return year + "-0109";
        } else if (input.contains("四季度") || input.contains("第四季度") || input.contains("4季度")) {
            return year + "-0112";
        }

        // 默认返回第一季度
        return year + "-0103";
    }

    /**
     * 转换半年度
     * 
     * @param input 输入字符串
     * @param year  年份
     * @return 转换后的半年度格式
     */
    private static String convertHalfYear(String input, String year) {
        if (input.contains("下半年")) {
            return year + "-0112";
        } else {
            // 默认上半年
            return year + "-0106";
        }
    }

    /**
     * 转换月度范围
     * 
     * @param input 输入字符串
     * @param year  年份
     * @return 转换后的月度范围格式，如果不匹配则返回null
     */
    private static String convertMonthRange(String input, String year) {
        // 匹配 1-5月、1月至5月 等格式
        Pattern pattern = Pattern.compile("(\\d{1,2})[-至到～](\\d{1,2})月");
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            String endMonth = matcher.group(2);
            return year + "-01" + String.format("%02d", Integer.parseInt(endMonth));
        }

        // 匹配 1-5月 简化格式
        pattern = Pattern.compile("(\\d{1,2})-(\\d{1,2})月");
        matcher = pattern.matcher(input);
        if (matcher.find()) {
            String endMonth = matcher.group(2);
            return year + "-01" + String.format("%02d", Integer.parseInt(endMonth));
        }

        return null;
    }

    /**
     * 转换单月
     * 
     * @param input 输入字符串
     * @param year  年份
     * @return 转换后的单月格式，如果不匹配则返回null
     */
    private static String convertSingleMonth(String input, String year) {
        // 匹配单月格式：5月、05月
        Pattern pattern = Pattern.compile("(\\d{1,2})月");
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            // 确保不是范围格式
            if (!input.matches(".*\\d+[-至到～]\\d+月.*")) {
                String month = matcher.group(1);
                return year + "-" + String.format("%02d", Integer.parseInt(month));
            }
        }

        return null;
    }
}