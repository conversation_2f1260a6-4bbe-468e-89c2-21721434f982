package com.dipspro.util;

/**
 * 数据掩码工具类
 * 
 * <AUTHOR>
 * 2025/5/12
 */
public class MaskUtil {
    
    /**
     * 对手机号进行掩码处理，只保留前2位和后2位，中间用*代替
     * 
     * @param mobile 手机号码
     * @return 掩码后的手机号
     */
    public static String maskMobile(String mobile) {
        if (mobile == null || mobile.length() <= 4) {
            return mobile;
        }
        
        int length = mobile.length();
        return mobile.substring(0, 2) + 
               "*".repeat(length - 4) + 
               mobile.substring(length - 2);
    }
} 