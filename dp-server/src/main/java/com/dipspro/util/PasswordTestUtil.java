package com.dipspro.util;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * 密码测试工具类
 * 用于生成测试数据的正确密码哈希值
 */
public class PasswordTestUtil {

    private static final PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    public static void main(String[] args) {
        String rawPassword = "123456";
        
        // 生成各个用户的正确密码哈希值
        String[] salts = {
            "salt001", "salt002", "salt003", "salt004", "salt005",
            "salt006", "salt007", "salt008", "salt009", "salt010",
            "salt011", "salt012", "salt013"
        };
        
        System.out.println("-- 正确的密码哈希值（密码: " + rawPassword + "）");
        for (int i = 0; i < salts.length; i++) {
            String saltedPassword = rawPassword + salts[i];
            String encodedPassword = passwordEncoder.encode(saltedPassword);
            System.out.println("-- 用户" + (i + 1) + " (salt: " + salts[i] + "): " + encodedPassword);
        }
        
        // 验证测试
        System.out.println("\n-- 验证测试:");
        String testSalt = "salt004";
        String testEncoded = passwordEncoder.encode(rawPassword + testSalt);
        boolean matches = passwordEncoder.matches(rawPassword + testSalt, testEncoded);
        System.out.println("验证结果: " + matches);
    }
} 