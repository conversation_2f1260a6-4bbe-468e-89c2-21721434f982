package com.dipspro.util;

import org.apache.commons.lang3.StringUtils;

/**
 * 楼盘名称解析工具类
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
public class ProjectNameParseUtil {

    private ProjectNameParseUtil() {
        // 私有构造函数，防止实例化
    }

    /**
     * 解析楼盘名称，支持两种格式：
     * 1. 不带中括号的：徐州时代之光
     * 2. 带中括号的：[万科]徐州时代之光
     *
     * @param name 原始楼盘名称
     * @return 解析结果，包含项目名称和开发商名称
     */
    public static ProjectNameParseResult parseProjectName(String name) {
        if (StringUtils.isBlank(name)) {
            return new ProjectNameParseResult("", "");
        }

        String trimmedName = name.trim();

        // 检查是否包含中括号格式 [开发商]项目名称
        if (trimmedName.startsWith("[") && trimmedName.contains("]")) {
            int endBracketIndex = trimmedName.indexOf("]");
            if (endBracketIndex > 1) { // 确保中括号内有内容
                String customerName = trimmedName.substring(1, endBracketIndex);
                String projectName = trimmedName.substring(endBracketIndex + 1).trim();

                // 如果项目名称为空，则使用原始名称作为项目名称
                if (StringUtils.isBlank(projectName)) {
                    projectName = trimmedName;
                    customerName = "";
                }

                return new ProjectNameParseResult(projectName, customerName);
            }
        }

        // 不包含中括号格式，直接作为项目名称
        return new ProjectNameParseResult(trimmedName, "");
    }

    /**
     * 楼盘名称解析结果类
     */
    public static class ProjectNameParseResult {
        /**
         * 项目名称
         */
        private String projectName;

        /**
         * 开发商名称
         */
        private String customerName;

        /**
         * 默认构造函数
         */
        public ProjectNameParseResult() {
        }

        /**
         * 全参构造函数
         * 
         * @param projectName  项目名称
         * @param customerName 开发商名称
         */
        public ProjectNameParseResult(String projectName, String customerName) {
            this.projectName = projectName;
            this.customerName = customerName;
        }

        /**
         * 获取项目名称
         * 
         * @return 项目名称
         */
        public String getProjectName() {
            return projectName;
        }

        /**
         * 设置项目名称
         * 
         * @param projectName 项目名称
         */
        public void setProjectName(String projectName) {
            this.projectName = projectName;
        }

        /**
         * 获取开发商名称
         * 
         * @return 开发商名称
         */
        public String getCustomerName() {
            return customerName;
        }

        /**
         * 设置开发商名称
         * 
         * @param customerName 开发商名称
         */
        public void setCustomerName(String customerName) {
            this.customerName = customerName;
        }
    }
}