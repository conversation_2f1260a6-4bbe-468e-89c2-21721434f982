package com.dipspro.util;

import java.util.Collection;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

/**
 * Map工具类，提供安全地从Map获取值的方法
 *
 * <AUTHOR>
 * 2025/5/13 10:05
 */
@Slf4j
public final class MapUtils {

    private MapUtils() {
        // 私有构造方法，禁止实例化
    }

    /**
     * 安全地从Map中获取Integer值，处理null值和类型转换
     * 
     * @param map Map对象
     * @param key 键名
     * @return Integer值，如果不存在或为null则返回null
     */
    public static Integer safeGetInteger(Map<String, Object> map, String key) {
        if (map == null || !map.containsKey(key) || map.get(key) == null) {
            return null;
        }
        
        Object value = map.get(key);
        
        // 处理数组或集合类型，直接返回null
        if (value.getClass().isArray() || value instanceof Collection<?> || 
            (value instanceof String && ((String) value).trim().startsWith("[") && ((String) value).trim().endsWith("]"))) {
            if (log.isDebugEnabled()) {
                log.debug("忽略数组或集合类型的值 [{}]，无法转换为Integer", value);
            }
            return null;
        }
        
        try {
            if (value instanceof Integer) {
                return (Integer) value;
            } else if (value instanceof Number) {
                return ((Number) value).intValue();
            } else if (value instanceof String) {
                String strValue = (String) value;
                return strValue.isEmpty() ? null : Integer.parseInt(strValue);
            }
        } catch (NumberFormatException e) {
            log.warn("无法将值 [{}] 转换为Integer: {}", value, e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 安全地从Map中获取Double值，处理null值和类型转换
     * 
     * @param map Map对象
     * @param key 键名
     * @return Double值，如果不存在或为null则返回null
     */
    public static Double safeGetDouble(Map<String, Object> map, String key) {
        if (map == null || !map.containsKey(key) || map.get(key) == null) {
            return null;
        }
        
        Object value = map.get(key);
        
        // 处理数组或集合类型，直接返回null
        if (value.getClass().isArray() || value instanceof Collection<?> || 
            (value instanceof String && ((String) value).trim().startsWith("[") && ((String) value).trim().endsWith("]"))) {
            if (log.isDebugEnabled()) {
                log.debug("忽略数组或集合类型的值 [{}]，无法转换为Double", value);
            }
            return null;
        }
        
        try {
            if (value instanceof Double) {
                return (Double) value;
            } else if (value instanceof Number) {
                return ((Number) value).doubleValue();
            } else if (value instanceof String) {
                String strValue = (String) value;
                return strValue.isEmpty() ? null : Double.parseDouble(strValue);
            }
        } catch (NumberFormatException e) {
            log.warn("无法将值 [{}] 转换为Double: {}", value, e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 安全地从Map中获取String值，处理null值情况
     * 
     * @param map Map对象
     * @param key 键名
     * @return String值，如果不存在或为null则返回空字符串
     */
    public static String safeGetString(Map<String, Object> map, String key) {
        if (map == null || !map.containsKey(key) || map.get(key) == null) {
            return "";
        }
        
        Object value = map.get(key);
        return value.toString();
    }
} 