package com.dipspro.util;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 计费相关工具类
 * 
 * 提供计费系统相关的工具方法，包括：
 * - 申诉单号生成
 * - 交易流水号生成
 * - 支付单号生成
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class BillingUtil {

    private BillingUtil() {
        // 私有构造函数，防止实例化
    }

    /**
     * 生成申诉单号
     * 
     * 格式：APL + 日期(yyyyMMdd) + 8位随机数
     * 例如：APL202501156789012
     * 
     * @return 申诉单号
     */
    public static String generateAppealNo() {
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String randomStr = String.format("%08d", ThreadLocalRandom.current().nextInt(100000000));
        return "APL" + dateStr + randomStr;
    }

    /**
     * 生成交易流水号
     * 
     * 格式：TXN + 时间戳 + 6位随机数
     * 
     * @return 交易流水号
     */
    public static String generateTransactionNo() {
        long timestamp = System.currentTimeMillis();
        String randomStr = String.format("%06d", ThreadLocalRandom.current().nextInt(1000000));
        return "TXN" + timestamp + randomStr;
    }

    /**
     * 生成支付单号
     * 
     * 格式：PAY + 时间戳 + 6位随机数
     * 
     * @return 支付单号
     */
    public static String generatePaymentNo() {
        long timestamp = System.currentTimeMillis();
        String randomStr = String.format("%06d", ThreadLocalRandom.current().nextInt(1000000));
        return "PAY" + timestamp + randomStr;
    }
}