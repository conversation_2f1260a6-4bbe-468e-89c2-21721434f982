package com.dipspro.util;

import org.springframework.util.StringUtils;

/**
 * 指标工具类
 * 提供处理指标代码的静态工具方法
 */
public class IndexUtils {

    /**
     * 根据子指标代码获取其对应的主指标代码
     * 例如：从"101005001"获取主指标代码"101005"
     *
     * @param subIndexCode 子指标代码
     * @return 主指标代码，如果输入不是子指标代码则返回null
     */
    public static String getMainIndexCodeFromSubIndex(String subIndexCode) {
        if (!StringUtils.hasText(subIndexCode) || subIndexCode.length() <= 6) {
            return null;
        }
        
        try {
            // 子指标代码的前缀部分就是主指标代码
            // 例如：101005001 -> 101005
            return subIndexCode.substring(0, 6);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 判断给定的指标代码是否为主指标代码
     * 主指标代码通常为6位数字，例如：101005、201006
     *
     * @param indexCode 指标代码
     * @return 是否为主指标代码
     */
    public static boolean isMainIndexCode(String indexCode) {
        if (!StringUtils.hasText(indexCode)) {
            return false;
        }
        
        // 主指标代码格式检查：6位数字
        return indexCode.matches("\\d{6}");
    }

    /**
     * 判断给定的指标代码是否为子指标代码
     * 子指标代码通常为9位数字，例如：101005001、201006003
     *
     * @param indexCode 指标代码
     * @return 是否为子指标代码
     */
    public static boolean isSubIndexCode(String indexCode) {
        if (!StringUtils.hasText(indexCode)) {
            return false;
        }
        
        // 子指标代码格式检查：9位或更长的数字
        return indexCode.matches("\\d{9,}");
    }

    /**
     * 检查子指标是否属于指定的主指标
     *
     * @param subIndexCode 子指标代码
     * @param mainIndexCode 主指标代码
     * @return 是否属于该主指标
     */
    public static boolean isSubIndexOfMain(String subIndexCode, String mainIndexCode) {
        if (!StringUtils.hasText(subIndexCode) || !StringUtils.hasText(mainIndexCode)) {
            return false;
        }
        
        // 子指标代码以主指标代码开头
        return subIndexCode.startsWith(mainIndexCode);
    }
} 