package com.dipspro.util;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import lombok.extern.slf4j.Slf4j;

/**
 * 安全工具类
 * 
 * 提供获取当前用户认证信息的静态方法，包括用户ID、用户名、JWT Token等。
 * 支持从Spring Security上下文和HTTP请求属性中获取用户信息。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class SecurityUtil {

    /**
     * 私有构造函数，防止实例化
     */
    private SecurityUtil() {
        // 工具类不应被实例化
    }

    /**
     * 获取当前用户的JWT Token
     * 
     * 从HTTP请求头的Authorization字段中提取Bearer Token。
     * 通常用于向外部服务传递认证信息。
     * 
     * @return JWT Token 或 null（如果未找到或格式不正确）
     */
    public static String getCurrentUserToken() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder
                    .getRequestAttributes();
            if (attributes != null) {
                String authHeader = attributes.getRequest().getHeader("Authorization");
                log.debug("Authorization头信息: {}",
                        authHeader != null ? authHeader.substring(0, Math.min(20, authHeader.length())) + "..."
                                : "null");

                if (authHeader != null && authHeader.startsWith("Bearer ")) {
                    String token = authHeader.substring(7);
                    log.debug("成功提取JWT token，长度: {}", token.length());
                    return token;
                } else {
                    log.debug("Authorization头格式不正确或不存在Bearer token");
                }
            } else {
                log.debug("无法获取ServletRequestAttributes，可能不在HTTP请求上下文中");
            }
        } catch (Exception e) {
            log.warn("获取当前用户Token失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取当前用户ID
     * 
     * 从HTTP请求属性中获取用户ID，通常由JWT认证过滤器设置。
     * 这是最常用的方法，用于获取当前登录用户的唯一标识。
     * 
     * @return 用户ID 或 null（如果未登录或获取失败）
     */
    public static Long getCurrentUserId() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder
                    .getRequestAttributes();
            if (attributes != null) {
                Object userId = attributes.getRequest().getAttribute("userId");
                if (userId instanceof Long) {
                    log.debug("成功获取当前用户ID: {}", userId);
                    return (Long) userId;
                } else if (userId != null) {
                    // 尝试转换其他类型为Long
                    try {
                        Long parsedUserId = Long.valueOf(userId.toString());
                        log.debug("成功转换并获取当前用户ID: {}", parsedUserId);
                        return parsedUserId;
                    } catch (NumberFormatException e) {
                        log.warn("用户ID格式转换失败: {}", userId);
                    }
                } else {
                    log.debug("请求属性中未找到userId");
                }
            } else {
                log.debug("无法获取ServletRequestAttributes，可能不在HTTP请求上下文中");
            }
        } catch (Exception e) {
            log.warn("获取当前用户ID失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取当前用户名
     * 
     * 优先从Spring Security上下文获取，如果失败则从HTTP请求属性获取。
     * 用于日志记录、权限检查等场景。
     * 
     * @return 用户名 或 null（如果未登录或获取失败）
     */
    public static String getCurrentUsername() {
        try {
            // 首先尝试从Spring Security上下文获取
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                String username = authentication.getName();
                if (username != null && !"anonymousUser".equals(username)) {
                    log.debug("从Spring Security上下文获取用户名: {}", username);
                    return username;
                }
            }

            // 如果Spring Security上下文没有，尝试从请求属性获取
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder
                    .getRequestAttributes();
            if (attributes != null) {
                Object username = attributes.getRequest().getAttribute("username");
                if (username instanceof String) {
                    log.debug("从请求属性获取用户名: {}", username);
                    return (String) username;
                }
            }

            log.debug("未能获取到当前用户名");
        } catch (Exception e) {
            log.warn("获取当前用户名失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 检查当前用户是否已登录
     * 
     * 通过检查是否能获取到有效的用户ID来判断用户登录状态。
     * 
     * @return true表示已登录，false表示未登录
     */
    public static boolean isUserLoggedIn() {
        Long userId = getCurrentUserId();
        boolean isLoggedIn = userId != null && userId > 0;
        log.debug("用户登录状态检查: {}", isLoggedIn ? "已登录" : "未登录");
        return isLoggedIn;
    }

    /**
     * 获取当前用户的认证对象
     * 
     * 从Spring Security上下文获取完整的认证对象，包含用户详情、权限等信息。
     * 
     * @return Authentication对象 或 null（如果未认证）
     */
    public static Authentication getCurrentAuthentication() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()
                    && !"anonymousUser".equals(authentication.getName())) {
                log.debug("获取到当前用户认证对象: {}", authentication.getName());
                return authentication;
            }
            log.debug("未获取到有效的用户认证对象");
        } catch (Exception e) {
            log.warn("获取当前用户认证对象失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 检查当前用户是否具有指定权限
     * 
     * @param authority 权限名称
     * @return true表示具有权限，false表示没有权限
     */
    public static boolean hasAuthority(String authority) {
        try {
            Authentication authentication = getCurrentAuthentication();
            if (authentication != null) {
                boolean hasAuth = authentication.getAuthorities().stream()
                        .anyMatch(grantedAuthority -> grantedAuthority.getAuthority().equals(authority));
                log.debug("权限检查 [{}]: {}", authority, hasAuth ? "具有" : "没有");
                return hasAuth;
            }
        } catch (Exception e) {
            log.warn("权限检查失败: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 获取当前用户的所有权限
     * 
     * @return 权限名称数组，如果未认证则返回空数组
     */
    public static String[] getCurrentUserAuthorities() {
        try {
            Authentication authentication = getCurrentAuthentication();
            if (authentication != null) {
                String[] authorities = authentication.getAuthorities().stream()
                        .map(grantedAuthority -> grantedAuthority.getAuthority())
                        .toArray(String[]::new);
                log.debug("当前用户权限: {}", String.join(", ", authorities));
                return authorities;
            }
        } catch (Exception e) {
            log.warn("获取用户权限失败: {}", e.getMessage());
        }
        return new String[0];
    }

    /**
     * 要求用户必须已登录，否则抛出异常
     * 
     * @return 当前用户ID
     * @throws SecurityException 如果用户未登录
     */
    public static Long requireCurrentUserId() {
        Long userId = getCurrentUserId();
        if (userId == null || userId <= 0) {
            throw new SecurityException("用户未登录或登录状态无效");
        }
        return userId;
    }

    /**
     * 要求用户必须已登录，否则抛出异常
     * 
     * @return 当前用户名
     * @throws SecurityException 如果用户未登录
     */
    public static String requireCurrentUsername() {
        String username = getCurrentUsername();
        if (username == null || username.trim().isEmpty()) {
            throw new SecurityException("用户未登录或用户名无效");
        }
        return username;
    }
}