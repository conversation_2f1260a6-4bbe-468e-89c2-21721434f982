package com.dipspro.util;

import static com.dipspro.constant.ProjectStatsConstants.FACILITY_SEPARATOR;
import static com.dipspro.constant.ProjectStatsConstants.FACILITY_SUFFIX;
import static com.dipspro.constant.ProjectStatsConstants.PRICE_FORMAT;
import static com.dipspro.constant.ProjectStatsConstants.PRICE_TO_WAN_RATIO;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import com.dipspro.modules.profile.dto.ProjectDetail;

/**
 * 项目统计相关工具类
 * 
 * <AUTHOR>
 */
public class ProjectStatsUtil {
    
    /**
     * 格式化价格为万元单位
     * @param price 价格字符串
     * @return 格式化后的价格
     */
    public static String formatPrice(String price) {
        if (StringUtils.isBlank(price)) {
            return price;
        }
        
        // 判断是否为纯数字
        if (NumberUtils.isDigits(price.trim())) {
            try {
                // 转换为数字并除以10000（万元）
                double priceNum = Double.parseDouble(price.trim());
                double priceInWan = priceNum / PRICE_TO_WAN_RATIO;
                // 格式化为1位小数
                return String.format(PRICE_FORMAT, priceInWan);
            } catch (NumberFormatException e) {
                // 转换失败则返回原始值
                return price;
            }
        }
        
        return price;
    }
    
    /**
     * 如果设施数量不为null且不为0，则添加到设施描述中
     * @param builder StringBuilder对象
     * @param amount 设施数量
     * @param name 设施名称
     * @param isFirst 是否是第一个设施（用于处理分隔符）
     * @return 是否添加了设施（用于后续分隔符处理）
     */
    public static boolean appendFacilityIfNotNull(StringBuilder builder, Integer amount, String name, boolean isFirst) {
        if (amount == null || amount == 0) {
            return false;
        }
        
        if (!isFirst) {
            builder.append(FACILITY_SEPARATOR);
        }
        
        builder.append(amount).append(FACILITY_SUFFIX).append(name);
        return true;
    }
    
    /**
     * 计算两个地理坐标点之间的距离（使用Haversine公式）
     * @param lat1 第一个点的纬度
     * @param lng1 第一个点的经度  
     * @param lat2 第二个点的纬度
     * @param lng2 第二个点的经度
     * @return 两点之间的距离（米）
     */
    public static double calculateDistance(double lat1, double lng1, double lat2, double lng2) {
        final int R = 6371; // 地球半径（千米）
        
        double latDistance = Math.toRadians(lat2 - lat1);
        double lngDistance = Math.toRadians(lng2 - lng1);
        
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lngDistance / 2) * Math.sin(lngDistance / 2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return R * c * 1000; // 转换为米
    }
    
    /**
     * 获取TOP20开发商列表
     * @return TOP20开发商列表
     */
    public static Set<String> getTop20Developers() {
        Set<String> developers = new HashSet<>();
        developers.add("保利");
        developers.add("中海");
        developers.add("华润");
        developers.add("万科");
        developers.add("招商");
        developers.add("绿城");
        developers.add("建发");
        developers.add("越秀");
        developers.add("滨江集团");
        developers.add("华发");
        developers.add("龙湖");
        developers.add("金茂");
        developers.add("中国铁建");
        developers.add("金地");
        developers.add("绿地集团");
        developers.add("碧桂园");
        developers.add("保利置业");
        developers.add("融创");
        developers.add("中建壹品");
        developers.add("中交地产");
        return developers;
    }
    
    /**
     * 获取旅游养老目的地列表
     * @return 旅游养老目的地列表
     */
    public static Set<String> getTourismRetirementDestinations() {
        Set<String> destinations = new HashSet<>();
        destinations.add("云南");
        destinations.add("海南");
        destinations.add("惠州");
        destinations.add("秦皇岛");
        destinations.add("贵州");
        destinations.add("广西");
        return destinations;
    }
    
    /**
     * 提取物业费数值
     * @param propertyFeeDesc 物业费描述字符串
     * @return 物业费数值，如果无法解析则返回0.0
     */
    public static double extractPropertyFee(String propertyFeeDesc) {
        if (StringUtils.isBlank(propertyFeeDesc)) {
            return 0.0;
        }
        
        try {
            // 检查是否包含范围符号（"~"或"-"）
            if (propertyFeeDesc.contains("~") || propertyFeeDesc.contains("-")) {
                // 替换所有非数字、非点、非范围符号的字符
                String cleanStr = propertyFeeDesc.replaceAll("[^0-9.~\\-]", "");
                
                // 分割字符串获取范围值
                String[] parts;
                if (cleanStr.contains("~")) {
                    parts = cleanStr.split("~");
                } else {
                    parts = cleanStr.split("-");
                }
                
                if (parts.length == 2) {
                    double min = Double.parseDouble(parts[0]);
                    double max = Double.parseDouble(parts[1]);
                    // 计算均值
                    return (min + max) / 2;
                }
            }
            
            // 如果不是范围格式，使用原有逻辑处理
            String feeValue = propertyFeeDesc.replaceAll("[^0-9.]", "");
            if (StringUtils.isNotBlank(feeValue)) {
                return Double.parseDouble(feeValue);
            }
        } catch (Exception e) {
            // 忽略解析错误
        }
        
        return 0.0;
    }
    
    /**
     * 获取楼盘档次权重
     * @param buildingGrade 楼盘档次
     * @return 权重值，越大档次越高
     */
    public static int getBuildingGradeWeight(String buildingGrade) {
        if (StringUtils.isBlank(buildingGrade) || StringUtils.equalsIgnoreCase(buildingGrade, "null")) {
            return 0;
        }
        
        String grade = buildingGrade.toLowerCase();
        
        if (grade.contains("豪宅")) {
            return 5;
        } else if (grade.contains("高端")) {
            return 4;
        } else if (grade.contains("精品")) {
            return 3;
        } else if (grade.contains("改善")) {
            return 2;
        } else if (grade.contains("刚需") || grade.contains("普通") || grade.contains("首置") || grade.contains("首改")) {
            return 1;
        } else {
            return 0;
        }
    }
    
    /**
     * 查找签约时间最近的项目
     * 
     * @param projects 项目列表
     * @return 签约时间最近的项目，如果没有有效签约时间则返回null
     */
    public static ProjectDetail findLatestProject(List<ProjectDetail> projects) {
        if (projects == null || projects.isEmpty()) {
            return null;
        }
        
        ProjectDetail latestProject = null;
        String latestDate = null;
        
        for (ProjectDetail project : projects) {
            String signDate = project.getSignDatetime();
            
            // 跳过无效日期
            if (StringUtils.isBlank(signDate) || 
                StringUtils.equalsIgnoreCase(signDate, "null") ||
                (signDate.length() == 5 && StringUtils.isNumeric(signDate))) {
                continue;
            }
            
            // 尝试找出最近日期
            if (latestDate == null || isMoreRecentDate(signDate, latestDate)) {
                latestDate = signDate;
                latestProject = project;
            }
        }
        
        return latestProject;
    }
    
    /**
     * 查找签约时间最早的项目
     * 
     * @param projects 项目列表
     * @return 签约时间最早的项目，如果没有有效签约时间则返回null
     */
    public static ProjectDetail findEarliestProject(List<ProjectDetail> projects) {
        if (projects == null || projects.isEmpty()) {
            return null;
        }
        
        ProjectDetail earliestProject = null;
        String earliestDate = null;
        
        for (ProjectDetail project : projects) {
            String signDate = project.getSignDatetime();
            
            // 跳过无效日期
            if (StringUtils.isBlank(signDate) || 
                StringUtils.equalsIgnoreCase(signDate, "null") ||
                (signDate.length() == 5 && StringUtils.isNumeric(signDate))) {
                continue;
            }
            
            // 尝试找出最早日期（判断条件与findLatestProject相反）
            if (earliestDate == null || !isMoreRecentDate(signDate, earliestDate)) {
                earliestDate = signDate;
                earliestProject = project;
            }
        }
        
        return earliestProject;
    }
    
    /**
     * 比较两个日期字符串，判断第一个是否比第二个更近
     * 
     * @param date1 第一个日期字符串
     * @param date2 第二个日期字符串
     * @return 如果date1比date2更近则返回true，否则返回false
     */
    public static boolean isMoreRecentDate(String date1, String date2) {
        try {
            // 简单比较，假设日期格式能够按字典序比较
            // 实际项目中可能需要解析为Date对象再比较
            return date1.compareTo(date2) > 0;
        } catch (Exception e) {
            // 忽略异常，返回false
            return false;
        }
    }
} 