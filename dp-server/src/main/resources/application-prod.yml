# =======================================
# Production Profile Configuration
# =======================================

server:
  port: ${SERVER_PORT:8080} # 生产环境使用8080端口，通过环境变量可覆盖

spring:
  # 生产环境数据库配置
  # !!! 强烈建议使用环境变量或配置中心管理生产环境的敏感信息 !!!
  datasource:
    # 主数据源 (PostgreSQL)
    primary:
      driver-class-name: org.postgresql.Driver
      jdbcUrl: ${SPRING_DATASOURCE_URL:************************************************} # 使用环境变量，提供默认值
      username: ${SPRING_DATASOURCE_USERNAME:prod_user} # 使用环境变量
      password: ${SPRING_DATASOURCE_PASSWORD:prod_password} # 使用环境变量
      hikari:
        maximum-pool-size: 10
        minimum-idle: 3
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
    # MySQL数据源 (用于data_namelist表)
    mysql:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbcUrl: ${MYSQL_URL:***************************************************************************************************} # 使用环境变量，提供默认值
      username: ${MYSQL_USERNAME:root} # 使用环境变量
      password: ${MYSQL_PASSWORD:PROD_PASSWORD} # 使用环境变量
      hikari:
        maximum-pool-size: 10
        minimum-idle: 5
        connection-timeout: 30000
  # 生产环境 JPA 配置
  jpa:
    hibernate:
      # 生产环境建议使用 validate 或 none，避免自动修改 schema
      ddl-auto: validate
    # 生产环境通常关闭 SQL 显示
    show-sql: false
    properties:
      hibernate:
        format_sql: false
  # Redis配置
  data:
    redis:
      host: ***********
      port: 6379
      password: KxpV4cCAZY
      database: 1  # 使用的数据库索引，默认为0
      timeout: 10000  # 连接超时时间(毫秒)
      lettuce:
        pool:
          max-active: 16  # 连接池最大连接数，增加以支持并发
          max-idle: 16    # 连接池最大空闲连接数，与max-active匹配
          min-idle: 8     # 连接池最小空闲连接数，保持足够的空闲连接以避免频繁创建
          max-wait: 3000  # 连接池最大阻塞等待时间(毫秒)，设置有限等待时间
          time-between-eviction-runs: 60000  # 空闲连接检测间隔(毫秒)
      client-name: dips-pro  # 客户端名称，便于在Redis监控中识别

# 生产环境日志级别
logging:
  level:
    com.dipspro: INFO # 生产环境通常设置为 INFO 或 WARN
    org.springframework: WARN
    org.hibernate: WARN

n8n:
  auth:
    username: ${N8N_AUTH_USERNAME:dipspro} # 使用环境变量，提供默认值
    password: ${N8N_AUTH_PASSWORD:L4NshqmpVKVL6fpZ8GDK} # 使用环境变量，提供默认值
  webhook:
    url: ${N8N_WEBHOOK_PATH_CHAT:http://***********:5678/webhook/25afd8bc-1b85-48ab-a965-1043d2813989}