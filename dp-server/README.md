# DIPS Pro Server

基于Spring Boot 3.3.2和PostgreSQL的多租户系统后端服务。

## 技术栈

- **框架**: Spring Boot 3.3.2
- **数据库**: PostgreSQL
- **认证**: JWT + Spring Security
- **ORM**: Spring Data JPA + Hibernate
- **缓存**: Redis
- **构建工具**: Maven
- **Java版本**: 23

## 项目特性

### 多租户架构
- 完整的租户隔离机制
- 租户级别的配置管理
- 套餐定义和使用量统计

### RBAC权限管理
- 用户、角色、权限三级管理
- 支持角色继承和权限组合
- 菜单和API资源权限控制

### 组织架构管理
- 部门层级管理
- 岗位定义和分配
- 用户部门/岗位关联

### 安全认证
- JWT令牌认证
- 访问令牌和刷新令牌机制
- 密码强度验证和加密存储

### 审计日志
- 操作日志记录
- 登录日志追踪
- 完整的审计链路

## 快速开始

### 1. 环境准备

确保已安装以下软件：
- Java 23
- PostgreSQL 12+
- Redis 6+
- Maven 3.8+

### 2. 数据库初始化

```bash
# 创建数据库
createdb dips_pro

# 执行数据库脚本
psql -d dips_pro -f sql/sys_postgresql.sql
```

### 3. 配置文件

复制并修改配置文件：

```bash
cp src/main/resources/application.yml src/main/resources/application-local.yml
```

修改数据库连接信息：

```yaml
spring:
  datasource:
    url: *****************************************
    username: your_username
    password: your_password
```

### 4. 启动应用

```bash
# 开发环境启动
mvn spring-boot:run -Dspring-boot.run.profiles=local

# 或者打包后启动
mvn clean package
java -jar target/dp-server-0.0.1-SNAPSHOT.jar --spring.profiles.active=local
```

## API接口

### 认证接口

#### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "tenantCode": "default",
  "loginId": "admin",
  "password": "Admin123!"
}
```

#### 用户注册
```http
POST /api/auth/register
Content-Type: application/json

{
  "tenantCode": "default",
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "Password123!",
  "realName": "新用户"
}
```

#### 重置密码
```http
POST /api/auth/reset-password
Content-Type: application/json

{
  "tenantCode": "default",
  "loginId": "admin",
  "newPassword": "NewPassword123!"
}
```

#### 刷新令牌
```http
POST /api/auth/refresh
Authorization: Bearer <refresh_token>
```

### 响应格式

所有API接口统一返回格式：

```json
{
  "status": 1,
  "data": {},
  "message": "操作成功"
}
```

- `status`: 1表示成功，0表示失败
- `data`: 返回的数据
- `message`: 操作消息
- `error`: 错误信息（仅在失败时返回）

## 项目结构

```
src/main/java/com/dipspro/
├── modules/                    # 业务模块
│   ├── auth/                  # 认证授权模块
│   │   ├── entity/           # 实体类
│   │   ├── repository/       # 数据访问层
│   │   ├── service/          # 业务逻辑层
│   │   ├── controller/       # 控制器层
│   │   └── dto/              # 数据传输对象
│   ├── tenant/               # 租户管理模块
│   ├── user/                 # 用户管理模块
│   └── ...
├── config/                    # 配置类
├── security/                  # 安全相关
├── util/                      # 工具类
└── DipsProApp.java           # 启动类
```

## 数据库设计

### 核心表结构

- `sys_tenant`: 租户基础信息
- `sys_user`: 用户信息
- `sys_role`: 角色定义
- `sys_permission`: 权限定义
- `sys_user_role_relation`: 用户角色关联
- `sys_role_permission_relation`: 角色权限关联

详细的数据库设计请参考 `sql/sys_postgresql.sql` 文件。

## 开发指南

### 添加新模块

1. 在 `src/main/java/com/dipspro/modules/` 下创建新模块目录
2. 按照标准结构创建 `entity`, `repository`, `service`, `controller` 包
3. 实体类命名去掉数据库表的 `sys_` 前缀

### 权限控制

在Controller方法上使用注解进行权限控制：

```java
@PreAuthorize("hasPermission('user:create')")
@PostMapping("/users")
public ResponseEntity<?> createUser() {
    // 实现逻辑
}
```

### 多租户数据隔离

所有业务数据都应该包含 `tenant_id` 字段，确保数据隔离：

```java
@Query("SELECT u FROM User u WHERE u.tenantId = :tenantId")
List<User> findByTenantId(@Param("tenantId") Long tenantId);
```

## 部署

### Docker部署

```bash
# 构建镜像
docker build -t dips-pro-server .

# 运行容器
docker run -d \
  --name dips-pro-server \
  -p 8080:8080 \
  -e DB_USERNAME=postgres \
  -e DB_PASSWORD=password \
  -e REDIS_HOST=redis \
  dips-pro-server
```

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| DB_USERNAME | 数据库用户名 | postgres |
| DB_PASSWORD | 数据库密码 | password |
| REDIS_HOST | Redis主机 | localhost |
| REDIS_PORT | Redis端口 | 6379 |
| JWT_SECRET | JWT密钥 | 默认密钥 |
| SERVER_PORT | 服务端口 | 8080 |

## 许可证

MIT License 