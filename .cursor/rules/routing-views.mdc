---
description:
globs:
alwaysApply: false
---
# 路由与视图指南

## 路由配置

项目使用 Vue Router 进行路由管理，主要配置文件包括：

- **router/index.ts**: 路由初始化和配置
- **router/routes.ts**: 路由表定义
- **router/guards.ts**: 路由守卫
- **router/modules/**: 按模块划分的路由配置

### 路由结构

路由配置采用模块化结构，在 `router/modules/` 下按功能模块分开定义路由，然后在 `routes.ts` 中统一导入。

### 路由元信息

路由元信息 (meta) 用于定义路由的额外属性，常用的属性包括：

- **title**: 页面标题
- **icon**: 菜单图标
- **permissions**: 权限控制
- **keepAlive**: 是否缓存页面
- **layout**: 使用的布局组件

## 视图组件

视图组件位于 `src/views` 目录下，通常按照路由路径结构组织。

### 视图组件命名约定

- 单词使用 kebab-case (短横线) 命名
- 同一模块下的页面放在同一目录下
- 文件名与路由路径保持一致

### 基于文件的路由

项目使用 `vite-plugin-pages` 插件支持基于文件的路由，可通过在 Vue 组件中使用 `<route>` 自定义块定义路由相关信息：

```vue
<route lang="yaml">
meta:
  title: 页面标题
  icon: home
  permissions: ['user:view']
</route>
```

### 视图组件结构示例

基本视图组件结构：

```vue
<route lang="yaml">
meta:
  title: 页面标题
  icon: home
</route>

<script setup lang="ts">
// 导入和逻辑
</script>

<template>
  <!-- 视图内容 -->
</template>
```

## 布局系统

视图组件可以使用不同的布局，布局组件位于 `src/layouts` 目录。

常用布局:

- **default**: 默认布局，包含侧边栏、顶部栏和内容区域
- **blank**: 空白布局，仅包含内容区域

## 路由守卫

在 `router/guards.ts` 中定义了全局路由守卫，处理权限验证、登录状态检查等。

主要守卫功能：

- **权限检查**: 检查用户是否有权限访问路由
- **登录状态检查**: 检查用户是否已登录
- **动态标题**: 根据路由元信息设置页面标题

## 示例文件

- [src/views/chat/container.vue](mdc:src/views/chat/container.vue): 带有布局的视图示例
- [src/router/routes.ts](mdc:src/router/routes.ts): 路由配置示例
