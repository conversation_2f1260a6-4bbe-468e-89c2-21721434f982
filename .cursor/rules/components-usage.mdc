---
description:
globs:
alwaysApply: false
---
# 组件使用指南

## 组件分类

本项目中的组件主要分为以下几类：

1. **UI 基础组件**：项目中定制的基础 UI 组件，位于 `src/ui` 目录
2. **业务组件**：与业务逻辑相关的可复用组件，位于 `src/components` 目录
3. **布局组件**：用于页面布局的组件，位于 `src/layouts` 目录
4. **第三方组件库**：如 Element Plus、Ant Design Vue 等

## 组件命名约定

- UI 基础组件通常以 `Fa` 前缀命名，如 `FaButton`、`FaLayoutContainer` 等
- 业务组件使用 PascalCase 命名方式，如 `AccountButton`、`ImageUpload` 等
- 组件文件夹与组件名保持一致

## 组件使用示例

### UI 基础组件

布局容器组件 `FaLayoutContainer` 的使用示例：

```vue
<FaLayoutContainer
  :vertical="vertical"
  :enable-left-side="enableLeftSide"
  :enable-right-side="enableRightSide"
  :left-side-width="leftSideWidth"
  :right-side-width="rightSideWidth"
>
  <template #leftSide>
    <!-- 左侧边栏内容 -->
  </template>
  <template #rightSide>
    <!-- 右侧边栏内容 -->
  </template>
  <!-- 主要内容 -->
</FaLayoutContainer>
```

### 业务组件

图片上传组件 `ImageUpload` 的使用示例：

```vue
<ImageUpload
  v-model:file-list="fileList"
  :max-count="1"
  :before-upload="beforeUpload"
/>
```

## 组件开发规范

1. **单一职责**：每个组件应该只有一个职责
2. **可复用性**：组件应该设计成可复用的
3. **类型定义**：使用 TypeScript 定义 props、emits 和其他类型
4. **自定义事件**：使用 emits 选项声明组件的自定义事件
5. **插槽使用**：适当使用具名插槽增强组件的可定制性

## 组件通信

1. **Props/Events**：父子组件通信的首选方式
2. **Provide/Inject**：跨越多层级组件的通信
3. **Pinia Store**：全局状态管理

## 示例文件

- [src/views/chat/container.vue](mdc:src/views/chat/container.vue)：使用 `FaLayoutContainer` 的示例
