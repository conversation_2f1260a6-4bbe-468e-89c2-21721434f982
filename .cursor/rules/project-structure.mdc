---
description:
globs:
alwaysApply: false
---
# 项目结构指南

## 核心目录结构

```
.
├── src/                      # 源代码目录
│   ├── api/                  # API 接口
│   ├── assets/               # 静态资源
│   ├── components/           # 全局组件
│   ├── layouts/              # 布局组件
│   ├── locales/              # 国际化语言包
│   ├── router/               # 路由配置
│   ├── store/                # Pinia 状态管理
│   ├── ui/                   # UI 组件库
│   ├── utils/                # 工具函数
│   ├── views/                # 页面视图
│   ├── App.vue               # 应用根组件
│   └── main.ts               # 应用入口
├── vite/                     # Vite 插件配置
├── public/                   # 静态资源目录
├── themes/                   # 主题配置
├── vite.config.ts            # Vite 配置
└── package.json              # 项目依赖
```

## 详细说明

### 源代码目录 (`src/`)

- **api/**：API 接口请求
  - 按模块划分的 API 请求函数

- **assets/**：静态资源
  - **icons/**：图标资源
  - **images/**：图片资源
  - **styles/**：全局样式

- **components/**：可复用组件
  - 全局通用组件，如表单、按钮等

- **layouts/**：布局组件
  - 页面布局组件

- **locales/**：国际化语言包
  - 多语言支持文件

- **router/**：路由配置
  - **modules/**：按模块划分的路由配置
  - **routes.ts**：路由主配置
  - **guards.ts**：路由守卫

- **store/**：Pinia 状态管理
  - 按模块划分的状态管理

- **ui/**：UI 组件库
  - 自定义 UI 组件

- **utils/**：工具函数
  - 各种工具函数和辅助方法

- **views/**：页面视图
  - 按模块划分的页面组件

### 配置文件

- **vite/plugins/**: Vite 插件配置
- **vite.config.ts**: Vite 构建配置
- **uno.config.ts**: UnoCSS 配置
- **tsconfig.json**: TypeScript 配置
