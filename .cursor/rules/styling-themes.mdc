---
description:
globs:
alwaysApply: false
---
# 样式与主题指南

## 样式技术栈

项目的样式主要基于以下技术：

1. **UnoCSS**: 原子化 CSS 框架
2. **SCSS**: CSS 预处理器
3. **CSS 变量**: 主题定制

## UnoCSS 使用规范

UnoCSS 是项目中主要使用的 CSS 框架，配置文件为 `uno.config.ts`。

### 常用类名

- **布局**: `flex`, `grid`, `block`, `hidden` 等
- **尺寸**: `w-{size}`, `h-{size}`, `max-w-{size}` 等
- **间距**: `m-{size}`, `p-{size}`, `gap-{size}` 等
- **颜色**: `text-{color}`, `bg-{color}`, `border-{color}` 等
- **交互**: `hover:`, `focus:`, `active:` 等

### 组件中的使用示例

```vue
<template>
  <div class="flex items-center justify-between p-4 bg-white rounded-lg shadow">
    <h2 class="text-xl font-bold text-primary">标题</h2>
    <button class="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark">
      按钮
    </button>
  </div>
</template>
```

## SCSS 使用规范

SCSS 主要用于组件内的复杂样式和全局样式定义。

### 全局 SCSS 资源

全局 SCSS 资源位于 `src/assets/styles/` 目录下，包括：

- **variables.scss**: 全局变量
- **mixins.scss**: 混合函数
- **base.scss**: 基础样式
- **utilities.scss**: 工具类

### 组件中的使用示例

```vue
<style lang="scss">
.component {
  @include flex-center;

  .title {
    font-size: var(--font-size-lg);
    color: var(--color-primary);
  }

  .content {
    margin-top: 1rem;

    &:hover {
      background-color: var(--color-bg-hover);
    }
  }
}
</style>
```

## 主题系统

项目使用 CSS 变量实现主题切换功能，主题配置文件位于 `themes/` 目录。

### 颜色变量

主要颜色变量包括：

- **--color-primary**: 主色调
- **--color-success**: 成功色
- **--color-warning**: 警告色
- **--color-danger**: 危险色
- **--color-info**: 信息色
- **--color-text-primary**: 主要文本色
- **--color-text-secondary**: 次要文本色
- **--color-bg**: 背景色

### 深色模式

深色模式通过给 `html` 标签添加 `dark` 类名切换，CSS 变量会根据模式自动切换。

## 响应式设计

项目使用断点变量实现响应式设计：

- **xs**: < 576px
- **sm**: ≥ 576px
- **md**: ≥ 768px
- **lg**: ≥ 992px
- **xl**: ≥ 1200px
- **xxl**: ≥ 1600px

### 响应式示例

```vue
<div class="hidden md:block">仅在中等及以上屏幕显示</div>
<div class="block md:hidden">仅在小屏幕显示</div>
```

## 样式最佳实践

1. **优先使用 UnoCSS**: 对于简单的样式，优先使用 UnoCSS 的原子类
2. **避免深层嵌套**: SCSS 嵌套不要超过 3 层
3. **使用变量**: 使用 CSS 变量而不是硬编码值
4. **组件样式隔离**: 使用 `<style scoped>` 防止样式污染
5. **响应式优先**: 设计时优先考虑移动端适配
