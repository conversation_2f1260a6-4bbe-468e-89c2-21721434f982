---
description:
globs:
alwaysApply: true
---
# DIPS Pro Web 项目概述

本项目是一个使用 Vue 3 + TypeScript + Vite 构建的前端应用程序。

## 技术栈

- **框架**: Vue 3.5+
- **语言**: TypeScript
- **构建工具**: Vite 6.x
- **状态管理**: Pinia 3.x
- **路由**: Vue Router 4.x
- **UI 组件库**:
  - Element Plus
  - Ant Design Vue
  - 自定义 UI 组件
- **样式**:
  - UnoCSS
  - SCSS
- **工具库**:
  - VueUse
  - dayjs
  - axios

## 项目结构

项目采用模块化的文件组织结构，主要包括以下部分：

- `src/components`: 可复用组件
- `src/views`: 页面视图
- `src/router`: 路由配置
- `src/store`: 状态管理
- `src/api`: API 接口
- `src/utils`: 工具函数
- `src/assets`: 静态资源
- `src/layouts`: 布局组件
- `src/ui`: UI 组件

## 关键文件

- [package.json](mdc:package.json): 项目依赖和脚本配置
- [vite.config.ts](mdc:vite.config.ts): Vite 构建配置
- [src/main.ts](mdc:src/main.ts): 应用入口文件
- [src/App.vue](mdc:src/App.vue): 应用根组件
- [src/router/index.ts](mdc:src/router/index.ts): 路由配置
